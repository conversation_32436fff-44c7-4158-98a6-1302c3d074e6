"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_marketing_ABTestingProvider_tsx"],{

/***/ "(app-pages-browser)/./src/components/marketing/ABTestingProvider.tsx":
/*!********************************************************!*\
  !*** ./src/components/marketing/ABTestingProvider.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ABTestingProvider: () => (/* binding */ ABTestingProvider),\n/* harmony export */   useABTesting: () => (/* binding */ useABTesting),\n/* harmony export */   useSafeABTesting: () => (/* binding */ useSafeABTesting)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ABTestingProvider,useABTesting,useSafeABTesting auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// إنشاء سياق التجارب\nconst ABTestingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ABTestingProvider(param) {\n    let { children } = param;\n    _s();\n    // حالة التجارب\n    const [experiments, setExperiments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        productLayout: 'A',\n        ctaColor: 'A',\n        heroImage: 'A',\n        checkoutProcess: 'A'\n    });\n    // تهيئة التجارب عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ABTestingProvider.useEffect\": ()=>{\n            // التحقق من وجود تجارب محفوظة في localStorage\n            const savedExperiments = localStorage.getItem('ab_testing_experiments');\n            if (savedExperiments) {\n                try {\n                    const parsedExperiments = JSON.parse(savedExperiments);\n                    setExperiments({\n                        \"ABTestingProvider.useEffect\": (prev)=>({\n                                ...prev,\n                                ...parsedExperiments\n                            })\n                    }[\"ABTestingProvider.useEffect\"]);\n                } catch (error) {\n                    console.error('Error parsing saved experiments:', error);\n                }\n            } else {\n                // إنشاء تجارب جديدة بشكل عشوائي\n                const newExperiments = {\n                    productLayout: Math.random() < 0.5 ? 'A' : 'B',\n                    ctaColor: Math.random() < 0.5 ? 'A' : 'B',\n                    heroImage: Math.random() < 0.5 ? 'A' : 'B',\n                    checkoutProcess: Math.random() < 0.5 ? 'A' : 'B'\n                };\n                setExperiments(newExperiments);\n                localStorage.setItem('ab_testing_experiments', JSON.stringify(newExperiments));\n            }\n        }\n    }[\"ABTestingProvider.useEffect\"], []);\n    // الحصول على متغير التجربة\n    const getVariant = (experimentName)=>{\n        return experiments[experimentName] || 'A';\n    };\n    // تتبع التحويل\n    const trackConversion = (experimentName)=>{\n        // هنا يمكن إضافة رمز لإرسال بيانات التحويل إلى خدمة تحليلات\n        console.log(\"Conversion tracked for experiment: \".concat(experimentName, \", variant: \").concat(experiments[experimentName]));\n        // يمكن استخدام Google Analytics أو أي خدمة تحليلات أخرى\n        if ( true && 'gtag' in window) {\n            const gtag = window.gtag;\n            gtag('event', 'ab_test_conversion', {\n                'experiment_name': experimentName,\n                'variant': experiments[experimentName]\n            });\n        }\n    };\n    // قيمة سياق التجارب\n    const contextValue = {\n        getVariant,\n        trackConversion,\n        experiments\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ABTestingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\marketing\\\\ABTestingProvider.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(ABTestingProvider, \"OTIYSbe/s9PlFnqdf+jWrxkl0HU=\");\n_c = ABTestingProvider;\n// Hook لاستخدام سياق التجارب مع معالجة آمنة للأخطاء\nfunction useABTesting() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ABTestingContext);\n    if (context === undefined) {\n        // Return safe fallback instead of throwing error\n        console.warn('useABTesting used outside ABTestingProvider, returning fallback');\n        return {\n            getVariant: ()=>'A',\n            trackConversion: ()=>{},\n            experiments: {\n                productLayout: 'A',\n                ctaColor: 'A',\n                heroImage: 'A',\n                checkoutProcess: 'A'\n            }\n        };\n    }\n    return context;\n}\n_s1(useABTesting, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Safe hook that never throws errors\nfunction useSafeABTesting() {\n    try {\n        const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ABTestingContext);\n        return context || {\n            getVariant: ()=>'A',\n            trackConversion: ()=>{},\n            experiments: {\n                productLayout: 'A',\n                ctaColor: 'A',\n                heroImage: 'A',\n                checkoutProcess: 'A'\n            }\n        };\n    } catch (error) {\n        console.warn('Error in useSafeABTesting:', error);\n        return {\n            getVariant: ()=>'A',\n            trackConversion: ()=>{},\n            experiments: {\n                productLayout: 'A',\n                ctaColor: 'A',\n                heroImage: 'A',\n                checkoutProcess: 'A'\n            }\n        };\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"ABTestingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hcmtldGluZy9BQlRlc3RpbmdQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFa0Y7QUFhbEYscUJBQXFCO0FBQ3JCLE1BQU1JLGlDQUFtQkosb0RBQWFBLENBQW1DSztBQU9sRSxTQUFTQyxrQkFBa0IsS0FBb0M7UUFBcEMsRUFBRUMsUUFBUSxFQUEwQixHQUFwQzs7SUFDaEMsZUFBZTtJQUNmLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBc0M7UUFDbEZRLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLGlCQUFpQjtJQUNuQjtJQUVBLGlDQUFpQztJQUNqQ1YsZ0RBQVNBO3VDQUFDO1lBQ1IsOENBQThDO1lBQzlDLE1BQU1XLG1CQUFtQkMsYUFBYUMsT0FBTyxDQUFDO1lBRTlDLElBQUlGLGtCQUFrQjtnQkFDcEIsSUFBSTtvQkFDRixNQUFNRyxvQkFBb0JDLEtBQUtDLEtBQUssQ0FBQ0w7b0JBQ3JDTDt1REFBZVcsQ0FBQUEsT0FBUztnQ0FBRSxHQUFHQSxJQUFJO2dDQUFFLEdBQUdILGlCQUFpQjs0QkFBQzs7Z0JBQzFELEVBQUUsT0FBT0ksT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLG9DQUFvQ0E7Z0JBQ3BEO1lBQ0YsT0FBTztnQkFDTCxnQ0FBZ0M7Z0JBQ2hDLE1BQU1FLGlCQUFzRDtvQkFDMURiLGVBQWVjLEtBQUtDLE1BQU0sS0FBSyxNQUFNLE1BQU07b0JBQzNDZCxVQUFVYSxLQUFLQyxNQUFNLEtBQUssTUFBTSxNQUFNO29CQUN0Q2IsV0FBV1ksS0FBS0MsTUFBTSxLQUFLLE1BQU0sTUFBTTtvQkFDdkNaLGlCQUFpQlcsS0FBS0MsTUFBTSxLQUFLLE1BQU0sTUFBTTtnQkFDL0M7Z0JBRUFoQixlQUFlYztnQkFDZlIsYUFBYVcsT0FBTyxDQUFDLDBCQUEwQlIsS0FBS1MsU0FBUyxDQUFDSjtZQUNoRTtRQUNGO3NDQUFHLEVBQUU7SUFFTCwyQkFBMkI7SUFDM0IsTUFBTUssYUFBYSxDQUFDQztRQUNsQixPQUFPckIsV0FBVyxDQUFDcUIsZUFBZSxJQUFJO0lBQ3hDO0lBRUEsZUFBZTtJQUNmLE1BQU1DLGtCQUFrQixDQUFDRDtRQUN2Qiw0REFBNEQ7UUFDNURQLFFBQVFTLEdBQUcsQ0FBQyxzQ0FBa0V2QixPQUE1QnFCLGdCQUFlLGVBQXlDLE9BQTVCckIsV0FBVyxDQUFDcUIsZUFBZTtRQUV6Ryx3REFBd0Q7UUFDeEQsSUFBSSxLQUE2QixJQUFJLFVBQVVHLFFBQVE7WUFDckQsTUFBTUMsT0FBTyxPQUFnQkEsSUFBSTtZQUNqQ0EsS0FBSyxTQUFTLHNCQUFzQjtnQkFDbEMsbUJBQW1CSjtnQkFDbkIsV0FBV3JCLFdBQVcsQ0FBQ3FCLGVBQWU7WUFDeEM7UUFDRjtJQUNGO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU1LLGVBQXFDO1FBQ3pDTjtRQUNBRTtRQUNBdEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDSixpQkFBaUIrQixRQUFRO1FBQUNDLE9BQU9GO2tCQUMvQjNCOzs7Ozs7QUFHUDtHQW5FZ0JEO0tBQUFBO0FBcUVoQixvREFBb0Q7QUFDN0MsU0FBUytCOztJQUNkLE1BQU1DLFVBQVVyQyxpREFBVUEsQ0FBQ0c7SUFFM0IsSUFBSWtDLFlBQVlqQyxXQUFXO1FBQ3pCLGlEQUFpRDtRQUNqRGlCLFFBQVFpQixJQUFJLENBQUM7UUFDYixPQUFPO1lBQ0xYLFlBQVksSUFBTTtZQUNsQkUsaUJBQWlCLEtBQU87WUFDeEJ0QixhQUFhO2dCQUNYRSxlQUFlO2dCQUNmQyxVQUFVO2dCQUNWQyxXQUFXO2dCQUNYQyxpQkFBaUI7WUFDbkI7UUFDRjtJQUNGO0lBRUEsT0FBT3lCO0FBQ1Q7SUFuQmdCRDtBQXFCaEIscUNBQXFDO0FBQzlCLFNBQVNHO0lBQ2QsSUFBSTtRQUNGLE1BQU1GLFVBQVVyQyxpREFBVUEsQ0FBQ0c7UUFDM0IsT0FBT2tDLFdBQVc7WUFDaEJWLFlBQVksSUFBTTtZQUNsQkUsaUJBQWlCLEtBQU87WUFDeEJ0QixhQUFhO2dCQUNYRSxlQUFlO2dCQUNmQyxVQUFVO2dCQUNWQyxXQUFXO2dCQUNYQyxpQkFBaUI7WUFDbkI7UUFDRjtJQUNGLEVBQUUsT0FBT1EsT0FBTztRQUNkQyxRQUFRaUIsSUFBSSxDQUFDLDhCQUE4QmxCO1FBQzNDLE9BQU87WUFDTE8sWUFBWSxJQUFNO1lBQ2xCRSxpQkFBaUIsS0FBTztZQUN4QnRCLGFBQWE7Z0JBQ1hFLGVBQWU7Z0JBQ2ZDLFVBQVU7Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLGlCQUFpQjtZQUNuQjtRQUNGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBa3JhbVlhaHlhXFxEZXNrdG9wXFxlY29tbWVyY2Vwcm9cXHNyY1xcY29tcG9uZW50c1xcbWFya2V0aW5nXFxBQlRlc3RpbmdQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbi8vINiq2LnYsdmK2YEg2KPZhtmI2KfYuSDYp9mE2KrYrNin2LHYqFxuZXhwb3J0IHR5cGUgRXhwZXJpbWVudE5hbWUgPSAncHJvZHVjdExheW91dCcgfCAnY3RhQ29sb3InIHwgJ2hlcm9JbWFnZScgfCAnY2hlY2tvdXRQcm9jZXNzJztcbmV4cG9ydCB0eXBlIFZhcmlhbnROYW1lID0gJ0EnIHwgJ0InO1xuXG4vLyDYqti52LHZitmBINmG2YjYuSDYs9mK2KfZgiDYp9mE2KrYrNin2LHYqFxuaW50ZXJmYWNlIEFCVGVzdGluZ0NvbnRleHRUeXBlIHtcbiAgZ2V0VmFyaWFudDogKGV4cGVyaW1lbnROYW1lOiBFeHBlcmltZW50TmFtZSkgPT4gVmFyaWFudE5hbWU7XG4gIHRyYWNrQ29udmVyc2lvbjogKGV4cGVyaW1lbnROYW1lOiBFeHBlcmltZW50TmFtZSkgPT4gdm9pZDtcbiAgZXhwZXJpbWVudHM6IFJlY29yZDxFeHBlcmltZW50TmFtZSwgVmFyaWFudE5hbWU+O1xufVxuXG4vLyDYpdmG2LTYp9ihINiz2YrYp9mCINin2YTYqtis2KfYsdioXG5jb25zdCBBQlRlc3RpbmdDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBQlRlc3RpbmdDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuLy8g2K7Ytdin2KbYtSDZhdiy2YjYryDYp9mE2KrYrNin2LHYqFxuaW50ZXJmYWNlIEFCVGVzdGluZ1Byb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQUJUZXN0aW5nUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBBQlRlc3RpbmdQcm92aWRlclByb3BzKSB7XG4gIC8vINit2KfZhNipINin2YTYqtis2KfYsdioXG4gIGNvbnN0IFtleHBlcmltZW50cywgc2V0RXhwZXJpbWVudHNdID0gdXNlU3RhdGU8UmVjb3JkPEV4cGVyaW1lbnROYW1lLCBWYXJpYW50TmFtZT4+KHtcbiAgICBwcm9kdWN0TGF5b3V0OiAnQScsXG4gICAgY3RhQ29sb3I6ICdBJyxcbiAgICBoZXJvSW1hZ2U6ICdBJyxcbiAgICBjaGVja291dFByb2Nlc3M6ICdBJ1xuICB9KTtcblxuICAvLyDYqtmH2YrYptipINin2YTYqtis2KfYsdioINi52YbYryDYqtit2YXZitmEINin2YTYtdmB2K3YqVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmI2KzZiNivINiq2KzYp9ix2Kgg2YXYrdmB2YjYuNipINmB2YogbG9jYWxTdG9yYWdlXG4gICAgY29uc3Qgc2F2ZWRFeHBlcmltZW50cyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhYl90ZXN0aW5nX2V4cGVyaW1lbnRzJyk7XG5cbiAgICBpZiAoc2F2ZWRFeHBlcmltZW50cykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcGFyc2VkRXhwZXJpbWVudHMgPSBKU09OLnBhcnNlKHNhdmVkRXhwZXJpbWVudHMpO1xuICAgICAgICBzZXRFeHBlcmltZW50cyhwcmV2ID0+ICh7IC4uLnByZXYsIC4uLnBhcnNlZEV4cGVyaW1lbnRzIH0pKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgc2F2ZWQgZXhwZXJpbWVudHM6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyDYpdmG2LTYp9ihINiq2KzYp9ix2Kgg2KzYr9mK2K/YqSDYqNi02YPZhCDYudi02YjYp9im2YpcbiAgICAgIGNvbnN0IG5ld0V4cGVyaW1lbnRzOiBSZWNvcmQ8RXhwZXJpbWVudE5hbWUsIFZhcmlhbnROYW1lPiA9IHtcbiAgICAgICAgcHJvZHVjdExheW91dDogTWF0aC5yYW5kb20oKSA8IDAuNSA/ICdBJyA6ICdCJyxcbiAgICAgICAgY3RhQ29sb3I6IE1hdGgucmFuZG9tKCkgPCAwLjUgPyAnQScgOiAnQicsXG4gICAgICAgIGhlcm9JbWFnZTogTWF0aC5yYW5kb20oKSA8IDAuNSA/ICdBJyA6ICdCJyxcbiAgICAgICAgY2hlY2tvdXRQcm9jZXNzOiBNYXRoLnJhbmRvbSgpIDwgMC41ID8gJ0EnIDogJ0InXG4gICAgICB9O1xuXG4gICAgICBzZXRFeHBlcmltZW50cyhuZXdFeHBlcmltZW50cyk7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWJfdGVzdGluZ19leHBlcmltZW50cycsIEpTT04uc3RyaW5naWZ5KG5ld0V4cGVyaW1lbnRzKSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8g2KfZhNit2LXZiNmEINi52YTZiSDZhdiq2LrZitixINin2YTYqtis2LHYqNipXG4gIGNvbnN0IGdldFZhcmlhbnQgPSAoZXhwZXJpbWVudE5hbWU6IEV4cGVyaW1lbnROYW1lKTogVmFyaWFudE5hbWUgPT4ge1xuICAgIHJldHVybiBleHBlcmltZW50c1tleHBlcmltZW50TmFtZV0gfHwgJ0EnO1xuICB9O1xuXG4gIC8vINiq2KrYqNi5INin2YTYqtit2YjZitmEXG4gIGNvbnN0IHRyYWNrQ29udmVyc2lvbiA9IChleHBlcmltZW50TmFtZTogRXhwZXJpbWVudE5hbWUpID0+IHtcbiAgICAvLyDZh9mG2Kcg2YrZhdmD2YYg2KXYttin2YHYqSDYsdmF2LIg2YTYpdix2LPYp9mEINio2YrYp9mG2KfYqiDYp9mE2KrYrdmI2YrZhCDYpdmE2Ykg2K7Yr9mF2Kkg2KrYrdmE2YrZhNin2KpcbiAgICBjb25zb2xlLmxvZyhgQ29udmVyc2lvbiB0cmFja2VkIGZvciBleHBlcmltZW50OiAke2V4cGVyaW1lbnROYW1lfSwgdmFyaWFudDogJHtleHBlcmltZW50c1tleHBlcmltZW50TmFtZV19YCk7XG5cbiAgICAvLyDZitmF2YPZhiDYp9iz2KrYrtiv2KfZhSBHb29nbGUgQW5hbHl0aWNzINij2Ygg2KPZiiDYrtiv2YXYqSDYqtit2YTZitmE2KfYqiDYo9iu2LHZiVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAnZ3RhZycgaW4gd2luZG93KSB7XG4gICAgICBjb25zdCBndGFnID0gKHdpbmRvdyBhcyBhbnkpLmd0YWc7XG4gICAgICBndGFnKCdldmVudCcsICdhYl90ZXN0X2NvbnZlcnNpb24nLCB7XG4gICAgICAgICdleHBlcmltZW50X25hbWUnOiBleHBlcmltZW50TmFtZSxcbiAgICAgICAgJ3ZhcmlhbnQnOiBleHBlcmltZW50c1tleHBlcmltZW50TmFtZV1cbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICAvLyDZgtmK2YXYqSDYs9mK2KfZgiDYp9mE2KrYrNin2LHYqFxuICBjb25zdCBjb250ZXh0VmFsdWU6IEFCVGVzdGluZ0NvbnRleHRUeXBlID0ge1xuICAgIGdldFZhcmlhbnQsXG4gICAgdHJhY2tDb252ZXJzaW9uLFxuICAgIGV4cGVyaW1lbnRzXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8QUJUZXN0aW5nQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17Y29udGV4dFZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0FCVGVzdGluZ0NvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbi8vIEhvb2sg2YTYp9iz2KrYrtiv2KfZhSDYs9mK2KfZgiDYp9mE2KrYrNin2LHYqCDZhdi5INmF2LnYp9mE2KzYqSDYotmF2YbYqSDZhNmE2KPYrti32KfYoVxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFCVGVzdGluZygpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQUJUZXN0aW5nQ29udGV4dCk7XG5cbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIC8vIFJldHVybiBzYWZlIGZhbGxiYWNrIGluc3RlYWQgb2YgdGhyb3dpbmcgZXJyb3JcbiAgICBjb25zb2xlLndhcm4oJ3VzZUFCVGVzdGluZyB1c2VkIG91dHNpZGUgQUJUZXN0aW5nUHJvdmlkZXIsIHJldHVybmluZyBmYWxsYmFjaycpO1xuICAgIHJldHVybiB7XG4gICAgICBnZXRWYXJpYW50OiAoKSA9PiAnQScgYXMgVmFyaWFudE5hbWUsXG4gICAgICB0cmFja0NvbnZlcnNpb246ICgpID0+IHt9LFxuICAgICAgZXhwZXJpbWVudHM6IHtcbiAgICAgICAgcHJvZHVjdExheW91dDogJ0EnIGFzIFZhcmlhbnROYW1lLFxuICAgICAgICBjdGFDb2xvcjogJ0EnIGFzIFZhcmlhbnROYW1lLFxuICAgICAgICBoZXJvSW1hZ2U6ICdBJyBhcyBWYXJpYW50TmFtZSxcbiAgICAgICAgY2hlY2tvdXRQcm9jZXNzOiAnQScgYXMgVmFyaWFudE5hbWVcbiAgICAgIH1cbiAgICB9O1xuICB9XG5cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbi8vIFNhZmUgaG9vayB0aGF0IG5ldmVyIHRocm93cyBlcnJvcnNcbmV4cG9ydCBmdW5jdGlvbiB1c2VTYWZlQUJUZXN0aW5nKCkge1xuICB0cnkge1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEFCVGVzdGluZ0NvbnRleHQpO1xuICAgIHJldHVybiBjb250ZXh0IHx8IHtcbiAgICAgIGdldFZhcmlhbnQ6ICgpID0+ICdBJyBhcyBWYXJpYW50TmFtZSxcbiAgICAgIHRyYWNrQ29udmVyc2lvbjogKCkgPT4ge30sXG4gICAgICBleHBlcmltZW50czoge1xuICAgICAgICBwcm9kdWN0TGF5b3V0OiAnQScgYXMgVmFyaWFudE5hbWUsXG4gICAgICAgIGN0YUNvbG9yOiAnQScgYXMgVmFyaWFudE5hbWUsXG4gICAgICAgIGhlcm9JbWFnZTogJ0EnIGFzIFZhcmlhbnROYW1lLFxuICAgICAgICBjaGVja291dFByb2Nlc3M6ICdBJyBhcyBWYXJpYW50TmFtZVxuICAgICAgfVxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS53YXJuKCdFcnJvciBpbiB1c2VTYWZlQUJUZXN0aW5nOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgZ2V0VmFyaWFudDogKCkgPT4gJ0EnIGFzIFZhcmlhbnROYW1lLFxuICAgICAgdHJhY2tDb252ZXJzaW9uOiAoKSA9PiB7fSxcbiAgICAgIGV4cGVyaW1lbnRzOiB7XG4gICAgICAgIHByb2R1Y3RMYXlvdXQ6ICdBJyBhcyBWYXJpYW50TmFtZSxcbiAgICAgICAgY3RhQ29sb3I6ICdBJyBhcyBWYXJpYW50TmFtZSxcbiAgICAgICAgaGVyb0ltYWdlOiAnQScgYXMgVmFyaWFudE5hbWUsXG4gICAgICAgIGNoZWNrb3V0UHJvY2VzczogJ0EnIGFzIFZhcmlhbnROYW1lXG4gICAgICB9XG4gICAgfTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJBQlRlc3RpbmdDb250ZXh0IiwidW5kZWZpbmVkIiwiQUJUZXN0aW5nUHJvdmlkZXIiLCJjaGlsZHJlbiIsImV4cGVyaW1lbnRzIiwic2V0RXhwZXJpbWVudHMiLCJwcm9kdWN0TGF5b3V0IiwiY3RhQ29sb3IiLCJoZXJvSW1hZ2UiLCJjaGVja291dFByb2Nlc3MiLCJzYXZlZEV4cGVyaW1lbnRzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInBhcnNlZEV4cGVyaW1lbnRzIiwiSlNPTiIsInBhcnNlIiwicHJldiIsImVycm9yIiwiY29uc29sZSIsIm5ld0V4cGVyaW1lbnRzIiwiTWF0aCIsInJhbmRvbSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJnZXRWYXJpYW50IiwiZXhwZXJpbWVudE5hbWUiLCJ0cmFja0NvbnZlcnNpb24iLCJsb2ciLCJ3aW5kb3ciLCJndGFnIiwiY29udGV4dFZhbHVlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUFCVGVzdGluZyIsImNvbnRleHQiLCJ3YXJuIiwidXNlU2FmZUFCVGVzdGluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/marketing/ABTestingProvider.tsx\n"));

/***/ })

}]);