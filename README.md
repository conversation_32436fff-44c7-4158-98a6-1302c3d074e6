# كوميرس برو - منصة تجارية متكاملة

منصة تجارية متكاملة تقدم خدمات البيع بالتجزئة والجملة وخطوط الإنتاج وخدمات الأعمال والمزيد.

## المميزات

- **متعدد اللغات**: دعم كامل للغة العربية والإنجليزية مع توجيه مستند إلى المسار (`/ar` و `/en`).
- **تصميم متجاوب**: تجربة مستخدم مثالية على جميع الأجهزة.
- **وضع الظلام**: دعم وضع الظلام لتجربة مستخدم أفضل.
- **تطبيق ويب تقدمي (PWA)**: يمكن تثبيته على الأجهزة المحمولة.
- **قاعدة بيانات SQLite**: تشغيل محلي دون الحاجة إلى اتصال بالإنترنت.
- **لوحة تحكم المسؤول**: إدارة كاملة للمنتجات والخدمات والطلبات والمستخدمين والمحتوى.
- **متجر إلكتروني**: منتجات، سلة تسوق، قائمة مفضلة، طلبات، مراجعات.
- **طلبات الجملة**: نظام طلبات الجملة للشركات.
- **خطوط الإنتاج**: عرض خطوط الإنتاج والمعدات.
- **خدمات الأعمال**: عرض وطلب الخدمات.
- **مدونة**: نشر وإدارة المحتوى.
- **تعدد العملات**: دعم عدة عملات (ريال سعودي، دولار أمريكي، يوان صيني).
- **طرق دفع متعددة**: الدفع عند الاستلام، مدى، فيزا/ماستركارد.
- **تحسين محركات البحث (SEO)**: تحسين للظهور في محركات البحث.

## التقنيات المستخدمة

- **Next.js**: إطار عمل React للتطبيقات الويب.
- **TypeScript**: لغة برمجة آمنة النوع.
- **Tailwind CSS**: إطار عمل CSS للتصميم.
- **React Query**: إدارة حالة API وتخزين مؤقت.
- **Zustand**: إدارة حالة التطبيق.
- **SQLite**: قاعدة بيانات محلية.
- **Framer Motion**: مكتبة للرسوم المتحركة.
- **Zod**: مكتبة للتحقق من صحة البيانات.
- **React Hook Form**: مكتبة لإدارة النماذج.

## متطلبات النظام

- **Node.js**: الإصدار 18 أو أحدث
- **npm**: الإصدار 9 أو أحدث

## التثبيت

### الطريقة السريعة (موصى بها)

1. استنساخ المستودع:

```bash
git clone https://github.com/yourusername/commercepro.git
cd commercepro
```

2. تشغيل سكريبت الإعداد:

```bash
node scripts/setup.js
```

3. تثبيت التبعيات وإعداد قاعدة البيانات:

```bash
npm run setup
```

4. تشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

5. فتح المتصفح على العنوان [http://localhost:3000](http://localhost:3000)

### التثبيت اليدوي

1. استنساخ المستودع:

```bash
git clone https://github.com/yourusername/commercepro.git
cd commercepro
```

2. تثبيت التبعيات:

```bash
npm install
```

3. تثبيت SQLite:

```bash
npm install sqlite3 --save --legacy-peer-deps
```

4. إنشاء ملف `.env.local` في المجلد الرئيسي وإضافة المتغيرات التالية:

```
# Configuración de SQLite
SQLITE_DB_PATH=./database.sqlite

# Configuración de la aplicación
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_DEFAULT_LANGUAGE=ar
NEXT_PUBLIC_SECONDARY_LANGUAGE=en

# Configuración de seguridad
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-here
NEXT_PUBLIC_CSRF_SECRET=your-csrf-secret-here
```

5. تهيئة قاعدة البيانات:

```bash
npm run db:init
```

6. إضافة بيانات تجريبية:

```bash
npm run db:seed
```

7. تشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

8. فتح المتصفح على العنوان [http://localhost:3000](http://localhost:3000)

## هيكل المشروع

```
commercepro/
├── public/             # الملفات الثابتة
├── src/                # كود المصدر
│   ├── app/            # مكونات Next.js App Router
│   ├── components/     # مكونات React
│   ├── data/           # بيانات ثابتة
│   ├── hooks/          # React Hooks
│   ├── lib/            # مكتبات ووظائف مساعدة
│   ├── pages/          # صفحات التطبيق
│   ├── services/       # خدمات البيانات
│   ├── stores/         # مخازن Zustand
│   ├── styles/         # أنماط CSS
│   ├── translations/   # ملفات الترجمة
│   └── types/          # تعريفات TypeScript
├── .env.local          # متغيرات البيئة المحلية
├── next.config.js      # تكوين Next.js
├── package.json        # تبعيات المشروع
├── tailwind.config.js  # تكوين Tailwind CSS
└── tsconfig.json       # تكوين TypeScript
```

## الاستخدام

### الواجهة الأمامية

- **الصفحة الرئيسية**: `/ar` أو `/en`
- **المتجر**: `/ar/shop` أو `/en/shop`
- **الخدمات**: `/ar/services` أو `/en/services`
- **خطوط الإنتاج**: `/ar/production-lines` أو `/en/production-lines`
- **المدونة**: `/ar/blog` أو `/en/blog`
- **طلبات الجملة**: `/ar/wholesale` أو `/en/wholesale`
- **حسابي**: `/ar/account` أو `/en/account`

### لوحة التحكم

- **لوحة التحكم**: `/ar/admin` أو `/en/admin`
- **المنتجات**: `/ar/admin/products` أو `/en/admin/products`
- **الخدمات**: `/ar/admin/services` أو `/en/admin/services`
- **خطوط الإنتاج**: `/ar/admin/production-lines` أو `/en/admin/production-lines`
- **الطلبات**: `/ar/admin/orders` أو `/en/admin/orders`
- **طلبات الجملة**: `/ar/admin/wholesale` أو `/en/admin/wholesale`
- **المستخدمين**: `/ar/admin/users` أو `/en/admin/users`
- **المحتوى**: `/ar/admin/content` أو `/en/admin/content`
- **الإعدادات**: `/ar/admin/settings` أو `/en/admin/settings`

## بيانات الدخول الافتراضية

### حساب المسؤول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

### حساب المدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

## التطوير

### البناء للإنتاج

```bash
npm run build
```

### تشغيل الإصدار المبني

```bash
npm run start
```

### تشغيل الاختبارات

```bash
npm run test
```

### تشغيل اختبارات E2E

```bash
npm run test:e2e
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. استنساخ المستودع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. ارتكاب التغييرات (`git commit -m 'Add some amazing feature'`)
4. دفع الفرع (`git push origin feature/amazing-feature`)
5. فتح طلب سحب

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).

## الاتصال

للأسئلة أو الاستفسارات، يرجى التواصل عبر البريد الإلكتروني: <EMAIL>
