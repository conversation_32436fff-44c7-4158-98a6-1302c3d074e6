# توثيق التحول من Supabase إلى SQLite

## مقدمة

تم تحويل المشروع من استخدام Supabase كقاعدة بيانات إلى استخدام SQLite. هذا التحويل يسمح بتشغيل المشروع محليًا دون الحاجة إلى اتصال بالإنترنت أو خدمة Supabase.

## الملفات الجديدة

1. **src/lib/sqlite.ts**: مكتبة SQLite للتعامل مع قاعدة البيانات المحلية
2. **src/lib/schema.ts**: تعريف هيكل قاعدة بيانات SQLite
3. **src/lib/sqliteServer.ts**: مكتبة SQLite للتعامل مع قاعدة البيانات في بيئة الخادم (Node.js)
4. **src/services/SQLiteUserService.ts**: خدمة إدارة المستخدمين باستخدام SQLite

## الملفات المعدلة

1. **src/services/AuthService.ts**: تم تحديثه لاستخدام SQLite بدلاً من Supabase
2. **src/lib/auth.ts**: تم تحديثه لاستخدام SQLite بدلاً من Supabase

## خطوات التثبيت

1. تثبيت مكتبة better-sqlite3:

```bash
npm install better-sqlite3 --save
```

2. إنشاء ملف .env.local في المجلد الرئيسي للمشروع وإضافة المتغيرات التالية:

```
SQLITE_DB_PATH=./database.sqlite
```

## كيفية استخدام SQLite

### في بيئة المتصفح

في بيئة المتصفح، يتم استخدام localStorage لمحاكاة قاعدة البيانات SQLite. هذا يسمح بتشغيل التطبيق في المتصفح دون الحاجة إلى خادم Node.js.

```typescript
import { sqliteDB } from '../lib/sqlite';

// الحصول على المستخدمين
const users = sqliteDB.getUsers();

// إضافة مستخدم جديد
const newUser = {
  id: 'user-id',
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user',
  createdAt: new Date().toISOString()
};
sqliteDB.saveUsers([...users, newUser]);
```

### في بيئة الخادم (Node.js)

في بيئة الخادم، يتم استخدام مكتبة better-sqlite3 للتعامل مع قاعدة بيانات SQLite الحقيقية.

```typescript
import { getSQLiteDB } from '../lib/sqliteServer';

// الحصول على نسخة من قاعدة البيانات
const db = getSQLiteDB();

// الحصول على المستخدمين
const users = await db.getUsers();

// إضافة مستخدم جديد
const newUser = await db.createUser({
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user'
});

// إغلاق قاعدة البيانات عند إيقاف التطبيق
import { closeSQLiteDB } from '../lib/sqliteServer';
closeSQLiteDB();
```

## واجهة محاكاة Supabase

تم إنشاء واجهة محاكاة لـ Supabase للحفاظ على توافق الكود الحالي. هذه الواجهة تستخدم SQLite في الخلفية.

```typescript
import { sqlite } from '../lib/sqlite';

// تسجيل الدخول
const { data, error } = await sqlite.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});

// إنشاء حساب جديد
const { data, error } = await sqlite.auth.signUp({
  email: '<EMAIL>',
  password: 'password'
});

// الاستعلام عن البيانات
const { data, error } = await sqlite.from('users').select('*').limit(10);
```

## هيكل قاعدة البيانات

تم تعريف هيكل قاعدة البيانات في ملف `src/lib/schema.ts`. يحتوي هذا الملف على تعريفات الجداول التالية:

1. **users**: جدول المستخدمين
2. **products**: جدول المنتجات
3. **services**: جدول الخدمات
4. **production_lines**: جدول خطوط الإنتاج
5. **blog_posts**: جدول منشورات المدونة
6. **reviews**: جدول المراجعات
7. **orders**: جدول الطلبات
8. **order_items**: جدول عناصر الطلبات
9. **wholesale_quotes**: جدول طلبات الجملة
10. **payment_methods**: جدول طرق الدفع
11. **currencies**: جدول العملات
12. **shipping_methods**: جدول طرق الشحن
13. **wishlist**: جدول قائمة المفضلة
14. **cart**: جدول سلة التسوق
15. **settings**: جدول الإعدادات

## ملاحظات هامة

1. **تشغيل المشروع محليًا**: يمكن تشغيل المشروع محليًا دون الحاجة إلى اتصال بالإنترنت أو خدمة Supabase.
2. **البيانات الافتراضية**: يتم إنشاء مستخدمين افتراضيين عند تشغيل التطبيق لأول مرة:
   - مستخدم مدير: <EMAIL> / password
   - مستخدم مدير ثانٍ: <EMAIL> / password
3. **التخزين المحلي**: في بيئة المتصفح، يتم استخدام localStorage لتخزين البيانات. هذا يعني أن البيانات ستبقى حتى بعد إغلاق المتصفح.
4. **الأمان**: في بيئة الإنتاج، يجب تشفير كلمات المرور وتنفيذ إجراءات أمان إضافية.

## الخطوات التالية

1. **تنفيذ وظائف إضافية**: إضافة وظائف إضافية لإدارة المنتجات والخدمات والطلبات وغيرها.
2. **تحسين الأداء**: تحسين أداء الاستعلامات وتقليل استهلاك الذاكرة.
3. **تحسين الأمان**: تنفيذ إجراءات أمان إضافية مثل تشفير كلمات المرور ومنع هجمات SQL injection.
4. **اختبار الوظائف**: إجراء اختبارات شاملة للتأكد من أن جميع الوظائف تعمل كما هو متوقع.
