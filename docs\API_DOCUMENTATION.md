# توثيق واجهة برمجة التطبيقات (API)

## مقدمة

هذا المستند يوثق واجهة برمجة التطبيقات (API) المستخدمة في مشروع كوميرس برو. يتضمن وصفًا لكل خدمة ووظائفها وكيفية استخدامها.

## خدمات المصادقة

### AuthService

خدمة إدارة المصادقة وتسجيل الدخول.

#### تسجيل الدخول

```typescript
import { signInWithEmail } from '../services/AuthService';

const result = await signInWithEmail(email, password);
if (result.success) {
  // تم تسجيل الدخول بنجاح
  console.log(result.user);
} else {
  // فشل تسجيل الدخول
  console.error(result.error);
}
```

#### إنشاء حساب جديد

```typescript
import { signUpWithEmail } from '../services/AuthService';

const userData = {
  firstName: 'اسم',
  lastName: 'العائلة',
  // بيانات إضافية
};

const result = await signUpWithEmail(email, password, userData);
if (result.success) {
  // تم إنشاء الحساب بنجاح
  console.log(result.user);
} else {
  // فشل إنشاء الحساب
  console.error(result.error);
}
```

#### تسجيل الخروج

```typescript
import { signOut } from '../services/AuthService';

const result = await signOut();
if (result.success) {
  // تم تسجيل الخروج بنجاح
} else {
  // فشل تسجيل الخروج
  console.error(result.error);
}
```

#### الحصول على المستخدم الحالي

```typescript
import { getCurrentUser } from '../services/AuthService';

const result = await getCurrentUser();
if (result.success) {
  // تم الحصول على المستخدم الحالي بنجاح
  console.log(result.user);
} else {
  // فشل الحصول على المستخدم الحالي
  console.error(result.error);
}
```

## خدمات المنتجات

### ProductService

خدمة إدارة المنتجات.

#### الحصول على جميع المنتجات

```typescript
import { getAllProducts } from '../services/ProductService';

const products = await getAllProducts();
console.log(products);
```

#### الحصول على منتج بواسطة المعرف

```typescript
import { getProductById } from '../services/ProductService';

const product = await getProductById('product-id');
if (product) {
  console.log(product);
} else {
  console.error('المنتج غير موجود');
}
```

#### الحصول على منتج بواسطة الرابط المختصر

```typescript
import { getProductBySlug } from '../services/ProductService';

const product = await getProductBySlug('product-slug');
if (product) {
  console.log(product);
} else {
  console.error('المنتج غير موجود');
}
```

#### إنشاء منتج جديد

```typescript
import { createProduct } from '../services/ProductService';

const productData = {
  name: 'Product Name',
  name_ar: 'اسم المنتج',
  slug: 'product-slug',
  description: 'Product description',
  description_ar: 'وصف المنتج',
  price: 100,
  category: 'category',
  // بيانات إضافية
};

try {
  const newProduct = await createProduct(productData);
  console.log(newProduct);
} catch (error) {
  console.error('فشل إنشاء المنتج:', error);
}
```

#### تحديث منتج

```typescript
import { updateProduct } from '../services/ProductService';

const productData = {
  name: 'Updated Product Name',
  name_ar: 'اسم المنتج المحدث',
  price: 120,
  // بيانات إضافية
};

const updatedProduct = await updateProduct('product-id', productData);
if (updatedProduct) {
  console.log(updatedProduct);
} else {
  console.error('فشل تحديث المنتج');
}
```

#### حذف منتج

```typescript
import { deleteProduct } from '../services/ProductService';

const success = await deleteProduct('product-id');
if (success) {
  console.log('تم حذف المنتج بنجاح');
} else {
  console.error('فشل حذف المنتج');
}
```

#### البحث عن منتجات

```typescript
import { searchProducts } from '../services/ProductService';

const products = await searchProducts('search-query');
console.log(products);
```

## خدمات الخدمات

### ServiceService

خدمة إدارة الخدمات.

#### الحصول على جميع الخدمات

```typescript
import { getAllServices } from '../services/ServiceService';

const services = await getAllServices();
console.log(services);
```

#### الحصول على خدمة بواسطة المعرف

```typescript
import { getServiceById } from '../services/ServiceService';

const service = await getServiceById('service-id');
if (service) {
  console.log(service);
} else {
  console.error('الخدمة غير موجودة');
}
```

## خدمات المدونة

### BlogService

خدمة إدارة منشورات المدونة.

#### الحصول على جميع منشورات المدونة

```typescript
import { getAllBlogPosts } from '../services/BlogService';

const blogPosts = await getAllBlogPosts();
console.log(blogPosts);
```

#### الحصول على منشور بواسطة الرابط المختصر

```typescript
import { getBlogPostBySlug } from '../services/BlogService';

const blogPost = await getBlogPostBySlug('post-slug');
if (blogPost) {
  console.log(blogPost);
} else {
  console.error('المنشور غير موجود');
}
```

## خدمات خطوط الإنتاج

### ProductionLineService

خدمة إدارة خطوط الإنتاج.

#### الحصول على جميع خطوط الإنتاج

```typescript
import { getAllProductionLines } from '../services/ProductionLineService';

const productionLines = await getAllProductionLines();
console.log(productionLines);
```

## خدمات المراجعات

### ReviewService

خدمة إدارة المراجعات.

#### الحصول على مراجعات منتج

```typescript
import { getReviewsByProductId } from '../services/ReviewService';

const reviews = await getReviewsByProductId('product-id');
console.log(reviews);
```

#### إنشاء مراجعة جديدة

```typescript
import { createReview } from '../services/ReviewService';

const reviewData = {
  productId: 'product-id',
  userId: 'user-id',
  userName: 'User Name',
  rating: 5,
  title: 'Review Title',
  comment: 'Review Comment',
  // بيانات إضافية
};

try {
  const newReview = await createReview(reviewData);
  console.log(newReview);
} catch (error) {
  console.error('فشل إنشاء المراجعة:', error);
}
```

## خدمات سلة التسوق

### CartService

خدمة إدارة سلة التسوق.

#### الحصول على عناصر سلة التسوق

```typescript
import { getCartItems } from '../services/CartService';

const cartItems = await getCartItems('user-id');
console.log(cartItems);
```

#### إضافة منتج إلى سلة التسوق

```typescript
import { addToCart } from '../services/CartService';

try {
  const cartItem = await addToCart('user-id', 'product-id', 2);
  console.log(cartItem);
} catch (error) {
  console.error('فشل إضافة المنتج إلى سلة التسوق:', error);
}
```

#### تحديث كمية منتج في سلة التسوق

```typescript
import { updateCartItemQuantity } from '../services/CartService';

try {
  const cartItem = await updateCartItemQuantity('user-id', 'product-id', 3);
  console.log(cartItem);
} catch (error) {
  console.error('فشل تحديث كمية المنتج في سلة التسوق:', error);
}
```

#### إزالة منتج من سلة التسوق

```typescript
import { removeCartItem } from '../services/CartService';

try {
  const removedItem = await removeCartItem('user-id', 'product-id');
  console.log(removedItem);
} catch (error) {
  console.error('فشل إزالة المنتج من سلة التسوق:', error);
}
```

#### تفريغ سلة التسوق

```typescript
import { clearCart } from '../services/CartService';

const success = await clearCart('user-id');
if (success) {
  console.log('تم تفريغ سلة التسوق بنجاح');
} else {
  console.error('فشل تفريغ سلة التسوق');
}
```

## خدمات قائمة المفضلة

### WishlistService

خدمة إدارة قائمة المفضلة.

#### الحصول على عناصر قائمة المفضلة

```typescript
import { getWishlistItems } from '../services/WishlistService';

const wishlistItems = await getWishlistItems('user-id');
console.log(wishlistItems);
```

#### إضافة منتج إلى قائمة المفضلة

```typescript
import { addToWishlist } from '../services/WishlistService';

try {
  const wishlistItem = await addToWishlist('user-id', 'product-id');
  console.log(wishlistItem);
} catch (error) {
  console.error('فشل إضافة المنتج إلى قائمة المفضلة:', error);
}
```

#### إزالة منتج من قائمة المفضلة

```typescript
import { removeFromWishlist } from '../services/WishlistService';

try {
  const removedItem = await removeFromWishlist('user-id', 'product-id');
  console.log(removedItem);
} catch (error) {
  console.error('فشل إزالة المنتج من قائمة المفضلة:', error);
}
```

## خدمات الطلبات

### OrderService

خدمة إدارة الطلبات.

#### الحصول على طلبات المستخدم

```typescript
import { getUserOrders } from '../services/OrderService';

const orders = await getUserOrders('user-id');
console.log(orders);
```

#### إنشاء طلب جديد

```typescript
import { createOrder } from '../services/OrderService';

const orderData = {
  shippingFee: 15,
  tax: 10,
  discount: 0,
  paymentMethod: 'cash_on_delivery',
  shippingAddress: {
    firstName: 'First Name',
    lastName: 'Last Name',
    street: 'Street',
    city: 'City',
    state: 'State',
    postalCode: '12345',
    country: 'Country',
    phone: '+1234567890'
  },
  // بيانات إضافية
};

const items = [
  { productId: 'product-id-1', quantity: 2 },
  { productId: 'product-id-2', quantity: 1 }
];

try {
  const newOrder = await createOrder('user-id', orderData, items);
  console.log(newOrder);
} catch (error) {
  console.error('فشل إنشاء الطلب:', error);
}
```

## خدمات طلبات الجملة

### WholesaleService

خدمة إدارة طلبات الجملة.

#### إنشاء طلب جملة جديد

```typescript
import { createWholesaleQuote } from '../services/WholesaleService';

const quoteData = {
  userId: 'user-id', // اختياري
  companyName: 'Company Name',
  contactName: 'Contact Name',
  email: '<EMAIL>',
  phone: '+1234567890',
  productInterest: 'Product Interest',
  quantity: 100,
  message: 'Message'
};

try {
  const newQuote = await createWholesaleQuote(quoteData);
  console.log(newQuote);
} catch (error) {
  console.error('فشل إنشاء طلب الجملة:', error);
}
```

## خدمات الإعدادات

### SettingsService

خدمة إدارة إعدادات النظام.

#### الحصول على جميع الإعدادات

```typescript
import { getAllSettings } from '../services/SettingsService';

const settings = await getAllSettings();
console.log(settings);
```

#### الحصول على إعداد محدد

```typescript
import { getSetting } from '../services/SettingsService';

const defaultLanguage = await getSetting('defaultLanguage');
console.log(defaultLanguage);
```

#### تحديث إعداد محدد

```typescript
import { updateSetting } from '../services/SettingsService';

const success = await updateSetting('defaultLanguage', 'ar');
if (success) {
  console.log('تم تحديث الإعداد بنجاح');
} else {
  console.error('فشل تحديث الإعداد');
}
```
