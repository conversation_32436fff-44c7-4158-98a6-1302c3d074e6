# هيكل قاعدة البيانات

## مقدمة

هذا المستند يوثق هيكل قاعدة بيانات SQLite المستخدمة في مشروع كوميرس برو. يتضمن وصفًا لكل جدول وحقوله والعلاقات بين الجداول.

## الجداول

### جدول المستخدمين (users)

يخزن معلومات المستخدمين المسجلين في النظام.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للمستخدم (المفتاح الأساسي) |
| email | TEXT | البريد الإلكتروني للمستخدم (فريد) |
| first_name | TEXT | الاسم الأول للمستخدم |
| last_name | TEXT | اسم العائلة للمستخدم |
| role | TEXT | دور المستخدم (admin, user) |
| created_at | TEXT | تاريخ إنشاء الحساب |
| updated_at | TEXT | تاريخ آخر تحديث للحساب |
| avatar | TEXT | رابط صورة المستخدم |
| phone | TEXT | رقم هاتف المستخدم |
| street | TEXT | الشارع في عنوان المستخدم |
| city | TEXT | المدينة في عنوان المستخدم |
| state | TEXT | المنطقة/الولاية في عنوان المستخدم |
| postal_code | TEXT | الرمز البريدي في عنوان المستخدم |
| country | TEXT | البلد في عنوان المستخدم |
| language | TEXT | لغة المستخدم المفضلة (ar, en) |
| theme | TEXT | سمة المستخدم المفضلة (light, dark) |
| notifications | INTEGER | تفضيلات الإشعارات (1 = مفعل، 0 = معطل) |
| newsletter | INTEGER | الاشتراك في النشرة الإخبارية (1 = مشترك، 0 = غير مشترك) |

### جدول المنتجات (products)

يخزن معلومات المنتجات المتاحة في المتجر.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للمنتج (المفتاح الأساسي) |
| name | TEXT | اسم المنتج باللغة الإنجليزية |
| name_ar | TEXT | اسم المنتج باللغة العربية |
| slug | TEXT | الرابط المختصر للمنتج (فريد) |
| description | TEXT | وصف المنتج باللغة الإنجليزية |
| description_ar | TEXT | وصف المنتج باللغة العربية |
| price | REAL | سعر المنتج |
| sale_price | REAL | سعر المنتج في حالة الخصم |
| category | TEXT | فئة المنتج |
| tags | TEXT | وسوم المنتج (JSON) |
| images | TEXT | روابط صور المنتج (JSON) |
| featured | INTEGER | هل المنتج مميز (1 = نعم، 0 = لا) |
| in_stock | INTEGER | هل المنتج متوفر في المخزون (1 = نعم، 0 = لا) |
| rating | REAL | تقييم المنتج |
| reviews | INTEGER | عدد المراجعات للمنتج |
| specifications | TEXT | مواصفات المنتج (JSON) |
| created_at | TEXT | تاريخ إضافة المنتج |
| updated_at | TEXT | تاريخ آخر تحديث للمنتج |

### جدول الخدمات (services)

يخزن معلومات الخدمات المقدمة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للخدمة (المفتاح الأساسي) |
| name | TEXT | اسم الخدمة باللغة الإنجليزية |
| name_ar | TEXT | اسم الخدمة باللغة العربية |
| slug | TEXT | الرابط المختصر للخدمة (فريد) |
| description | TEXT | وصف الخدمة باللغة الإنجليزية |
| description_ar | TEXT | وصف الخدمة باللغة العربية |
| icon | TEXT | أيقونة الخدمة |
| image | TEXT | صورة الخدمة |
| features | TEXT | ميزات الخدمة باللغة الإنجليزية (JSON) |
| features_ar | TEXT | ميزات الخدمة باللغة العربية (JSON) |
| price | REAL | سعر الخدمة |
| price_unit | TEXT | وحدة السعر (per hour, per day, etc.) |
| category | TEXT | فئة الخدمة |
| created_at | TEXT | تاريخ إضافة الخدمة |
| updated_at | TEXT | تاريخ آخر تحديث للخدمة |

### جدول خطوط الإنتاج (production_lines)

يخزن معلومات خطوط الإنتاج المتاحة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد لخط الإنتاج (المفتاح الأساسي) |
| name | TEXT | اسم خط الإنتاج باللغة الإنجليزية |
| name_ar | TEXT | اسم خط الإنتاج باللغة العربية |
| slug | TEXT | الرابط المختصر لخط الإنتاج (فريد) |
| description | TEXT | وصف خط الإنتاج باللغة الإنجليزية |
| description_ar | TEXT | وصف خط الإنتاج باللغة العربية |
| capacity | TEXT | سعة خط الإنتاج |
| specifications | TEXT | مواصفات خط الإنتاج (JSON) |
| images | TEXT | روابط صور خط الإنتاج (JSON) |
| created_at | TEXT | تاريخ إضافة خط الإنتاج |
| updated_at | TEXT | تاريخ آخر تحديث لخط الإنتاج |

### جدول منشورات المدونة (blog_posts)

يخزن معلومات منشورات المدونة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للمنشور (المفتاح الأساسي) |
| title | TEXT | عنوان المنشور باللغة الإنجليزية |
| title_ar | TEXT | عنوان المنشور باللغة العربية |
| slug | TEXT | الرابط المختصر للمنشور (فريد) |
| excerpt | TEXT | مقتطف من المنشور باللغة الإنجليزية |
| excerpt_ar | TEXT | مقتطف من المنشور باللغة العربية |
| content | TEXT | محتوى المنشور باللغة الإنجليزية |
| content_ar | TEXT | محتوى المنشور باللغة العربية |
| author | TEXT | اسم الكاتب |
| author_title | TEXT | لقب الكاتب |
| author_image | TEXT | صورة الكاتب |
| cover_image | TEXT | صورة الغلاف للمنشور |
| category | TEXT | فئة المنشور |
| tags | TEXT | وسوم المنشور (JSON) |
| published_at | TEXT | تاريخ نشر المنشور |
| updated_at | TEXT | تاريخ آخر تحديث للمنشور |
| read_time | TEXT | وقت القراءة المقدر |

### جدول المراجعات (reviews)

يخزن مراجعات المستخدمين للمنتجات.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للمراجعة (المفتاح الأساسي) |
| product_id | TEXT | معرف المنتج (مفتاح خارجي) |
| user_id | TEXT | معرف المستخدم (مفتاح خارجي) |
| user_name | TEXT | اسم المستخدم |
| user_avatar | TEXT | صورة المستخدم |
| rating | INTEGER | تقييم المنتج (1-5) |
| title | TEXT | عنوان المراجعة |
| comment | TEXT | نص المراجعة |
| created_at | TEXT | تاريخ إضافة المراجعة |
| updated_at | TEXT | تاريخ آخر تحديث للمراجعة |
| helpful | INTEGER | عدد المستخدمين الذين وجدوا المراجعة مفيدة |
| verified | INTEGER | هل المراجعة من مشتري مؤكد (1 = نعم، 0 = لا) |
| images | TEXT | روابط صور المراجعة (JSON) |

### جدول الطلبات (orders)

يخزن معلومات طلبات المستخدمين.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للطلب (المفتاح الأساسي) |
| user_id | TEXT | معرف المستخدم (مفتاح خارجي) |
| status | TEXT | حالة الطلب (pending, processing, shipped, delivered, cancelled) |
| total | REAL | إجمالي الطلب |
| shipping_fee | REAL | رسوم الشحن |
| tax | REAL | الضريبة |
| discount | REAL | الخصم |
| payment_method | TEXT | طريقة الدفع |
| payment_status | TEXT | حالة الدفع (pending, paid, failed) |
| shipping_address | TEXT | عنوان الشحن (JSON) |
| billing_address | TEXT | عنوان الفوترة (JSON) |
| notes | TEXT | ملاحظات الطلب |
| created_at | TEXT | تاريخ إنشاء الطلب |
| updated_at | TEXT | تاريخ آخر تحديث للطلب |

### جدول عناصر الطلبات (order_items)

يخزن عناصر الطلبات.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للعنصر (المفتاح الأساسي) |
| order_id | TEXT | معرف الطلب (مفتاح خارجي) |
| product_id | TEXT | معرف المنتج (مفتاح خارجي) |
| quantity | INTEGER | الكمية |
| price | REAL | سعر الوحدة |
| total | REAL | إجمالي السعر |
| created_at | TEXT | تاريخ إضافة العنصر |

### جدول طلبات الجملة (wholesale_quotes)

يخزن طلبات عروض أسعار الجملة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للطلب (المفتاح الأساسي) |
| user_id | TEXT | معرف المستخدم (مفتاح خارجي) |
| company_name | TEXT | اسم الشركة |
| contact_name | TEXT | اسم جهة الاتصال |
| email | TEXT | البريد الإلكتروني |
| phone | TEXT | رقم الهاتف |
| product_interest | TEXT | المنتج المطلوب |
| quantity | INTEGER | الكمية المطلوبة |
| message | TEXT | رسالة الطلب |
| status | TEXT | حالة الطلب (pending, processing, approved, rejected) |
| created_at | TEXT | تاريخ إنشاء الطلب |
| updated_at | TEXT | تاريخ آخر تحديث للطلب |

### جدول طرق الدفع (payment_methods)

يخزن طرق الدفع المتاحة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد لطريقة الدفع (المفتاح الأساسي) |
| name | TEXT | اسم طريقة الدفع باللغة الإنجليزية |
| name_ar | TEXT | اسم طريقة الدفع باللغة العربية |
| type | TEXT | نوع طريقة الدفع |
| icon | TEXT | أيقونة طريقة الدفع |
| supported_currencies | TEXT | العملات المدعومة (JSON) |
| processing_fee | REAL | رسوم المعالجة |
| processing_fee_type | TEXT | نوع رسوم المعالجة (percentage, fixed) |
| enabled | INTEGER | هل طريقة الدفع مفعلة (1 = نعم، 0 = لا) |
| created_at | TEXT | تاريخ إضافة طريقة الدفع |
| updated_at | TEXT | تاريخ آخر تحديث لطريقة الدفع |

### جدول العملات (currencies)

يخزن العملات المدعومة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| code | TEXT | رمز العملة (المفتاح الأساسي) |
| name | TEXT | اسم العملة باللغة الإنجليزية |
| name_ar | TEXT | اسم العملة باللغة العربية |
| symbol | TEXT | رمز العملة |
| rate | REAL | سعر الصرف مقابل العملة الافتراضية |
| is_default | INTEGER | هل العملة هي الافتراضية (1 = نعم، 0 = لا) |
| created_at | TEXT | تاريخ إضافة العملة |
| updated_at | TEXT | تاريخ آخر تحديث للعملة |

### جدول طرق الشحن (shipping_methods)

يخزن طرق الشحن المتاحة.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد لطريقة الشحن (المفتاح الأساسي) |
| name | TEXT | اسم طريقة الشحن باللغة الإنجليزية |
| name_ar | TEXT | اسم طريقة الشحن باللغة العربية |
| description | TEXT | وصف طريقة الشحن باللغة الإنجليزية |
| description_ar | TEXT | وصف طريقة الشحن باللغة العربية |
| price | REAL | سعر الشحن |
| estimated_delivery | TEXT | وقت التسليم المقدر |
| countries | TEXT | البلدان المدعومة (JSON) |
| icon | TEXT | أيقونة طريقة الشحن |
| enabled | INTEGER | هل طريقة الشحن مفعلة (1 = نعم، 0 = لا) |
| created_at | TEXT | تاريخ إضافة طريقة الشحن |
| updated_at | TEXT | تاريخ آخر تحديث لطريقة الشحن |

### جدول قائمة المفضلة (wishlist)

يخزن منتجات قائمة المفضلة للمستخدمين.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للعنصر (المفتاح الأساسي) |
| user_id | TEXT | معرف المستخدم (مفتاح خارجي) |
| product_id | TEXT | معرف المنتج (مفتاح خارجي) |
| created_at | TEXT | تاريخ إضافة العنصر |

### جدول سلة التسوق (cart)

يخزن منتجات سلة التسوق للمستخدمين.

| الحقل | النوع | الوصف |
|-------|------|-------|
| id | TEXT | المعرف الفريد للعنصر (المفتاح الأساسي) |
| user_id | TEXT | معرف المستخدم (مفتاح خارجي) |
| product_id | TEXT | معرف المنتج (مفتاح خارجي) |
| quantity | INTEGER | الكمية |
| created_at | TEXT | تاريخ إضافة العنصر |
| updated_at | TEXT | تاريخ آخر تحديث للعنصر |

### جدول الإعدادات (settings)

يخزن إعدادات النظام.

| الحقل | النوع | الوصف |
|-------|------|-------|
| key | TEXT | مفتاح الإعداد (المفتاح الأساسي) |
| value | TEXT | قيمة الإعداد |
| updated_at | TEXT | تاريخ آخر تحديث للإعداد |

## العلاقات بين الجداول

- **المستخدمين والطلبات**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة طلبات)
- **المستخدمين وطلبات الجملة**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة طلبات جملة)
- **المستخدمين والمراجعات**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة مراجعات)
- **المستخدمين وقائمة المفضلة**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة منتجات في المفضلة)
- **المستخدمين وسلة التسوق**: علاقة واحد إلى متعدد (مستخدم واحد يمكن أن يكون له عدة منتجات في السلة)
- **المنتجات والمراجعات**: علاقة واحد إلى متعدد (منتج واحد يمكن أن يكون له عدة مراجعات)
- **المنتجات وعناصر الطلبات**: علاقة واحد إلى متعدد (منتج واحد يمكن أن يكون في عدة طلبات)
- **الطلبات وعناصر الطلبات**: علاقة واحد إلى متعدد (طلب واحد يمكن أن يحتوي على عدة منتجات)
