# دليل التطوير

## مقدمة

هذا المستند يوفر دليلًا للمطورين الذين يعملون على مشروع كوميرس برو. يتضمن إرشادات حول كيفية إعداد بيئة التطوير، وأفضل الممارسات، وكيفية المساهمة في المشروع.

## إعداد بيئة التطوير

### المتطلبات الأساسية

- **Node.js**: الإصدار 18 أو أحدث
- **npm**: الإصدار 9 أو أحدث
- **Git**: أحدث إصدار

### خطوات الإعداد

1. استنساخ المستودع:

```bash
git clone https://github.com/yourusername/commercepro.git
cd commercepro
```

2. تشغيل سكريبت الإعداد:

```bash
node scripts/setup.js
```

3. تثبيت التبعيات وإعداد قاعدة البيانات:

```bash
npm run setup
```

4. تشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

5. فتح المتصفح على العنوان [http://localhost:3000](http://localhost:3000)

## هيكل المشروع

راجع ملف [PROJECT_STRUCTURE.md](./PROJECT_STRUCTURE.md) للحصول على معلومات مفصلة حول هيكل المشروع.

## قاعدة البيانات

راجع ملف [DATABASE_STRUCTURE.md](./DATABASE_STRUCTURE.md) للحصول على معلومات مفصلة حول هيكل قاعدة البيانات.

## واجهة برمجة التطبيقات (API)

راجع ملف [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) للحصول على معلومات مفصلة حول واجهة برمجة التطبيقات.

## أفضل الممارسات

### عام

- استخدم TypeScript لجميع الملفات الجديدة
- اكتب تعليقات توضيحية للكود المعقد
- اتبع مبدأ المسؤولية الفردية (SRP) للمكونات والوظائف
- استخدم أسماء وصفية للمتغيرات والوظائف والمكونات
- تجنب التكرار (DRY)
- اكتب اختبارات لجميع الوظائف الجديدة

### React

- استخدم مكونات وظيفية بدلاً من مكونات الفئة
- استخدم React Hooks بدلاً من Higher-Order Components
- استخدم Memo لتحسين أداء المكونات
- استخدم Lazy Loading للمكونات الكبيرة
- استخدم Context API للحالة العالمية
- استخدم Zustand للحالة المعقدة

### Next.js

- استخدم App Router بدلاً من Pages Router للصفحات الجديدة
- استخدم Server Components حيثما أمكن
- استخدم Static Site Generation (SSG) للصفحات الثابتة
- استخدم Incremental Static Regeneration (ISR) للصفحات شبه الثابتة
- استخدم Server-Side Rendering (SSR) للصفحات الديناميكية
- استخدم API Routes للوظائف الخلفية

### CSS

- استخدم Tailwind CSS للتصميم
- استخدم CSS Modules للأنماط المخصصة
- استخدم متغيرات CSS للألوان والخطوط والمسافات
- استخدم Media Queries للتصميم المتجاوب
- استخدم RTL Styles للغة العربية

### الأمان

- استخدم CSRF Tokens لحماية النماذج
- استخدم Input Validation للتحقق من صحة المدخلات
- استخدم Rate Limiting لمنع هجمات Brute Force
- استخدم Content Security Policy (CSP) لمنع هجمات XSS
- استخدم HTTPS لتشفير البيانات

### الأداء

- استخدم Lazy Loading للصور والمكونات
- استخدم Code Splitting لتقليل حجم الحزمة
- استخدم Caching لتحسين الأداء
- استخدم Compression لتقليل حجم الملفات
- استخدم CDN لتسريع تحميل الملفات الثابتة

## سير العمل

### التطوير

1. إنشاء فرع جديد من `main`:

```bash
git checkout -b feature/new-feature
```

2. تنفيذ التغييرات وإضافتها:

```bash
git add .
git commit -m "Add new feature"
```

3. دفع التغييرات إلى المستودع:

```bash
git push origin feature/new-feature
```

4. إنشاء طلب سحب (Pull Request) على GitHub

### الاختبار

1. تشغيل اختبارات الوحدة:

```bash
npm run test
```

2. تشغيل اختبارات E2E:

```bash
npm run test:e2e
```

3. التحقق من التوافق مع المتصفحات:

```bash
npm run build
npm run start
```

### النشر

1. دمج التغييرات في فرع `main`
2. إنشاء إصدار جديد:

```bash
git tag v1.0.0
git push origin v1.0.0
```

3. بناء التطبيق للإنتاج:

```bash
npm run build
```

4. نشر التطبيق على الخادم

## الترجمة

### إضافة ترجمات جديدة

1. إضافة المفاتيح الجديدة إلى ملف `src/translations/ar.json`
2. إضافة الترجمات المقابلة إلى ملف `src/translations/en.json`
3. استخدام المفاتيح في المكونات:

```tsx
import { useTranslation } from '../hooks/useTranslation';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('my_component.title')}</h1>
      <p>{t('my_component.description')}</p>
    </div>
  );
}
```

## إضافة صفحات جديدة

### صفحة باللغة العربية

1. إنشاء ملف `src/app/ar/new-page/page.tsx`:

```tsx
export default function NewPage() {
  return (
    <div>
      <h1>صفحة جديدة</h1>
      <p>محتوى الصفحة الجديدة</p>
    </div>
  );
}
```

### صفحة باللغة الإنجليزية

1. إنشاء ملف `src/app/en/new-page/page.tsx`:

```tsx
export default function NewPage() {
  return (
    <div>
      <h1>New Page</h1>
      <p>New page content</p>
    </div>
  );
}
```

## إضافة مكونات جديدة

1. إنشاء ملف `src/components/common/NewComponent.tsx`:

```tsx
import { useTranslation } from '../../hooks/useTranslation';

interface NewComponentProps {
  title: string;
  description: string;
}

export default function NewComponent({ title, description }: NewComponentProps) {
  const { t } = useTranslation();
  
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold">{title}</h2>
      <p className="mt-2 text-gray-600">{description}</p>
      <button className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md">
        {t('common.learn_more')}
      </button>
    </div>
  );
}
```

## إضافة خدمات جديدة

1. إنشاء ملف `src/services/NewService.ts`:

```typescript
import { sqlite, sqliteDB } from '../lib/sqlite';

// نوع البيانات
interface NewData {
  id: string;
  name: string;
  description: string;
  createdAt: string;
}

// الحصول على جميع البيانات
export async function getAllData(): Promise<NewData[]> {
  try {
    // محاولة الحصول على البيانات من localStorage
    const dataJson = localStorage.getItem('local-new-data');
    return dataJson ? JSON.parse(dataJson) : [];
  } catch (error) {
    console.error('Error getting data:', error);
    return [];
  }
}

// إنشاء بيانات جديدة
export async function createData(data: Partial<NewData>): Promise<NewData> {
  try {
    const allData = await getAllData();
    
    // إنشاء معرف فريد للبيانات الجديدة
    const id = data.id || `data-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء البيانات الجديدة
    const newData: NewData = {
      id,
      name: data.name || '',
      description: data.description || '',
      createdAt: now
    };
    
    // إضافة البيانات الجديدة
    localStorage.setItem('local-new-data', JSON.stringify([...allData, newData]));
    
    return newData;
  } catch (error) {
    console.error('Error creating data:', error);
    throw new Error('فشل إنشاء البيانات');
  }
}
```

## استخدام قاعدة البيانات SQLite

### في بيئة المتصفح

```typescript
import { sqliteDB } from '../lib/sqlite';

// الحصول على المستخدمين
const users = sqliteDB.getUsers();

// إضافة مستخدم جديد
const newUser = {
  id: 'user-id',
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user',
  createdAt: new Date().toISOString()
};
sqliteDB.saveUsers([...users, newUser]);
```

### في بيئة الخادم

```typescript
import { getSQLiteDB, closeSQLiteDB } from '../lib/sqliteServer';

// الحصول على نسخة من قاعدة البيانات
const db = getSQLiteDB();

// الحصول على المستخدمين
const users = await db.getUsers();

// إضافة مستخدم جديد
const newUser = await db.createUser({
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user'
});

// إغلاق قاعدة البيانات عند إيقاف التطبيق
closeSQLiteDB();
```

## الموارد

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [React Query Documentation](https://tanstack.com/query/latest/docs/react/overview)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Framer Motion Documentation](https://www.framer.com/motion/)
