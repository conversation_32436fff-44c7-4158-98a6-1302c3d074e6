# هيكل المشروع

## مقدمة

هذا المستند يوثق هيكل مشروع كوميرس برو، بما في ذلك المجلدات والملفات الرئيسية ووظائفها.

## نظرة عامة

```
commercepro/
├── docs/               # وثائق المشروع
├── public/             # الملفات الثابتة
├── scripts/            # سكريبتات مساعدة
├── src/                # كود المصدر
│   ├── app/            # مكونات Next.js App Router
│   ├── components/     # مكونات React
│   ├── data/           # بيانات ثابتة
│   ├── hooks/          # React Hooks
│   ├── lib/            # مكتبات ووظائف مساعدة
│   ├── pages/          # صفحات التطبيق
│   ├── services/       # خدمات البيانات
│   ├── stores/         # مخازن Zustand
│   ├── styles/         # أنماط CSS
│   ├── tests/          # اختبارات
│   ├── translations/   # ملفات الترجمة
│   └── types/          # تعريفات TypeScript
├── .env.local          # متغيرات البيئة المحلية
├── database.sqlite     # قاعدة بيانات SQLite
├── next.config.js      # تكوين Next.js
├── package.json        # تبعيات المشروع
├── tailwind.config.js  # تكوين Tailwind CSS
└── tsconfig.json       # تكوين TypeScript
```

## المجلدات الرئيسية

### docs/

يحتوي على وثائق المشروع، بما في ذلك:

- `API_DOCUMENTATION.md`: توثيق واجهة برمجة التطبيقات
- `DATABASE_STRUCTURE.md`: توثيق هيكل قاعدة البيانات
- `PROJECT_STRUCTURE.md`: توثيق هيكل المشروع
- `SUPABASE_TO_SQLITE_MIGRATION.md`: توثيق عملية التحول من Supabase إلى SQLite

### public/

يحتوي على الملفات الثابتة التي يمكن الوصول إليها مباشرة من المتصفح، مثل:

- `favicon.ico`: أيقونة الموقع
- `logo.svg`: شعار الموقع
- `images/`: صور الموقع
- `fonts/`: خطوط الموقع

### scripts/

يحتوي على سكريبتات مساعدة لإدارة المشروع، مثل:

- `init-db.js`: سكريبت لإنشاء قاعدة البيانات وجداولها
- `seed-db.js`: سكريبت لإضافة بيانات تجريبية إلى قاعدة البيانات
- `setup.js`: سكريبت لإعداد المشروع

### src/

يحتوي على كود المصدر للمشروع، وينقسم إلى عدة مجلدات:

#### src/app/

يحتوي على مكونات Next.js App Router، وينظم حسب المسارات:

- `ar/`: الصفحات باللغة العربية
  - `page.tsx`: الصفحة الرئيسية باللغة العربية
  - `layout.tsx`: تخطيط الصفحات باللغة العربية
  - `shop/`: صفحات المتجر باللغة العربية
  - `services/`: صفحات الخدمات باللغة العربية
  - `production-lines/`: صفحات خطوط الإنتاج باللغة العربية
  - `blog/`: صفحات المدونة باللغة العربية
  - `wholesale/`: صفحات طلبات الجملة باللغة العربية
  - `account/`: صفحات الحساب باللغة العربية
  - `admin/`: صفحات لوحة التحكم باللغة العربية
- `en/`: الصفحات باللغة الإنجليزية (نفس هيكل المجلد `ar/`)
- `api/`: نقاط نهاية API
- `layout.tsx`: تخطيط الصفحات العام
- `not-found.tsx`: صفحة 404
- `error.tsx`: صفحة الخطأ
- `loading.tsx`: صفحة التحميل
- `globals.css`: أنماط CSS العامة

#### src/components/

يحتوي على مكونات React المستخدمة في الصفحات، وينقسم إلى:

- `common/`: مكونات مشتركة (أزرار، حقول إدخال، إلخ)
- `layout/`: مكونات التخطيط (رأس الصفحة، تذييل الصفحة، إلخ)
- `shop/`: مكونات المتجر (بطاقات المنتجات، تصفية المنتجات، إلخ)
- `services/`: مكونات الخدمات
- `blog/`: مكونات المدونة
- `admin/`: مكونات لوحة التحكم
- `icons/`: أيقونات SVG
- `ui/`: مكونات واجهة المستخدم الأساسية

#### src/data/

يحتوي على بيانات ثابتة مستخدمة في المشروع، مثل:

- `products.ts`: بيانات المنتجات الافتراضية
- `services.ts`: بيانات الخدمات الافتراضية
- `productionLines.ts`: بيانات خطوط الإنتاج الافتراضية
- `blogPosts.ts`: بيانات منشورات المدونة الافتراضية
- `navigation.ts`: بيانات قائمة التنقل
- `categories.ts`: بيانات الفئات

#### src/hooks/

يحتوي على React Hooks المخصصة، مثل:

- `useAuth.ts`: hook للمصادقة
- `useCart.ts`: hook لسلة التسوق
- `useWishlist.ts`: hook لقائمة المفضلة
- `useLanguage.ts`: hook للغة
- `useTheme.ts`: hook للسمة (وضع الظلام/الفاتح)
- `useLocalStorage.ts`: hook للتخزين المحلي
- `useMediaQuery.ts`: hook للاستعلام عن وسائط CSS

#### src/lib/

يحتوي على مكتبات ووظائف مساعدة، مثل:

- `sqlite.ts`: مكتبة SQLite للتعامل مع قاعدة البيانات المحلية
- `schema.ts`: تعريف هيكل قاعدة البيانات SQLite
- `sqliteServer.ts`: مكتبة SQLite للتعامل مع قاعدة البيانات في بيئة الخادم
- `auth.ts`: وظائف المصادقة
- `encryption.ts`: وظائف التشفير
- `utils.ts`: وظائف مساعدة عامة
- `validation.ts`: وظائف التحقق من صحة البيانات
- `formatters.ts`: وظائف تنسيق البيانات

#### src/pages/

يحتوي على صفحات التطبيق (لدعم Next.js Pages Router)، مثل:

- `_app.tsx`: مكون التطبيق الرئيسي
- `_document.tsx`: مكون المستند
- `index.tsx`: الصفحة الرئيسية (إعادة توجيه إلى `/ar`)
- `404.tsx`: صفحة 404
- `500.tsx`: صفحة الخطأ

#### src/services/

يحتوي على خدمات البيانات، مثل:

- `AuthService.ts`: خدمة المصادقة
- `SQLiteUserService.ts`: خدمة إدارة المستخدمين باستخدام SQLite
- `ProductService.ts`: خدمة إدارة المنتجات
- `ServiceService.ts`: خدمة إدارة الخدمات
- `BlogService.ts`: خدمة إدارة منشورات المدونة
- `ProductionLineService.ts`: خدمة إدارة خطوط الإنتاج
- `ReviewService.ts`: خدمة إدارة المراجعات
- `CartService.ts`: خدمة إدارة سلة التسوق
- `WishlistService.ts`: خدمة إدارة قائمة المفضلة
- `OrderService.ts`: خدمة إدارة الطلبات
- `WholesaleService.ts`: خدمة إدارة طلبات الجملة
- `SettingsService.ts`: خدمة إدارة الإعدادات

#### src/stores/

يحتوي على مخازن Zustand لإدارة حالة التطبيق، مثل:

- `authStore.ts`: مخزن حالة المصادقة
- `cartStore.ts`: مخزن حالة سلة التسوق
- `wishlistStore.ts`: مخزن حالة قائمة المفضلة
- `uiStore.ts`: مخزن حالة واجهة المستخدم
- `settingsStore.ts`: مخزن حالة الإعدادات

#### src/styles/

يحتوي على أنماط CSS، مثل:

- `globals.css`: أنماط CSS العامة
- `tailwind.css`: أنماط Tailwind CSS
- `animations.css`: أنماط الرسوم المتحركة
- `rtl.css`: أنماط للغات من اليمين إلى اليسار

#### src/tests/

يحتوي على اختبارات المشروع، مثل:

- `sqlite.test.ts`: اختبارات SQLite
- `auth.test.ts`: اختبارات المصادقة
- `cart.test.ts`: اختبارات سلة التسوق
- `wishlist.test.ts`: اختبارات قائمة المفضلة

#### src/translations/

يحتوي على ملفات الترجمة، مثل:

- `ar.json`: ترجمات اللغة العربية
- `en.json`: ترجمات اللغة الإنجليزية

#### src/types/

يحتوي على تعريفات TypeScript، مثل:

- `index.ts`: تعريفات عامة
- `user.ts`: تعريفات المستخدم
- `product.ts`: تعريفات المنتج
- `service.ts`: تعريفات الخدمة
- `blog.ts`: تعريفات المدونة
- `order.ts`: تعريفات الطلب

## الملفات الرئيسية

### .env.local

يحتوي على متغيرات البيئة المحلية، مثل:

```
SQLITE_DB_PATH=./database.sqlite
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_DEFAULT_LANGUAGE=ar
NEXT_PUBLIC_SECONDARY_LANGUAGE=en
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-here
NEXT_PUBLIC_CSRF_SECRET=your-csrf-secret-here
```

### database.sqlite

قاعدة بيانات SQLite المحلية.

### next.config.js

ملف تكوين Next.js، يحتوي على إعدادات مثل:

- i18n: تكوين اللغات المدعومة
- redirects: إعادة توجيه المسارات
- images: تكوين الصور
- webpack: تكوين webpack

### package.json

يحتوي على تبعيات المشروع وسكريبتات npm، مثل:

- dependencies: التبعيات الرئيسية
- devDependencies: تبعيات التطوير
- scripts: سكريبتات npm

### tailwind.config.js

ملف تكوين Tailwind CSS، يحتوي على إعدادات مثل:

- theme: سمة Tailwind CSS
- plugins: إضافات Tailwind CSS
- content: ملفات المحتوى

### tsconfig.json

ملف تكوين TypeScript، يحتوي على إعدادات مثل:

- compilerOptions: خيارات المترجم
- include: الملفات المضمنة
- exclude: الملفات المستبعدة
