# توثيق التحول من Supabase إلى SQLite

## مقدمة

هذا المستند يوثق عملية التحول من استخدام Supabase كقاعدة بيانات إلى استخدام SQLite في مشروع كوميرس برو. الهدف الرئيسي من هذا التحول هو تمكين تشغيل المشروع محليًا دون الحاجة إلى اتصال بالإنترنت أو خدمة Supabase.

## الملفات الجديدة

تم إنشاء الملفات التالية لدعم التحول إلى SQLite:

1. **src/lib/sqlite.ts**: مكتبة SQLite للتعامل مع قاعدة البيانات المحلية
2. **src/lib/schema.ts**: تعريف هيكل قاعدة بيانات SQLite
3. **src/lib/sqliteServer.ts**: مكتبة SQLite للتعامل مع قاعدة البيانات في بيئة الخادم (Node.js)
4. **src/services/SQLiteUserService.ts**: خدمة إدارة المستخدمين باستخدام SQLite
5. **scripts/init-db.js**: سكريبت لإنشاء قاعدة البيانات وجداولها
6. **scripts/seed-db.js**: سكريبت لإضافة بيانات تجريبية إلى قاعدة البيانات

## الملفات المعدلة

تم تعديل الملفات التالية لاستخدام SQLite بدلاً من Supabase:

1. **src/services/AuthService.ts**: تم تحديثه لاستخدام SQLite بدلاً من Supabase
2. **src/lib/auth.ts**: تم تحديثه لاستخدام SQLite بدلاً من Supabase
3. **package.json**: تمت إضافة سكريبتات جديدة لإدارة قاعدة البيانات

## خدمات البيانات الجديدة

تم إنشاء خدمات بيانات جديدة لإدارة مختلف أنواع البيانات:

1. **src/services/ProductService.ts**: لإدارة المنتجات
2. **src/services/ServiceService.ts**: لإدارة الخدمات
3. **src/services/BlogService.ts**: لإدارة منشورات المدونة
4. **src/services/ProductionLineService.ts**: لإدارة خطوط الإنتاج
5. **src/services/ReviewService.ts**: لإدارة المراجعات
6. **src/services/CartService.ts**: لإدارة سلة التسوق
7. **src/services/WishlistService.ts**: لإدارة قائمة المفضلة
8. **src/services/OrderService.ts**: لإدارة الطلبات
9. **src/services/WholesaleService.ts**: لإدارة طلبات الجملة
10. **src/services/SettingsService.ts**: لإدارة الإعدادات

## هيكل قاعدة البيانات

تم تعريف هيكل قاعدة البيانات في ملف `src/lib/schema.ts`. يحتوي هذا الملف على تعريفات الجداول التالية:

1. **users**: جدول المستخدمين
2. **products**: جدول المنتجات
3. **services**: جدول الخدمات
4. **production_lines**: جدول خطوط الإنتاج
5. **blog_posts**: جدول منشورات المدونة
6. **reviews**: جدول المراجعات
7. **orders**: جدول الطلبات
8. **order_items**: جدول عناصر الطلبات
9. **wholesale_quotes**: جدول طلبات الجملة
10. **payment_methods**: جدول طرق الدفع
11. **currencies**: جدول العملات
12. **shipping_methods**: جدول طرق الشحن
13. **wishlist**: جدول قائمة المفضلة
14. **cart**: جدول سلة التسوق
15. **settings**: جدول الإعدادات

## واجهة محاكاة Supabase

تم إنشاء واجهة محاكاة لـ Supabase في ملف `src/lib/sqlite.ts` للحفاظ على توافق الكود الحالي. هذه الواجهة تستخدم SQLite في الخلفية وتوفر نفس واجهة البرمجة التي يوفرها Supabase.

مثال على استخدام واجهة محاكاة Supabase:

```typescript
import { sqlite } from '../lib/sqlite';

// تسجيل الدخول
const { data, error } = await sqlite.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});

// إنشاء حساب جديد
const { data, error } = await sqlite.auth.signUp({
  email: '<EMAIL>',
  password: 'password'
});

// الاستعلام عن البيانات
const { data, error } = await sqlite.from('users').select('*').limit(10);
```

## استخدام SQLite في بيئة المتصفح

في بيئة المتصفح، يتم استخدام localStorage لمحاكاة قاعدة البيانات SQLite. هذا يسمح بتشغيل التطبيق في المتصفح دون الحاجة إلى خادم Node.js.

مثال على استخدام SQLite في بيئة المتصفح:

```typescript
import { sqliteDB } from '../lib/sqlite';

// الحصول على المستخدمين
const users = sqliteDB.getUsers();

// إضافة مستخدم جديد
const newUser = {
  id: 'user-id',
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user',
  createdAt: new Date().toISOString()
};
sqliteDB.saveUsers([...users, newUser]);
```

## استخدام SQLite في بيئة الخادم (Node.js)

في بيئة الخادم، يتم استخدام مكتبة sqlite3 للتعامل مع قاعدة بيانات SQLite الحقيقية.

مثال على استخدام SQLite في بيئة الخادم:

```typescript
import { getSQLiteDB, closeSQLiteDB } from '../lib/sqliteServer';

// الحصول على نسخة من قاعدة البيانات
const db = getSQLiteDB();

// الحصول على المستخدمين
const users = await db.getUsers();

// إضافة مستخدم جديد
const newUser = await db.createUser({
  email: '<EMAIL>',
  firstName: 'اسم',
  lastName: 'العائلة',
  role: 'user'
});

// إغلاق قاعدة البيانات عند إيقاف التطبيق
closeSQLiteDB();
```

## إدارة قاعدة البيانات

تم إضافة سكريبتات جديدة إلى ملف `package.json` لإدارة قاعدة البيانات:

- **db:init**: لإنشاء قاعدة البيانات وجداولها
- **db:seed**: لإضافة بيانات تجريبية إلى قاعدة البيانات
- **db:reset**: لإعادة تعيين قاعدة البيانات وإضافة بيانات تجريبية
- **setup**: لتثبيت التبعيات وإعداد قاعدة البيانات

## بيانات الدخول الافتراضية

تم إنشاء حسابات افتراضية للاختبار:

### حساب المسؤول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

### حساب المدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

### حساب المستخدم
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

## ملاحظات هامة

1. **تشغيل المشروع محليًا**: يمكن تشغيل المشروع محليًا دون الحاجة إلى اتصال بالإنترنت أو خدمة Supabase.
2. **البيانات الافتراضية**: يتم إنشاء مستخدمين افتراضيين عند تشغيل التطبيق لأول مرة.
3. **التخزين المحلي**: في بيئة المتصفح، يتم استخدام localStorage لتخزين البيانات. هذا يعني أن البيانات ستبقى حتى بعد إغلاق المتصفح.
4. **الأمان**: في بيئة الإنتاج، يجب تشفير كلمات المرور وتنفيذ إجراءات أمان إضافية.

## الخطوات التالية

1. **تحسين الأداء**: تحسين أداء الاستعلامات وتقليل استهلاك الذاكرة.
2. **تحسين الأمان**: تنفيذ إجراءات أمان إضافية مثل تشفير كلمات المرور ومنع هجمات SQL injection.
3. **تحسين التوافق**: تحسين توافق واجهة محاكاة Supabase مع واجهة Supabase الحقيقية.
4. **تحسين التوثيق**: تحسين توثيق الكود والواجهات البرمجية.

## الخلاصة

تم تنفيذ التحول من Supabase إلى SQLite بنجاح، مما يسمح بتشغيل المشروع محليًا دون الحاجة إلى اتصال بالإنترنت أو خدمة Supabase. تم إنشاء واجهة محاكاة لـ Supabase للحفاظ على توافق الكود الحالي، مما يسهل عملية التحول.
