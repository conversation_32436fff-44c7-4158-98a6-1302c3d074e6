import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('should open the auth modal when clicking the sign in button', async ({ page }) => {
    await page.goto('/');
    
    // Click the sign in button
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Check if the auth modal is visible
    const authModal = page.locator('.auth-modal');
    await expect(authModal).toBeVisible();
    
    // Check if the sign in form is visible
    const signInForm = page.locator('.sign-in-form');
    await expect(signInForm).toBeVisible();
  });
  
  test('should switch to sign up form when clicking the sign up link', async ({ page }) => {
    await page.goto('/');
    
    // Click the sign in button
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Click the sign up link
    await page.getByRole('button', { name: /sign up/i }).click();
    
    // Check if the sign up form is visible
    const signUpForm = page.locator('.sign-up-form');
    await expect(signUpForm).toBeVisible();
  });
  
  test('should show validation errors for invalid sign in form', async ({ page }) => {
    await page.goto('/');
    
    // Click the sign in button
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Submit the form without filling it
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Check if validation errors are visible
    const emailError = page.locator('text=Email is required');
    const passwordError = page.locator('text=Password is required');
    
    await expect(emailError).toBeVisible();
    await expect(passwordError).toBeVisible();
  });
  
  test('should show validation errors for invalid sign up form', async ({ page }) => {
    await page.goto('/');
    
    // Click the sign in button
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Click the sign up link
    await page.getByRole('button', { name: /sign up/i }).click();
    
    // Submit the form without filling it
    await page.getByRole('button', { name: /sign up/i }).click();
    
    // Check if validation errors are visible
    const firstNameError = page.locator('text=First name is required');
    const lastNameError = page.locator('text=Last name is required');
    const emailError = page.locator('text=Email is required');
    const passwordError = page.locator('text=Password is required');
    
    await expect(firstNameError).toBeVisible();
    await expect(lastNameError).toBeVisible();
    await expect(emailError).toBeVisible();
    await expect(passwordError).toBeVisible();
  });
  
  test('should sign in successfully with valid credentials', async ({ page }) => {
    await page.goto('/');
    
    // Click the sign in button
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Fill in the form with valid credentials
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('password123');
    
    // Submit the form
    await page.getByRole('button', { name: /sign in/i }).click();
    
    // Check if the auth modal is closed
    const authModal = page.locator('.auth-modal');
    await expect(authModal).not.toBeVisible();
    
    // Check if the user is signed in (account button is visible)
    const accountButton = page.getByRole('button', { name: /account/i });
    await expect(accountButton).toBeVisible();
  });
});
