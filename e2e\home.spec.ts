import { test, expect } from '@playwright/test';

test.describe('Home Page', () => {
  test('should load the home page correctly', async ({ page }) => {
    await page.goto('/');
    
    // Check if the page title is correct
    await expect(page).toHaveTitle(/CommercePro/);
    
    // Check if the header is visible
    const header = page.locator('header');
    await expect(header).toBeVisible();
    
    // Check if the logo is visible
    const logo = page.getByText('CommercePro');
    await expect(logo).toBeVisible();
    
    // Check if the navigation links are visible
    const homeLink = page.getByRole('link', { name: 'Home' });
    const shopLink = page.getByRole('link', { name: 'Shop' });
    
    await expect(homeLink).toBeVisible();
    await expect(shopLink).toBeVisible();
    
    // Check if the footer is visible
    const footer = page.locator('footer');
    await expect(footer).toBeVisible();
  });
  
  test('should navigate to the shop page when clicking the shop link', async ({ page }) => {
    await page.goto('/');
    
    // Click on the shop link
    await page.getByRole('link', { name: 'Shop' }).click();
    
    // Check if the URL has changed to the shop page
    await expect(page).toHaveURL(/\/shop/);
    
    // Check if the shop page title is visible
    const shopTitle = page.getByRole('heading', { name: /shop/i, level: 1 });
    await expect(shopTitle).toBeVisible();
  });
  
  test('should toggle theme when clicking the theme toggle button', async ({ page }) => {
    await page.goto('/');
    
    // Get the initial theme state
    const initialIsDarkMode = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark');
    });
    
    // Click the theme toggle button
    await page.getByRole('button', { name: /toggle theme/i }).click();
    
    // Check if the theme has changed
    const newIsDarkMode = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark');
    });
    
    expect(newIsDarkMode).not.toBe(initialIsDarkMode);
  });
});
