import { test, expect } from '@playwright/test';

test.describe('PWA Features', () => {
  test('should have a valid manifest.json file', async ({ page }) => {
    // Navigate to the manifest.json file
    const response = await page.goto('/manifest.json');
    
    // Check if the response is successful
    expect(response?.status()).toBe(200);
    
    // Parse the manifest.json content
    const manifestContent = await response?.json();
    
    // Check if the manifest has the required fields
    expect(manifestContent).toHaveProperty('name', 'CommercePro');
    expect(manifestContent).toHaveProperty('short_name', 'CommercePro');
    expect(manifestContent).toHaveProperty('start_url', '/');
    expect(manifestContent).toHaveProperty('display', 'standalone');
    expect(manifestContent).toHaveProperty('background_color');
    expect(manifestContent).toHaveProperty('theme_color');
    expect(manifestContent).toHaveProperty('icons');
    expect(Array.isArray(manifestContent.icons)).toBe(true);
    expect(manifestContent.icons.length).toBeGreaterThan(0);
  });
  
  test('should have a service worker', async ({ page }) => {
    await page.goto('/');
    
    // Check if the service worker is registered
    const serviceWorkerStatus = await page.evaluate(() => {
      return 'serviceWorker' in navigator;
    });
    
    expect(serviceWorkerStatus).toBe(true);
  });
  
  test('should have a meta theme-color tag', async ({ page }) => {
    await page.goto('/');
    
    // Check if the meta theme-color tag exists
    const themeColorMeta = page.locator('meta[name="theme-color"]');
    await expect(themeColorMeta).toHaveAttribute('content', '#0066CC');
  });
  
  test('should have a link to manifest.json', async ({ page }) => {
    await page.goto('/');
    
    // Check if the link to manifest.json exists
    const manifestLink = page.locator('link[rel="manifest"]');
    await expect(manifestLink).toHaveAttribute('href', '/manifest.json');
  });
});
