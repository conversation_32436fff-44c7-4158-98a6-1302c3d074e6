import { test, expect } from '@playwright/test';

test.describe('Shop Page', () => {
  test('should load the shop page correctly', async ({ page }) => {
    await page.goto('/shop');
    
    // Check if the page title is correct
    await expect(page).toHaveTitle(/Shop/);
    
    // Check if the shop title is visible
    const shopTitle = page.getByRole('heading', { name: /shop/i, level: 1 });
    await expect(shopTitle).toBeVisible();
    
    // Check if the product filters are visible
    const filters = page.locator('.filters');
    await expect(filters).toBeVisible();
    
    // Check if the product grid is visible
    const productGrid = page.locator('.product-grid');
    await expect(productGrid).toBeVisible();
  });
  
  test('should filter products when using the search input', async ({ page }) => {
    await page.goto('/shop');
    
    // Get the initial number of products
    const initialProductCount = await page.locator('.product-card').count();
    
    // Type in the search input
    await page.getByPlaceholder(/search/i).fill('pump');
    await page.keyboard.press('Enter');
    
    // Wait for the filtered results
    await page.waitForTimeout(500);
    
    // Get the filtered number of products
    const filteredProductCount = await page.locator('.product-card').count();
    
    // The filtered count should be less than or equal to the initial count
    expect(filteredProductCount).toBeLessThanOrEqual(initialProductCount);
    
    // Check if the filtered products contain the search term
    if (filteredProductCount > 0) {
      const firstProductTitle = await page.locator('.product-card').first().locator('h3').textContent();
      expect(firstProductTitle?.toLowerCase()).toContain('pump');
    }
  });
  
  test('should add a product to cart', async ({ page }) => {
    await page.goto('/shop');
    
    // Click on the first product
    await page.locator('.product-card').first().click();
    
    // Wait for the product page to load
    await page.waitForURL(/\/shop\/product\//);
    
    // Click the "Add to Cart" button
    await page.getByRole('button', { name: /add to cart/i }).click();
    
    // Check if the cart count has increased
    const cartCount = page.locator('.cart-count');
    await expect(cartCount).toBeVisible();
    await expect(cartCount).toHaveText('1');
    
    // Navigate to the cart page
    await page.getByRole('link', { name: /cart/i }).click();
    
    // Check if we're on the cart page
    await expect(page).toHaveURL(/\/cart/);
    
    // Check if the product is in the cart
    const cartItem = page.locator('.cart-item');
    await expect(cartItem).toBeVisible();
    await expect(cartItem).toHaveCount(1);
  });
  
  test('should add a product to wishlist', async ({ page }) => {
    await page.goto('/shop');
    
    // Click on the first product
    await page.locator('.product-card').first().click();
    
    // Wait for the product page to load
    await page.waitForURL(/\/shop\/product\//);
    
    // Click the wishlist button
    await page.getByRole('button', { name: /add to wishlist/i }).click();
    
    // Check if the wishlist count has increased
    const wishlistCount = page.locator('.wishlist-count');
    await expect(wishlistCount).toBeVisible();
    await expect(wishlistCount).toHaveText('1');
    
    // Navigate to the wishlist page
    await page.getByRole('link', { name: /wishlist/i }).click();
    
    // Check if we're on the wishlist page
    await expect(page).toHaveURL(/\/shop\/wishlist/);
    
    // Check if the product is in the wishlist
    const wishlistItem = page.locator('.wishlist-item');
    await expect(wishlistItem).toBeVisible();
    await expect(wishlistItem).toHaveCount(1);
  });
});
