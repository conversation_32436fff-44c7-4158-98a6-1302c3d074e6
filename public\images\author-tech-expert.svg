<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="authorGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#667eea" />
      <stop offset="100%" stopColor="#764ba2" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="url(#authorGradient1)" />
  
  <!-- Avatar -->
  <g opacity="0.9">
    <!-- Head -->
    <circle cx="100" cy="80" r="35" fill="rgba(255,255,255,0.9)" />
    
    <!-- Body -->
    <ellipse cx="100" cy="140" rx="45" ry="35" fill="rgba(255,255,255,0.9)" />
    
    <!-- Tech elements -->
    <g opacity="0.7">
      <!-- Glasses -->
      <circle cx="90" cy="75" r="8" fill="none" stroke="rgba(100,100,100,0.8)" strokeWidth="2" />
      <circle cx="110" cy="75" r="8" fill="none" stroke="rgba(100,100,100,0.8)" strokeWidth="2" />
      <line x1="98" y1="75" x2="102" y2="75" stroke="rgba(100,100,100,0.8)" strokeWidth="2" />
      
      <!-- Tech symbols around -->
      <text x="60" y="50" fill="rgba(255,255,255,0.6)" fontSize="16" fontFamily="monospace">&lt;/&gt;</text>
      <text x="130" y="50" fill="rgba(255,255,255,0.6)" fontSize="16" fontFamily="monospace">{}</text>
      <text x="50" y="150" fill="rgba(255,255,255,0.6)" fontSize="14" fontFamily="monospace">AI</text>
      <text x="140" y="150" fill="rgba(255,255,255,0.6)" fontSize="14" fontFamily="monospace">IoT</text>
    </g>
  </g>
</svg>
