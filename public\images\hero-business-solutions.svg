<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#667eea" />
      <stop offset="100%" stopColor="#764ba2" />
    </linearGradient>
    <pattern id="heroPattern1" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)" />
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#heroGradient1)" />
  <rect width="1200" height="600" fill="url(#heroPattern1)" />
  
  <!-- Abstract shapes representing business solutions -->
  <g opacity="0.3">
    <!-- Building/Office shapes -->
    <rect x="100" y="200" width="80" height="120" fill="rgba(255,255,255,0.2)" rx="4" />
    <rect x="200" y="180" width="60" height="140" fill="rgba(255,255,255,0.15)" rx="4" />
    <rect x="280" y="220" width="70" height="100" fill="rgba(255,255,255,0.25)" rx="4" />
    
    <!-- Technology elements -->
    <circle cx="500" cy="150" r="40" fill="rgba(255,255,255,0.2)" />
    <circle cx="550" cy="200" r="30" fill="rgba(255,255,255,0.15)" />
    <circle cx="480" cy="220" r="25" fill="rgba(255,255,255,0.1)" />
    
    <!-- Network connections -->
    <line x1="500" y1="150" x2="550" y2="200" stroke="rgba(255,255,255,0.3)" strokeWidth="2" />
    <line x1="550" y1="200" x2="480" y2="220" stroke="rgba(255,255,255,0.3)" strokeWidth="2" />
    <line x1="480" y1="220" x2="500" y2="150" stroke="rgba(255,255,255,0.3)" strokeWidth="2" />
    
    <!-- Growth chart -->
    <polyline points="800,350 850,320 900,280 950,250 1000,200" 
              stroke="rgba(255,255,255,0.4)" strokeWidth="3" fill="none" />
    <circle cx="800" cy="350" r="4" fill="rgba(255,255,255,0.6)" />
    <circle cx="850" cy="320" r="4" fill="rgba(255,255,255,0.6)" />
    <circle cx="900" cy="280" r="4" fill="rgba(255,255,255,0.6)" />
    <circle cx="950" cy="250" r="4" fill="rgba(255,255,255,0.6)" />
    <circle cx="1000" cy="200" r="4" fill="rgba(255,255,255,0.6)" />
  </g>
  
  <!-- Central focus area -->
  <g transform="translate(600, 300)">
    <!-- Main business icon -->
    <circle cx="0" cy="0" r="60" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" strokeWidth="2" />
    <rect x="-20" y="-20" width="40" height="40" fill="rgba(255,255,255,0.8)" rx="4" />
    <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="rgba(100,100,100,0.8)" strokeWidth="2" rx="2" />
    <line x1="-10" y1="-5" x2="10" y2="-5" stroke="rgba(100,100,100,0.8)" strokeWidth="1.5" />
    <line x1="-10" y1="0" x2="10" y2="0" stroke="rgba(100,100,100,0.8)" strokeWidth="1.5" />
    <line x1="-10" y1="5" x2="5" y2="5" stroke="rgba(100,100,100,0.8)" strokeWidth="1.5" />
  </g>
</svg>
