<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#f093fb" />
      <stop offset="100%" stopColor="#f5576c" />
    </linearGradient>
    <pattern id="heroPattern2" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
      <polygon points="25,5 35,20 25,35 15,20" fill="rgba(255,255,255,0.08)" />
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#heroGradient2)" />
  <rect width="1200" height="600" fill="url(#heroPattern2)" />
  
  <!-- Professional services elements -->
  <g opacity="0.4">
    <!-- Team/People representation -->
    <circle cx="200" cy="200" r="25" fill="rgba(255,255,255,0.3)" />
    <circle cx="250" cy="180" r="25" fill="rgba(255,255,255,0.25)" />
    <circle cx="300" cy="200" r="25" fill="rgba(255,255,255,0.35)" />
    
    <!-- Service icons -->
    <rect x="500" y="150" width="50" height="50" fill="rgba(255,255,255,0.2)" rx="8" />
    <rect x="570" y="150" width="50" height="50" fill="rgba(255,255,255,0.25)" rx="8" />
    <rect x="640" y="150" width="50" height="50" fill="rgba(255,255,255,0.2)" rx="8" />
    
    <!-- Process flow -->
    <circle cx="800" cy="300" r="30" fill="rgba(255,255,255,0.2)" />
    <circle cx="900" cy="300" r="30" fill="rgba(255,255,255,0.25)" />
    <circle cx="1000" cy="300" r="30" fill="rgba(255,255,255,0.2)" />
    
    <!-- Connecting arrows -->
    <path d="M830 300 L870 300" stroke="rgba(255,255,255,0.4)" strokeWidth="3" markerEnd="url(#arrowhead)" />
    <path d="M930 300 L970 300" stroke="rgba(255,255,255,0.4)" strokeWidth="3" markerEnd="url(#arrowhead)" />
  </g>
  
  <!-- Central professional icon -->
  <g transform="translate(600, 300)">
    <circle cx="0" cy="0" r="70" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.4)" strokeWidth="2" />
    <!-- Briefcase icon -->
    <rect x="-25" y="-15" width="50" height="30" fill="rgba(255,255,255,0.9)" rx="4" />
    <rect x="-20" y="-20" width="40" height="8" fill="rgba(255,255,255,0.9)" rx="2" />
    <line x1="-15" y1="-5" x2="15" y2="-5" stroke="rgba(100,100,100,0.8)" strokeWidth="1" />
    <line x1="0" y1="-15" x2="0" y2="15" stroke="rgba(100,100,100,0.6)" strokeWidth="1" />
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="rgba(255,255,255,0.4)" />
    </marker>
  </defs>
</svg>
