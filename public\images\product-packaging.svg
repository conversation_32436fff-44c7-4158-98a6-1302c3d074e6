<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="productGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#f093fb" />
      <stop offset="100%" stopColor="#f5576c" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#productGradient2)" />
  
  <!-- Packaging elements -->
  <g opacity="0.9">
    <!-- Boxes -->
    <g transform="translate(100, 120)">
      <rect x="0" y="0" width="50" height="50" fill="rgba(255,255,255,0.8)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="0" y1="0" x2="10" y2="-10" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="50" y1="0" x2="60" y2="-10" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="50" y1="50" x2="60" y2="40" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <rect x="10" y="-10" width="50" height="50" fill="rgba(255,255,255,0.6)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
    </g>
    
    <g transform="translate(200, 140)">
      <rect x="0" y="0" width="40" height="40" fill="rgba(255,255,255,0.8)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="0" y1="0" x2="8" y2="-8" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="40" y1="0" x2="48" y2="-8" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="40" y1="40" x2="48" y2="32" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <rect x="8" y="-8" width="40" height="40" fill="rgba(255,255,255,0.6)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
    </g>
    
    <g transform="translate(280, 130)">
      <rect x="0" y="0" width="45" height="45" fill="rgba(255,255,255,0.8)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="0" y1="0" x2="9" y2="-9" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="45" y1="0" x2="54" y2="-9" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <line x1="45" y1="45" x2="54" y2="36" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
      <rect x="9" y="-9" width="45" height="45" fill="rgba(255,255,255,0.6)" stroke="rgba(100,100,100,0.6)" strokeWidth="2" />
    </g>
    
    <!-- Packaging machine -->
    <rect x="50" y="200" width="120" height="60" fill="rgba(255,255,255,0.4)" rx="8" />
    <rect x="60" y="210" width="100" height="20" fill="rgba(255,255,255,0.6)" rx="4" />
    <circle cx="70" cy="245" r="8" fill="rgba(255,255,255,0.7)" />
    <circle cx="90" cy="245" r="8" fill="rgba(255,255,255,0.7)" />
    <circle cx="110" cy="245" r="8" fill="rgba(255,255,255,0.7)" />
    <circle cx="130" cy="245" r="8" fill="rgba(255,255,255,0.7)" />
    <circle cx="150" cy="245" r="8" fill="rgba(255,255,255,0.7)" />
    
    <!-- Conveyor -->
    <rect x="180" y="230" width="150" height="15" fill="rgba(255,255,255,0.3)" rx="7" />
    <rect x="190" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="210" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="230" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="250" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="270" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="290" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    <rect x="310" y="233" width="12" height="9" fill="rgba(255,255,255,0.5)" rx="1" />
    
    <!-- Labels -->
    <rect x="120" y="140" width="20" height="15" fill="rgba(255,255,255,0.9)" />
    <rect x="220" y="160" width="20" height="15" fill="rgba(255,255,255,0.9)" />
    <rect x="300" y="150" width="20" height="15" fill="rgba(255,255,255,0.9)" />
  </g>
  
  <!-- Title area -->
  <rect x="0" y="0" width="400" height="50" fill="rgba(0,0,0,0.3)" />
  <text x="200" y="30" textAnchor="middle" fill="white" fontSize="18" fontWeight="bold">Packaging Solutions</text>
</svg>
