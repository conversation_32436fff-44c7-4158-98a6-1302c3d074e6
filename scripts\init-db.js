/**
 * Script para inicializar la base de datos SQLite
 * 
 * Este script crea la base de datos SQLite y las tablas necesarias
 * para el funcionamiento de la aplicación.
 * 
 * Uso: node scripts/init-db.js
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Obtener la ruta de la base de datos desde .env.local o usar una ruta predeterminada
let dbPath = './database.sqlite';
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const dbPathMatch = envContent.match(/SQLITE_DB_PATH=(.+)/);
  if (dbPathMatch && dbPathMatch[1]) {
    dbPath = dbPathMatch[1];
  }
} catch (error) {
  console.log('No se pudo leer .env.local, usando ruta predeterminada:', dbPath);
}

// Asegurarse de que la ruta a la base de datos exista
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Crear la base de datos
const db = new sqlite3.Database(dbPath);

// Definición de las tablas
const tables = [
  // Tabla de usuarios
  `CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    last_name TEXT,
    role TEXT NOT NULL DEFAULT 'user',
    created_at TEXT NOT NULL,
    updated_at TEXT,
    avatar TEXT,
    phone TEXT,
    street TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT,
    language TEXT DEFAULT 'ar',
    theme TEXT DEFAULT 'light',
    notifications INTEGER DEFAULT 1,
    newsletter INTEGER DEFAULT 0
  )`,

  // Tabla de productos
  `CREATE TABLE IF NOT EXISTS products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    price REAL NOT NULL,
    sale_price REAL,
    category TEXT,
    tags TEXT,
    images TEXT,
    featured INTEGER DEFAULT 0,
    in_stock INTEGER DEFAULT 1,
    rating REAL DEFAULT 0,
    reviews INTEGER DEFAULT 0,
    specifications TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de servicios
  `CREATE TABLE IF NOT EXISTS services (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    icon TEXT,
    image TEXT,
    features TEXT,
    features_ar TEXT,
    price REAL,
    price_unit TEXT,
    category TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de líneas de producción
  `CREATE TABLE IF NOT EXISTS production_lines (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    capacity TEXT,
    specifications TEXT,
    images TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de publicaciones del blog
  `CREATE TABLE IF NOT EXISTS blog_posts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    title_ar TEXT,
    slug TEXT UNIQUE NOT NULL,
    excerpt TEXT,
    excerpt_ar TEXT,
    content TEXT,
    content_ar TEXT,
    author TEXT,
    author_title TEXT,
    author_image TEXT,
    cover_image TEXT,
    category TEXT,
    tags TEXT,
    published_at TEXT NOT NULL,
    updated_at TEXT,
    read_time TEXT
  )`,

  // Tabla de reseñas
  `CREATE TABLE IF NOT EXISTS reviews (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    user_name TEXT NOT NULL,
    user_avatar TEXT,
    rating INTEGER NOT NULL,
    title TEXT,
    comment TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    helpful INTEGER DEFAULT 0,
    verified INTEGER DEFAULT 0,
    images TEXT,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
  )`,

  // Tabla de pedidos
  `CREATE TABLE IF NOT EXISTS orders (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    status TEXT NOT NULL,
    total REAL NOT NULL,
    shipping_fee REAL DEFAULT 0,
    tax REAL DEFAULT 0,
    discount REAL DEFAULT 0,
    payment_method TEXT,
    payment_status TEXT,
    shipping_address TEXT,
    billing_address TEXT,
    notes TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
  )`,

  // Tabla de elementos de pedidos
  `CREATE TABLE IF NOT EXISTS order_items (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price REAL NOT NULL,
    total REAL NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
  )`,

  // Tabla de cotizaciones al por mayor
  `CREATE TABLE IF NOT EXISTS wholesale_quotes (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    company_name TEXT,
    contact_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    product_interest TEXT,
    quantity INTEGER,
    message TEXT,
    status TEXT DEFAULT 'pending',
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
  )`,

  // Tabla de métodos de pago
  `CREATE TABLE IF NOT EXISTS payment_methods (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    type TEXT NOT NULL,
    icon TEXT,
    supported_currencies TEXT,
    processing_fee REAL DEFAULT 0,
    processing_fee_type TEXT DEFAULT 'percentage',
    enabled INTEGER DEFAULT 1,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de monedas
  `CREATE TABLE IF NOT EXISTS currencies (
    code TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    symbol TEXT NOT NULL,
    rate REAL NOT NULL,
    is_default INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de métodos de envío
  `CREATE TABLE IF NOT EXISTS shipping_methods (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT,
    description TEXT,
    description_ar TEXT,
    price REAL NOT NULL,
    estimated_delivery TEXT,
    countries TEXT,
    icon TEXT,
    enabled INTEGER DEFAULT 1,
    created_at TEXT NOT NULL,
    updated_at TEXT
  )`,

  // Tabla de lista de deseos
  `CREATE TABLE IF NOT EXISTS wishlist (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
  )`,

  // Tabla de carrito
  `CREATE TABLE IF NOT EXISTS cart (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
  )`,

  // Tabla de configuraciones
  `CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT,
    updated_at TEXT
  )`
];

// Crear las tablas
db.serialize(() => {
  // Habilitar las claves foráneas
  db.run('PRAGMA foreign_keys = ON');

  // Crear cada tabla
  tables.forEach(tableSchema => {
    db.run(tableSchema, err => {
      if (err) {
        console.error('Error al crear la tabla:', err.message);
      }
    });
  });

  console.log('Base de datos inicializada correctamente');
});

// Cerrar la conexión a la base de datos
db.close(err => {
  if (err) {
    console.error('Error al cerrar la base de datos:', err.message);
  } else {
    console.log('Conexión a la base de datos cerrada');
  }
});
