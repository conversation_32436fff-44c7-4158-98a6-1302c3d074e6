/**
 * Script para poblar la base de datos SQLite con datos de ejemplo
 * 
 * Este script inserta datos de ejemplo en la base de datos SQLite
 * para facilitar el desarrollo y las pruebas.
 * 
 * Uso: node scripts/seed-db.js
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Obtener la ruta de la base de datos desde .env.local o usar una ruta predeterminada
let dbPath = './database.sqlite';
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const dbPathMatch = envContent.match(/SQLITE_DB_PATH=(.+)/);
  if (dbPathMatch && dbPathMatch[1]) {
    dbPath = dbPathMatch[1];
  }
} catch (error) {
  console.log('No se pudo leer .env.local, usando ruta predeterminada:', dbPath);
}

// Verificar si la base de datos existe
if (!fs.existsSync(dbPath)) {
  console.error(`La base de datos no existe en ${dbPath}. Ejecute primero scripts/init-db.js`);
  process.exit(1);
}

// Abrir la base de datos
const db = new sqlite3.Database(dbPath);

// Datos de ejemplo
const sampleData = {
  // Usuarios de ejemplo
  users: [
    {
      id: 'admin-user-id',
      email: '<EMAIL>',
      first_name: 'أحمد',
      last_name: 'المدير',
      role: 'admin',
      created_at: new Date().toISOString(),
      phone: '+966500000000',
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      postal_code: '12345',
      country: 'المملكة العربية السعودية',
      language: 'ar',
      theme: 'light',
      notifications: 1,
      newsletter: 0
    },
    {
      id: 'manager-user-id',
      email: '<EMAIL>',
      first_name: 'خالد',
      last_name: 'المدير',
      role: 'admin',
      created_at: new Date().toISOString(),
      phone: '+966500000001',
      street: 'شارع العليا',
      city: 'الرياض',
      state: 'الرياض',
      postal_code: '12345',
      country: 'المملكة العربية السعودية',
      language: 'ar',
      theme: 'light',
      notifications: 1,
      newsletter: 0
    },
    {
      id: 'user-id-1',
      email: '<EMAIL>',
      first_name: 'محمد',
      last_name: 'العميل',
      role: 'user',
      created_at: new Date().toISOString(),
      phone: '+966500000002',
      street: 'شارع التحلية',
      city: 'جدة',
      state: 'مكة المكرمة',
      postal_code: '23456',
      country: 'المملكة العربية السعودية',
      language: 'ar',
      theme: 'light',
      notifications: 1,
      newsletter: 1
    }
  ],

  // Productos de ejemplo
  products: [
    {
      id: 'product-1',
      name: 'Industrial Mixer',
      name_ar: 'خلاط صناعي',
      slug: 'industrial-mixer',
      description: 'High-quality industrial mixer for various applications.',
      description_ar: 'خلاط صناعي عالي الجودة لمختلف التطبيقات.',
      price: 1500,
      sale_price: 1350,
      category: 'equipment',
      tags: JSON.stringify(['mixer', 'industrial', 'manufacturing']),
      images: JSON.stringify(['/images/products/mixer1.jpg', '/images/products/mixer2.jpg']),
      featured: 1,
      in_stock: 1,
      rating: 4.5,
      reviews: 12,
      specifications: JSON.stringify({
        power: '2200W',
        capacity: '50L',
        weight: '75kg',
        dimensions: '80x60x120cm'
      }),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'product-2',
      name: 'Conveyor Belt System',
      name_ar: 'نظام السير الناقل',
      slug: 'conveyor-belt-system',
      description: 'Efficient conveyor belt system for production lines.',
      description_ar: 'نظام سير ناقل فعال لخطوط الإنتاج.',
      price: 3000,
      sale_price: null,
      category: 'equipment',
      tags: JSON.stringify(['conveyor', 'production', 'manufacturing']),
      images: JSON.stringify(['/images/products/conveyor1.jpg', '/images/products/conveyor2.jpg']),
      featured: 1,
      in_stock: 1,
      rating: 4.8,
      reviews: 8,
      specifications: JSON.stringify({
        length: '10m',
        width: '60cm',
        speed: '0.5-2m/s',
        maxLoad: '200kg/m'
      }),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'product-3',
      name: 'Packaging Machine',
      name_ar: 'آلة تعبئة وتغليف',
      slug: 'packaging-machine',
      description: 'Automated packaging machine for various products.',
      description_ar: 'آلة تعبئة وتغليف آلية لمختلف المنتجات.',
      price: 5000,
      sale_price: 4500,
      category: 'equipment',
      tags: JSON.stringify(['packaging', 'automation', 'manufacturing']),
      images: JSON.stringify(['/images/products/packaging1.jpg', '/images/products/packaging2.jpg']),
      featured: 0,
      in_stock: 1,
      rating: 4.2,
      reviews: 5,
      specifications: JSON.stringify({
        speed: '30-60 packages/min',
        power: '3500W',
        weight: '250kg',
        dimensions: '200x100x150cm'
      }),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],

  // Servicios de ejemplo
  services: [
    {
      id: 'service-1',
      name: 'Production Line Installation',
      name_ar: 'تركيب خط إنتاج',
      slug: 'production-line-installation',
      description: 'Professional installation of production lines.',
      description_ar: 'تركيب احترافي لخطوط الإنتاج.',
      icon: 'tools',
      image: '/images/services/installation.jpg',
      features: JSON.stringify(['Expert technicians', 'Quick setup', 'Quality assurance', 'Post-installation support']),
      features_ar: JSON.stringify(['فنيون خبراء', 'إعداد سريع', 'ضمان الجودة', 'دعم ما بعد التركيب']),
      price: 2000,
      price_unit: 'per day',
      category: 'installation',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'service-2',
      name: 'Maintenance Service',
      name_ar: 'خدمة الصيانة',
      slug: 'maintenance-service',
      description: 'Regular maintenance for industrial equipment.',
      description_ar: 'صيانة دورية للمعدات الصناعية.',
      icon: 'wrench',
      image: '/images/services/maintenance.jpg',
      features: JSON.stringify(['Preventive maintenance', 'Emergency repairs', 'Spare parts supply', 'Technical support']),
      features_ar: JSON.stringify(['صيانة وقائية', 'إصلاحات طارئة', 'توريد قطع غيار', 'دعم فني']),
      price: 500,
      price_unit: 'per visit',
      category: 'maintenance',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'service-3',
      name: 'Consulting Services',
      name_ar: 'خدمات استشارية',
      slug: 'consulting-services',
      description: 'Expert consulting for industrial processes optimization.',
      description_ar: 'استشارات خبيرة لتحسين العمليات الصناعية.',
      icon: 'lightbulb',
      image: '/images/services/consulting.jpg',
      features: JSON.stringify(['Process analysis', 'Efficiency improvement', 'Cost reduction', 'Implementation support']),
      features_ar: JSON.stringify(['تحليل العمليات', 'تحسين الكفاءة', 'تقليل التكاليف', 'دعم التنفيذ']),
      price: 1000,
      price_unit: 'per day',
      category: 'consulting',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],

  // Líneas de producción de ejemplo
  productionLines: [
    {
      id: 'line-1',
      name: 'Food Processing Line',
      name_ar: 'خط معالجة الأغذية',
      slug: 'food-processing-line',
      description: 'Complete food processing line for various food products.',
      description_ar: 'خط كامل لمعالجة الأغذية لمختلف المنتجات الغذائية.',
      capacity: '500kg/hour',
      specifications: JSON.stringify({
        power: '15kW',
        length: '20m',
        width: '3m',
        height: '2.5m'
      }),
      images: JSON.stringify(['/images/production-lines/food1.jpg', '/images/production-lines/food2.jpg']),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'line-2',
      name: 'Beverage Bottling Line',
      name_ar: 'خط تعبئة المشروبات',
      slug: 'beverage-bottling-line',
      description: 'Automated bottling line for various beverages.',
      description_ar: 'خط تعبئة آلي لمختلف المشروبات.',
      capacity: '5000 bottles/hour',
      specifications: JSON.stringify({
        power: '25kW',
        length: '30m',
        width: '4m',
        height: '3m'
      }),
      images: JSON.stringify(['/images/production-lines/beverage1.jpg', '/images/production-lines/beverage2.jpg']),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],

  // Publicaciones del blog de ejemplo
  blogPosts: [
    {
      id: 'post-1',
      title: 'The Future of Industrial Automation',
      title_ar: 'مستقبل الأتمتة الصناعية',
      slug: 'future-of-industrial-automation',
      excerpt: 'Exploring the latest trends in industrial automation and their impact on manufacturing.',
      excerpt_ar: 'استكشاف أحدث اتجاهات الأتمتة الصناعية وتأثيرها على التصنيع.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      content_ar: 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربي.',
      author: 'Ahmed Al-Madani',
      author_title: 'Industrial Expert',
      author_image: '/images/authors/ahmed.jpg',
      cover_image: '/images/blog/automation.jpg',
      category: 'automation',
      tags: JSON.stringify(['automation', 'industry', 'future', 'technology']),
      published_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      read_time: '5 min'
    },
    {
      id: 'post-2',
      title: 'Improving Production Efficiency',
      title_ar: 'تحسين كفاءة الإنتاج',
      slug: 'improving-production-efficiency',
      excerpt: 'Practical tips to improve efficiency in your production line.',
      excerpt_ar: 'نصائح عملية لتحسين الكفاءة في خط الإنتاج الخاص بك.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      content_ar: 'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربي.',
      author: 'Khalid Al-Otaibi',
      author_title: 'Production Manager',
      author_image: '/images/authors/khalid.jpg',
      cover_image: '/images/blog/efficiency.jpg',
      category: 'production',
      tags: JSON.stringify(['efficiency', 'production', 'optimization', 'management']),
      published_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      read_time: '7 min'
    }
  ]
};

// Insertar datos en la base de datos
db.serialize(() => {
  // Habilitar las claves foráneas
  db.run('PRAGMA foreign_keys = ON');

  // Insertar usuarios
  const userStmt = db.prepare(`
    INSERT OR REPLACE INTO users (
      id, email, first_name, last_name, role, created_at, phone, street, city, state, postal_code, country, language, theme, notifications, newsletter
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  sampleData.users.forEach(user => {
    userStmt.run(
      user.id,
      user.email,
      user.first_name,
      user.last_name,
      user.role,
      user.created_at,
      user.phone,
      user.street,
      user.city,
      user.state,
      user.postal_code,
      user.country,
      user.language,
      user.theme,
      user.notifications,
      user.newsletter
    );
  });

  userStmt.finalize();
  console.log(`Insertados ${sampleData.users.length} usuarios`);

  // Insertar productos
  const productStmt = db.prepare(`
    INSERT OR REPLACE INTO products (
      id, name, name_ar, slug, description, description_ar, price, sale_price, category, tags, images, featured, in_stock, rating, reviews, specifications, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  sampleData.products.forEach(product => {
    productStmt.run(
      product.id,
      product.name,
      product.name_ar,
      product.slug,
      product.description,
      product.description_ar,
      product.price,
      product.sale_price,
      product.category,
      product.tags,
      product.images,
      product.featured,
      product.in_stock,
      product.rating,
      product.reviews,
      product.specifications,
      product.created_at,
      product.updated_at
    );
  });

  productStmt.finalize();
  console.log(`Insertados ${sampleData.products.length} productos`);

  // Insertar servicios
  const serviceStmt = db.prepare(`
    INSERT OR REPLACE INTO services (
      id, name, name_ar, slug, description, description_ar, icon, image, features, features_ar, price, price_unit, category, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  sampleData.services.forEach(service => {
    serviceStmt.run(
      service.id,
      service.name,
      service.name_ar,
      service.slug,
      service.description,
      service.description_ar,
      service.icon,
      service.image,
      service.features,
      service.features_ar,
      service.price,
      service.price_unit,
      service.category,
      service.created_at,
      service.updated_at
    );
  });

  serviceStmt.finalize();
  console.log(`Insertados ${sampleData.services.length} servicios`);

  // Insertar líneas de producción
  const lineStmt = db.prepare(`
    INSERT OR REPLACE INTO production_lines (
      id, name, name_ar, slug, description, description_ar, capacity, specifications, images, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  sampleData.productionLines.forEach(line => {
    lineStmt.run(
      line.id,
      line.name,
      line.name_ar,
      line.slug,
      line.description,
      line.description_ar,
      line.capacity,
      line.specifications,
      line.images,
      line.created_at,
      line.updated_at
    );
  });

  lineStmt.finalize();
  console.log(`Insertadas ${sampleData.productionLines.length} líneas de producción`);

  // Insertar publicaciones del blog
  const postStmt = db.prepare(`
    INSERT OR REPLACE INTO blog_posts (
      id, title, title_ar, slug, excerpt, excerpt_ar, content, content_ar, author, author_title, author_image, cover_image, category, tags, published_at, updated_at, read_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  sampleData.blogPosts.forEach(post => {
    postStmt.run(
      post.id,
      post.title,
      post.title_ar,
      post.slug,
      post.excerpt,
      post.excerpt_ar,
      post.content,
      post.content_ar,
      post.author,
      post.author_title,
      post.author_image,
      post.cover_image,
      post.category,
      post.tags,
      post.published_at,
      post.updated_at,
      post.read_time
    );
  });

  postStmt.finalize();
  console.log(`Insertadas ${sampleData.blogPosts.length} publicaciones del blog`);

  console.log('Base de datos poblada correctamente con datos de ejemplo');
});

// Cerrar la conexión a la base de datos
db.close(err => {
  if (err) {
    console.error('Error al cerrar la base de datos:', err.message);
  } else {
    console.log('Conexión a la base de datos cerrada');
  }
});
