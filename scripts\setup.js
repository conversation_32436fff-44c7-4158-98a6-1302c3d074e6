/**
 * Script de configuración para facilitar la instalación del proyecto
 * 
 * Este script realiza las siguientes tareas:
 * 1. Verifica que Node.js y npm estén instalados
 * 2. Crea el archivo .env.local si no existe
 * 3. Crea la carpeta scripts si no existe
 * 4. Verifica que los scripts de base de datos existan
 * 5. Muestra instrucciones para completar la configuración
 * 
 * Uso: node scripts/setup.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Verificar que Node.js y npm estén instalados
try {
  const nodeVersion = execSync('node --version').toString().trim();
  const npmVersion = execSync('npm --version').toString().trim();
  console.log(`Node.js: ${nodeVersion}`);
  console.log(`npm: ${npmVersion}`);
} catch (error) {
  console.error('Error: Node.js o npm no están instalados correctamente.');
  process.exit(1);
}

// Crear el archivo .env.local si no existe
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  const envContent = `# Configuración de SQLite
SQLITE_DB_PATH=./database.sqlite

# Configuración de la aplicación
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_DEFAULT_LANGUAGE=ar
NEXT_PUBLIC_SECONDARY_LANGUAGE=en

# Configuración de seguridad
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-here
NEXT_PUBLIC_CSRF_SECRET=your-csrf-secret-here

# Configuración de correo electrónico (opcional)
# EMAIL_SERVER=smtp://username:<EMAIL>:587
# EMAIL_FROM=<EMAIL>

# Configuración de pagos (opcional)
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
# STRIPE_SECRET_KEY=your-stripe-secret-key
# NEXT_PUBLIC_MADA_PUBLISHABLE_KEY=your-mada-publishable-key
# MADA_SECRET_KEY=your-mada-secret-key`;

  fs.writeFileSync(envPath, envContent);
  console.log('Archivo .env.local creado correctamente.');
} else {
  console.log('El archivo .env.local ya existe.');
}

// Crear la carpeta scripts si no existe
const scriptsDir = path.join(process.cwd(), 'scripts');
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir);
  console.log('Carpeta scripts creada correctamente.');
} else {
  console.log('La carpeta scripts ya existe.');
}

// Verificar que los scripts de base de datos existan
const initDbPath = path.join(scriptsDir, 'init-db.js');
const seedDbPath = path.join(scriptsDir, 'seed-db.js');

if (!fs.existsSync(initDbPath) || !fs.existsSync(seedDbPath)) {
  console.error('Error: Los scripts de base de datos no existen.');
  console.error('Por favor, crea los archivos scripts/init-db.js y scripts/seed-db.js.');
  process.exit(1);
}

// Mostrar instrucciones para completar la configuración
console.log('\n=== Configuración completada ===');
console.log('\nPara completar la instalación, ejecuta los siguientes comandos:');
console.log('\n1. Instalar las dependencias:');
console.log('   npm install');
console.log('\n2. Inicializar la base de datos:');
console.log('   npm run db:init');
console.log('\n3. Poblar la base de datos con datos de ejemplo:');
console.log('   npm run db:seed');
console.log('\n4. Iniciar el servidor de desarrollo:');
console.log('   npm run dev');
console.log('\nO simplemente ejecuta:');
console.log('   npm run setup');
console.log('\nLuego abre http://localhost:3000 en tu navegador.');
console.log('\n=== Credenciales de acceso ===');
console.log('\nAdministrador:');
console.log('   Email: <EMAIL>');
console.log('   Contraseña: password');
console.log('\nGerente:');
console.log('   Email: <EMAIL>');
console.log('   Contraseña: password');
console.log('\nUsuario:');
console.log('   Email: <EMAIL>');
console.log('   Contraseña: password');
console.log('\n¡Gracias por usar Commerce Pro!');
