import { render, screen, fireEvent } from '@testing-library/react';
import { Header } from '@/components/layout/Header';

// Mock the stores
jest.mock('../../../stores/cartStore', () => ({
  useCartStore: () => ({
    items: [],
    getTotalItems: () => 0,
  }),
}));

jest.mock('../../../stores/wishlistStore', () => ({
  useWishlistStore: () => ({
    items: [],
    getTotalItems: () => 0,
  }),
}));

jest.mock('../../../stores/authStore', () => ({
  useAuthStore: () => ({
    user: null,
  }),
}));

jest.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    isDarkMode: false,
    toggleTheme: jest.fn(),
  }),
}));

jest.mock('../../../stores/languageStore', () => ({
  useLanguageStore: () => ({
    language: 'en',
    setLanguage: jest.fn(),
  }),
}));

describe('Header Component', () => {
  it('renders correctly', () => {
    render(<Header />);
    
    // Check if the logo is visible
    const logo = screen.getByText('CommercePro');
    expect(logo).toBeInTheDocument();
    
    // Check if the navigation links are visible
    const homeLink = screen.getByRole('link', { name: /home/<USER>
    const shopLink = screen.getByRole('link', { name: /shop/i });
    
    expect(homeLink).toBeInTheDocument();
    expect(shopLink).toBeInTheDocument();
    
    // Check if the cart and wishlist icons are visible
    const cartIcon = screen.getByLabelText(/shopping cart/i);
    const wishlistIcon = screen.getByLabelText(/wishlist/i);
    
    expect(cartIcon).toBeInTheDocument();
    expect(wishlistIcon).toBeInTheDocument();
  });
  
  it('opens search when search button is clicked', () => {
    render(<Header />);
    
    // Click the search button
    const searchButton = screen.getByLabelText(/search/i);
    fireEvent.click(searchButton);
    
    // Check if the search input is visible
    const searchInput = screen.getByPlaceholder(/search/i);
    expect(searchInput).toBeInTheDocument();
  });
  
  it('opens auth modal when sign in button is clicked', () => {
    render(<Header />);
    
    // Click the account button
    const accountButton = screen.getByLabelText(/account/i);
    fireEvent.click(accountButton);
    
    // Check if the auth modal is visible
    const authModal = screen.getByText(/sign in/i);
    expect(authModal).toBeInTheDocument();
  });
  
  it('opens mobile menu when menu button is clicked', () => {
    render(<Header />);
    
    // Click the menu button
    const menuButton = screen.getByLabelText(/menu/i);
    fireEvent.click(menuButton);
    
    // Check if the mobile menu is visible
    const mobileMenu = screen.getByText(/special offers/i);
    expect(mobileMenu).toBeInTheDocument();
  });
});
