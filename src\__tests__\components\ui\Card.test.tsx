import { render, screen } from '@testing-library/react';
import { Card } from '@/components/ui/Card';

describe('Card Component', () => {
  it('renders correctly with default props', () => {
    render(
      <Card>
        <p>Card content</p>
      </Card>
    );
    
    const card = screen.getByText('Card content').closest('div');
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('bg-white');
    expect(card).toHaveClass('rounded-lg');
    expect(card).toHaveClass('shadow-md');
  });

  it('applies custom className', () => {
    render(
      <Card className="custom-class">
        <p>Card content</p>
      </Card>
    );
    
    const card = screen.getByText('Card content').closest('div');
    expect(card).toHaveClass('custom-class');
  });

  it('renders with custom HTML attributes', () => {
    render(
      <Card data-testid="custom-card" aria-label="Card component">
        <p>Card content</p>
      </Card>
    );
    
    const card = screen.getByTestId('custom-card');
    expect(card).toHaveAttribute('aria-label', 'Card component');
  });

  it('renders children correctly', () => {
    render(
      <Card>
        <h2>Card Title</h2>
        <p>Card description</p>
        <button>Card button</button>
      </Card>
    );
    
    expect(screen.getByText('Card Title')).toBeInTheDocument();
    expect(screen.getByText('Card description')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
