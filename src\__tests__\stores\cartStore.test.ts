import { renderHook, act } from '@testing-library/react';
import { useCartStore } from '@/stores/cartStore';

// Mock product data
const mockProduct = {
  id: '1',
  name: 'Test Product',
  price: 100,
  image: 'test-image.jpg',
  images: ['test-image.jpg'],
  description: 'Test description',
  inStock: true,
  quantity: 1,
};

describe('cartStore', () => {
  beforeEach(() => {
    // Clear the store before each test
    const { result } = renderHook(() => useCartStore());
    act(() => {
      result.current.clearCart();
    });
  });

  it('should add an item to the cart', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
    });
    
    expect(result.current.items.length).toBe(1);
    expect(result.current.items[0].id).toBe('1');
    expect(result.current.items[0].quantity).toBe(1);
  });

  it('should increase quantity when adding the same item', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.addItem(mockProduct, 2);
    });
    
    expect(result.current.items.length).toBe(1);
    expect(result.current.items[0].quantity).toBe(3);
  });

  it('should remove an item from the cart', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.removeItem('1');
    });
    
    expect(result.current.items.length).toBe(0);
  });

  it('should update item quantity', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.updateQuantity('1', 5);
    });
    
    expect(result.current.items[0].quantity).toBe(5);
  });

  it('should clear the cart', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.addItem({ ...mockProduct, id: '2' }, 1);
      result.current.clearCart();
    });
    
    expect(result.current.items.length).toBe(0);
  });

  it('should calculate total price correctly', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 2); // 2 * 100 = 200
      result.current.addItem({ ...mockProduct, id: '2', price: 50 }, 3); // 3 * 50 = 150
    });
    
    expect(result.current.getTotalPrice()).toBe(350);
  });

  it('should check if a product is in the cart', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
    });
    
    expect(result.current.isProductInCart('1')).toBe(true);
    expect(result.current.isProductInCart('2')).toBe(false);
  });

  it('should get total items count correctly', () => {
    const { result } = renderHook(() => useCartStore());
    
    act(() => {
      result.current.addItem(mockProduct, 2);
      result.current.addItem({ ...mockProduct, id: '2' }, 3);
    });
    
    expect(result.current.getTotalItems()).toBe(5);
  });
});
