'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthModal } from '../../../components/auth/AuthModal';
import { createDefaultAdminUser, createSecondAdminUser } from '../../../services/AuthService';
import { useAuthStore } from '../../../stores/authStore';

export function AdminAuth() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // إنشاء مستخدمين افتراضيين عند تحميل المكون
  useEffect(() => {
    const initUsers = async () => {
      try {
        await createDefaultAdminUser();
        await createSecondAdminUser();
        console.log('Default admin users created for testing');
      } catch (error) {
        console.error('Error creating default users:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initUsers();
  }, []);

  // التحقق من صلاحيات المستخدم - فقط بعد تهيئة المستخدمين الافتراضيين
  useEffect(() => {
    if (!isInitialized) {
      return; // انتظر حتى تكتمل تهيئة المستخدمين الافتراضيين
    }

    // فقط إذا كان المستخدم مسجل الدخول ولكن ليس لديه صلاحيات المسؤول
    if (isAuthenticated && user && user.role !== 'admin') {
      console.log('User is authenticated but not an admin, redirecting to home page');
      router.push('/');
    }
  }, [isAuthenticated, user, router, isInitialized]);

  // إذا لم تكتمل تهيئة المستخدمين الافتراضيين، عرض شاشة التحميل
  if (!isInitialized) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-100/90 dark:bg-slate-900/90 backdrop-blur-sm">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mb-4"></div>
          <p className="text-slate-700 dark:text-slate-300">جاري تهيئة النظام...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن المستخدم مسجل الدخول، عرض نافذة تسجيل الدخول
  if (!isAuthenticated) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-100/90 dark:bg-slate-900/90 backdrop-blur-sm">
        <AuthModal
          onClose={() => {}} // لا نسمح بإغلاق النافذة لأن تسجيل الدخول مطلوب
          mode="sign-in"
        />
      </div>
    );
  }

  // إذا كان المستخدم مسجل الدخول وله صلاحيات المسؤول، لا تعرض شيئًا
  return null;
}
