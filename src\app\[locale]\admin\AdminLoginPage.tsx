'use client';

import { useState, useEffect } from 'react';
import { Card } from '../../../components/ui/Card';
import { Input } from '../../../components/ui/Input';
import { Button } from '../../../components/ui/Button';
import { useAuthStore } from '../../../stores/authStore';
import { AlertCircle, CheckCircle, User, Mail, Lock } from 'lucide-react';
import { useTranslation } from '../../../translations';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../../stores/languageStore';
import { cn } from '../../../lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { createDefaultAdminUser, createSecondAdminUser } from '../../../services/AuthService';
import { resetLocalUsers } from '../../../services/LocalUserService';

export default function AdminLoginPage() {
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: 'password',
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const { signIn, isLoading, user, signOut, isAuthenticated } = useAuthStore();
  const { t, locale } = useTranslation();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // إنشاء مستخدمين افتراضيين عند تحميل المكون
  useEffect(() => {
    const initUsers = async () => {
      try {
        await createDefaultAdminUser();
        await createSecondAdminUser();
        console.log('Default admin users created for testing');
      } catch (error) {
        console.error('Error creating default users:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initUsers();
  }, []);

  // التحقق من حالة المستخدم المسجل الدخول
  useEffect(() => {
    // إذا كان المستخدم مسجل الدخول ولكن ليس لديه صلاحيات المسؤول، عرض رسالة خطأ
    if (isAuthenticated && user && user.role !== 'admin') {
      setError(t('auth.notAuthorized') || 'ليس لديك صلاحيات كافية للوصول إلى لوحة تحكم الإدارة');
      // تسجيل الخروج بعد 2 ثانية
      setTimeout(() => {
        signOut();
      }, 2000);
    }
  }, [isAuthenticated, user, t, signOut]);

  // التحقق من صحة النموذج
  const validateForm = () => {
    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      setError(t('auth.emailRequired'));
      return false;
    }

    // التحقق من تنسيق البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError(t('auth.invalidEmail'));
      return false;
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      setError(t('auth.passwordRequired'));
      return false;
    }

    // التحقق من طول كلمة المرور
    if (formData.password.length < 6) {
      setError(t('auth.passwordTooShort'));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // التحقق من صحة النموذج
    if (!validateForm()) {
      return;
    }

    try {
      // تسجيل الدخول
      console.log('Attempting to sign in with:', formData.email);
      setIsSubmitting(true);

      // إعادة تعيين بيانات المستخدمين المحليين
      console.log('Resetting local users before sign in');
      resetLocalUsers();

      // إنشاء مستخدمين افتراضيين
      console.log('Creating default admin users');
      await createDefaultAdminUser();
      await createSecondAdminUser();
      console.log('Default admin users created successfully');

      const result = await signIn(formData.email, formData.password);

      // Revised logic to handle signIn result and fix lint errors
      if (result && result.error) {
        // Handle cases where signIn resolves with an error object (e.g., invalid credentials)
        let errorMessage = t('auth.genericError') || 'An unknown error occurred.';
        if (typeof result.error === 'string') {
          errorMessage = result.error;
        } else if (typeof result.error === 'object' && result.error !== null && 'message' in result.error && typeof (result.error as any).message === 'string') {
          errorMessage = (result.error as any).message;
        }

        if (errorMessage === 'User not found' || errorMessage === 'Incorrect password') {
          setError(t('auth.invalidCredentials') || 'بيانات الدخول غير صحيحة.');
        } else {
          setError(errorMessage);
        }
      } else if (result && result.user && result.user.role === 'admin') {
        // Handle successful admin login
        setSuccess(t('auth.signInSuccess') || 'تم تسجيل الدخول بنجاح! جارٍ إعادة توجيهك...');
        setTimeout(() => {
          window.location.reload(); // Or use Next.js router for client-side navigation
        }, 1500);
      } else {
        // Handle other cases: e.g., user authenticated but not an admin, or unexpected result structure
        // The useEffect hook also handles signing out an authenticated non-admin user if they somehow get past here.
        setError(t('auth.notAuthorized') || 'Failed to sign in, user is not authorized, or an unexpected issue occurred.');
      }
    } catch (networkOrOtherError: any) {
      // This catch block handles actual thrown exceptions from signIn (e.g., network errors)
      console.error('Sign in exception:', networkOrOtherError);
      // It's possible some errors previously caught by result.error might now be thrown exceptions
      // depending on how signIn is implemented. Keeping robust error handling here.
      if (networkOrOtherError.message === 'User not found' || networkOrOtherError.message === 'Incorrect password') {
        setError(t('auth.invalidCredentials') || 'بيانات الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.');
      } else {
        setError(networkOrOtherError.message || t('auth.genericError') || 'حدث خطأ غير متوقع أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // إذا لم تكتمل تهيئة المستخدمين الافتراضيين، عرض شاشة التحميل
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-100 dark:bg-slate-900">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mb-4"></div>
          <p className="text-slate-700 dark:text-slate-300">جاري تهيئة النظام...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-100 dark:bg-slate-900 p-4">
      <div className="w-full max-w-md">
        <Card className={cn(
          "w-full p-8 relative overflow-hidden border-0 rounded-2xl",
          currentIsDark
            ? "bg-slate-800/95 text-white shadow-xl shadow-slate-900/30"
            : "bg-white/95 text-slate-900 shadow-xl shadow-slate-300/30"
        )}>
          {/* زخرفة خلفية */}
          <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 via-accent-500 to-secondary-500"></div>
          <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-primary-500/10 blur-3xl"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-accent-500/10 blur-3xl"></div>

          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mb-4">
              <User className="h-8 w-8 text-primary-600 dark:text-primary-400" />
            </div>
            <h2 className="text-2xl font-bold mb-2">
              {currentLanguage === 'ar' ? 'تسجيل الدخول للإدارة' : 'Admin Login'}
            </h2>
            <p className={cn(
              "text-sm",
              currentIsDark ? "text-slate-400" : "text-slate-500"
            )}>
              {currentLanguage === 'ar'
                ? 'أهلاً بك في لوحة تحكم الإدارة. يرجى تسجيل الدخول للوصول إلى لوحة التحكم.'
                : 'Welcome to the admin dashboard. Please sign in to access the dashboard.'}
            </p>
          </div>

          <AnimatePresence>
            {error && (
              <motion.div
                className={cn(
                  "p-4 rounded-lg mb-6 flex items-center border-l-4 border-red-500",
                  currentIsDark ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
                )}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <AlertCircle className={`${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 flex-shrink-0 animate-pulse`} />
                <p className="text-sm font-medium">{error}</p>
              </motion.div>
            )}
          </AnimatePresence>

          <AnimatePresence>
            {success && (
              <motion.div
                className={cn(
                  "p-4 rounded-lg mb-6 flex items-center border-l-4 border-green-500",
                  currentIsDark ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
                )}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <CheckCircle className={`${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 flex-shrink-0`} />
                <p className="text-sm font-medium">{success}</p>
              </motion.div>
            )}
          </AnimatePresence>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <label className={cn(
                "block text-sm font-medium mb-2",
                currentIsDark ? "text-slate-300" : "text-slate-700"
              )}>
                {t('auth.email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-slate-400" />
                </div>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                  className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                  placeholder={t('auth.emailPlaceholder') || "أدخل بريدك الإلكتروني"}
                />
              </div>
            </div>

            <div className="relative">
              <label className={cn(
                "block text-sm font-medium mb-2",
                currentIsDark ? "text-slate-300" : "text-slate-700"
              )}>
                {t('auth.password')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-slate-400" />
                </div>
                <Input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  required
                  className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                  placeholder={t('auth.passwordPlaceholder') || "أدخل كلمة المرور"}
                />
              </div>
            </div>

            <motion.div
              className="pt-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="submit"
                className="w-full py-2.5 text-base font-medium"
                variant="primary"
                size="lg"
                disabled={isLoading || isSubmitting || !!success}
                isLoading={isLoading || isSubmitting}
              >
                {isLoading || isSubmitting
                  ? t('auth.processing')
                  : t('auth.signIn')
                }
              </Button>
            </motion.div>
          </form>
        </Card>
      </div>
    </div>
  );
}
