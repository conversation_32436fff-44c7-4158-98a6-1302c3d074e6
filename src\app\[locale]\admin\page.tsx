'use client';

import { Suspense } from 'react';
import { AdminDashboard } from '../../../components/admin/dashboard/AdminDashboard';
import { useAuthStore } from '../../../stores/authStore';
import AdminLoginPage from './AdminLoginPage';

// مكون التحميل
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>
);

export default function AdminPage() {
  const { user, isAuthenticated } = useAuthStore();

  // إضافة سجلات للتصحيح
  console.log('Admin page rendering, authenticated:', isAuthenticated, 'user role:', user?.role);

  // دائماً عرض صفحة تسجيل الدخول للإدارة إذا لم يكن المستخدم مسجل الدخول أو ليس لديه صلاحيات المسؤول
  if (!isAuthenticated || user?.role !== 'admin') {
    return <AdminLoginPage />;
  }

  // إذا كان المستخدم مسجل الدخول ولديه صلاحيات المسؤول، عرض لوحة التحكم
  return (
    <Suspense fallback={<LoadingFallback />}>
      <AdminDashboard />
    </Suspense>
  );
}
