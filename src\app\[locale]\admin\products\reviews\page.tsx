'use client';

import { Suspense } from 'react';
import { ReviewsManager } from '../../../../../components/admin/products/ReviewsManager';

// مكون التحميل
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>
);

export default function ReviewsPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <ReviewsManager />
    </Suspense>
  );
}
