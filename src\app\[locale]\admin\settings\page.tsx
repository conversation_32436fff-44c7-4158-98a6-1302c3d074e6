'use client';

import { Suspense } from 'react';
import { SettingsManager } from '../../../../components/admin/settings/SettingsManager';

// مكون التحميل
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>
);

export default function SettingsPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SettingsManager />
    </Suspense>
  );
}
