'use client';

import { Suspense } from 'react';
import { MainLayout } from '../../../components/layout/MainLayout';
import CheckoutPage from '../../../pages/shop/CheckoutPage';

// Loading fallback component
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>
);

export default function Checkout() {
  return (
    <MainLayout>
      <Suspense fallback={<LoadingFallback />}>
        <CheckoutPage />
      </Suspense>
    </MainLayout>
  );
}
