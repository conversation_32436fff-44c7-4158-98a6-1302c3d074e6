import type { Metadata, Viewport } from 'next';
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';
import { RootLayout } from '../../components/layout/RootLayout';
import { Providers } from '../providers';
import '../../index.css';

// Initialize fonts with const declarations
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const tajawal = Tajawal({
  subsets: ['arabic'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'ARTAL | Your Complete Business Solution',
  description: 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.',
  manifest: '/manifest.json',
};

export const viewport: Viewport = {
  themeColor: '#9C27B0',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const fontUtilityClass = "font-sans";

  const themeInitializerScript = `
    (function() {
      function getInitialTheme() {
        try {
          const storedTheme = localStorage.getItem('ui-theme');
          if (storedTheme) {
            return storedTheme;
          }
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          if (mediaQuery.matches) {
            return 'dark';
          }
        } catch (e) {
          // localStorage is not available or other error
        }
        return 'light';
      }

      const theme = getInitialTheme();
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    })();
  `;

  return (
    <html lang="en" dir="ltr" className={`${inter.variable} ${tajawal.variable} ${fontUtilityClass}`} suppressHydrationWarning>
      <head>
        <script dangerouslySetInnerHTML={{ __html: themeInitializerScript }} />
      </head>
      <body suppressHydrationWarning>
        <Providers locale="en">
          <RootLayout>
            {children}
          </RootLayout>
        </Providers>
      </body>
    </html>
  );
}
