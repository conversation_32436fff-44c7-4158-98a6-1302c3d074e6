import type { Metadata, Viewport } from 'next';
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';
import { RootLayout } from '../../components/layout/RootLayout';
import { Providers } from '../providers';
import '../../index.css';

// Initialize fonts with const declarations
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const tajawal = Tajawal({
  subsets: ['arabic'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'ARTAL | Your Complete Business Solution',
  description: 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.',
  manifest: '/manifest.json',
};

export const viewport: Viewport = {
  themeColor: '#9C27B0',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const fontUtilityClass = "font-sans";
  const resolvedParams = await params;
  const currentLocale = resolvedParams?.locale || 'en';

  // Simplified theme script to prevent hydration issues
  const themeInitializerScript = `
    (function() {
      try {
        const theme = localStorage.getItem('ui-theme') || 'light';
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      } catch (e) {
        // Ignore localStorage errors during SSR
      }
    })();
  `;

  return (
    <html
      lang={currentLocale}
      dir={currentLocale === 'ar' ? 'rtl' : 'ltr'}
      className={`${inter.variable} ${tajawal.variable} ${fontUtilityClass}`}
      suppressHydrationWarning
    >
      <head>
        <script
          dangerouslySetInnerHTML={{ __html: themeInitializerScript }}
          suppressHydrationWarning
        />
      </head>
      <body suppressHydrationWarning>
        <Providers locale={currentLocale}>
          <RootLayout>
            {children}
          </RootLayout>
        </Providers>
      </body>
    </html>
  );
}
