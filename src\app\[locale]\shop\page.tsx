'use client';

import { Suspense } from 'react';
import { MainLayout } from '../../../components/layout/MainLayout';
import { ShopPageEnhanced } from '../../../components/shop/ShopPageEnhanced';
import { useSearchParams } from 'next/navigation';

// Loading fallback component
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>
);

export default function Shop() {
  const searchParams = useSearchParams();
  const featuredParam = searchParams?.get('featured');
  const categoryParam = searchParams?.get('category');
  const searchQuery = searchParams?.get('q');

  return (
    <MainLayout>
      <Suspense fallback={<LoadingFallback />}>
        <ShopPageEnhanced
          initialFilters={{
            featured: featuredParam === 'true',
            category: categoryParam || 'all',
            searchQuery: searchQuery || ''
          }}
        />
      </Suspense>
    </MainLayout>
  );
}
