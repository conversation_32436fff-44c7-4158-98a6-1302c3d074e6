'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useLanguageStore } from '../../../stores/languageStore';

export default function WholesalePage() {
  const router = useRouter();
  const { language } = useLanguageStore();
  
  // تحويل المستخدم إلى الصفحة الرئيسية
  useEffect(() => {
    router.push(`/${language}`);
  }, [router, language]);
  
  // لن يتم عرض هذا المحتوى لأن المستخدم سيتم تحويله
  return null;
}
