'use client';

import { Button } from '@/components/ui/Button';
import { Wifi, WifiOff } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function Offline() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Check if we're online when the component mounts
    setIsOnline(navigator.onLine);

    // Add event listeners for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = () => {
    window.location.reload();
  };

  if (isOnline) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-slate-900">
      <div className="text-center p-6 max-w-md">
        <WifiOff className="mx-auto h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2 dark:text-white">You&apos;re offline</h1>
        <p className="text-slate-600 dark:text-slate-400 mb-6">
          Please check your internet connection and try again.
        </p>
        <Button onClick={handleRetry} className="flex items-center mx-auto">
          <Wifi className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      </div>
    </div>
  );
}
