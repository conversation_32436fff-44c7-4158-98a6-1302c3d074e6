'use client';

import { ReactNode, useEffect, useState } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '../lib/queryClient';
import { useLanguageStore } from '../stores/languageStore';
import { Locale } from '../lib/i18n';
import { ErrorBoundary } from '../components/error/ErrorBoundary';
import { initializeDefaultUsers } from '../services/AuthService';

// Dynamically import providers to prevent SSR issues
import dynamic from 'next/dynamic';

const ABTestingProvider = dynamic(
  () => import('../components/marketing/ABTestingProvider').then(mod => ({ default: mod.ABTestingProvider })),
  {
    ssr: false,
    loading: () => null
  }
);

const ThemeProvider = dynamic(
  () => import('next-themes').then(mod => ({ default: mod.ThemeProvider })),
  {
    ssr: false,
    loading: () => null
  }
);

export function Providers({
  children,
  locale
}: {
  children: ReactNode;
  locale?: string;
}) {
  const { setLanguage } = useLanguageStore();
  const [isClient, setIsClient] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Effect for client-side initialization
  useEffect(() => {
    setIsClient(true);

    // Initialize language based on locale prop
    if (locale && (locale === 'ar' || locale === 'en')) {
      setLanguage(locale as Locale);
    }

    // Initialize default users for development
    const initializeApp = async () => {
      try {
        await initializeDefaultUsers();
        setIsInitialized(true);
      } catch (error) {
        console.warn('Failed to initialize default users:', error);
        setIsInitialized(true); // Continue even if initialization fails
      }
    };

    initializeApp();
  }, [locale, setLanguage]);

  // Show loading state during SSR and initial client render
  if (!isClient || !isInitialized) {
    return (
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          <div suppressHydrationWarning>
            {children}
          </div>
        </QueryClientProvider>
      </ErrorBoundary>
    );
  }

  // Full provider tree for client-side after initialization
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          storageKey="ui-theme"
          suppressHydrationWarning
        >
          <ABTestingProvider>
            <div suppressHydrationWarning>
              {children}
            </div>
          </ABTestingProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}
