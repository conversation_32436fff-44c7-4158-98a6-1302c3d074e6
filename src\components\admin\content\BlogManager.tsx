'use client';

import { useState, useEffect, useMemo } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  Calendar,
  Tag,
  User,
  PenTool
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { blogPosts as initialBlogPostsData } from '../../../data/blogPosts';
import { BlogPost } from '../../../types/index';
import { BlogPostForm } from './BlogPostForm';
import Link from 'next/link';
import Image from 'next/image';

// مكون إدارة المدونة
export function BlogManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof BlogPost>('publishedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  // حالة المقالات
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>(initialBlogPostsData);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(initialBlogPostsData);
  const [showPostForm, setShowPostForm] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  
  // تحديث المقالات المصفاة عند تغيير البحث أو التصفية
  useEffect(() => {
    let filtered = [...blogPosts];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(post => 
        post.title.toLowerCase().includes(query) || 
        post.excerpt.toLowerCase().includes(query) ||
        post.category.toLowerCase().includes(query) ||
        post.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // تطبيق تصفية الفئة
    if (selectedCategory) {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }
    
    setFilteredPosts(filtered);
  }, [searchQuery, selectedCategory, blogPosts]);
  
  // الحصول على الفئات الفريدة
  const categories = Array.from(new Set(blogPosts.map(post => post.category)));
  
  // ترتيب المقالات
  const sortedPosts = useMemo(() => {
    if (!sortField) return filteredPosts;

    return [...filteredPosts].sort((a, b) => {
      const aValue = String(a[sortField] || '');
      const bValue = String(b[sortField] || '');

      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      }
      return bValue.localeCompare(aValue);
    });
  }, [filteredPosts, sortField, sortDirection]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedPosts.length / itemsPerPage);
  
  // الحصول على المقالات للصفحة الحالية
  const currentPosts = sortedPosts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof BlogPost) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // تحرير مقال
  const handleEditPost = (post: BlogPost) => {
    setEditingPost(post);
    setShowPostForm(true);
  };
  
  // إضافة مقال جديد
  const handleAddPost = () => {
    setEditingPost(null);
    setShowPostForm(true);
  };
  
  // حذف مقال
  const handleDeletePost = (postId: string) => {
    // هنا سيتم تنفيذ منطق حذف المقال
    console.log('Delete post:', postId);
    
    // في الإنتاج، سيتم استدعاء API لحذف المقال
    // وتحديث قائمة المقالات
  };
  
  // عرض تفاصيل المقال
  const handleViewPost = (slug: string) => {
    // هنا سيتم تنفيذ منطق عرض تفاصيل المقال
    console.log('View post:', slug);
    
    // في الإنتاج، سيتم الانتقال إلى صفحة تفاصيل المقال
    window.open(`/${language}/blog/${slug}`, '_blank');
  };
  
  // حفظ المقال (إضافة أو تحديث)
  const handleSavePost = (post: BlogPost) => {
    if (editingPost) {
      // تحديث مقال موجود
      console.log('Update post:', post);
      
      // في الإنتاج، سيتم استدعاء API لتحديث المقال
      // وتحديث قائمة المقالات
    } else {
      // إضافة مقال جديد
      console.log('Add post:', post);
      
      // في الإنتاج، سيتم استدعاء API لإضافة المقال
      // وتحديث قائمة المقالات
    }
    
    setShowPostForm(false);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة المدونة' : 'Blog Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة مقالات المدونة والفئات والوسوم'
              : 'Manage blog posts, categories, and tags'}
          </p>
        </div>
        
        <Button
          onClick={handleAddPost}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة مقال' : 'Add Post'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن المقالات...' : 'Search posts...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedCategory || ''}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الفئات' : 'All Categories'}
              </option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>
      
      {/* جدول المقالات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('title')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'العنوان' : 'Title'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('category')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الفئة' : 'Category'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('author')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الكاتب' : 'Author'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('publishedAt')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'تاريخ النشر' : 'Published'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentPosts.map((post) => (
                <tr key={post.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <div className="relative w-10 h-10 rounded-md overflow-hidden flex-shrink-0"> 
                        <Image 
                          src={post.coverImage} 
                          alt={post.title}
                          fill 
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                          className="object-cover" 
                        />
                      </div>
                      <div>
                        <p className="font-medium line-clamp-1">{post.title}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400 line-clamp-1">
                          {post.slug}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                    )}>
                      {post.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-slate-400" />
                      <span>{post.author}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-slate-400" />
                      <span>
                        {new Date(post.publishedAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewPost(post.slug)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditPost(post)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeletePost(post.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredPosts.length)} من ${filteredPosts.length} مقال`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredPosts.length)} of ${filteredPosts.length} posts`
              }
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
      
      {/* نموذج إضافة/تحرير المقال */}
      {showPostForm && (
        <BlogPostForm
          post={editingPost}
          onSave={handleSavePost}
          onCancel={() => setShowPostForm(false)}
        />
      )}
    </div>
  );
}
