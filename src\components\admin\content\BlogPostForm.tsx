'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Trash2, Upload, Tag } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { BlogPost } from '../../../types/index';
import { cn } from '../../../lib/utils';
import Image from 'next/image';

interface BlogPostFormProps {
  post: BlogPost | null;
  onSave: (post: BlogPost) => void;
  onCancel: () => void;
}

export function BlogPostForm({ post, onSave, onCancel }: BlogPostFormProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<BlogPost>>({
    id: '',
    title: '',
    title_ar: '',
    slug: '',
    excerpt: '',
    excerpt_ar: '',
    content: '',
    content_ar: '',
    coverImage: '',
    author: '',
    authorTitle: '',
    authorImage: '',
    readTime: '',
    category: '',
    tags: [],
    publishedAt: new Date().toISOString(),
    featured: false,
  });
  
  // حالة الوسوم
  const [tagInput, setTagInput] = useState('');
  
  // حالة رابط الصورة الجديدة
  const [newImageUrl, setNewImageUrl] = useState<string>('');

  // تحميل بيانات المقال إذا كان موجودًا
  useEffect(() => {
    if (post) {
      setFormData({
        ...post,
      });
      setNewImageUrl(post.coverImage || '');
    } else {
      setFormData({
        id: '',
        title: '',
        title_ar: '',
        slug: '',
        excerpt: '',
        excerpt_ar: '',
        content: '',
        content_ar: '',
        coverImage: '',
        author: '',
        authorTitle: '',
        authorImage: '',
        readTime: '',
        category: '',
        tags: [],
        publishedAt: new Date().toISOString(),
        featured: false,
      });
      setNewImageUrl('');
    }
  }, [post]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // تحديث حقل الميزة
  const handleFeaturedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      featured: e.target.checked,
    }));
  };
  
  // إنشاء الرابط الدائم من العنوان
  const generateSlug = () => {
    if (formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');
      
      setFormData(prev => ({
        ...prev,
        slug,
      }));
    }
  };
  
  // إضافة وسم
  const handleAddTag = () => {
    if (tagInput && !formData.tags?.includes(tagInput)) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput],
      }));
      setTagInput('');
    }
  };
  
  // حذف وسم
  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag) || [],
    }));
  };
  
  // تحديث صورة الغلاف
  const handleCoverImageChange = (url: string) => {
    setFormData(prev => ({
      ...prev,
      coverImage: url,
    }));
    setNewImageUrl(url);
  };
  
  // حفظ المقال
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.title || !formData.content || !formData.excerpt || !formData.category) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كان مقال جديد
    const postData: BlogPost = {
      id: post?.id || `post-${Date.now()}`,
      title: formData.title || '',
      title_ar: formData.title_ar,
      slug: formData.slug || '',
      excerpt: formData.excerpt || '',
      excerpt_ar: formData.excerpt_ar,
      content: formData.content || '',
      content_ar: formData.content_ar,
      coverImage: formData.coverImage || '',
      author: formData.author || '',
      authorTitle: formData.authorTitle || '',
      authorImage: formData.authorImage || '',
      readTime: formData.readTime || '',
      category: formData.category || '',
      tags: formData.tags || [],
      publishedAt: formData.publishedAt || new Date().toISOString(),
      featured: formData.featured || false,
    };
    
    onSave(postData);
  };
  
  // الفئات المتاحة
  const categories = [
    'Industry News',
    'Manufacturing',
    'Technology',
    'Supply Chain',
    'Quality Control',
    'Business',
  ];
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {post
              ? language === 'ar' ? 'تحرير المقال' : 'Edit Post'
              : language === 'ar' ? 'إضافة مقال جديد' : 'Add New Post'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* صورة الغلاف */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">
              {language === 'ar' ? 'صورة الغلاف' : 'Cover Image URL'}
              <span className="text-red-500">*</span>
            </label>
            <div className="flex items-center gap-2">
              <Input
                type="text"
                name="coverImage"
                placeholder={language === 'ar' ? 'أدخل رابط صورة الغلاف' : 'Enter cover image URL'}
                value={newImageUrl}
                onChange={(e) => setNewImageUrl(e.target.value)}
                className={cn(
                  "flex-grow",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              />
              <Button 
                type="button" 
                variant="outline"
                onClick={() => handleCoverImageChange(newImageUrl)}
              >
                {language === 'ar' ? 'تطبيق' : 'Apply'}
              </Button>
            </div>
            {(formData.coverImage || newImageUrl) && (
              <div className="mt-2 relative w-full max-h-40 h-40"> 
                <Image 
                  src={formData.coverImage || newImageUrl} 
                  alt={language === 'ar' ? 'معاينة صورة الغلاف' : 'Cover Image Preview'}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                  className="rounded-md object-contain" 
                />
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* العنوان بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'العنوان (بالإنجليزية)' : 'Title (English)'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="title"
                value={formData.title || ''}
                onChange={handleChange}
                onBlur={generateSlug}
                required
              />
            </div>
            
            {/* العنوان بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'العنوان (بالعربية)' : 'Title (Arabic)'}
              </label>
              <Input
                name="title_ar"
                value={formData.title_ar || ''}
                onChange={handleChange}
                dir="rtl"
              />
            </div>
            
            {/* الرابط الدائم */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الرابط الدائم' : 'Slug'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="slug"
                value={formData.slug || ''}
                onChange={handleChange}
                required
              />
            </div>
            
            {/* الفئة */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الفئة' : 'Category'}
                <span className="text-red-500">*</span>
              </label>
              <select
                name="category"
                value={formData.category || ''}
                onChange={handleChange}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
                required
              >
                <option value="">
                  {language === 'ar' ? 'اختر فئة' : 'Select Category'}
                </option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            
            {/* الكاتب */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الكاتب' : 'Author'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="author"
                value={formData.author || ''}
                onChange={handleChange}
                required
              />
            </div>
            
            {/* تاريخ النشر */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'تاريخ النشر' : 'Publish Date'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                type="date"
                name="publishedAt"
                value={formData.publishedAt ? new Date(formData.publishedAt).toISOString().split('T')[0] : ''}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          {/* الوسوم */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'الوسوم' : 'Tags'}
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags?.map((tag) => (
                <div
                  key={tag}
                  className={cn(
                    "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
                    isDarkMode
                      ? "bg-slate-700 text-white"
                      : "bg-gray-100 text-slate-800"
                  )}
                >
                  <span>{tag}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder={language === 'ar' ? 'أضف وسمًا' : 'Add a tag'}
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button
                type="button"
                onClick={handleAddTag}
                disabled={!tagInput}
              >
                <Plus className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          {/* المقتطف بالإنجليزية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المقتطف (بالإنجليزية)' : 'Excerpt (English)'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="excerpt"
              value={formData.excerpt || ''}
              onChange={handleChange}
              rows={3}
              className={cn(
                "w-full px-3 py-2 rounded-md border resize-none",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
              required
            />
          </div>
          
          {/* المقتطف بالعربية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المقتطف (بالعربية)' : 'Excerpt (Arabic)'}
            </label>
            <textarea
              name="excerpt_ar"
              value={formData.excerpt_ar || ''}
              onChange={handleChange}
              rows={3}
              dir="rtl"
              className={cn(
                "w-full px-3 py-2 rounded-md border resize-none",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            />
          </div>
          
          {/* المحتوى بالإنجليزية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المحتوى (بالإنجليزية)' : 'Content (English)'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="content"
              value={formData.content || ''}
              onChange={handleChange}
              rows={10}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
              required
            />
          </div>
          
          {/* المحتوى بالعربية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المحتوى (بالعربية)' : 'Content (Arabic)'}
            </label>
            <textarea
              name="content_ar"
              value={formData.content_ar || ''}
              onChange={handleChange}
              rows={10}
              dir="rtl"
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            />
          </div>
          
          {/* مميز */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="featured"
              checked={formData.featured || false}
              onChange={handleFeaturedChange}
              className="h-4 w-4"
            />
            <label htmlFor="featured" className="text-sm font-medium">
              {language === 'ar' ? 'مميز' : 'Featured'}
            </label>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button type="submit">
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
