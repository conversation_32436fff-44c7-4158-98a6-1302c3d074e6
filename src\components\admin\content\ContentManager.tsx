'use client';

import { useState } from 'react';
import {
  FileText,
  Image,
  PenTool
} from 'lucide-react';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { BlogManager } from './BlogManager';
import { PagesManager } from './PagesManager';
import { MediaManager } from './MediaManager';

// مكون إدارة المحتوى
export function ContentManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';

  // حالة التبويب النشط
  const [activeTab, setActiveTab] = useState<'blog' | 'pages' | 'media'>('blog');

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة المحتوى' : 'Content Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar'
              ? 'إدارة المدونة والصفحات والوسائط'
              : 'Manage blog posts, pages, and media'}
          </p>
        </div>
      </div>

      {/* علامات التبويب */}
      <div className={cn(
        "flex border-b",
        isDarkMode ? "border-slate-700" : "border-gray-200"
      )}>
        <button
          onClick={() => setActiveTab('blog')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'blog'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <PenTool className="h-5 w-5" aria-hidden="true" />
          <span>{language === 'ar' ? 'المدونة' : 'Blog'}</span>
        </button>

        <button
          onClick={() => setActiveTab('pages')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'pages'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <FileText className="h-5 w-5" aria-hidden="true" />
          <span>{language === 'ar' ? 'الصفحات' : 'Pages'}</span>
        </button>

        <button
          onClick={() => setActiveTab('media')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'media'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          {/* eslint-disable-next-line jsx-a11y/alt-text */}
          <Image className="h-5 w-5" aria-hidden="true" />
          <span>{language === 'ar' ? 'الوسائط' : 'Media'}</span>
        </button>
      </div>

      {/* محتوى التبويب النشط */}
      {activeTab === 'blog' && <BlogManager />}
      {activeTab === 'pages' && <PagesManager />}
      {activeTab === 'media' && <MediaManager />}
    </div>
  );
}
