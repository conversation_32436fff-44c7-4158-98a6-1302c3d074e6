'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Image as LucideImage, // Renamed to avoid conflict with next/image
  Upload,
  Download,
  Copy,
  Check,
  X,
  Grid,
  List
} from 'lucide-react';
import Image from 'next/image'; // Import next/image
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';

// نوع الوسائط
interface MediaItem {
  id: string;
  name: string;
  url: string;
  type: 'image' | 'video' | 'document';
  size: number;
  dimensions?: string;
  uploadedAt: string;
}

// بيانات الوسائط (محاكاة)
const mediaItems: MediaItem[] = Array.from({ length: 24 }, (_, index) => ({
  id: `media-${index + 1}`,
  name: `image-${index + 1}.jpg`,
  url: `https://picsum.photos/seed/${index + 1}/800/600`,
  type: 'image',
  size: Math.floor(Math.random() * 5000000) + 100000, // 100KB to 5MB
  dimensions: '800x600',
  uploadedAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
}));

// مكون إدارة الوسائط
export function MediaManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  // حالة الوسائط
  const [filteredMedia, setFilteredMedia] = useState<MediaItem[]>([]);
  const [selectedMedia, setSelectedMedia] = useState<string[]>([]);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  
  // تحديث الوسائط المصفاة عند تغيير البحث أو التصفية
  useEffect(() => {
    let filtered = [...mediaItems];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query)
      );
    }
    
    // تطبيق تصفية النوع
    if (selectedType) {
      filtered = filtered.filter(item => item.type === selectedType);
    }
    
    setFilteredMedia(filtered);
  }, [searchQuery, selectedType]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredMedia.length / itemsPerPage);
  
  // الحصول على الوسائط للصفحة الحالية
  const currentMedia = filteredMedia.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تحديد/إلغاء تحديد عنصر وسائط
  const toggleSelectMedia = (id: string) => {
    if (selectedMedia.includes(id)) {
      setSelectedMedia(prev => prev.filter(itemId => itemId !== id));
    } else {
      setSelectedMedia(prev => [...prev, id]);
    }
  };
  
  // تحديد الكل
  const selectAll = () => {
    setSelectedMedia(currentMedia.map(item => item.id));
  };
  
  // إلغاء تحديد الكل
  const deselectAll = () => {
    setSelectedMedia([]);
  };
  
  // حذف الوسائط المحددة
  const deleteSelected = () => {
    if (selectedMedia.length === 0) return;
    
    // هنا سيتم تنفيذ منطق حذف الوسائط
    console.log('Delete media:', selectedMedia);
    
    // في الإنتاج، سيتم استدعاء API لحذف الوسائط
    // وتحديث قائمة الوسائط
    
    setSelectedMedia([]);
  };
  
  // نسخ رابط الوسائط
  const copyMediaUrl = (url: string) => {
    navigator.clipboard.writeText(url).then(() => {
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    });
  };
  
  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة الوسائط' : 'Media Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة الصور والفيديوهات والملفات'
              : 'Manage images, videos, and files'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => setShowUploadForm(true)}
            className="flex items-center gap-2"
          >
            <Upload className="h-5 w-5" />
            <span>{language === 'ar' ? 'رفع وسائط' : 'Upload Media'}</span>
          </Button>
          
          {selectedMedia.length > 0 && (
            <Button
              variant="destructive"
              onClick={deleteSelected}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-5 w-5" />
              <span>
                {language === 'ar'
                  ? `حذف (${selectedMedia.length})`
                  : `Delete (${selectedMedia.length})`
                }
              </span>
            </Button>
          )}
        </div>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن الوسائط...' : 'Search media...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedType || ''}
              onChange={(e) => setSelectedType(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الأنواع' : 'All Types'}
              </option>
              <option value="image">
                {language === 'ar' ? 'صور' : 'Images'}
              </option>
              <option value="video">
                {language === 'ar' ? 'فيديوهات' : 'Videos'}
              </option>
              <option value="document">
                {language === 'ar' ? 'مستندات' : 'Documents'}
              </option>
            </select>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'outline'}
              size="icon"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-5 w-5" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'primary' : 'outline'}
              size="icon"
              onClick={() => setViewMode('list')}
            >
              <List className="h-5 w-5" />
            </Button>
          </div>
        </div>
        
        {/* أدوات التحديد */}
        {currentMedia.length > 0 && (
          <div className="flex items-center gap-4 mt-4 pt-4 border-t dark:border-slate-700">
            <div className="text-sm">
              {selectedMedia.length > 0
                ? language === 'ar'
                  ? `تم تحديد ${selectedMedia.length} عنصر`
                  : `${selectedMedia.length} items selected`
                : language === 'ar'
                  ? 'لم يتم تحديد أي عنصر'
                  : 'No items selected'
              }
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                disabled={currentMedia.length === selectedMedia.length}
              >
                {language === 'ar' ? 'تحديد الكل' : 'Select All'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={deselectAll}
                disabled={selectedMedia.length === 0}
              >
                {language === 'ar' ? 'إلغاء التحديد' : 'Deselect All'}
              </Button>
            </div>
          </div>
        )}
      </Card>
      
      {/* عرض الوسائط */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {currentMedia.map((item) => (
            <div
              key={item.id}
              className={cn(
                "relative group rounded-lg overflow-hidden aspect-square",
                isDarkMode ? "bg-slate-700" : "bg-gray-100",
                selectedMedia.includes(item.id) && "ring-2 ring-primary-500"
              )}
            >
              <Image // Replaced img with next/image
                src={item.url}
                alt={item.name}
                fill // Added fill prop
                className="object-cover" // Kept object-cover, fill handles size
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Added sizes prop for optimization
              />
              
              {/* الإجراءات */}
              <div className={cn(
                "absolute inset-0 flex flex-col justify-between p-2 transition-opacity",
                "bg-gradient-to-b from-black/60 via-transparent to-black/60",
                selectedMedia.includes(item.id) ? "opacity-100" : "opacity-0 group-hover:opacity-100"
              )}>
                <div className="flex justify-between">
                  <button
                    onClick={() => toggleSelectMedia(item.id)}
                    className={cn(
                      "p-1 rounded-full",
                      selectedMedia.includes(item.id)
                        ? "bg-primary-500 text-white"
                        : "bg-white/80 text-slate-900"
                    )}
                  >
                    {selectedMedia.includes(item.id) ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                  </button>
                  
                  <div className="flex gap-1">
                    <button
                      onClick={() => copyMediaUrl(item.url)}
                      className="p-1 rounded-full bg-white/80 text-slate-900 hover:bg-white"
                    >
                      {copiedUrl === item.url ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                    <a
                      href={item.url}
                      download={item.name}
                      className="p-1 rounded-full bg-white/80 text-slate-900 hover:bg-white"
                    >
                      <Download className="h-4 w-4" />
                    </a>
                    <button
                      onClick={() => toggleSelectMedia(item.id)}
                      className="p-1 rounded-full bg-red-500/80 text-white hover:bg-red-500"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="text-white text-xs">
                  <div className="truncate">{item.name}</div>
                  <div className="flex justify-between mt-1">
                    <span>{item.dimensions}</span>
                    <span>{formatFileSize(item.size)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Card className={cn(
          "overflow-hidden",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={cn(
                "text-xs uppercase",
                isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
              )}>
                <tr>
                  <th className="px-6 py-3 text-left w-10">
                    <input
                      type="checkbox"
                      checked={currentMedia.length > 0 && selectedMedia.length === currentMedia.length}
                      onChange={() => {
                        if (selectedMedia.length === currentMedia.length) {
                          deselectAll();
                        } else {
                          selectAll();
                        }
                      }}
                      className="h-4 w-4"
                    />
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'الملف' : 'File'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'الأبعاد' : 'Dimensions'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'الحجم' : 'Size'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'تاريخ الرفع' : 'Uploaded'}
                  </th>
                  <th className="px-6 py-3 text-right">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y dark:divide-slate-700">
                {currentMedia.map((item) => (
                  <tr
                    key={item.id}
                    className={cn(
                      "hover:bg-gray-50 dark:hover:bg-slate-700/50",
                      selectedMedia.includes(item.id) && (
                        isDarkMode ? "bg-slate-700/50" : "bg-blue-50"
                      )
                    )}
                  >
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedMedia.includes(item.id)}
                        onChange={() => toggleSelectMedia(item.id)}
                        className="h-4 w-4"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0">
                          <Image // Replaced img with next/image
                            src={item.url}
                            alt={item.name}
                            fill // Added fill prop
                            className="object-cover" // Kept object-cover, fill handles size
                            sizes="10vw" // Added sizes prop for optimization
                          />
                        </div>
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            {item.type}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {item.dimensions || '-'}
                    </td>
                    <td className="px-6 py-4">
                      {formatFileSize(item.size)}
                    </td>
                    <td className="px-6 py-4">
                      {new Date(item.uploadedAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => copyMediaUrl(item.url)}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          {copiedUrl === item.url ? (
                            <Check className="h-5 w-5 text-green-500" />
                          ) : (
                            <Copy className="h-5 w-5 text-blue-500" />
                          )}
                        </button>
                        <a
                          href={item.url}
                          download={item.name}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Download className="h-5 w-5 text-green-500" />
                        </a>
                        <button
                          onClick={() => {
                            setSelectedMedia([item.id]);
                            deleteSelected();
                          }}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Trash2 className="h-5 w-5 text-red-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}
      
      {/* ترقيم الصفحات */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {language === 'ar'
              ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredMedia.length)} من ${filteredMedia.length} عنصر`
              : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredMedia.length)} of ${filteredMedia.length} items`
            }
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      
      {/* نموذج رفع الوسائط */}
      {showUploadForm && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className={cn(
            "w-full max-w-lg",
            isDarkMode ? "bg-slate-800" : "bg-white"
          )}>
            <div className={cn(
              "px-6 py-4 flex items-center justify-between border-b",
              isDarkMode ? "border-slate-700" : "border-gray-200"
            )}>
              <h2 className="text-xl font-bold">
                {language === 'ar' ? 'رفع وسائط' : 'Upload Media'}
              </h2>
              <button
                onClick={() => setShowUploadForm(false)}
                className={cn(
                  "p-2 rounded-full",
                  isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                )}
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6">
              <div className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center",
                isDarkMode ? "border-slate-700" : "border-gray-300"
              )}>
                <Upload className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <p className="mb-2 font-medium">
                  {language === 'ar' ? 'اسحب وأفلت الملفات هنا' : 'Drag and drop files here'}
                </p>
                <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
                  {language === 'ar'
                    ? 'أو انقر لتحديد الملفات من جهازك'
                    : 'Or click to select files from your device'
                  }
                </p>
                <Button variant="primary">
                  {language === 'ar' ? 'تحديد الملفات' : 'Select Files'}
                </Button>
              </div>
              
              <div className="flex justify-end gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowUploadForm(false)}
                >
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button variant="primary">
                  {language === 'ar' ? 'رفع' : 'Upload'}
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
