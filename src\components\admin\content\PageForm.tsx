'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// نوع الصفحة
interface Page {
  id: string;
  title: string;
  title_ar?: string;
  slug: string;
  content: string;
  content_ar?: string;
  lastUpdated: string;
  isPublished: boolean;
}

interface PageFormProps {
  page: Page | null;
  onSave: (page: Page) => void;
  onCancel: () => void;
}

export function PageForm({ page, onSave, onCancel }: PageFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<Page>>({
    id: '',
    title: '',
    title_ar: '',
    slug: '',
    content: '',
    content_ar: '',
    lastUpdated: new Date().toISOString(),
    isPublished: true,
  });
  
  // تحميل بيانات الصفحة إذا كانت موجودة
  useEffect(() => {
    if (page) {
      setFormData({
        ...page,
      });
    }
  }, [page]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // تحديث حقل النشر
  const handlePublishedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      isPublished: e.target.checked,
    }));
  };
  
  // إنشاء الرابط من العنوان
  const generateSlug = () => {
    if (formData.title && !page) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');
      
      setFormData(prev => ({
        ...prev,
        slug: `/${slug}`,
      }));
    }
  };
  
  // حفظ الصفحة
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.title || !formData.content || !formData.slug) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كانت صفحة جديدة
    const pageData: Page = {
      ...(formData as Page),
      id: page?.id || `page-${Date.now()}`,
      lastUpdated: new Date().toISOString(),
    };
    
    onSave(pageData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {page
              ? language === 'ar' ? 'تحرير الصفحة' : 'Edit Page'
              : language === 'ar' ? 'إضافة صفحة جديدة' : 'Add New Page'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* العنوان بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'العنوان (بالإنجليزية)' : 'Title (English)'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="title"
                value={formData.title || ''}
                onChange={handleChange}
                onBlur={generateSlug}
                required
              />
            </div>
            
            {/* العنوان بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'العنوان (بالعربية)' : 'Title (Arabic)'}
              </label>
              <Input
                name="title_ar"
                value={formData.title_ar || ''}
                onChange={handleChange}
                dir="rtl"
              />
            </div>
            
            {/* الرابط */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الرابط' : 'URL'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="slug"
                value={formData.slug || ''}
                onChange={handleChange}
                required
                disabled={page?.slug === '/'}
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'يجب أن يبدأ بـ / مثل: /about'
                  : 'Must start with / e.g. /about'
                }
              </p>
            </div>
            
            {/* حالة النشر */}
            <div className="flex items-center h-full">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={formData.isPublished || false}
                  onChange={handlePublishedChange}
                  className="h-4 w-4"
                />
                <label htmlFor="isPublished" className="text-sm font-medium">
                  {language === 'ar' ? 'منشورة' : 'Published'}
                </label>
              </div>
            </div>
          </div>
          
          {/* المحتوى بالإنجليزية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المحتوى (بالإنجليزية)' : 'Content (English)'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="content"
              value={formData.content || ''}
              onChange={handleChange}
              rows={15}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
              required
            />
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              {language === 'ar'
                ? 'يمكنك استخدام Markdown لتنسيق المحتوى'
                : 'You can use Markdown for formatting'
              }
            </p>
          </div>
          
          {/* المحتوى بالعربية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المحتوى (بالعربية)' : 'Content (Arabic)'}
            </label>
            <textarea
              name="content_ar"
              value={formData.content_ar || ''}
              onChange={handleChange}
              rows={15}
              dir="rtl"
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            />
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              {language === 'ar'
                ? 'يمكنك استخدام Markdown لتنسيق المحتوى'
                : 'You can use Markdown for formatting'
              }
            </p>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button type="submit">
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
