'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  FileText,
  Calendar,
  Link as LinkIcon
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { PageForm } from './PageForm';
import Link from 'next/link';

// نوع الصفحة
interface Page {
  id: string;
  title: string;
  title_ar?: string;
  slug: string;
  content: string;
  content_ar?: string;
  lastUpdated: string;
  isPublished: boolean;
}

// بيانات الصفحات (محاكاة)
const pages: Page[] = [
  {
    id: 'home',
    title: 'Home Page',
    title_ar: 'الصفحة الرئيسية',
    slug: '/',
    content: 'Home page content...',
    content_ar: 'محتوى الصفحة الرئيسية...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'about',
    title: 'About Us',
    title_ar: 'من نحن',
    slug: '/about',
    content: 'About us page content...',
    content_ar: 'محتوى صفحة من نحن...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'contact',
    title: 'Contact Us',
    title_ar: 'اتصل بنا',
    slug: '/contact',
    content: 'Contact us page content...',
    content_ar: 'محتوى صفحة اتصل بنا...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'faq',
    title: 'FAQ',
    title_ar: 'الأسئلة الشائعة',
    slug: '/faq',
    content: 'FAQ page content...',
    content_ar: 'محتوى صفحة الأسئلة الشائعة...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'privacy',
    title: 'Privacy Policy',
    title_ar: 'سياسة الخصوصية',
    slug: '/privacy-policy',
    content: 'Privacy policy page content...',
    content_ar: 'محتوى صفحة سياسة الخصوصية...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'terms',
    title: 'Terms of Service',
    title_ar: 'شروط الخدمة',
    slug: '/terms-of-service',
    content: 'Terms of service page content...',
    content_ar: 'محتوى صفحة شروط الخدمة...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'shipping',
    title: 'Shipping Policy',
    title_ar: 'سياسة الشحن',
    slug: '/shipping',
    content: 'Shipping policy page content...',
    content_ar: 'محتوى صفحة سياسة الشحن...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  },
  {
    id: 'returns',
    title: 'Returns Policy',
    title_ar: 'سياسة الإرجاع',
    slug: '/returns',
    content: 'Returns policy page content...',
    content_ar: 'محتوى صفحة سياسة الإرجاع...',
    lastUpdated: new Date().toISOString(),
    isPublished: true
  }
];

// مكون إدارة الصفحات
export function PagesManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof Page>('title');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // حالة الصفحات
  const [filteredPages, setFilteredPages] = useState<Page[]>([]);
  const [showPageForm, setShowPageForm] = useState(false);
  const [editingPage, setEditingPage] = useState<Page | null>(null);
  
  // تحديث الصفحات المصفاة عند تغيير البحث
  useEffect(() => {
    let filtered = [...pages];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(page => 
        page.title.toLowerCase().includes(query) || 
        page.slug.toLowerCase().includes(query)
      );
    }
    
    setFilteredPages(filtered);
  }, [searchQuery]);
  
  // ترتيب الصفحات
  const sortedPages = [...filteredPages].sort((a, b) => {
    let valA = a[sortField];
    let valB = b[sortField];

    // Handle undefined values by providing defaults
    if (typeof valA === 'undefined') {
      valA = sortField === 'isPublished' ? false : ""; 
    }
    if (typeof valB === 'undefined') {
      valB = sortField === 'isPublished' ? false : "";
    }

    if (typeof valA === 'string' && typeof valB === 'string') {
      return sortDirection === 'asc'
        ? valA.localeCompare(valB)
        : valB.localeCompare(valA);
    }

    if (typeof valA === 'boolean' && typeof valB === 'boolean') {
      return sortDirection === 'asc'
        ? (valA === valB ? 0 : valA ? 1 : -1)
        : (valA === valB ? 0 : valB ? 1 : -1);
    }
    
    // Fallback for other types or mixed types (should ideally not happen with Page interface)
    // Or if comparing numbers (not present in Page interface currently)
    if (valA === valB) return 0;
    return sortDirection === 'asc'
      ? (valA > valB ? 1 : -1)
      : (valA < valB ? 1 : -1);
  });
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedPages.length / itemsPerPage);
  
  // الحصول على الصفحات للصفحة الحالية
  const currentPages = sortedPages.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Page) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // تحرير صفحة
  const handleEditPage = (page: Page) => {
    setEditingPage(page);
    setShowPageForm(true);
  };
  
  // إضافة صفحة جديدة
  const handleAddPage = () => {
    setEditingPage(null);
    setShowPageForm(true);
  };
  
  // حذف صفحة
  const handleDeletePage = (pageId: string) => {
    // هنا سيتم تنفيذ منطق حذف الصفحة
    console.log('Delete page:', pageId);
    
    // في الإنتاج، سيتم استدعاء API لحذف الصفحة
    // وتحديث قائمة الصفحات
  };
  
  // عرض الصفحة
  const handleViewPage = (slug: string) => {
    // هنا سيتم تنفيذ منطق عرض الصفحة
    console.log('View page:', slug);
    
    // في الإنتاج، سيتم الانتقال إلى الصفحة
    window.open(`/${language}${slug === '/' ? '' : slug}`, '_blank');
  };
  
  // حفظ الصفحة (إضافة أو تحديث)
  const handleSavePage = (page: Page) => {
    if (editingPage) {
      // تحديث صفحة موجودة
      console.log('Update page:', page);
      
      // في الإنتاج، سيتم استدعاء API لتحديث الصفحة
      // وتحديث قائمة الصفحات
    } else {
      // إضافة صفحة جديدة
      console.log('Add page:', page);
      
      // في الإنتاج، سيتم استدعاء API لإضافة الصفحة
      // وتحديث قائمة الصفحات
    }
    
    setShowPageForm(false);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة الصفحات' : 'Pages Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة الصفحات الثابتة للموقع'
              : 'Manage static pages of the website'}
          </p>
        </div>
        
        <Button
          onClick={handleAddPage}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة صفحة' : 'Add Page'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن الصفحات...' : 'Search pages...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </Card>
      
      {/* جدول الصفحات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('title')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'العنوان' : 'Title'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('slug')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الرابط' : 'URL'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('lastUpdated')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'آخر تحديث' : 'Last Updated'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentPages.map((page) => (
                <tr key={page.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-slate-400" />
                      <div>
                        <p className="font-medium">{page.title}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          {page.title_ar}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <LinkIcon className="h-4 w-4 text-slate-400" />
                      <span>{page.slug}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-slate-400" />
                      <span>
                        {new Date(page.lastUpdated).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      page.isPublished
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                    )}>
                      {page.isPublished
                        ? language === 'ar' ? 'منشورة' : 'Published'
                        : language === 'ar' ? 'مسودة' : 'Draft'
                      }
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewPage(page.slug)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditPage(page)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeletePage(page.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                        disabled={page.slug === '/'}
                      >
                        <Trash2 className={cn(
                          "h-5 w-5",
                          page.slug === '/' ? "text-gray-400" : "text-red-500"
                        )} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredPages.length)} من ${filteredPages.length} صفحة`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredPages.length)} of ${filteredPages.length} pages`
              }
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
      
      {/* نموذج إضافة/تحرير الصفحة */}
      {showPageForm && (
        <PageForm
          page={editingPage}
          onSave={handleSavePage}
          onCancel={() => setShowPageForm(false)}
        />
      )}
    </div>
  );
}
