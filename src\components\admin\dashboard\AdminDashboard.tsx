'use client';

import {
  ShoppingCart,
  Users,
  Package,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Eye,
  Clock,
  AlertCircle,
  Briefcase,
  Factory,
  Handshake,
  LineChart,
  <PERSON><PERSON>hart,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';

// مكون البطاقة الإحصائية
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: {
    value: number;
    isPositive: boolean;
  };
  bgColor: string;
}

function StatCard({ title, value, icon, change, bgColor }: StatCardProps) {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';

  return (
    <div className={cn(
      "rounded-lg p-6 shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-slate-600 dark:text-slate-300">{title}</h3>
        <div className={cn("p-3 rounded-full", bgColor)}>
          {icon}
        </div>
      </div>
      <div className="flex items-end justify-between">
        <div>
          <p className="text-2xl font-bold">{value}</p>
          {change && (
            <div className="flex items-center mt-2">
              {change.isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={change.isPositive ? "text-green-500" : "text-red-500"}>
                {change.value}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// مكون الطلبات الأخيرة
function RecentOrders() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // بيانات تجريبية للطلبات الأخيرة
  const recentOrders = [
    { id: 'ORD-1234', customer: 'أحمد محمد', date: '2023-05-01', status: 'completed', total: 1250 },
    { id: 'ORD-1235', customer: 'سارة أحمد', date: '2023-05-02', status: 'processing', total: 850 },
    { id: 'ORD-1236', customer: 'محمد علي', date: '2023-05-03', status: 'pending', total: 2100 },
    { id: 'ORD-1237', customer: 'فاطمة حسن', date: '2023-05-04', status: 'shipped', total: 1500 },
    { id: 'ORD-1238', customer: 'خالد عبدالله', date: '2023-05-05', status: 'cancelled', total: 750 },
  ];

  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return language === 'ar' ? 'مكتمل' : 'Completed';
      case 'processing':
        return language === 'ar' ? 'قيد المعالجة' : 'Processing';
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'shipped':
        return language === 'ar' ? 'تم الشحن' : 'Shipped';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };

  // لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className={cn(
      "rounded-lg shadow-sm overflow-hidden",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'الطلبات الأخيرة' : 'Recent Orders'}
        </h3>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className={cn(
            "text-xs uppercase",
            currentIsDark ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
          )}>
            <tr>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'رقم الطلب' : 'Order ID'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'العميل' : 'Customer'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'التاريخ' : 'Date'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'الحالة' : 'Status'}
              </th>
              <th className="px-6 py-3 text-right">
                {language === 'ar' ? 'المجموع' : 'Total'}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y dark:divide-slate-700">
            {recentOrders.map((order) => (
              <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {order.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {order.customer}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {new Date(order.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={cn(
                    "px-2 py-1 rounded-full text-xs",
                    getStatusColor(order.status)
                  )}>
                    {getStatusText(order.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  {order.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                    style: 'currency',
                    currency: 'SAR'
                  })}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// مكون المنتجات الأكثر مبيعًا
function TopProducts() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // بيانات تجريبية للمنتجات الأكثر مبيعًا
  const topProducts = [
    { id: 1, name: 'Smart Factory IoT Sensor Kit', sales: 120, revenue: 359998.8 },
    { id: 2, name: 'Industrial Automation Controller', sales: 95, revenue: 237500 },
    { id: 3, name: 'Commercial Grade 3D Printer', sales: 82, revenue: 328000 },
    { id: 4, name: 'Warehouse Management System', sales: 78, revenue: 195000 },
    { id: 5, name: 'Supply Chain Optimization Software', sales: 65, revenue: 162500 },
  ];

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'المنتجات الأكثر مبيعًا' : 'Top Selling Products'}
        </h3>
      </div>
      <div className="p-6">
        <ul className="space-y-4">
          {topProducts.map((product) => (
            <li key={product.id} className="flex items-center justify-between">
              <div>
                <p className="font-medium">{product.name}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  {language === 'ar' ? `المبيعات: ${product.sales}` : `Sales: ${product.sales}`}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium">
                  {product.revenue.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                    style: 'currency',
                    currency: 'SAR'
                  })}
                </p>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

// مكون التنبيهات
function Alerts() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // بيانات تجريبية للتنبيهات
  const alerts = [
    { id: 1, type: 'warning', message: 'المخزون منخفض: Smart Factory IoT Sensor Kit', time: '2 ساعة' },
    { id: 2, type: 'info', message: 'تم تحديث أسعار 15 منتجًا', time: '3 ساعات' },
    { id: 3, type: 'error', message: 'فشل عملية دفع للطلب ORD-1239', time: '5 ساعات' },
  ];

  // أيقونة نوع التنبيه
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'info':
        return <Eye className="h-5 w-5 text-blue-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'التنبيهات' : 'Alerts'}
        </h3>
      </div>
      <div className="p-6">
        <ul className="space-y-4">
          {alerts.map((alert) => (
            <li key={alert.id} className="flex items-start gap-3">
              {getAlertIcon(alert.type)}
              <div>
                <p className="font-medium">{alert.message}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  {language === 'ar' ? `منذ ${alert.time}` : `${alert.time} ago`}
                </p>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

// مكون الرسم البياني
function SalesChart() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'تحليل المبيعات' : 'Sales Analytics'}
        </h3>
      </div>
      <div className="p-6">
        <div className="h-64 flex items-center justify-center">
          <div className="text-center">
            <LineChart className="h-12 w-12 mx-auto text-slate-400 mb-4" />
            <p className="text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? 'سيتم عرض الرسم البياني للمبيعات هنا'
                : 'Sales chart will be displayed here'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// مكون توزيع المبيعات
function SalesDistribution() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'توزيع المبيعات' : 'Sales Distribution'}
        </h3>
      </div>
      <div className="p-6">
        <div className="h-64 flex items-center justify-center">
          <div className="text-center">
            <PieChart className="h-12 w-12 mx-auto text-slate-400 mb-4" />
            <p className="text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? 'سيتم عرض توزيع المبيعات هنا'
                : 'Sales distribution chart will be displayed here'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// مكون طلبات الجملة الأخيرة
function RecentWholesaleOrders() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // بيانات تجريبية لطلبات الجملة الأخيرة
  const recentOrders = [
    { id: 'WO-1234', customer: 'شركة العلي للتجارة', date: '2023-05-01', status: 'quoted', total: 125000 },
    { id: 'WO-1235', customer: 'مجموعة الناصر', date: '2023-05-02', status: 'accepted', total: 85000 },
    { id: 'WO-1236', customer: 'Wei Manufacturing', date: '2023-05-03', status: 'pending', total: 210000 },
  ];

  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'quoted':
        return language === 'ar' ? 'تم تقديم عرض' : 'Quoted';
      case 'accepted':
        return language === 'ar' ? 'مقبول' : 'Accepted';
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      default:
        return status;
    }
  };

  // لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'quoted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'طلبات الجملة الأخيرة' : 'Recent Wholesale Orders'}
        </h3>
        <a href={`/${language}/admin/wholesale-orders`} className="text-sm text-primary-500 flex items-center">
          {language === 'ar' ? 'عرض الكل' : 'View All'}
          <ArrowRight className="h-4 w-4 ml-1" />
        </a>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className={cn(
            "text-xs uppercase",
            currentIsDark ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
          )}>
            <tr>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'رقم الطلب' : 'Order ID'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'العميل' : 'Customer'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'التاريخ' : 'Date'}
              </th>
              <th className="px-6 py-3 text-left">
                {language === 'ar' ? 'الحالة' : 'Status'}
              </th>
              <th className="px-6 py-3 text-right">
                {language === 'ar' ? 'المجموع' : 'Total'}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y dark:divide-slate-700">
            {recentOrders.map((order) => (
              <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {order.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {order.customer}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {new Date(order.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={cn(
                    "px-2 py-1 rounded-full text-xs",
                    getStatusColor(order.status)
                  )}>
                    {getStatusText(order.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                  {order.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                    style: 'currency',
                    currency: 'SAR'
                  })}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// مكون طلبات الخدمات الأخيرة
function RecentServiceRequests() {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();

  // بيانات تجريبية لطلبات الخدمات الأخيرة
  const recentRequests = [
    { id: 'SR-1234', customer: 'Global Trading Co.', service: 'Inspection Services', date: '2023-05-01', status: 'pending' },
    { id: 'SR-1235', customer: 'Johnson Imports', service: 'Logistics Consulting', date: '2023-05-02', status: 'in-progress' },
    { id: 'SR-1236', customer: 'Hassan Trading', service: 'Sourcing Services', date: '2023-05-03', status: 'completed' },
  ];

  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'in-progress':
        return language === 'ar' ? 'قيد المعالجة' : 'In Progress';
      case 'completed':
        return language === 'ar' ? 'مكتمل' : 'Completed';
      default:
        return status;
    }
  };

  // لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className={cn(
      "rounded-lg shadow-sm",
      currentIsDark ? "bg-slate-800" : "bg-white"
    )}>
      <div className="p-6 border-b dark:border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {language === 'ar' ? 'طلبات الخدمات الأخيرة' : 'Recent Service Requests'}
        </h3>
        <a href={`/${language}/admin/service-requests`} className="text-sm text-primary-500 flex items-center">
          {language === 'ar' ? 'عرض الكل' : 'View All'}
          <ArrowRight className="h-4 w-4 ml-1" />
        </a>
      </div>
      <div className="p-6">
        <ul className="space-y-4">
          {recentRequests.map((request) => (
            <li key={request.id} className="flex items-start justify-between">
              <div>
                <p className="font-medium">{request.customer}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">{request.service}</p>
                <div className="flex items-center mt-1">
                  <Calendar className="h-3 w-3 mr-1 text-slate-400" />
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {new Date(request.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </span>
                </div>
              </div>
              <span className={cn(
                "px-2 py-1 rounded-full text-xs",
                getStatusColor(request.status)
              )}>
                {getStatusText(request.status)}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export function AdminDashboard() {
  const { language } = useLanguageStore();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">
          {language === 'ar' ? 'لوحة القيادة' : 'Dashboard'}
        </h1>
        <p className="text-slate-600 dark:text-slate-400">
          {language === 'ar'
            ? 'نظرة عامة على أداء المنصة والإحصائيات الرئيسية'
            : 'Overview of platform performance and key metrics'}
        </p>
      </div>

      {/* الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
          value={language === 'ar' ? '١,٢٥٠,٠٠٠ ر.س' : 'SAR 1,250,000'}
          icon={<DollarSign className="h-6 w-6 text-white" />}
          change={{ value: 12.5, isPositive: true }}
          bgColor="bg-primary-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'طلبات التجزئة' : 'Retail Orders'}
          value={language === 'ar' ? '٥٦٨' : '568'}
          icon={<ShoppingCart className="h-6 w-6 text-white" />}
          change={{ value: 8.2, isPositive: true }}
          bgColor="bg-secondary-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'طلبات الجملة' : 'Wholesale Orders'}
          value={language === 'ar' ? '٤٥' : '45'}
          icon={<Handshake className="h-6 w-6 text-white" />}
          change={{ value: 15.3, isPositive: true }}
          bgColor="bg-accent-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'طلبات الخدمات' : 'Service Requests'}
          value={language === 'ar' ? '٧٢' : '72'}
          icon={<Briefcase className="h-6 w-6 text-white" />}
          change={{ value: 10.8, isPositive: true }}
          bgColor="bg-purple-500 text-white"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={language === 'ar' ? 'العملاء' : 'Customers'}
          value={language === 'ar' ? '١,٢٤٥' : '1,245'}
          icon={<Users className="h-6 w-6 text-white" />}
          change={{ value: 5.3, isPositive: true }}
          bgColor="bg-yellow-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'المنتجات' : 'Products'}
          value={language === 'ar' ? '٣٥٠' : '350'}
          icon={<Package className="h-6 w-6 text-white" />}
          change={{ value: 2.1, isPositive: false }}
          bgColor="bg-red-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'الخدمات' : 'Services'}
          value={language === 'ar' ? '٢٤' : '24'}
          icon={<Briefcase className="h-6 w-6 text-white" />}
          change={{ value: 0, isPositive: true }}
          bgColor="bg-green-500 text-white"
        />
        <StatCard
          title={language === 'ar' ? 'خطوط الإنتاج' : 'Production Lines'}
          value={language === 'ar' ? '١٢' : '12'}
          icon={<Factory className="h-6 w-6 text-white" />}
          change={{ value: 9.1, isPositive: true }}
          bgColor="bg-blue-500 text-white"
        />
      </div>

      {/* الرسوم البيانية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SalesChart />
        <SalesDistribution />
      </div>

      {/* الطلبات الأخيرة */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentOrders />
        <RecentWholesaleOrders />
      </div>

      {/* المنتجات الأكثر مبيعًا وطلبات الخدمات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TopProducts />
        <RecentServiceRequests />
      </div>

      {/* التنبيهات */}
      <div>
        <Alerts />
      </div>
    </div>
  );
}
