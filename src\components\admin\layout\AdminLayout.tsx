'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  FileText,
  Settings,
  BarChart,
  Menu,
  X,
  LogOut,
  Sun,
  Moon,
  Layers,
  Briefcase,
  Truck,
  Factory,
  Store,
  Handshake,
  Tag,
  Bell,
  Globe,
  CreditCard,
  Mail,
  Shield,
  ImageIcon
} from 'lucide-react';
import { useAuthStore } from '../../../stores/authStore';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { useTranslation } from '../../../translations';
import { cn } from '../../../lib/utils';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { signOut, user } = useAuthStore();
  const { language } = useLanguageStore();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { t } = useTranslation();

  // تبديل الوضع المظلم
  const toggleTheme = () => {
    setTheme(currentIsDark ? 'light' : 'dark');
  };

  // تنظيم الروابط في مجموعات
  const navGroups = [
    {
      title: language === 'ar' ? 'الرئيسية' : 'Main',
      links: [
        {
          name: language === 'ar' ? 'لوحة القيادة' : 'Dashboard',
          href: `/${language}/admin`,
          icon: <LayoutDashboard className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'المنتجات والخدمات' : 'Products & Services',
      links: [
        {
          name: language === 'ar' ? 'المنتجات' : 'Products',
          href: `/${language}/admin/products`,
          icon: <Package className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'فئات المنتجات' : 'Categories',
          href: `/${language}/admin/products/categories`,
          icon: <Tag className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'المخزون' : 'Inventory',
          href: `/${language}/admin/products/inventory`,
          icon: <Store className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الخدمات' : 'Services',
          href: `/${language}/admin/services`,
          icon: <Briefcase className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'خطوط الإنتاج' : 'Production Lines',
          href: `/${language}/admin/production-lines`,
          icon: <Factory className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'المبيعات' : 'Sales',
      links: [
        {
          name: language === 'ar' ? 'طلبات التجزئة' : 'Retail Orders',
          href: `/${language}/admin/orders`,
          icon: <ShoppingCart className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'طلبات الجملة' : 'Wholesale Orders',
          href: `/${language}/admin/wholesale-orders`,
          icon: <Handshake className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'طلبات الخدمات' : 'Service Requests',
          href: `/${language}/admin/service-requests`,
          icon: <Briefcase className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الشحن' : 'Shipping',
          href: `/${language}/admin/shipping`,
          icon: <Truck className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'العملاء' : 'Customers',
      links: [
        {
          name: language === 'ar' ? 'المستخدمين' : 'Users',
          href: `/${language}/admin/users`,
          icon: <Users className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'العملاء' : 'Customers',
          href: `/${language}/admin/customers`,
          icon: <Users className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الأدوار والصلاحيات' : 'Roles & Permissions',
          href: `/${language}/admin/roles`,
          icon: <Shield className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program',
          href: `/${language}/admin/loyalty`,
          icon: <Tag className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'المحتوى' : 'Content',
      links: [
        {
          name: language === 'ar' ? 'المدونة' : 'Blog',
          href: `/${language}/admin/content/blog`,
          icon: <FileText className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الصفحات' : 'Pages',
          href: `/${language}/admin/content/pages`,
          icon: <Layers className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الوسائط' : 'Media',
          href: `/${language}/admin/content/media`,
          icon: <ImageIcon className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'التسويق' : 'Marketing',
      links: [
        {
          name: language === 'ar' ? 'العروض والخصومات' : 'Promotions',
          href: `/${language}/admin/marketing/promotions`,
          icon: <Tag className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الإشعارات' : 'Notifications',
          href: `/${language}/admin/marketing/notifications`,
          icon: <Bell className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'البريد الإلكتروني' : 'Email',
          href: `/${language}/admin/marketing/email`,
          icon: <Mail className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'التقارير' : 'Reports',
      links: [
        {
          name: language === 'ar' ? 'تقارير المبيعات' : 'Sales Reports',
          href: `/${language}/admin/reports/sales`,
          icon: <BarChart className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'تقارير المخزون' : 'Inventory Reports',
          href: `/${language}/admin/reports/inventory`,
          icon: <Package className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'تقارير العملاء' : 'Customer Reports',
          href: `/${language}/admin/reports/customers`,
          icon: <Users className="h-5 w-5" />,
        },
      ]
    },
    {
      title: language === 'ar' ? 'الإعدادات' : 'Settings',
      links: [
        {
          name: language === 'ar' ? 'إعدادات المتجر' : 'Store Settings',
          href: `/${language}/admin/settings/store`,
          icon: <Settings className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'طرق الدفع' : 'Payment Methods',
          href: `/${language}/admin/settings/payment`,
          icon: <CreditCard className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'اللغات والترجمة' : 'Languages',
          href: `/${language}/admin/settings/languages`,
          icon: <Globe className="h-5 w-5" />,
        },
        {
          name: language === 'ar' ? 'الأمان' : 'Security',
          href: `/${language}/admin/settings/security`,
          icon: <Shield className="h-5 w-5" />,
        },
      ]
    },
  ];

  // التحقق مما إذا كان الرابط نشطًا
  const isActive = (href: string) => {
    if (href === `/${language}/admin`) {
      return pathname === `/${language}/admin`;
    }
    return pathname ? pathname.startsWith(href) : false;
  };

  // تسجيل الخروج
  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className={cn(
      "min-h-screen",
      currentIsDark ? "bg-slate-900 text-white" : "bg-gray-100 text-slate-900"
    )}>
      {/* الشريط العلوي */}
      <header className={cn(
        "fixed top-0 left-0 right-0 z-40 h-16 flex items-center px-4 md:px-6",
        currentIsDark ? "bg-slate-800 border-b border-slate-700" : "bg-white border-b border-gray-200"
      )}>
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="md:hidden p-2 rounded-md"
        >
          {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>

        <div className="flex-1 flex justify-center md:justify-start">
          <Link href={`/${language}/admin`} className="text-xl font-bold">
            {language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'}
          </Link>
        </div>

        <div className="flex items-center gap-4">
          {/* زر تبديل الوضع المظلم */}
          <button
            onClick={toggleTheme}
            className={cn(
              "p-2 rounded-full",
              currentIsDark ? "bg-slate-700 text-yellow-400" : "bg-gray-200 text-slate-700"
            )}
          >
            {currentIsDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          </button>

          {/* معلومات المستخدم */}
          <div className="flex items-center gap-2">
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center",
              currentIsDark ? "bg-slate-700" : "bg-gray-200"
            )}>
              <span className="text-sm font-medium">
                {user?.firstName?.charAt(0) || user?.email?.charAt(0) || 'A'}
              </span>
            </div>
            <div className="hidden md:block">
              <p className="text-sm font-medium">{user?.firstName} {user?.lastName}</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">{user?.email}</p>
            </div>
          </div>

          {/* زر تسجيل الخروج */}
          <button
            onClick={handleSignOut}
            className={cn(
              "p-2 rounded-md",
              currentIsDark ? "text-red-400 hover:bg-slate-700" : "text-red-600 hover:bg-gray-200"
            )}
          >
            <LogOut className="h-5 w-5" />
          </button>
        </div>
      </header>

      {/* الشريط الجانبي */}
      <aside className={cn(
        "fixed inset-y-0 left-0 z-30 w-64 transform transition-transform duration-300 ease-in-out",
        sidebarOpen ? "translate-x-0" : "-translate-x-full",
        "md:translate-x-0 md:pt-16",
        currentIsDark ? "bg-slate-800 border-r border-slate-700" : "bg-white border-r border-gray-200"
      )}>
        <nav className="p-4 pt-20 md:pt-4 h-full overflow-y-auto">
          <div className="space-y-6">
            {navGroups.map((group, index) => (
              <div key={index} className="space-y-2">
                <h3 className={cn(
                  "text-xs font-semibold uppercase tracking-wider px-4 mb-2",
                  currentIsDark ? "text-slate-400" : "text-slate-500"
                )}>
                  {group.title}
                </h3>
                <ul className="space-y-1">
                  {group.links.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className={cn(
                          "flex items-center gap-3 px-4 py-2 rounded-md transition-colors text-sm",
                          isActive(link.href)
                            ? currentIsDark
                              ? "bg-primary-900/30 text-primary-400"
                              : "bg-primary-50 text-primary-700"
                            : currentIsDark
                              ? "text-slate-300 hover:bg-slate-700"
                              : "text-slate-700 hover:bg-gray-100"
                        )}
                      >
                        {link.icon}
                        <span>{link.name}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </nav>
      </aside>

      {/* المحتوى الرئيسي */}
      <main className={cn(
        "pt-16 md:pt-16 md:ml-64 min-h-screen",
        currentIsDark ? "bg-slate-900" : "bg-gray-100"
      )}>
        <div className="p-4 md:p-6">
          {children}
        </div>
      </main>
    </div>
  );
}
