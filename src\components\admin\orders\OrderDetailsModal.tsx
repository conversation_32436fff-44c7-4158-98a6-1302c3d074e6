'use client';

import { 
  X, 
  Package, 
  Truck, 
  MapPin, 
  User, 
  CreditCard, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Printer,
  Download,
  Send,
  ShoppingBag
} from 'lucide-react';
import Image from 'next/image';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';

// نوع الطلب
interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

interface Order {
  id: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: OrderItem[];
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed';
  paymentMethod: string;
  shippingMethod: string;
  trackingNumber?: string;
  total: number;
  subtotal: number;
  shipping: number;
  tax: number;
  notes?: string;
}

interface OrderDetailsModalProps {
  order: Order;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: string) => void;
  onUpdatePaymentStatus: (orderId: string, status: string) => void;
}

export function OrderDetailsModal({ 
  order, 
  onClose, 
  onUpdateStatus, 
  onUpdatePaymentStatus 
}: OrderDetailsModalProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'processing':
        return language === 'ar' ? 'قيد المعالجة' : 'Processing';
      case 'shipped':
        return language === 'ar' ? 'تم الشحن' : 'Shipped';
      case 'delivered':
        return language === 'ar' ? 'تم التسليم' : 'Delivered';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case 'processing':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case 'shipped':
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";
      case 'delivered':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case 'cancelled':
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400";
    }
  };
  
  // ترجمة حالة الدفع
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'paid':
        return language === 'ar' ? 'مدفوع' : 'Paid';
      case 'failed':
        return language === 'ar' ? 'فشل الدفع' : 'Failed';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الدفع
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case 'paid':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case 'failed':
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400";
    }
  };
  
  // تنسيق العملة
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-bold">
              {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
            </h2>
            <span className="text-sm text-slate-500 dark:text-slate-400">
              {order.id}
            </span>
          </div>
          <button
            onClick={onClose}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          {/* معلومات الطلب */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <Calendar className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'تاريخ الطلب' : 'Order Date'}
                </h3>
              </div>
              <p>
                {new Date(order.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <Package className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'حالة الطلب' : 'Order Status'}
                </h3>
              </div>
              <div className="flex items-center justify-between">
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs",
                  getStatusColor(order.status)
                )}>
                  {getStatusText(order.status)}
                </span>
                
                <select
                  value={order.status}
                  onChange={(e) => onUpdateStatus(order.id, e.target.value)}
                  className={cn(
                    "px-2 py-1 rounded-md border text-sm",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white"
                      : "bg-white border-slate-300 text-slate-900"
                  )}
                >
                  <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                  <option value="processing">{language === 'ar' ? 'قيد المعالجة' : 'Processing'}</option>
                  <option value="shipped">{language === 'ar' ? 'تم الشحن' : 'Shipped'}</option>
                  <option value="delivered">{language === 'ar' ? 'تم التسليم' : 'Delivered'}</option>
                  <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                </select>
              </div>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <CreditCard className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'حالة الدفع' : 'Payment Status'}
                </h3>
              </div>
              <div className="flex items-center justify-between">
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs",
                  getPaymentStatusColor(order.paymentStatus)
                )}>
                  {getPaymentStatusText(order.paymentStatus)}
                </span>
                
                <select
                  value={order.paymentStatus}
                  onChange={(e) => onUpdatePaymentStatus(order.id, e.target.value)}
                  className={cn(
                    "px-2 py-1 rounded-md border text-sm",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white"
                      : "bg-white border-slate-300 text-slate-900"
                  )}
                >
                  <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                  <option value="paid">{language === 'ar' ? 'مدفوع' : 'Paid'}</option>
                  <option value="failed">{language === 'ar' ? 'فشل الدفع' : 'Failed'}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* معلومات العميل */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'معلومات العميل' : 'Customer Information'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <User className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'بيانات العميل' : 'Customer Details'}
                  </h4>
                </div>
                <p className="font-bold">{order.customer.name}</p>
                <p>{order.customer.email}</p>
                <p>{order.customer.phone}</p>
              </div>
              
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <MapPin className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'عنوان الشحن' : 'Shipping Address'}
                  </h4>
                </div>
                <p>{order.shippingAddress.street}</p>
                <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}</p>
                <p>{order.shippingAddress.country}</p>
              </div>
            </div>
          </div>
          
          {/* معلومات الشحن والدفع */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'معلومات الشحن' : 'Shipping Information'}
              </h3>
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <Truck className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'طريقة الشحن' : 'Shipping Method'}
                  </h4>
                </div>
                <p>{order.shippingMethod}</p>
                
                {order.trackingNumber && (
                  <div className="mt-3">
                    <h4 className="font-medium mb-1">
                      {language === 'ar' ? 'رقم التتبع' : 'Tracking Number'}
                    </h4>
                    <p className="font-mono">{order.trackingNumber}</p>
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'معلومات الدفع' : 'Payment Information'}
              </h3>
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <CreditCard className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'طريقة الدفع' : 'Payment Method'}
                  </h4>
                </div>
                <p>{order.paymentMethod}</p>
              </div>
            </div>
          </div>
          
          {/* عناصر الطلب */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'عناصر الطلب' : 'Order Items'}
            </h3>
            <div className={cn(
              "rounded-lg overflow-hidden",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <table className="w-full">
                <thead className={cn(
                  "text-xs uppercase",
                  isDarkMode ? "bg-slate-600 text-slate-300" : "bg-slate-200 text-slate-700"
                )}>
                  <tr>
                    <th className="px-6 py-3 text-left">
                      {language === 'ar' ? 'المنتج' : 'Product'}
                    </th>
                    <th className="px-6 py-3 text-center">
                      {language === 'ar' ? 'الكمية' : 'Quantity'}
                    </th>
                    <th className="px-6 py-3 text-right">
                      {language === 'ar' ? 'السعر' : 'Price'}
                    </th>
                    <th className="px-6 py-3 text-right">
                      {language === 'ar' ? 'المجموع' : 'Total'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y dark:divide-slate-600">
                  {order.items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <Image 
                            src={item.image} 
                            alt={item.name}
                            width={48} 
                            height={48} 
                            className="rounded-md object-cover" 
                          />
                          <div>
                            <p className="font-medium">{item.name}</p>
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              ID: {item.id}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-center">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 text-right">
                        {formatCurrency(item.price)}
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {formatCurrency(item.price * item.quantity)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className={cn(
                  "text-sm",
                  isDarkMode ? "bg-slate-600 text-white" : "bg-slate-100 text-slate-900"
                )}>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.subtotal)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'الشحن' : 'Shipping'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.shipping)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'الضريبة' : 'Tax'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.tax)}
                    </td>
                  </tr>
                  <tr className="text-base">
                    <td colSpan={3} className="px-6 py-3 text-right font-bold">
                      {language === 'ar' ? 'المجموع الكلي' : 'Total'}
                    </td>
                    <td className="px-6 py-3 text-right font-bold">
                      {formatCurrency(order.total)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          
          {/* ملاحظات */}
          {order.notes && (
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'ملاحظات' : 'Notes'}
              </h3>
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <p>{order.notes}</p>
              </div>
            </div>
          )}
          
          {/* الإجراءات */}
          <div className="flex flex-wrap justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Printer className="h-5 w-5" />
              <span>{language === 'ar' ? 'طباعة الفاتورة' : 'Print Invoice'}</span>
            </Button>
            
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-5 w-5" />
              <span>{language === 'ar' ? 'تنزيل الفاتورة' : 'Download Invoice'}</span>
            </Button>
            
            <Button
              className="flex items-center gap-2"
            >
              <Send className="h-5 w-5" />
              <span>{language === 'ar' ? 'إرسال إشعار للعميل' : 'Send Notification'}</span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
