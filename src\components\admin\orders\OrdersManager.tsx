'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Eye,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Download,
  Printer,
  CheckCircle,
  XCircle,
  TruckIcon,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { OrderDetailsModal } from './OrderDetailsModal';

// نوع عنصر الطلب
interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

// نوع الطلب
interface Order {
  id: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: OrderItem[];
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed';
  paymentMethod: string;
  shippingMethod: string;
  trackingNumber?: string;
  total: number;
  subtotal: number;
  shipping: number;
  tax: number;
  notes?: string;
}

// بيانات تجريبية للطلبات
const mockOrders: Order[] = [
  {
    id: 'ORD-1234',
    customer: {
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '+966 50 123 4567'
    },
    shippingAddress: {
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      zip: '12345',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-001',
        name: 'كرسي مكتبي فاخر',
        price: 350,
        quantity: 2,
        image: '/images/products/office-chair.jpg'
      },
      {
        id: 'PROD-002',
        name: 'طاولة مكتب خشبية',
        price: 550,
        quantity: 1,
        image: '/images/products/wooden-desk.jpg'
      }
    ],
    date: '2023-05-01',
    status: 'delivered',
    paymentStatus: 'paid',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن سريع',
    trackingNumber: 'TRK-*********',
    total: 1250,
    subtotal: 1200,
    shipping: 50,
    tax: 0,
    notes: 'يرجى التسليم في الصباح'
  },
  {
    id: 'ORD-1235',
    customer: {
      name: 'سارة أحمد',
      email: '<EMAIL>',
      phone: '+966 50 987 6543'
    },
    shippingAddress: {
      street: 'شارع العليا',
      city: 'جدة',
      state: 'مكة المكرمة',
      zip: '23456',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-003',
        name: 'مصباح مكتبي LED',
        price: 150,
        quantity: 1,
        image: '/images/products/desk-lamp.jpg'
      },
      {
        id: 'PROD-004',
        name: 'حامل شاشة قابل للتعديل',
        price: 350,
        quantity: 2,
        image: '/images/products/monitor-stand.jpg'
      }
    ],
    date: '2023-05-02',
    status: 'processing',
    paymentStatus: 'paid',
    paymentMethod: 'مدى',
    shippingMethod: 'شحن قياسي',
    total: 850,
    subtotal: 800,
    shipping: 30,
    tax: 20
  },
  {
    id: 'ORD-1236',
    customer: {
      name: 'محمد علي',
      email: '<EMAIL>',
      phone: '+966 55 123 4567'
    },
    shippingAddress: {
      street: 'شارع الأمير سلطان',
      city: 'الدمام',
      state: 'المنطقة الشرقية',
      zip: '34567',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-005',
        name: 'كمبيوتر محمول',
        price: 3500,
        quantity: 1,
        image: '/images/products/laptop.jpg'
      }
    ],
    date: '2023-05-03',
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'تحويل بنكي',
    shippingMethod: 'شحن سريع',
    total: 2100,
    subtotal: 2000,
    shipping: 50,
    tax: 50
  },
  {
    id: 'ORD-1237',
    customer: {
      name: 'فاطمة حسن',
      email: '<EMAIL>',
      phone: '+966 50 111 2222'
    },
    shippingAddress: {
      street: 'شارع التحلية',
      city: 'الرياض',
      state: 'الرياض',
      zip: '12345',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-006',
        name: 'سماعات لاسلكية',
        price: 450,
        quantity: 1,
        image: '/images/products/headphones.jpg'
      },
      {
        id: 'PROD-007',
        name: 'لوحة مفاتيح ميكانيكية',
        price: 350,
        quantity: 1,
        image: '/images/products/keyboard.jpg'
      }
    ],
    date: '2023-05-04',
    status: 'shipped',
    paymentStatus: 'paid',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن دولي',
    trackingNumber: 'TRK-*********',
    total: 1500,
    subtotal: 1400,
    shipping: 70,
    tax: 30
  },
  {
    id: 'ORD-1238',
    customer: {
      name: 'خالد عبدالله',
      email: '<EMAIL>',
      phone: '+966 55 333 4444'
    },
    shippingAddress: {
      street: 'شارع الملك عبدالله',
      city: 'الرياض',
      state: 'الرياض',
      zip: '11111',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-008',
        name: 'حقيبة لابتوب',
        price: 750,
        quantity: 1,
        image: '/images/products/laptop-bag.jpg'
      }
    ],
    date: '2023-05-05',
    status: 'cancelled',
    paymentStatus: 'failed',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن قياسي',
    total: 750,
    subtotal: 700,
    shipping: 30,
    tax: 20
  },
  {
    id: 'ORD-1239',
    customer: {
      name: 'نورة سعيد',
      email: '<EMAIL>',
      phone: '+966 50 555 6666'
    },
    shippingAddress: {
      street: 'شارع الأمير محمد',
      city: 'جدة',
      state: 'مكة المكرمة',
      zip: '22222',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-009',
        name: 'شاشة كمبيوتر',
        price: 1200,
        quantity: 1,
        image: '/images/products/monitor.jpg'
      },
      {
        id: 'PROD-010',
        name: 'ماوس لاسلكي',
        price: 150,
        quantity: 2,
        image: '/images/products/mouse.jpg'
      },
      {
        id: 'PROD-011',
        name: 'حامل شاشة',
        price: 300,
        quantity: 1,
        image: '/images/products/monitor-stand.jpg'
      }
    ],
    date: '2023-05-06',
    status: 'delivered',
    paymentStatus: 'paid',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن سريع',
    total: 1800,
    subtotal: 1700,
    shipping: 50,
    tax: 50
  },
  {
    id: 'ORD-1240',
    customer: {
      name: 'عمر فاروق',
      email: '<EMAIL>',
      phone: '+966 55 777 8888'
    },
    shippingAddress: {
      street: 'شارع الملك فيصل',
      city: 'الدمام',
      state: 'المنطقة الشرقية',
      zip: '33333',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-012',
        name: 'سماعات بلوتوث',
        price: 450,
        quantity: 1,
        image: '/images/products/bluetooth-speaker.jpg'
      },
      {
        id: 'PROD-013',
        name: 'شاحن لاسلكي',
        price: 250,
        quantity: 2,
        image: '/images/products/wireless-charger.jpg'
      }
    ],
    date: '2023-05-07',
    status: 'processing',
    paymentStatus: 'paid',
    paymentMethod: 'مدى',
    shippingMethod: 'شحن قياسي',
    total: 950,
    subtotal: 900,
    shipping: 30,
    tax: 20
  },
  {
    id: 'ORD-1241',
    customer: {
      name: 'ليلى محمود',
      email: '<EMAIL>',
      phone: '+966 50 999 0000'
    },
    shippingAddress: {
      street: 'شارع الستين',
      city: 'الرياض',
      state: 'الرياض',
      zip: '12345',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-014',
        name: 'طابعة ليزر',
        price: 800,
        quantity: 1,
        image: '/images/products/laser-printer.jpg'
      },
      {
        id: 'PROD-015',
        name: 'ورق طباعة',
        price: 50,
        quantity: 4,
        image: '/images/products/printer-paper.jpg'
      },
      {
        id: 'PROD-016',
        name: 'حبر طابعة',
        price: 100,
        quantity: 2,
        image: '/images/products/printer-ink.jpg'
      }
    ],
    date: '2023-05-08',
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'تحويل بنكي',
    shippingMethod: 'شحن قياسي',
    total: 1200,
    subtotal: 1150,
    shipping: 30,
    tax: 20
  },
  {
    id: 'ORD-1242',
    customer: {
      name: 'يوسف أحمد',
      email: '<EMAIL>',
      phone: '+966 55 111 2222'
    },
    shippingAddress: {
      street: 'شارع الملك سعود',
      city: 'جدة',
      state: 'مكة المكرمة',
      zip: '22222',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-017',
        name: 'كمبيوتر مكتبي',
        price: 2000,
        quantity: 1,
        image: '/images/products/desktop.jpg'
      },
      {
        id: 'PROD-018',
        name: 'شاشة كمبيوتر',
        price: 500,
        quantity: 1,
        image: '/images/products/monitor.jpg'
      }
    ],
    date: '2023-05-09',
    status: 'shipped',
    paymentStatus: 'paid',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن سريع',
    trackingNumber: 'TRK-*********',
    total: 2500,
    subtotal: 2400,
    shipping: 50,
    tax: 50
  },
  {
    id: 'ORD-1243',
    customer: {
      name: 'هدى سالم',
      email: '<EMAIL>',
      phone: '+966 50 333 4444'
    },
    shippingAddress: {
      street: 'شارع الأمير سلطان',
      city: 'الرياض',
      state: 'الرياض',
      zip: '12345',
      country: 'المملكة العربية السعودية'
    },
    items: [
      {
        id: 'PROD-019',
        name: 'هارد ديسك خارجي',
        price: 650,
        quantity: 1,
        image: '/images/products/external-hdd.jpg'
      }
    ],
    date: '2023-05-10',
    status: 'cancelled',
    paymentStatus: 'failed',
    paymentMethod: 'بطاقة ائتمان',
    shippingMethod: 'شحن قياسي',
    total: 650,
    subtotal: 600,
    shipping: 30,
    tax: 20
  }
];

// مكون إدارة الطلبات
export function OrdersManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof Order>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // حالة الطلبات
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // تصفية وترتيب الطلبات
  useEffect(() => {
    let result = [...mockOrders];

    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(order =>
        order.id.toLowerCase().includes(query) ||
        order.customer.name.toLowerCase().includes(query) ||
        order.customer.email.toLowerCase().includes(query)
      );
    }

    // تطبيق تصفية الحالة
    if (selectedStatus) {
      result = result.filter(order => order.status === selectedStatus);
    }

    // تطبيق الترتيب
    result.sort((a, b) => {
      if (sortField === 'date') {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      }

      if (sortField === 'total') {
        return sortDirection === 'asc' ? a.total - b.total : b.total - a.total;
      }

      const fieldA = String(a[sortField]);
      const fieldB = String(b[sortField]);

      return sortDirection === 'asc'
        ? fieldA.localeCompare(fieldB)
        : fieldB.localeCompare(fieldA);
    });

    setFilteredOrders(result);
  }, [searchQuery, selectedStatus, sortField, sortDirection]);

  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);

  // الحصول على الطلبات للصفحة الحالية
  const currentOrders = filteredOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Order) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // عرض تفاصيل الطلب
  const handleViewOrder = (orderId: string) => {
    const order = mockOrders.find(o => o.id === orderId);
    if (order) {
      setSelectedOrder(order);
      setShowOrderDetails(true);
    }
  };

  // تحديث حالة الطلب
  const handleUpdateStatus = (orderId: string, status: string) => {
    // هنا سيتم تنفيذ منطق تحديث حالة الطلب
    console.log('Update order status:', orderId, status);

    // في الإنتاج، سيتم استدعاء API لتحديث حالة الطلب
    // وتحديث قائمة الطلبات
  };

  // تحديث حالة الدفع
  const handleUpdatePaymentStatus = (orderId: string, status: string) => {
    // هنا سيتم تنفيذ منطق تحديث حالة الدفع
    console.log('Update payment status:', orderId, status);

    // في الإنتاج، سيتم استدعاء API لتحديث حالة الدفع
    // وتحديث قائمة الطلبات
  };

  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'processing':
        return language === 'ar' ? 'قيد المعالجة' : 'Processing';
      case 'shipped':
        return language === 'ar' ? 'تم الشحن' : 'Shipped';
      case 'delivered':
        return language === 'ar' ? 'تم التسليم' : 'Delivered';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };

  // لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'delivered':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  // أيقونة حالة الطلب
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5" />;
      case 'processing':
        return <AlertCircle className="h-5 w-5" />;
      case 'shipped':
        return <TruckIcon className="h-5 w-5" />;
      case 'delivered':
        return <CheckCircle className="h-5 w-5" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5" />;
      default:
        return null;
    }
  };

  // ترجمة حالة الدفع
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'paid':
        return language === 'ar' ? 'مدفوع' : 'Paid';
      case 'failed':
        return language === 'ar' ? 'فشل' : 'Failed';
      default:
        return status;
    }
  };

  // لون حالة الدفع
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة الطلبات' : 'Orders Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar'
              ? 'إدارة الطلبات وتتبع الشحنات وتحديث الحالة'
              : 'Manage orders, track shipments, and update status'}
          </p>
        </div>

      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن الطلبات...' : 'Search orders...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="w-full md:w-64">
            <select
              value={selectedStatus || ''}
              onChange={(e) => setSelectedStatus(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الحالات' : 'All Statuses'}
              </option>
              <option value="pending">
                {language === 'ar' ? 'قيد الانتظار' : 'Pending'}
              </option>
              <option value="processing">
                {language === 'ar' ? 'قيد المعالجة' : 'Processing'}
              </option>
              <option value="shipped">
                {language === 'ar' ? 'تم الشحن' : 'Shipped'}
              </option>
              <option value="delivered">
                {language === 'ar' ? 'تم التسليم' : 'Delivered'}
              </option>
              <option value="cancelled">
                {language === 'ar' ? 'ملغي' : 'Cancelled'}
              </option>
            </select>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              <span>{language === 'ar' ? 'تصدير' : 'Export'}</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Printer className="h-5 w-5" />
              <span>{language === 'ar' ? 'طباعة' : 'Print'}</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* جدول الطلبات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('id')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'رقم الطلب' : 'Order ID'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'العميل' : 'Customer'}
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('date')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'التاريخ' : 'Date'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-center">
                  {language === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-center">
                  {language === 'ar' ? 'حالة الدفع' : 'Payment'}
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('total')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'المجموع' : 'Total'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap font-medium">
                    {order.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <p>{order.customer.name}</p>
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        {order.customer.email}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {new Date(order.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs flex items-center justify-center gap-1 w-fit mx-auto",
                      getStatusColor(order.status)
                    )}>
                      {getStatusIcon(order.status)}
                      {getStatusText(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs w-fit mx-auto",
                      getPaymentStatusColor(order.paymentStatus)
                    )}>
                      {getPaymentStatusText(order.paymentStatus)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    {order.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                      style: 'currency',
                      currency: 'SAR'
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewOrder(order.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* ترقيم الصفحات */}
        <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
          <div>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredOrders.length)} من ${filteredOrders.length} طلب`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredOrders.length)} of ${filteredOrders.length} orders`
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={cn(
                "p-2 rounded-md",
                currentPage === 1
                  ? "opacity-50 cursor-not-allowed"
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>

            <span className="text-sm">
              {language === 'ar'
                ? `${currentPage} من ${totalPages}`
                : `${currentPage} of ${totalPages}`
              }
            </span>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={cn(
                "p-2 rounded-md",
                currentPage === totalPages
                  ? "opacity-50 cursor-not-allowed"
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </Card>
    </div>

      {/* نافذة تفاصيل الطلب */}
      {showOrderDetails && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => setShowOrderDetails(false)}
          onUpdateStatus={handleUpdateStatus}
          onUpdatePaymentStatus={handleUpdatePaymentStatus}
        />
      )}
    </>
  );
}
