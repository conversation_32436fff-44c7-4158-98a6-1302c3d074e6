'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Trash2, Upload, Image as LucideImage } from 'lucide-react';
import Image from 'next/image';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { ProductionLine } from '../../../types/index';

interface ProductionLineFormProps {
  productionLine: ProductionLine | null;
  onSave: (productionLine: ProductionLine) => void;
  onCancel: () => void;
}

// Interface for form data, extending Partial<ProductionLine>
interface ProductionLineFormData extends Partial<ProductionLine> {
  newImageUrl?: string;
}

export function ProductionLineForm({ productionLine, onSave, onCancel }: ProductionLineFormProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة النموذج
  const [formData, setFormData] = useState<ProductionLineFormData>({
    id: '',
    name: '',
    slug: '',
    description: '',
    capacity: '',
    specifications: {},
    images: [],
    createdAt: new Date().toISOString(),
  });
  
  // حالة المواصفات
  const [specKey, setSpecKey] = useState('');
  const [specValue, setSpecValue] = useState('');
  
  // تحميل بيانات خط الإنتاج إذا كان موجودًا
  useEffect(() => {
    if (productionLine) {
      setFormData({
        ...productionLine,
      });
    }
  }, [productionLine]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // إنشاء slug من الاسم
  const generateSlug = () => {
    const slug = formData.name
      ?.toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    setFormData(prev => ({
      ...prev,
      slug,
    }));
  };
  
  // إضافة مواصفة جديدة
  const addSpecification = () => {
    if (!specKey || !specValue) return;
    
    setFormData(prev => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        [specKey]: specValue,
      },
    }));
    
    setSpecKey('');
    setSpecValue('');
  };
  
  // حذف مواصفة
  const removeSpecification = (key: string) => {
    const newSpecs = { ...formData.specifications };
    delete newSpecs[key];
    
    setFormData(prev => ({
      ...prev,
      specifications: newSpecs,
    }));
  };
  
  // إضافة صورة
  const addImage = (url: string) => {
    if (!url) return;
    
    setFormData(prev => ({
      ...prev,
      images: [...(prev.images || []), url],
    }));
  };
  
  // حذف صورة
  const removeImage = (index: number) => {
    const newImages = [...(formData.images || [])];
    newImages.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      images: newImages,
    }));
  };
  
  // حفظ خط الإنتاج
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.description || !formData.capacity) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كان خط إنتاج جديد
    const productionLineData: ProductionLine = {
      ...(formData as ProductionLine),
      id: productionLine?.id || `production-line-${Date.now()}`,
      createdAt: productionLine?.createdAt || new Date().toISOString(),
    };
    
    onSave(productionLineData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {productionLine
              ? language === 'ar' ? 'تحرير خط الإنتاج' : 'Edit Production Line'
              : language === 'ar' ? 'إضافة خط إنتاج جديد' : 'Add New Production Line'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم خط الإنتاج' : 'Production Line Name'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                onBlur={generateSlug}
                required
              />
            </div>
            
            {/* Slug */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الرابط الثابت (Slug)' : 'Slug'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="slug"
                value={formData.slug || ''}
                onChange={handleChange}
                required
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'سيتم استخدام هذا في عنوان URL لخط الإنتاج'
                  : 'This will be used in the production line URL'
                }
              </p>
            </div>
          </div>
          
          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'الوصف' : 'Description'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              required
              rows={4}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
            />
          </div>
          
          {/* القدرة الإنتاجية */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'القدرة الإنتاجية' : 'Capacity'}
              <span className="text-red-500">*</span>
            </label>
            <Input
              name="capacity"
              value={formData.capacity || ''}
              onChange={handleChange}
              placeholder="e.g. 10,000 units per day"
              required
            />
          </div>
          
          {/* المواصفات */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium">
                {language === 'ar' ? 'المواصفات' : 'Specifications'}
              </label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <Input
                placeholder={language === 'ar' ? 'المفتاح' : 'Key'}
                value={specKey}
                onChange={(e) => setSpecKey(e.target.value)}
              />
              <Input
                placeholder={language === 'ar' ? 'القيمة' : 'Value'}
                value={specValue}
                onChange={(e) => setSpecValue(e.target.value)}
              />
            </div>
            
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addSpecification}
              className="flex items-center gap-1 mb-4"
              disabled={!specKey || !specValue}
            >
              <Plus className="h-4 w-4" />
              <span>{language === 'ar' ? 'إضافة مواصفة' : 'Add Specification'}</span>
            </Button>
            
            {Object.keys(formData.specifications || {}).length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className={cn(
                    "text-xs",
                    isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
                  )}>
                    <tr>
                      <th className="px-4 py-2 text-left">{language === 'ar' ? 'المفتاح' : 'Key'}</th>
                      <th className="px-4 py-2 text-left">{language === 'ar' ? 'القيمة' : 'Value'}</th>
                      <th className="px-4 py-2 text-right">{language === 'ar' ? 'الإجراءات' : 'Actions'}</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
                    {Object.entries(formData.specifications || {}).map(([key, value]) => (
                      <tr key={key} className={isDarkMode ? "bg-slate-800" : "bg-white"}>
                        <td className="px-4 py-2">{key}</td>
                        <td className="px-4 py-2">{value}</td>
                        <td className="px-4 py-2 text-right">
                          <button
                            type="button"
                            onClick={() => removeSpecification(key)}
                            className={cn(
                              "p-1 rounded-md",
                              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                            )}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                {language === 'ar'
                  ? 'لا توجد مواصفات. أضف بعض المواصفات لخط الإنتاج.'
                  : 'No specifications. Add some specifications for the production line.'
                }
              </p>
            )}
          </div>
          
          {/* الصور */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium">
                {language === 'ar' ? 'الصور' : 'Images'}
              </label>
            </div>
            
            <div className="flex flex-wrap gap-4 mb-4">
              {formData.images?.map((image, index) => (
                <div key={index} className="relative w-24 h-24 group">
                  <Image
                    src={image}
                    alt={`Production Line ${index + 1}`}
                    fill
                    className="object-cover rounded-md"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 p-1 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
              
              <div className="w-24 h-24 border-2 border-dashed rounded-md flex items-center justify-center">
                <div className="text-center">
                  <Upload className="h-6 w-6 mx-auto text-slate-400" />
                  <span className="text-xs text-slate-500 mt-1">
                    {language === 'ar' ? 'إضافة صورة' : 'Add Image'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2 items-center">
              <Input
                placeholder={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                value={formData.newImageUrl || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, newImageUrl: e.target.value }))}
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (formData.newImageUrl) {
                    addImage(formData.newImageUrl);
                    setFormData(prev => ({ ...prev, newImageUrl: '' }));
                  }
                }}
                disabled={!formData.newImageUrl}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              {language === 'ar' ? 'حفظ خط الإنتاج' : 'Save Production Line'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
