'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  Factory,
  Settings,
  Gauge
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { ProductionLine } from '../../../types/index';
import { ProductionLineForm } from './ProductionLineForm';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { productionLines } from '../../../data/productionLines';

// مكون إدارة خطوط الإنتاج
export function ProductionLinesManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof ProductionLine>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // حالة النموذج
  const [showProductionLineForm, setShowProductionLineForm] = useState(false);
  const [editingProductionLine, setEditingProductionLine] = useState<ProductionLine | null>(null);
  
  // تصفية خطوط الإنتاج بناءً على البحث
  const filteredProductionLines = productionLines.filter((line) => {
    const searchRegex = new RegExp(searchQuery, 'i');
    return (
      searchRegex.test(line.name) ||
      searchRegex.test(line.description) ||
      searchRegex.test(line.capacity)
    );
  });
  
  // ترتيب خطوط الإنتاج
  const sortedProductionLines = [...filteredProductionLines].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedProductionLines.length / itemsPerPage);
  
  // الحصول على خطوط الإنتاج للصفحة الحالية
  const currentProductionLines = sortedProductionLines.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof ProductionLine) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // تحرير خط إنتاج
  const handleEditProductionLine = (productionLine: ProductionLine) => {
    setEditingProductionLine(productionLine);
    setShowProductionLineForm(true);
  };
  
  // إضافة خط إنتاج جديد
  const handleAddProductionLine = () => {
    setEditingProductionLine(null);
    setShowProductionLineForm(true);
  };
  
  // حذف خط إنتاج
  const handleDeleteProductionLine = (productionLineId: string) => {
    // هنا سيتم تنفيذ منطق حذف خط الإنتاج
    console.log('Delete production line:', productionLineId);
    
    // في الإنتاج، سيتم استدعاء API لحذف خط الإنتاج
    // وتحديث قائمة خطوط الإنتاج
  };
  
  // عرض تفاصيل خط إنتاج
  const handleViewProductionLine = (productionLineId: string) => {
    // هنا سيتم تنفيذ منطق عرض تفاصيل خط الإنتاج
    console.log('View production line:', productionLineId);
    
    // في الإنتاج، سيتم الانتقال إلى صفحة تفاصيل خط الإنتاج
    // أو فتح نافذة منبثقة لعرض التفاصيل
  };
  
  // حفظ خط الإنتاج (إضافة أو تحديث)
  const handleSaveProductionLine = (productionLine: ProductionLine) => {
    if (editingProductionLine) {
      // تحديث خط إنتاج موجود
      console.log('Update production line:', productionLine);
      
      // في الإنتاج، سيتم استدعاء API لتحديث خط الإنتاج
      // وتحديث قائمة خطوط الإنتاج
    } else {
      // إضافة خط إنتاج جديد
      console.log('Add production line:', productionLine);
      
      // في الإنتاج، سيتم استدعاء API لإضافة خط الإنتاج
      // وتحديث قائمة خطوط الإنتاج
    }
    
    setShowProductionLineForm(false);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة خطوط الإنتاج' : 'Production Lines Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة خطوط الإنتاج والمواصفات والقدرات'
              : 'Manage production lines, specifications, and capacities'}
          </p>
        </div>
        
        <Button
          onClick={handleAddProductionLine}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة خط إنتاج' : 'Add Production Line'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن خطوط الإنتاج...' : 'Search production lines...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            
            <Button variant="outline">
              <Filter className="h-5 w-5" />
              <span className="ml-2">{language === 'ar' ? 'تصفية' : 'Filter'}</span>
            </Button>
          </div>
        </div>
      </Card>
      
      {/* جدول خطوط الإنتاج */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'اسم خط الإنتاج' : 'Production Line Name'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </th>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('capacity')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'القدرة الإنتاجية' : 'Capacity'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
              {currentProductionLines.map((line) => (
                <tr 
                  key={line.id}
                  className={cn(
                    "hover:bg-gray-50 dark:hover:bg-slate-700/50",
                    isDarkMode ? "bg-slate-800" : "bg-white"
                  )}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Factory className="h-5 w-5 text-primary-500 mr-3" />
                      <div>
                        <div className="font-medium">{line.name}</div>
                        <div className="text-sm text-slate-500 dark:text-slate-400">
                          {line.slug}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm line-clamp-2">
                      {line.description}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Gauge className="h-4 w-4 mr-2 text-slate-400" />
                      <span>{line.capacity}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewProductionLine(line.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditProductionLine(line)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeleteProductionLine(line.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        <div className={cn(
          "px-6 py-4 flex items-center justify-between border-t",
          isDarkMode ? "border-slate-700" : "border-gray-200"
        )}>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {language === 'ar'
              ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, sortedProductionLines.length)} من ${sortedProductionLines.length} خط إنتاج`
              : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, sortedProductionLines.length)} of ${sortedProductionLines.length} production lines`}
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
      
      {/* نموذج إضافة/تحرير خط الإنتاج */}
      {showProductionLineForm && (
        <ProductionLineForm
          productionLine={editingProductionLine}
          onSave={handleSaveProductionLine}
          onCancel={() => setShowProductionLineForm(false)}
        />
      )}
    </div>
  );
}
