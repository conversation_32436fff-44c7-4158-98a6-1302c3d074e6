'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  Image as LucideImage
} from 'lucide-react';
import Image from 'next/image';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { productCategories } from '../../../data/products';
import { CategoryForm } from './CategoryForm';

// نوع فئة المنتج
interface ProductCategory {
  id: string;
  name: { en: string; ar: string }; 
  description: { en: string; ar: string }; 
  image: string;
}

// مكون إدارة فئات المنتجات
export function CategoriesManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  // حالة الفئات
  const [filteredCategories, setFilteredCategories] = useState<ProductCategory[]>([]);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null);
  
  // تصفية الفئات
  useEffect(() => {
    let result = [...productCategories];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(category => {
        const nameInCurrentLang = category.name[language as keyof typeof category.name]?.toLowerCase() || '';
        const descriptionInCurrentLang = category.description[language as keyof typeof category.description]?.toLowerCase() || '';
        return nameInCurrentLang.includes(query) || descriptionInCurrentLang.includes(query);
      });
    }
    
    setFilteredCategories(result);
  }, [searchQuery, language]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredCategories.length / itemsPerPage);
  
  // الحصول على الفئات للصفحة الحالية
  const currentCategories = filteredCategories.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تحرير فئة
  const handleEditCategory = (category: ProductCategory) => {
    setEditingCategory(category);
    setShowCategoryForm(true);
  };
  
  // إضافة فئة جديدة
  const handleAddCategory = () => {
    setEditingCategory(null);
    setShowCategoryForm(true);
  };
  
  // حذف فئة
  const handleDeleteCategory = (categoryId: string) => {
    // هنا سيتم تنفيذ منطق حذف الفئة
    console.log('Delete category:', categoryId);
    
    // في الإنتاج، سيتم استدعاء API لحذف الفئة
    // وتحديث قائمة الفئات
  };
  
  // حفظ الفئة (إضافة أو تحديث)
  const handleSaveCategory = (category: ProductCategory) => {
    if (editingCategory) {
      // تحديث فئة موجودة
      console.log('Update category:', category);
      
      // في الإنتاج، سيتم استدعاء API لتحديث الفئة
      // وتحديث قائمة الفئات
    } else {
      // إضافة فئة جديدة
      console.log('Add category:', category);
      
      // في الإنتاج، سيتم استدعاء API لإضافة الفئة
      // وتحديث قائمة الفئات
    }
    
    setShowCategoryForm(false);
  };
  
  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {language === 'ar' ? 'إدارة فئات المنتجات' : 'Product Categories Management'}
            </h1>
            <p className="text-slate-600 dark:text-slate-400">
              {language === 'ar' 
                ? 'إدارة وتنظيم فئات المنتجات'
                : 'Manage and organize product categories'}
            </p>
          </div>
          
          <Button
            onClick={handleAddCategory}
            className="flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            <span>{language === 'ar' ? 'إضافة فئة' : 'Add Category'}</span>
          </Button>
        </div>
        
        {/* أدوات البحث */}
        <Card className={cn(
          "p-4",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
              <Input
                type="text"
                placeholder={language === 'ar' ? 'البحث عن الفئات...' : 'Search categories...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </Card>
        
        {/* جدول الفئات */}
        <Card className={cn(
          "overflow-hidden",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={cn(
                "text-xs uppercase",
                isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
              )}>
                <tr>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'الفئة' : 'Category'}
                  </th>
                  <th className="px-6 py-3 text-left">
                    {language === 'ar' ? 'الوصف' : 'Description'}
                  </th>
                  <th className="px-6 py-3 text-center">
                    {language === 'ar' ? 'الصورة' : 'Image'}
                  </th>
                  <th className="px-6 py-3 text-right">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y dark:divide-slate-700">
                {currentCategories.map((category) => (
                  <tr key={category.id} className={isDarkMode ? "hover:bg-slate-750" : "hover:bg-gray-50"}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <p className="font-medium">{category.name[language as keyof typeof category.name]}</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400 truncate max-w-xs">
                          {category.description[language as keyof typeof category.description]}
                        </p>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <p className="line-clamp-2">{category.description[language as keyof typeof category.description]}</p>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex justify-center">
                        <Image 
                          src={category.image} 
                          alt={category.name[language as keyof typeof category.name]}
                          width={48} 
                          height={48} 
                          className="rounded-md object-cover" 
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => handleEditCategory(category)}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Edit className="h-5 w-5 text-yellow-500" />
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className={cn(
                            "p-1 rounded-md",
                            isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Trash2 className="h-5 w-5 text-red-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* ترقيم الصفحات */}
          {totalPages > 1 && (
            <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {language === 'ar'
                    ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredCategories.length)} من ${filteredCategories.length} فئة`
                    : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredCategories.length)} of ${filteredCategories.length} categories`
                  }
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={cn(
                    "p-2 rounded-md",
                    currentPage === 1 
                      ? "opacity-50 cursor-not-allowed" 
                      : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                  )}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                
                <span className="text-sm">
                  {language === 'ar'
                    ? `${currentPage} من ${totalPages}`
                    : `${currentPage} of ${totalPages}`
                  }
                </span>
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={cn(
                    "p-2 rounded-md",
                    currentPage === totalPages 
                      ? "opacity-50 cursor-not-allowed" 
                      : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                  )}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}
        </Card>
      </div>
      
      {/* نموذج إضافة/تحرير الفئة */}
      {showCategoryForm && (
        <CategoryForm
          category={editingCategory}
          onSave={handleSaveCategory}
          onCancel={() => setShowCategoryForm(false)}
        />
      )}
    </>
  );
}
