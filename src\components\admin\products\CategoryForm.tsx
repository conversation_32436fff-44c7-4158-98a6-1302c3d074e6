'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Upload } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import Image from 'next/image';

// نوع فئة المنتج
interface ProductCategory {
  id: string;
  name: { en: string; ar: string };
  description: { en: string; ar: string };
  image: string;
}

interface CategoryFormProps {
  category: ProductCategory | null;
  onSave: (category: ProductCategory) => void;
  onCancel: () => void;
}

export function CategoryForm({ category, onSave, onCancel }: CategoryFormProps) {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<ProductCategory>>({
    id: '',
    name: { en: '', ar: '' },
    description: { en: '', ar: '' },
    image: '',
  });
  
  // تحميل بيانات الفئة إذا كانت موجودة
  useEffect(() => {
    if (category) {
      setFormData({
        id: category.id,
        name: { 
          en: category.name.en || '', 
          ar: category.name.ar || '' 
        },
        description: { 
          en: category.description.en || '', 
          ar: category.description.ar || '' 
        },
        image: category.image,
      });
    } else {
      // Reset form for new category
      setFormData({
        id: '',
        name: { en: '', ar: '' },
        description: { en: '', ar: '' },
        image: '',
      });
    }
  }, [category]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const [field, lang] = name.split('.'); // e.g., "name.en"

    if (lang && (field === 'name' || field === 'description')) {
      setFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field] as { en: string; ar: string }), // Type assertion
          [lang]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  
  // إنشاء معرف من الاسم
  const generateId = () => {
    if (!formData.name?.en) return; // Use English name for ID generation or a specific logic
    
    const id = formData.name.en
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    setFormData(prev => ({
      ...prev,
      id,
    }));
  };
  
  // حفظ الفئة
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.name?.en || !formData.name?.ar || 
        !formData.description?.en || !formData.description?.ar || 
        !formData.image) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة (بما في ذلك اللغتين)' : 'Please fill all required fields (including both languages)');
      return;
    }
    
    // Ensure name and description are not undefined before creating categoryData
    const finalName = formData.name || { en: '', ar: '' };
    const finalDescription = formData.description || { en: '', ar: '' };

    // إنشاء معرف جديد إذا كانت فئة جديدة
    const categoryData: ProductCategory = {
      id: category?.id || formData.id || `category-${Date.now()}`,
      name: finalName,
      description: finalDescription,
      image: formData.image as string, // Already checked formData.image
    };
    
    onSave(categoryData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-2xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {category
              ? language === 'ar' ? 'تحرير الفئة' : 'Edit Category'
              : language === 'ar' ? 'إضافة فئة جديدة' : 'Add New Category'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* الاسم */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'اسم الفئة (انجليزي)' : 'Category Name (English)'}
              <span className="text-red-500">*</span>
            </label>
            <Input
              name="name.en"
              value={formData.name?.en || ''} // Safe access
              onChange={handleChange}
              onBlur={generateId} // Generate ID based on English name
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'اسم الفئة (عربي)' : 'Category Name (Arabic)'}
              <span className="text-red-500">*</span>
            </label>
            <Input
              name="name.ar"
              value={formData.name?.ar || ''} // Safe access
              onChange={handleChange}
              required
            />
          </div>
          
          {/* المعرف */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'المعرف' : 'ID'}
              <span className="text-red-500">*</span>
            </label>
            <Input
              name="id"
              value={formData.id || ''}
              onChange={handleChange}
              required
              disabled={!!category}
            />
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              {language === 'ar'
                ? 'سيتم استخدام هذا كمعرف فريد للفئة'
                : 'This will be used as a unique identifier for the category'
              }
            </p>
          </div>
          
          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'الوصف (انجليزي)' : 'Description (English)'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description.en"
              value={formData.description?.en || ''} // Safe access
              onChange={handleChange}
              required
              rows={3}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description.ar"
              value={formData.description?.ar || ''} // Safe access
              onChange={handleChange}
              required
              rows={3}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
            />
          </div>
          
          {/* الصورة */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'صورة الفئة' : 'Category Image'}
              <span className="text-red-500">*</span>
            </label>
            
            {formData.image && (
              <div className="mb-4">
                <div className="relative w-32 h-32 mx-auto">
                  <Image 
                    src={formData.image}
                    alt={formData.name?.en || formData.name?.ar || 'Category'} // Safe access
                    fill 
                    className="object-cover rounded-md"
                    sizes="128px"
                  />
                </div>
              </div>
            )}
            
            <div className="flex gap-2 items-center">
              <Input
                name="image"
                placeholder={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                value={formData.image || ''}
                onChange={handleChange}
                required
              />
              <div className="bg-primary-500 text-white p-2 rounded-md cursor-pointer">
                <Upload className="h-5 w-5" />
              </div>
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              {language === 'ar'
                ? 'أدخل رابط الصورة أو استخدم زر التحميل'
                : 'Enter image URL or use upload button'
              }
            </p>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              {language === 'ar' ? 'حفظ الفئة' : 'Save Category'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
