'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Edit, 
  ChevronLeft, 
  ChevronRight,
  ArrowUpDown,
  Save,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Package
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useTheme } from 'next-themes';
import { cn } from '../../../lib/utils';
import { products } from '../../../data/products';
import { Product } from '../../../types/index';
import Image from 'next/image'; // Added import

// Define a specific type for keys of Product that are sortable (string or number valued)
type SortableProductKeys =
  | 'id'
  | 'name'
  | 'slug'
  | 'description'
  | 'price'
  | 'category'
  | 'stock'
  | 'createdAt'
  | 'rating'
  | 'reviewCount';

// مكون إدارة المخزون
export function InventoryManager() {
  const { language } = useLanguageStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<SortableProductKeys>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [lowStockOnly, setLowStockOnly] = useState(false);
  
  // حالة المنتجات
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [editableProducts, setEditableProducts] = useState<Record<string, number>>({});
  const [hasChanges, setHasChanges] = useState(false);
  
  // استخراج الفئات الفريدة
  const categories = Array.from(new Set(products.map(product => product.category)));
  
  // تصفية وترتيب المنتجات
  useEffect(() => {
    let result = [...products];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(query) || 
        product.id.toLowerCase().includes(query)
      );
    }
    
    // تطبيق تصفية الفئة
    if (selectedCategory) {
      result = result.filter(product => product.category === selectedCategory);
    }
    
    // تطبيق تصفية المخزون المنخفض
    if (lowStockOnly) {
      result = result.filter(product => (product.stock || 0) < 10);
    }
    
    // تطبيق الترتيب
    result.sort((a, b) => {
      const fieldA = a[sortField];
      const fieldB = b[sortField];
      
      if (typeof fieldA === 'string' && typeof fieldB === 'string') {
        return sortDirection === 'asc' 
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      }
      
      if (typeof fieldA === 'number' && typeof fieldB === 'number') {
        return sortDirection === 'asc' 
          ? fieldA - fieldB
          : fieldB - fieldA;
      }
      
      // Handle cases for potentially undefined sortable fields like rating or reviewCount
      if (fieldA === undefined && fieldB !== undefined) return sortDirection === 'asc' ? -1 : 1;
      if (fieldA !== undefined && fieldB === undefined) return sortDirection === 'asc' ? 1 : -1;
      if (fieldA === undefined && fieldB === undefined) return 0;

      return 0;
    });
    
    setFilteredProducts(result);
    
    // تهيئة المنتجات القابلة للتحرير
    const initialEditableProducts: Record<string, number> = {};
    result.forEach(product => {
      initialEditableProducts[product.id] = product.stock || 0;
    });
    setEditableProducts(initialEditableProducts);
    setHasChanges(false);
  }, [searchQuery, selectedCategory, lowStockOnly, sortField, sortDirection]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  
  // الحصول على المنتجات للصفحة الحالية
  const currentProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: SortableProductKeys) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // تحديث كمية المخزون
  const handleStockChange = (productId: string, value: string) => {
    const numValue = parseInt(value, 10) || 0;
    setEditableProducts(prev => ({
      ...prev,
      [productId]: numValue
    }));
    setHasChanges(true);
  };
  
  // حفظ التغييرات
  const handleSaveChanges = () => {
    console.log('Save inventory changes:', editableProducts);
    
    setHasChanges(false);
  };
  
  // الحصول على لون حالة المخزون
  const getStockStatusColor = (stock: number) => {
    if (stock <= 0) {
      return "text-red-500";
    } else if (stock < 10) {
      return "text-yellow-500";
    } else {
      return "text-green-500";
    }
  };
  
  // الحصول على أيقونة حالة المخزون
  const getStockStatusIcon = (stock: number) => {
    if (stock <= 0) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    } else if (stock < 10) {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    } else {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة المخزون' : 'Inventory Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة وتحديث مخزون المنتجات'
              : 'Manage and update product inventory'}
          </p>
        </div>
        
        {hasChanges && (
          <Button
            onClick={handleSaveChanges}
            className="flex items-center gap-2"
          >
            <Save className="h-5 w-5" />
            <span>{language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'}</span>
          </Button>
        )}
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن المنتجات...' : 'Search products...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedCategory || ''}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الفئات' : 'All Categories'}
              </option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={lowStockOnly}
                onChange={(e) => setLowStockOnly(e.target.checked)}
                className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
              />
              <span>{language === 'ar' ? 'المخزون المنخفض فقط' : 'Low Stock Only'}</span>
            </label>
          </div>
        </div>
      </Card>
      
      {/* جدول المخزون */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'اسم المنتج' : 'Product Name'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('category')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الفئة' : 'Category'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-center">
                  {language === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-center">
                  <button 
                    className="flex items-center gap-1 mx-auto"
                  >
                    {language === 'ar' ? 'المخزون الحالي' : 'Current Stock'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-4 text-center">
                  {language === 'ar' ? 'تحديث المخزون' : 'Update Stock'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <Image
                        src={product.images[0] || '/images/placeholder.png'} 
                        alt={product.name}
                        width={40}
                        height={40}
                        className="rounded-md object-cover"
                      />
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">
                          ID: {product.id}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {product.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex items-center justify-center gap-1">
                      {getStockStatusIcon(product.stock || 0)}
                      <span className={cn(
                        getStockStatusColor(product.stock || 0)
                      )}>
                        {(product.stock || 0) <= 0 
                          ? (language === 'ar' ? 'نفذ المخزون' : 'Out of Stock')
                          : (product.stock || 0) < 10
                            ? (language === 'ar' ? 'مخزون منخفض' : 'Low Stock')
                            : (language === 'ar' ? 'متوفر' : 'In Stock')
                        }
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className="font-medium">{product.stock || 0}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center justify-center">
                      <div className="w-24">
                        <Input
                          type="number"
                          min="0"
                          value={(editableProducts[product.id] || 0).toString()}
                          onChange={(e) => handleStockChange(product.id, e.target.value)}
                          className="text-center"
                        />
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
          <div>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} من ${filteredProducts.length} منتج`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} of ${filteredProducts.length} products`
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={cn(
                "p-2 rounded-md",
                currentPage === 1 
                  ? "opacity-50 cursor-not-allowed" 
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            
            <span className="text-sm">
              {language === 'ar'
                ? `${currentPage} من ${totalPages}`
                : `${currentPage} of ${totalPages}`
              }
            </span>
            
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={cn(
                "p-2 rounded-md",
                currentPage === totalPages 
                  ? "opacity-50 cursor-not-allowed" 
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
}
