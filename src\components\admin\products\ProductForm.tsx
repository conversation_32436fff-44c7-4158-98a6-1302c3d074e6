'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Trash2, Upload, Tag } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { Product } from '../../../types/index';
import { productCategories } from '../../../data/products';
import Image from 'next/image'; // Added import

interface ProductFormProps {
  product: Product | null;
  onSave: (product: Product) => void;
  onCancel: () => void;
}

export function ProductForm({ product, onSave, onCancel }: ProductFormProps) {
  const { language } = useLanguageStore();
  const { themeMode } = useThemeStore();
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<Product>>({
    id: '',
    name: '',
    slug: '',
    description: '',
    price: 0,
    compareAtPrice: 0,
    images: [],
    category: '',
    tags: [],
    stock: 0,
    featured: false,
    specifications: {},
    createdAt: new Date().toISOString(),
    reviews: [],
    rating: 0,
    relatedProducts: [],
  });
  
  // حالة المواصفات
  const [specKey, setSpecKey] = useState('');
  const [specValue, setSpecValue] = useState('');
  
  // حالة العلامات
  const [tagInput, setTagInput] = useState('');
  
  // حالة رابط الصورة الجديدة
  const [newImageUrl, setNewImageUrl] = useState('');
  
  // تحميل بيانات المنتج إذا كان موجودًا
  useEffect(() => {
    if (product) {
      setFormData({
        ...product,
      });
    }
  }, [product]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: checkbox.checked,
      }));
    } else if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0,
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  
  // إنشاء slug من الاسم
  const generateSlug = () => {
    const slug = formData.name
      ?.toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    setFormData(prev => ({
      ...prev,
      slug,
    }));
  };
  
  // إضافة مواصفة جديدة
  const addSpecification = () => {
    if (!specKey || !specValue) return;
    
    setFormData(prev => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        [specKey]: specValue,
      },
    }));
    
    setSpecKey('');
    setSpecValue('');
  };
  
  // حذف مواصفة
  const removeSpecification = (key: string) => {
    const newSpecs = { ...formData.specifications };
    delete newSpecs[key];
    
    setFormData(prev => ({
      ...prev,
      specifications: newSpecs,
    }));
  };
  
  // إضافة علامة
  const addTag = () => {
    if (!tagInput) return;
    
    if (!formData.tags?.includes(tagInput)) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput],
      }));
    }
    
    setTagInput('');
  };
  
  // حذف علامة
  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag) || [],
    }));
  };
  
  // إضافة صورة
  const addImage = (url: string) => {
    if (!url) return;
    
    setFormData(prev => ({
      ...prev,
      images: [...(prev.images || []), url],
    }));
  };
  
  // حذف صورة
  const removeImage = (index: number) => {
    const newImages = [...(formData.images || [])];
    newImages.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      images: newImages,
    }));
  };
  
  // حفظ المنتج
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.description || !formData.category) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كان منتج جديد
    const productData: Product = {
      ...(formData as Product),
      id: product?.id || `product-${Date.now()}`,
      createdAt: product?.createdAt || new Date().toISOString(),
    };
    
    onSave(productData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        themeMode === 'dark' ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          themeMode === 'dark' ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {product
              ? language === 'ar' ? 'تحرير المنتج' : 'Edit Product'
              : language === 'ar' ? 'إضافة منتج جديد' : 'Add New Product'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم المنتج' : 'Product Name'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                onBlur={generateSlug}
                required
              />
            </div>
            
            {/* Slug */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الرابط الثابت (Slug)' : 'Slug'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="slug"
                value={formData.slug || ''}
                onChange={handleChange}
                required
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'سيتم استخدام هذا في عنوان URL للمنتج'
                  : 'This will be used in the product URL'
                }
              </p>
            </div>
          </div>
          
          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'الوصف' : 'Description'}
              <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              required
              rows={4}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                themeMode === 'dark'
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* السعر */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'السعر' : 'Price'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="price"
                type="number"
                min="0"
                step="0.01"
                value={formData.price || ''}
                onChange={handleChange}
                required
              />
            </div>
            
            {/* سعر المقارنة */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'سعر المقارنة' : 'Compare At Price'}
              </label>
              <Input
                name="compareAtPrice"
                type="number"
                min="0"
                step="0.01"
                value={formData.compareAtPrice || ''}
                onChange={handleChange}
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'السعر الأصلي قبل الخصم'
                  : 'Original price before discount'
                }
              </p>
            </div>
            
            {/* المخزون */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'المخزون' : 'Stock'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="stock"
                type="number"
                min="0"
                step="1"
                value={formData.stock || ''}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الفئة */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الفئة' : 'Category'}
                <span className="text-red-500">*</span>
              </label>
              <select
                name="category"
                value={formData.category || ''}
                onChange={handleChange}
                required
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                  themeMode === 'dark'
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-300 text-slate-900"
                )}
              >
                <option value="">
                  {language === 'ar' ? 'اختر فئة' : 'Select a category'}
                </option>
                {productCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {language === 'ar' ? category.name.ar : category.name.en}
                  </option>
                ))}
              </select>
            </div>
            
            {/* العلامات */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'العلامات' : 'Tags'}
              </label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder={language === 'ar' ? 'أضف علامة...' : 'Add a tag...'}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addTag}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.tags?.map((tag) => (
                  <div
                    key={tag}
                    className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
                      themeMode === 'dark'
                        ? "bg-slate-700 text-white"
                        : "bg-slate-100 text-slate-800"
                    )}
                  >
                    <Tag className="h-3 w-3" />
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 text-slate-500 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* المواصفات */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium">
                {language === 'ar' ? 'المواصفات' : 'Specifications'}
              </label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <Input
                placeholder={language === 'ar' ? 'المفتاح' : 'Key'}
                value={specKey}
                onChange={(e) => setSpecKey(e.target.value)}
              />
              <Input
                placeholder={language === 'ar' ? 'القيمة' : 'Value'}
                value={specValue}
                onChange={(e) => setSpecValue(e.target.value)}
              />
            </div>
            
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addSpecification}
              className="flex items-center gap-1 mb-4"
              disabled={!specKey || !specValue}
            >
              <Plus className="h-4 w-4" />
              <span>{language === 'ar' ? 'إضافة مواصفة' : 'Add Specification'}</span>
            </Button>
            
            {Object.keys(formData.specifications || {}).length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className={cn(
                    "text-xs",
                    themeMode === 'dark' ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
                  )}>
                    <tr>
                      <th className="px-4 py-2 text-left">{language === 'ar' ? 'المفتاح' : 'Key'}</th>
                      <th className="px-4 py-2 text-left">{language === 'ar' ? 'القيمة' : 'Value'}</th>
                      <th className="px-4 py-2 text-right">{language === 'ar' ? 'الإجراءات' : 'Actions'}</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
                    {Object.entries(formData.specifications || {}).map(([key, value]) => (
                      <tr key={key} className={themeMode === 'dark' ? "bg-slate-800" : "bg-white"}>
                        <td className="px-4 py-2">{key}</td>
                        <td className="px-4 py-2">{value}</td>
                        <td className="px-4 py-2 text-right">
                          <button
                            type="button"
                            onClick={() => removeSpecification(key)}
                            className={cn(
                              "p-1 rounded-md",
                              themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                            )}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                {language === 'ar'
                  ? 'لا توجد مواصفات. أضف بعض المواصفات للمنتج.'
                  : 'No specifications. Add some specifications for the product.'
                }
              </p>
            )}
          </div>
          
          {/* الصور */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium">
                {language === 'ar' ? 'الصور' : 'Images'}
              </label>
            </div>
            
            <div className="flex flex-wrap gap-4 mb-4">
              {formData.images?.map((image, index) => (
                <div key={index} className="relative w-24 h-24 group">
                  <Image 
                    src={image || '/images/placeholder.png'} // Added placeholder
                    alt={`Product ${index + 1}`}
                    width={96}
                    height={96}
                    className="object-cover rounded-md"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 p-1 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
              
              <div className="w-24 h-24 border-2 border-dashed rounded-md flex items-center justify-center">
                <div className="text-center">
                  <Upload className="h-6 w-6 mx-auto text-slate-400" />
                  <span className="text-xs text-slate-500 mt-1">
                    {language === 'ar' ? 'إضافة صورة' : 'Add Image'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2 items-center">
              <Input
                placeholder={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                value={newImageUrl} // Use newImageUrl state
                onChange={(e) => setNewImageUrl(e.target.value)} // Update newImageUrl state
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (newImageUrl) {
                    addImage(newImageUrl); // Use newImageUrl state
                    setNewImageUrl(''); // Clear newImageUrl state
                  }
                }}
                disabled={!newImageUrl} // Depend on newImageUrl state
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* خيارات إضافية */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured || false}
                onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
                className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
              />
              <span>{language === 'ar' ? 'منتج مميز' : 'Featured Product'}</span>
            </label>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              {language === 'ar' ? 'حفظ المنتج' : 'Save Product'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
