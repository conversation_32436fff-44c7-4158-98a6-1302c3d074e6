'use client';

import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Eye,
  ArrowUpDown
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { products } from '../../../data/products';
import { Product } from '../../../types/index';
import { ProductForm } from './ProductForm';
import Image from 'next/image';

// مكون إدارة المنتجات
export function ProductsManager() {
  const { language } = useLanguageStore();
  const { themeMode } = useThemeStore();

  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof Product>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // حالة المنتجات
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);

  // استخراج الفئات الفريدة
  const categories = Array.from(new Set(products.map(product => product.category)));

  // تصفية وترتيب المنتجات
  useEffect(() => {
    let result = [...products];

    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query)
      );
    }

    // تطبيق تصفية الفئة
    if (selectedCategory) {
      result = result.filter(product => product.category === selectedCategory);
    }

    // تطبيق الترتيب
    result.sort((a, b) => {
      const fieldA = a[sortField];
      const fieldB = b[sortField];

      if (typeof fieldA === 'string' && typeof fieldB === 'string') {
        return sortDirection === 'asc'
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      }

      if (typeof fieldA === 'number' && typeof fieldB === 'number') {
        return sortDirection === 'asc'
          ? fieldA - fieldB
          : fieldB - fieldA;
      }

      return 0;
    });

    setFilteredProducts(result);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedCategory, sortField, sortDirection, products]);

  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // الحصول على المنتجات للصفحة الحالية
  const currentProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Product) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // تحرير منتج
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };

  // إضافة منتج جديد
  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowProductForm(true);
  };

  // حذف منتج
  const handleDeleteProduct = (productId: string) => {
    // هنا سيتم تنفيذ منطق حذف المنتج
    console.log('Delete product:', productId);

    // في الإنتاج، سيتم استدعاء API لحذف المنتج
    // وتحديث قائمة المنتجات
  };

  // حفظ المنتج (إضافة أو تحديث)
  const handleSaveProduct = (product: Product) => {
    if (editingProduct) {
      // تحديث منتج موجود
      console.log('Update product:', product);

      // في الإنتاج، سيتم استدعاء API لتحديث المنتج
      // وتحديث قائمة المنتجات
    } else {
      // إضافة منتج جديد
      console.log('Add product:', product);

      // في الإنتاج، سيتم استدعاء API لإضافة المنتج
      // وتحديث قائمة المنتجات
    }

    setShowProductForm(false);
  };

  // عرض تفاصيل المنتج
  const handleViewProduct = (productId: string) => {
    // هنا سيتم تنفيذ منطق عرض تفاصيل المنتج
    console.log('View product:', productId);

    // في الإنتاج، سيتم الانتقال إلى صفحة تفاصيل المنتج
    // أو فتح نافذة منبثقة لعرض التفاصيل
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {language === 'ar' ? 'إدارة المنتجات' : 'Products Management'}
            </h1>
            <p className="text-slate-600 dark:text-slate-400">
              {language === 'ar'
                ? 'إدارة المنتجات والفئات والمخزون'
                : 'Manage products, categories, and inventory'}
            </p>
          </div>

          <Button
            onClick={handleAddProduct}
            className="flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            <span>{language === 'ar' ? 'إضافة منتج' : 'Add Product'}</span>
          </Button>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card className={cn(
          "p-4",
          themeMode === 'dark' ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
              <Input
                type="text"
                placeholder={language === 'ar' ? 'البحث عن المنتجات...' : 'Search products...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="w-full md:w-64">
              <select
                value={selectedCategory || ''}
                onChange={(e) => setSelectedCategory(e.target.value || null)}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  themeMode === 'dark'
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-gray-300 text-slate-900"
                )}
              >
                <option value="">
                  {language === 'ar' ? 'جميع الفئات' : 'All Categories'}
                </option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Card>

        {/* جدول المنتجات */}
        <Card className={cn(
          "overflow-hidden",
          themeMode === 'dark' ? "bg-slate-800" : "bg-white"
        )}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className={cn(
                "text-xs uppercase",
                themeMode === 'dark' ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
              )}>
                <tr>
                  <th className="px-6 py-3 text-left">
                    <button
                      onClick={() => handleSort('name')}
                      className="flex items-center gap-1"
                    >
                      {language === 'ar' ? 'اسم المنتج' : 'Product Name'}
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="px-6 py-3 text-left">
                    <button
                      onClick={() => handleSort('category')}
                      className="flex items-center gap-1"
                    >
                      {language === 'ar' ? 'الفئة' : 'Category'}
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="px-6 py-3 text-right">
                    <button
                      onClick={() => handleSort('price')}
                      className="flex items-center gap-1 ml-auto"
                    >
                      {language === 'ar' ? 'السعر' : 'Price'}
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="px-6 py-3 text-right">
                    <button
                      className="flex items-center gap-1 ml-auto"
                    >
                      {language === 'ar' ? 'المخزون' : 'Stock'}
                      <ArrowUpDown className="h-4 w-4" />
                    </button>
                  </th>
                  <th className="px-6 py-3 text-center">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className="px-6 py-3 text-right">
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y dark:divide-slate-700">
                {currentProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <Image
                          src={product.images && product.images.length > 0 ? product.images[0] : '/images/placeholder.png'}
                          alt={product.name}
                          width={40}
                          height={40}
                          className="rounded-md object-cover"
                        />
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            ID: {product.id}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      {product.price.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                        style: 'currency',
                        currency: 'SAR'
                      })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      {product.stock || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        (product.stock || 0) > 0
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                      )}>
                        {(product.stock || 0) > 0
                          ? (language === 'ar' ? 'متوفر' : 'In Stock')
                          : (language === 'ar' ? 'نفذ المخزون' : 'Out of Stock')
                        }
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => handleViewProduct(product.id.toString())}
                          className={cn(
                            "p-1 rounded-md",
                            themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Eye className="h-5 w-5 text-blue-500" />
                        </button>
                        <button
                          onClick={() => handleEditProduct(product)}
                          className={cn(
                            "p-1 rounded-md",
                            themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Edit className="h-5 w-5 text-yellow-500" />
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(product.id.toString())}
                          className={cn(
                            "p-1 rounded-md",
                            themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                          )}
                        >
                          <Trash2 className="h-5 w-5 text-red-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* ترقيم الصفحات */}
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {language === 'ar'
                  ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} من ${filteredProducts.length} منتج`
                  : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} of ${filteredProducts.length} products`
                }
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={cn(
                  "p-2 rounded-md",
                  currentPage === 1
                    ? "opacity-50 cursor-not-allowed"
                    : themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                )}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              <span className="text-sm">
                {language === 'ar'
                  ? `${currentPage} من ${totalPages}`
                  : `${currentPage} of ${totalPages}`
                }
              </span>

              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={cn(
                  "p-2 rounded-md",
                  currentPage === totalPages
                    ? "opacity-50 cursor-not-allowed"
                    : themeMode === 'dark' ? "hover:bg-slate-700" : "hover:bg-gray-100"
                )}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        </Card>
      </div>

      {/* نموذج إضافة/تحرير المنتج */}
      {showProductForm && (
        <ProductForm
          product={editingProduct}
          onSave={handleSaveProduct}
          onCancel={() => setShowProductForm(false)}
        />
      )}
    </>
  );
}
