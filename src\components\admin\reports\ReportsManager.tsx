'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Calendar, 
  Download, 
  Printer,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  ShoppingCart,
  Users,
  Package
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// مكون إدارة التقارير
export function ReportsManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة التبويب النشط
  const [activeTab, setActiveTab] = useState<'sales' | 'customers' | 'products' | 'inventory'>('sales');
  
  // حالة نطاق التاريخ
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'year'>('month');
  
  // بيانات تجريبية للإحصائيات
  const stats = {
    sales: {
      total: 125000,
      change: 12.5,
      isPositive: true
    },
    orders: {
      total: 568,
      change: 8.2,
      isPositive: true
    },
    customers: {
      total: 1245,
      change: 5.3,
      isPositive: true
    },
    aov: { // Average Order Value
      total: 220,
      change: 2.1,
      isPositive: false
    }
  };
  
  // بيانات تجريبية للمبيعات الشهرية
  const monthlySalesData = [
    { month: 'Jan', sales: 65000 },
    { month: 'Feb', sales: 72000 },
    { month: 'Mar', sales: 68000 },
    { month: 'Apr', sales: 75000 },
    { month: 'May', sales: 82000 },
    { month: 'Jun', sales: 90000 },
    { month: 'Jul', sales: 95000 },
    { month: 'Aug', sales: 102000 },
    { month: 'Sep', sales: 110000 },
    { month: 'Oct', sales: 115000 },
    { month: 'Nov', sales: 125000 },
    { month: 'Dec', sales: 135000 }
  ];
  
  // بيانات تجريبية لتوزيع المبيعات حسب الفئة
  const salesByCategory = [
    { category: 'Electronics', percentage: 35 },
    { category: 'Machinery', percentage: 25 },
    { category: 'Software', percentage: 20 },
    { category: 'Services', percentage: 15 },
    { category: 'Other', percentage: 5 }
  ];
  
  // بيانات تجريبية للمنتجات الأكثر مبيعًا
  const topProducts = [
    { id: 1, name: 'Smart Factory IoT Sensor Kit', sales: 120, revenue: 359998.8 },
    { id: 2, name: 'Industrial Automation Controller', sales: 95, revenue: 237500 },
    { id: 3, name: 'Commercial Grade 3D Printer', sales: 82, revenue: 328000 },
    { id: 4, name: 'Warehouse Management System', sales: 78, revenue: 195000 },
    { id: 5, name: 'Supply Chain Optimization Software', sales: 65, revenue: 162500 }
  ];
  
  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, change }: { 
    title: string;
    value: string | number;
    icon: React.ReactNode;
    change: {
      value: number;
      isPositive: boolean;
    };
  }) => (
    <Card className={cn(
      "p-6",
      isDarkMode ? "bg-slate-800" : "bg-white"
    )}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-slate-600 dark:text-slate-300">{title}</h3>
        <div className={cn(
          "p-3 rounded-full",
          isDarkMode ? "bg-slate-700" : "bg-slate-100"
        )}>
          {icon}
        </div>
      </div>
      <div className="flex items-end justify-between">
        <div>
          <p className="text-2xl font-bold">{value}</p>
          <div className="flex items-center mt-2">
            {change.isPositive ? (
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={change.isPositive ? "text-green-500" : "text-red-500"}>
              {change.value}%
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'التقارير والتحليلات' : 'Reports & Analytics'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'تحليل أداء المتجر والمبيعات والعملاء'
              : 'Analyze store performance, sales, and customers'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-5 w-5" />
            <span>{language === 'ar' ? 'تصدير' : 'Export'}</span>
          </Button>
          
          <Button
            variant="outline"
            className="flex items-center gap-2"
          >
            <Printer className="h-5 w-5" />
            <span>{language === 'ar' ? 'طباعة' : 'Print'}</span>
          </Button>
        </div>
      </div>
      
      {/* علامات التبويب */}
      <div className={cn(
        "flex border-b overflow-x-auto",
        isDarkMode ? "border-slate-700" : "border-gray-200"
      )}>
        <button
          onClick={() => setActiveTab('sales')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium whitespace-nowrap",
            activeTab === 'sales'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <BarChart className="h-5 w-5" />
          <span>{language === 'ar' ? 'المبيعات' : 'Sales'}</span>
        </button>
        
        <button
          onClick={() => setActiveTab('customers')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium whitespace-nowrap",
            activeTab === 'customers'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <Users className="h-5 w-5" />
          <span>{language === 'ar' ? 'العملاء' : 'Customers'}</span>
        </button>
        
        <button
          onClick={() => setActiveTab('products')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium whitespace-nowrap",
            activeTab === 'products'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <Package className="h-5 w-5" />
          <span>{language === 'ar' ? 'المنتجات' : 'Products'}</span>
        </button>
        
        <button
          onClick={() => setActiveTab('inventory')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium whitespace-nowrap",
            activeTab === 'inventory'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <LineChart className="h-5 w-5" />
          <span>{language === 'ar' ? 'المخزون' : 'Inventory'}</span>
        </button>
      </div>
      
      {/* أدوات التصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={dateRange === 'today' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDateRange('today')}
          >
            {language === 'ar' ? 'اليوم' : 'Today'}
          </Button>
          <Button
            variant={dateRange === 'week' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDateRange('week')}
          >
            {language === 'ar' ? 'هذا الأسبوع' : 'This Week'}
          </Button>
          <Button
            variant={dateRange === 'month' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDateRange('month')}
          >
            {language === 'ar' ? 'هذا الشهر' : 'This Month'}
          </Button>
          <Button
            variant={dateRange === 'year' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDateRange('year')}
          >
            {language === 'ar' ? 'هذا العام' : 'This Year'}
          </Button>
          
          <div className="ml-auto flex items-center gap-2">
            <Calendar className="h-5 w-5 text-slate-400" />
            <span className="text-sm text-slate-600 dark:text-slate-300">
              {language === 'ar' ? 'تخصيص النطاق' : 'Custom Range'}
            </span>
          </div>
        </div>
      </Card>
      
      {/* محتوى التبويب النشط */}
      {activeTab === 'sales' && (
        <div className="space-y-6">
          {/* الإحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title={language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
              value={stats.sales.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                style: 'currency',
                currency: 'SAR'
              })}
              icon={<DollarSign className="h-6 w-6 text-primary-500" />}
              change={stats.sales}
            />
            <StatCard
              title={language === 'ar' ? 'الطلبات' : 'Orders'}
              value={stats.orders.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
              icon={<ShoppingCart className="h-6 w-6 text-secondary-500" />}
              change={stats.orders}
            />
            <StatCard
              title={language === 'ar' ? 'العملاء' : 'Customers'}
              value={stats.customers.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
              icon={<Users className="h-6 w-6 text-yellow-500" />}
              change={stats.customers}
            />
            <StatCard
              title={language === 'ar' ? 'متوسط قيمة الطلب' : 'Avg. Order Value'}
              value={stats.aov.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                style: 'currency',
                currency: 'SAR'
              })}
              icon={<BarChart className="h-6 w-6 text-red-500" />}
              change={stats.aov}
            />
          </div>
          
          {/* الرسوم البيانية */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className={cn(
              "lg:col-span-2 p-6",
              isDarkMode ? "bg-slate-800" : "bg-white"
            )}>
              <h3 className="text-lg font-medium mb-6">
                {language === 'ar' ? 'المبيعات الشهرية' : 'Monthly Sales'}
              </h3>
              <div className="h-80 flex items-end justify-between gap-2">
                {monthlySalesData.map((data, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div 
                      className="w-12 bg-primary-500 rounded-t"
                      style={{ 
                        height: `${(data.sales / 135000) * 100}%`,
                        minHeight: '10px'
                      }}
                    ></div>
                    <span className="text-xs mt-2 text-slate-600 dark:text-slate-400">
                      {data.month}
                    </span>
                  </div>
                ))}
              </div>
            </Card>
            
            <Card className={cn(
              "p-6",
              isDarkMode ? "bg-slate-800" : "bg-white"
            )}>
              <h3 className="text-lg font-medium mb-6">
                {language === 'ar' ? 'المبيعات حسب الفئة' : 'Sales by Category'}
              </h3>
              <div className="h-80 flex items-center justify-center">
                <div className="relative w-48 h-48">
                  {salesByCategory.map((category, index) => {
                    const colors = ['#A855F7', '#06B6D4', '#FBBF24', '#EC4899', '#8B5CF6'];
                    const startAngle = salesByCategory
                      .slice(0, index)
                      .reduce((sum, cat) => sum + cat.percentage, 0) * 3.6;
                    const endAngle = startAngle + category.percentage * 3.6;
                    
                    return (
                      <div 
                        key={index}
                        className="absolute inset-0"
                        style={{
                          background: `conic-gradient(transparent ${startAngle}deg, ${colors[index % colors.length]} ${startAngle}deg, ${colors[index % colors.length]} ${endAngle}deg, transparent ${endAngle}deg)`,
                          borderRadius: '50%'
                        }}
                      ></div>
                    );
                  })}
                  <div 
                    className={cn(
                      "absolute inset-0 m-auto w-24 h-24 rounded-full flex items-center justify-center",
                      isDarkMode ? "bg-slate-800" : "bg-white"
                    )}
                  >
                    <PieChart className="h-10 w-10 text-primary-500" />
                  </div>
                </div>
              </div>
              <div className="space-y-2 mt-4">
                {salesByCategory.map((category, index) => {
                  const colors = ['#A855F7', '#06B6D4', '#FBBF24', '#EC4899', '#8B5CF6'];
                  
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: colors[index % colors.length] }}
                        ></div>
                        <span className="text-sm">
                          {language === 'ar' 
                            ? category.category === 'Electronics' ? 'إلكترونيات'
                              : category.category === 'Machinery' ? 'آلات'
                              : category.category === 'Software' ? 'برمجيات'
                              : category.category === 'Services' ? 'خدمات'
                              : 'أخرى'
                            : category.category
                          }
                        </span>
                      </div>
                      <span className="text-sm font-medium">
                        {category.percentage}%
                      </span>
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>
          
          {/* المنتجات الأكثر مبيعًا */}
          <Card className={cn(
            "p-6",
            isDarkMode ? "bg-slate-800" : "bg-white"
          )}>
            <h3 className="text-lg font-medium mb-6">
              {language === 'ar' ? 'المنتجات الأكثر مبيعًا' : 'Top Selling Products'}
            </h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={cn(
                  "text-xs uppercase",
                  isDarkMode ? "text-slate-400" : "text-slate-600"
                )}>
                  <tr>
                    <th className="px-4 py-2 text-left">
                      {language === 'ar' ? 'المنتج' : 'Product'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {language === 'ar' ? 'المبيعات' : 'Sales'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {language === 'ar' ? 'الإيرادات' : 'Revenue'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y dark:divide-slate-700">
                  {topProducts.map((product) => (
                    <tr key={product.id}>
                      <td className="px-4 py-3">
                        {product.name}
                      </td>
                      <td className="px-4 py-3 text-right">
                        {product.sales.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </td>
                      <td className="px-4 py-3 text-right font-medium">
                        {product.revenue.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
                          style: 'currency',
                          currency: 'SAR'
                        })}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </div>
      )}
      
      {/* محتوى التبويبات الأخرى */}
      {activeTab !== 'sales' && (
        <div className="flex items-center justify-center p-12">
          <div className="text-center">
            <div className={cn(
              "w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4",
              isDarkMode ? "bg-slate-700" : "bg-slate-100"
            )}>
              {activeTab === 'customers' ? (
                <Users className="h-8 w-8 text-primary-500" />
              ) : activeTab === 'products' ? (
                <Package className="h-8 w-8 text-primary-500" />
              ) : (
                <LineChart className="h-8 w-8 text-primary-500" />
              )}
            </div>
            <h3 className="text-xl font-medium mb-2">
              {language === 'ar' 
                ? activeTab === 'customers' ? 'تقارير العملاء'
                  : activeTab === 'products' ? 'تقارير المنتجات'
                  : 'تقارير المخزون'
                : activeTab === 'customers' ? 'Customer Reports'
                  : activeTab === 'products' ? 'Product Reports'
                  : 'Inventory Reports'
              }
            </h3>
            <p className="text-slate-600 dark:text-slate-400 max-w-md">
              {language === 'ar'
                ? 'سيتم تنفيذ هذا القسم قريبًا. يرجى التحقق مرة أخرى لاحقًا.'
                : 'This section will be implemented soon. Please check back later.'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
