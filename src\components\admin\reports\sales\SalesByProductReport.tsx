'use client';

import { useState } from 'react';
import { 
  Download, 
  Printer, 
  Search, 
  ArrowUpDown, 
  BarChart,
  Package,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Card } from '../../../../components/ui/Card';
import { Input } from '../../../../components/ui/Input';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع المنتج
interface Product {
  id: string;
  name: string;
  name_ar?: string;
  category: string;
  sku: string;
  price: number;
  quantity: number;
  revenue: number;
  profit: number;
  profitMargin: number;
  salesCount: number;
  returnRate: number;
}

// مكون تقرير المبيعات حسب المنتج
export function SalesByProductReport() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [sortField, setSortField] = useState<keyof Product>('revenue');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  // بيانات تجريبية للمنتجات
  const products: Product[] = [
    {
      id: 'p1',
      name: 'Smart Factory IoT Sensor Kit',
      name_ar: 'مجموعة أجهزة استشعار إنترنت الأشياء للمصانع الذكية',
      category: 'Electronics',
      sku: 'IOT-001',
      price: 2999.99,
      quantity: 120,
      revenue: 359998.8,
      profit: 143999.52,
      profitMargin: 40,
      salesCount: 120,
      returnRate: 2.5
    },
    {
      id: 'p2',
      name: 'Industrial Automation Controller',
      name_ar: 'وحدة تحكم الأتمتة الصناعية',
      category: 'Electronics',
      sku: 'IAC-002',
      price: 2500,
      quantity: 95,
      revenue: 237500,
      profit: 83125,
      profitMargin: 35,
      salesCount: 95,
      returnRate: 1.8
    },
    {
      id: 'p3',
      name: 'Commercial Grade 3D Printer',
      name_ar: 'طابعة ثلاثية الأبعاد للاستخدام التجاري',
      category: 'Machinery',
      sku: '3DP-003',
      price: 4000,
      quantity: 82,
      revenue: 328000,
      profit: 147600,
      profitMargin: 45,
      salesCount: 82,
      returnRate: 3.2
    },
    {
      id: 'p4',
      name: 'Warehouse Management System',
      name_ar: 'نظام إدارة المستودعات',
      category: 'Software',
      sku: 'WMS-004',
      price: 2500,
      quantity: 78,
      revenue: 195000,
      profit: 136500,
      profitMargin: 70,
      salesCount: 78,
      returnRate: 0.5
    },
    {
      id: 'p5',
      name: 'Supply Chain Optimization Software',
      name_ar: 'برنامج تحسين سلسلة التوريد',
      category: 'Software',
      sku: 'SCO-005',
      price: 2500,
      quantity: 65,
      revenue: 162500,
      profit: 113750,
      profitMargin: 70,
      salesCount: 65,
      returnRate: 0.8
    },
    {
      id: 'p6',
      name: 'Industrial Robot Arm',
      name_ar: 'ذراع روبوت صناعي',
      category: 'Machinery',
      sku: 'IRA-006',
      price: 8500,
      quantity: 42,
      revenue: 357000,
      profit: 142800,
      profitMargin: 40,
      salesCount: 42,
      returnRate: 1.2
    },
    {
      id: 'p7',
      name: 'Quality Control Scanner',
      name_ar: 'ماسح ضبط الجودة',
      category: 'Electronics',
      sku: 'QCS-007',
      price: 1800,
      quantity: 110,
      revenue: 198000,
      profit: 79200,
      profitMargin: 40,
      salesCount: 110,
      returnRate: 2.1
    },
    {
      id: 'p8',
      name: 'Manufacturing Analytics Platform',
      name_ar: 'منصة تحليلات التصنيع',
      category: 'Software',
      sku: 'MAP-008',
      price: 3000,
      quantity: 55,
      revenue: 165000,
      profit: 115500,
      profitMargin: 70,
      salesCount: 55,
      returnRate: 0.3
    },
    {
      id: 'p9',
      name: 'Conveyor Belt System',
      name_ar: 'نظام السير الناقل',
      category: 'Machinery',
      sku: 'CBS-009',
      price: 5500,
      quantity: 38,
      revenue: 209000,
      profit: 73150,
      profitMargin: 35,
      salesCount: 38,
      returnRate: 0.9
    },
    {
      id: 'p10',
      name: 'Predictive Maintenance Toolkit',
      name_ar: 'مجموعة أدوات الصيانة التنبؤية',
      category: 'Software',
      sku: 'PMT-010',
      price: 1500,
      quantity: 92,
      revenue: 138000,
      profit: 96600,
      profitMargin: 70,
      salesCount: 92,
      returnRate: 0.4
    },
    {
      id: 'p11',
      name: 'Smart Safety Glasses',
      name_ar: 'نظارات السلامة الذكية',
      category: 'Safety',
      sku: 'SSG-011',
      price: 450,
      quantity: 210,
      revenue: 94500,
      profit: 37800,
      profitMargin: 40,
      salesCount: 210,
      returnRate: 1.5
    },
    {
      id: 'p12',
      name: 'Industrial Air Purifier',
      name_ar: 'منقي الهواء الصناعي',
      category: 'Safety',
      sku: 'IAP-012',
      price: 1200,
      quantity: 75,
      revenue: 90000,
      profit: 36000,
      profitMargin: 40,
      salesCount: 75,
      returnRate: 0.7
    }
  ];
  
  // الحصول على الفئات الفريدة
  const categories = Array.from(new Set(products.map(product => product.category)));
  
  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    // تطبيق البحث
    const searchMatch = searchQuery === '' || 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.name_ar && product.name_ar.includes(searchQuery)) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase());
    
    // تطبيق تصفية الفئة
    const categoryMatch = selectedCategory === null || product.category === selectedCategory;
    
    return searchMatch && categoryMatch;
  });
  
  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return sortDirection === 'asc'
      ? (aValue > bValue ? 1 : -1)
      : (aValue < bValue ? 1 : -1);
  });
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);
  
  // الحصول على المنتجات للصفحة الحالية
  const currentProducts = sortedProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Product) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // تنسيق العملة
  const formatCurrency = (value: number) => {
    return value.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    });
  };
  
  // تنسيق النسبة المئوية
  const formatPercentage = (value: number) => {
    return value.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    });
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'تقرير المبيعات حسب المنتج' : 'Sales by Product Report'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'تحليل أداء المبيعات لكل منتج'
              : 'Analyze sales performance for each product'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            <span>{language === 'ar' ? 'تصدير' : 'Export'}</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <Printer className="h-4 w-4" />
            <span>{language === 'ar' ? 'طباعة' : 'Print'}</span>
          </Button>
        </div>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن المنتجات...' : 'Search products...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedCategory || ''}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الفئات' : 'All Categories'}
              </option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>
      
      {/* ملخص الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className={cn(
          "p-6",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex items-center gap-4 mb-2">
            <div className={cn(
              "p-3 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-100"
            )}>
              <Package className="h-6 w-6 text-primary-500" />
            </div>
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {language === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
              </p>
              <h3 className="text-2xl font-bold">{products.length}</h3>
            </div>
          </div>
        </Card>
        
        <Card className={cn(
          "p-6",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex items-center gap-4 mb-2">
            <div className={cn(
              "p-3 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-100"
            )}>
              <BarChart className="h-6 w-6 text-secondary-500" />
            </div>
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
              </p>
              <h3 className="text-2xl font-bold">
                {products.reduce((sum, product) => sum + product.salesCount, 0).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
              </h3>
            </div>
          </div>
        </Card>
        
        <Card className={cn(
          "p-6",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex items-center gap-4 mb-2">
            <div className={cn(
              "p-3 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-100"
            )}>
              <BarChart className="h-6 w-6 text-yellow-500" />
            </div>
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {language === 'ar' ? 'إجمالي الإيرادات' : 'Total Revenue'}
              </p>
              <h3 className="text-2xl font-bold">
                {formatCurrency(products.reduce((sum, product) => sum + product.revenue, 0))}
              </h3>
            </div>
          </div>
        </Card>
        
        <Card className={cn(
          "p-6",
          isDarkMode ? "bg-slate-800" : "bg-white"
        )}>
          <div className="flex items-center gap-4 mb-2">
            <div className={cn(
              "p-3 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-100"
            )}>
              <BarChart className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {language === 'ar' ? 'إجمالي الأرباح' : 'Total Profit'}
              </p>
              <h3 className="text-2xl font-bold">
                {formatCurrency(products.reduce((sum, product) => sum + product.profit, 0))}
              </h3>
            </div>
          </div>
        </Card>
      </div>
      
      {/* جدول المنتجات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'المنتج' : 'Product'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('category')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الفئة' : 'Category'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('price')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'السعر' : 'Price'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('salesCount')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'المبيعات' : 'Sales'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('revenue')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'الإيرادات' : 'Revenue'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('profit')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'الربح' : 'Profit'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  <button
                    onClick={() => handleSort('profitMargin')}
                    className="flex items-center gap-1 ml-auto"
                  >
                    {language === 'ar' ? 'هامش الربح' : 'Margin'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium">{language === 'ar' && product.name_ar ? product.name_ar : product.name}</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">{product.sku}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                    )}>
                      {product.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-right">
                    {formatCurrency(product.price)}
                  </td>
                  <td className="px-6 py-4 text-right">
                    {product.salesCount.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </td>
                  <td className="px-6 py-4 text-right">
                    {formatCurrency(product.revenue)}
                  </td>
                  <td className="px-6 py-4 text-right">
                    {formatCurrency(product.profit)}
                  </td>
                  <td className="px-6 py-4 text-right">
                    {formatPercentage(product.profitMargin / 100)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} من ${filteredProducts.length} منتج`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredProducts.length)} of ${filteredProducts.length} products`
              }
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
