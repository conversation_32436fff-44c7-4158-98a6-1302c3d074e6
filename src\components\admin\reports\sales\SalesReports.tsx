'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  Download, 
  Printer, 
  Calendar, 
  ArrowUpRight, 
  ArrowDownRight,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Filter
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// مكون تقارير المبيعات
export function SalesReports() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة نطاق التاريخ
  const [dateRange, setDateRange] = useState<'day' | 'week' | 'month' | 'year' | 'custom'>('month');
  
  // حالة نوع التقرير
  const [reportType, setReportType] = useState<'summary' | 'detailed'>('summary');
  
  // حالة تصفية البيانات
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    minAmount: '',
    maxAmount: '',
    paymentMethod: 'all',
    status: 'all'
  });
  
  // بيانات تجريبية للإحصائيات
  const stats = {
    sales: {
      total: 125000,
      change: 12.5,
      isPositive: true
    },
    orders: {
      total: 568,
      change: 8.2,
      isPositive: true
    },
    customers: {
      total: 1245,
      change: 5.3,
      isPositive: true
    },
    aov: { // Average Order Value
      total: 220,
      change: 2.1,
      isPositive: false
    }
  };
  
  // بيانات تجريبية للمبيعات اليومية
  const dailySalesData = Array.from({ length: 30 }, (_, i) => ({
    date: new Date(new Date().setDate(new Date().getDate() - 29 + i)).toLocaleDateString(),
    sales: Math.floor(Math.random() * 10000) + 5000
  }));
  
  // بيانات تجريبية للمبيعات الأسبوعية
  const weeklySalesData = Array.from({ length: 12 }, (_, i) => ({
    week: `W${i + 1}`,
    sales: Math.floor(Math.random() * 50000) + 20000
  }));
  
  // بيانات تجريبية للمبيعات الشهرية
  const monthlySalesData = [
    { month: 'Jan', sales: 65000 },
    { month: 'Feb', sales: 72000 },
    { month: 'Mar', sales: 68000 },
    { month: 'Apr', sales: 75000 },
    { month: 'May', sales: 82000 },
    { month: 'Jun', sales: 90000 },
    { month: 'Jul', sales: 95000 },
    { month: 'Aug', sales: 102000 },
    { month: 'Sep', sales: 110000 },
    { month: 'Oct', sales: 115000 },
    { month: 'Nov', sales: 125000 },
    { month: 'Dec', sales: 135000 }
  ];
  
  // بيانات تجريبية للمبيعات السنوية
  const yearlySalesData = Array.from({ length: 5 }, (_, i) => ({
    year: `${new Date().getFullYear() - 4 + i}`,
    sales: Math.floor(Math.random() * 500000) + 800000
  }));
  
  // تحديث نطاق التاريخ
  const handleDateRangeChange = (range: 'day' | 'week' | 'month' | 'year' | 'custom') => {
    setDateRange(range);
    
    // تحديث تواريخ التصفية بناءً على النطاق المحدد
    const today = new Date();
    let startDate = new Date();
    
    switch (range) {
      case 'day':
        startDate = today;
        break;
      case 'week':
        startDate = new Date(today.setDate(today.getDate() - 7));
        break;
      case 'month':
        startDate = new Date(today.setMonth(today.getMonth() - 1));
        break;
      case 'year':
        startDate = new Date(today.setFullYear(today.getFullYear() - 1));
        break;
      default:
        // الإبقاء على التواريخ الحالية للنطاق المخصص
        return;
    }
    
    setFilters(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    }));
  };
  
  // تحديث التصفية
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // الحصول على بيانات المبيعات بناءً على نطاق التاريخ
  const getSalesData = () => {
    switch (dateRange) {
      case 'day':
        return dailySalesData;
      case 'week':
        return weeklySalesData;
      case 'year':
        return yearlySalesData;
      default:
        return monthlySalesData;
    }
  };
  
  // مكون بطاقة الإحصائيات
  const StatCard = ({ title, value, icon, change }: { 
    title: string;
    value: string | number;
    icon: React.ReactNode;
    change: {
      value: number;
      isPositive: boolean;
    };
  }) => (
    <Card className={cn(
      "p-6",
      isDarkMode ? "bg-slate-800" : "bg-white"
    )}>
      <div className="flex justify-between items-start mb-4">
        <div className={cn(
          "p-3 rounded-lg",
          isDarkMode ? "bg-slate-700" : "bg-slate-100"
        )}>
          {icon}
        </div>
        <div className={cn(
          "flex items-center px-2 py-1 rounded-full text-xs font-medium",
          change.isPositive
            ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
            : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
        )}>
          {change.isPositive ? <ArrowUpRight className="h-3 w-3 mr-1" /> : <ArrowDownRight className="h-3 w-3 mr-1" />}
          {change.value}%
        </div>
      </div>
      <h3 className="text-2xl font-bold mb-1">{value}</h3>
      <p className="text-sm text-slate-500 dark:text-slate-400">{title}</p>
    </Card>
  );
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'تقارير المبيعات' : 'Sales Reports'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'تحليل أداء المبيعات والإيرادات'
              : 'Analyze sales performance and revenue'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            <span>{language === 'ar' ? 'تصدير' : 'Export'}</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <Printer className="h-4 w-4" />
            <span>{language === 'ar' ? 'طباعة' : 'Print'}</span>
          </Button>
        </div>
      </div>
      
      {/* أدوات التصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={dateRange === 'day' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange('day')}
            >
              {language === 'ar' ? 'اليوم' : 'Today'}
            </Button>
            <Button
              variant={dateRange === 'week' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange('week')}
            >
              {language === 'ar' ? 'هذا الأسبوع' : 'This Week'}
            </Button>
            <Button
              variant={dateRange === 'month' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange('month')}
            >
              {language === 'ar' ? 'هذا الشهر' : 'This Month'}
            </Button>
            <Button
              variant={dateRange === 'year' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange('year')}
            >
              {language === 'ar' ? 'هذا العام' : 'This Year'}
            </Button>
            <Button
              variant={dateRange === 'custom' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange('custom')}
              className="flex items-center gap-1"
            >
              <Calendar className="h-4 w-4" />
              <span>{language === 'ar' ? 'مخصص' : 'Custom'}</span>
            </Button>
          </div>
          
          <div className="flex items-center gap-2 ml-auto">
            <Button
              variant={reportType === 'summary' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setReportType('summary')}
            >
              {language === 'ar' ? 'ملخص' : 'Summary'}
            </Button>
            <Button
              variant={reportType === 'detailed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setReportType('detailed')}
            >
              {language === 'ar' ? 'مفصل' : 'Detailed'}
            </Button>
          </div>
        </div>
        
        {dateRange === 'custom' && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'من تاريخ' : 'From Date'}
              </label>
              <input
                type="date"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'إلى تاريخ' : 'To Date'}
              </label>
              <input
                type="date"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'طريقة الدفع' : 'Payment Method'}
              </label>
              <select
                name="paymentMethod"
                value={filters.paymentMethod}
                onChange={handleFilterChange}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              >
                <option value="all">{language === 'ar' ? 'الكل' : 'All'}</option>
                <option value="credit_card">{language === 'ar' ? 'بطاقة ائتمان' : 'Credit Card'}</option>
                <option value="bank_transfer">{language === 'ar' ? 'تحويل بنكي' : 'Bank Transfer'}</option>
                <option value="cash">{language === 'ar' ? 'نقدي' : 'Cash'}</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الحالة' : 'Status'}
              </label>
              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className={cn(
                  "w-full px-3 py-2 rounded-md border",
                  isDarkMode 
                    ? "bg-slate-700 border-slate-600 text-white" 
                    : "bg-white border-gray-300 text-slate-900"
                )}
              >
                <option value="all">{language === 'ar' ? 'الكل' : 'All'}</option>
                <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                <option value="processing">{language === 'ar' ? 'قيد المعالجة' : 'Processing'}</option>
                <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
              </select>
            </div>
          </div>
        )}
      </Card>
      
      {/* الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
          value={stats.sales.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: 'SAR'
          })}
          icon={<DollarSign className="h-6 w-6 text-primary-500" />}
          change={stats.sales}
        />
        <StatCard
          title={language === 'ar' ? 'الطلبات' : 'Orders'}
          value={stats.orders.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
          icon={<ShoppingCart className="h-6 w-6 text-secondary-500" />}
          change={stats.orders}
        />
        <StatCard
          title={language === 'ar' ? 'العملاء' : 'Customers'}
          value={stats.customers.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
          icon={<Users className="h-6 w-6 text-yellow-500" />}
          change={stats.customers}
        />
        <StatCard
          title={language === 'ar' ? 'متوسط قيمة الطلب' : 'Avg. Order Value'}
          value={stats.aov.total.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: 'SAR'
          })}
          icon={<BarChart className="h-6 w-6 text-red-500" />}
          change={stats.aov}
        />
      </div>
      
      {/* الرسم البياني */}
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <h3 className="text-lg font-medium mb-6">
          {language === 'ar' 
            ? dateRange === 'day' ? 'المبيعات اليومية'
              : dateRange === 'week' ? 'المبيعات الأسبوعية'
              : dateRange === 'year' ? 'المبيعات السنوية'
              : 'المبيعات الشهرية'
            : dateRange === 'day' ? 'Daily Sales'
              : dateRange === 'week' ? 'Weekly Sales'
              : dateRange === 'year' ? 'Yearly Sales'
              : 'Monthly Sales'
          }
        </h3>
        <div className="h-80 flex items-end justify-between gap-2">
          {getSalesData().map((data, index) => (
            <div key={index} className="flex flex-col items-center">
              <div 
                className="w-12 bg-primary-500 rounded-t"
                style={{ 
                  height: `${(data.sales / Math.max(...getSalesData().map(d => d.sales))) * 100}%`,
                  minHeight: '10px'
                }}
              ></div>
              <span className="text-xs mt-2 text-slate-600 dark:text-slate-400">
                {dateRange === 'day' ? data.date
                  : dateRange === 'week' ? data.week
                  : dateRange === 'year' ? data.year
                  : data.month
                }
              </span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
