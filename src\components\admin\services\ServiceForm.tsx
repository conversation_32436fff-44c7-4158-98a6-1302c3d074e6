'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Trash2, Upload } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { Service } from '../../../types';

interface ServiceFormProps {
  service: Service | null;
  onSave: (service: Service) => void;
  onCancel: () => void;
}

export function ServiceForm({ service, onSave, onCancel }: ServiceFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<Service>>({
    id: '',
    name: '',
    name_ar: '',
    slug: '',
    description: '',
    description_ar: '',
    icon: '',
    features: [],
    features_ar: [],
    createdAt: new Date().toISOString(),
  });
  
  // تحميل بيانات الخدمة إذا كانت موجودة
  useEffect(() => {
    if (service) {
      setFormData({
        ...service,
      });
    }
  }, [service]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // إنشاء slug من الاسم
  const generateSlug = () => {
    const slug = formData.name
      ?.toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    setFormData(prev => ({
      ...prev,
      slug,
    }));
  };
  
  // إضافة ميزة جديدة
  const addFeature = (language: 'en' | 'ar') => {
    if (language === 'en') {
      setFormData(prev => ({
        ...prev,
        features: [...(prev.features || []), ''],
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        features_ar: [...(prev.features_ar || []), ''],
      }));
    }
  };
  
  // تحديث ميزة
  const updateFeature = (index: number, value: string, language: 'en' | 'ar') => {
    if (language === 'en') {
      const updatedFeatures = [...(formData.features || [])];
      updatedFeatures[index] = value;
      setFormData(prev => ({
        ...prev,
        features: updatedFeatures,
      }));
    } else {
      const updatedFeatures = [...(formData.features_ar || [])];
      updatedFeatures[index] = value;
      setFormData(prev => ({
        ...prev,
        features_ar: updatedFeatures,
      }));
    }
  };
  
  // حذف ميزة
  const removeFeature = (index: number, language: 'en' | 'ar') => {
    if (language === 'en') {
      const updatedFeatures = [...(formData.features || [])];
      updatedFeatures.splice(index, 1);
      setFormData(prev => ({
        ...prev,
        features: updatedFeatures,
      }));
    } else {
      const updatedFeatures = [...(formData.features_ar || [])];
      updatedFeatures.splice(index, 1);
      setFormData(prev => ({
        ...prev,
        features_ar: updatedFeatures,
      }));
    }
  };
  
  // حفظ الخدمة
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.description) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    // إنشاء معرف جديد إذا كانت خدمة جديدة
    const serviceData: Service = {
      ...(formData as Service),
      id: service?.id || `service-${Date.now()}`,
      createdAt: service?.createdAt || new Date().toISOString(),
    };
    
    onSave(serviceData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {service
              ? language === 'ar' ? 'تحرير الخدمة' : 'Edit Service'
              : language === 'ar' ? 'إضافة خدمة جديدة' : 'Add New Service'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم الخدمة (بالإنجليزية)' : 'Service Name (English)'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                onBlur={generateSlug}
                required
              />
            </div>
            
            {/* الاسم بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'اسم الخدمة (بالعربية)' : 'Service Name (Arabic)'}
              </label>
              <Input
                name="name_ar"
                value={formData.name_ar || ''}
                onChange={handleChange}
                dir="rtl"
              />
            </div>
            
            {/* Slug */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الرابط الثابت (Slug)' : 'Slug'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="slug"
                value={formData.slug || ''}
                onChange={handleChange}
                required
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'سيتم استخدام هذا في عنوان URL للخدمة'
                  : 'This will be used in the service URL'
                }
              </p>
            </div>
            
            {/* الأيقونة */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الأيقونة' : 'Icon'}
              </label>
              <Input
                name="icon"
                value={formData.icon || ''}
                onChange={handleChange}
                placeholder="e.g. Briefcase, Shield, etc."
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {language === 'ar'
                  ? 'اسم الأيقونة من مكتبة Lucide React'
                  : 'Icon name from Lucide React library'
                }
              </p>
            </div>
          </div>
          
          {/* الوصف */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الوصف بالإنجليزية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الوصف (بالإنجليزية)' : 'Description (English)'}
                <span className="text-red-500">*</span>
              </label>
              <textarea
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                required
                rows={4}
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-300 text-slate-900"
                )}
              />
            </div>
            
            {/* الوصف بالعربية */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الوصف (بالعربية)' : 'Description (Arabic)'}
              </label>
              <textarea
                name="description_ar"
                value={formData.description_ar || ''}
                onChange={handleChange}
                rows={4}
                dir="rtl"
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-300 text-slate-900"
                )}
              />
            </div>
          </div>
          
          {/* الميزات */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الميزات بالإنجليزية */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  {language === 'ar' ? 'الميزات (بالإنجليزية)' : 'Features (English)'}
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addFeature('en')}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>{language === 'ar' ? 'إضافة ميزة' : 'Add Feature'}</span>
                </Button>
              </div>
              
              <div className="space-y-2">
                {formData.features?.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={feature}
                      onChange={(e) => updateFeature(index, e.target.value, 'en')}
                      placeholder={`Feature ${index + 1}`}
                    />
                    <button
                      type="button"
                      onClick={() => removeFeature(index, 'en')}
                      className={cn(
                        "p-2 rounded-md",
                        isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                      )}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                  </div>
                ))}
                
                {(!formData.features || formData.features.length === 0) && (
                  <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                    {language === 'ar'
                      ? 'لا توجد ميزات. انقر على "إضافة ميزة" لإضافة واحدة.'
                      : 'No features. Click "Add Feature" to add one.'
                    }
                  </p>
                )}
              </div>
            </div>
            
            {/* الميزات بالعربية */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  {language === 'ar' ? 'الميزات (بالعربية)' : 'Features (Arabic)'}
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addFeature('ar')}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>{language === 'ar' ? 'إضافة ميزة' : 'Add Feature'}</span>
                </Button>
              </div>
              
              <div className="space-y-2">
                {formData.features_ar?.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={feature}
                      onChange={(e) => updateFeature(index, e.target.value, 'ar')}
                      placeholder={`الميزة ${index + 1}`}
                      dir="rtl"
                    />
                    <button
                      type="button"
                      onClick={() => removeFeature(index, 'ar')}
                      className={cn(
                        "p-2 rounded-md",
                        isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                      )}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                  </div>
                ))}
                
                {(!formData.features_ar || formData.features_ar.length === 0) && (
                  <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                    {language === 'ar'
                      ? 'لا توجد ميزات. انقر على "إضافة ميزة" لإضافة واحدة.'
                      : 'No features. Click "Add Feature" to add one.'
                    }
                  </p>
                )}
              </div>
            </div>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              {language === 'ar' ? 'حفظ الخدمة' : 'Save Service'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
