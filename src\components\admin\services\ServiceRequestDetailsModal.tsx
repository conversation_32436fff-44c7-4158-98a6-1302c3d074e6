'use client';

import { 
  X, 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Clock,
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Briefcase,
  Building,
  MessageSquare,
  Send,
  Printer,
  Download
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// نوع طلب الخدمة
interface ServiceRequest {
  id: string;
  serviceName: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  companyName: string;
  message: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: string;
}

interface ServiceRequestDetailsModalProps {
  request: ServiceRequest;
  onClose: () => void;
  onUpdateStatus: (requestId: string, status: string) => void;
}

export function ServiceRequestDetailsModal({ 
  request, 
  onClose, 
  onUpdateStatus
}: ServiceRequestDetailsModalProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // ترجمة حالة طلب الخدمة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'in-progress':
        return language === 'ar' ? 'قيد المعالجة' : 'In Progress';
      case 'completed':
        return language === 'ar' ? 'مكتمل' : 'Completed';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة طلب الخدمة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case 'in-progress':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case 'completed':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case 'cancelled':
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };
  
  // الحصول على أيقونة حالة طلب الخدمة
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5" />;
      case 'in-progress':
        return <AlertCircle className="h-5 w-5" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5" />;
      default:
        return null;
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-3xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-bold">
              {language === 'ar' ? 'تفاصيل طلب الخدمة' : 'Service Request Details'}
            </h2>
            <span className="text-sm text-slate-500 dark:text-slate-400">
              {request.id}
            </span>
          </div>
          <button
            onClick={onClose}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          {/* معلومات الطلب */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <Briefcase className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'الخدمة المطلوبة' : 'Requested Service'}
                </h3>
              </div>
              <p className="font-bold text-lg">{request.serviceName}</p>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <Calendar className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'تاريخ الطلب' : 'Request Date'}
                </h3>
              </div>
              <p>
                {new Date(request.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>
          
          {/* حالة الطلب */}
          <div className={cn(
            "p-4 rounded-lg",
            isDarkMode ? "bg-slate-700" : "bg-slate-50"
          )}>
            <div className="flex items-center gap-2 mb-3">
              <Clock className="h-5 w-5 text-primary-500" />
              <h3 className="font-medium">
                {language === 'ar' ? 'حالة الطلب' : 'Request Status'}
              </h3>
            </div>
            <div className="flex items-center justify-between">
              <span className={cn(
                "px-2 py-1 rounded-full text-xs flex items-center gap-1",
                getStatusColor(request.status)
              )}>
                {getStatusIcon(request.status)}
                <span>{getStatusText(request.status)}</span>
              </span>
              
              <select
                value={request.status}
                onChange={(e) => onUpdateStatus(request.id, e.target.value)}
                className={cn(
                  "px-2 py-1 rounded-md border text-sm",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-300 text-slate-900"
                )}
              >
                <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                <option value="in-progress">{language === 'ar' ? 'قيد المعالجة' : 'In Progress'}</option>
                <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
              </select>
            </div>
          </div>
          
          {/* معلومات العميل */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'معلومات العميل' : 'Customer Information'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <User className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'بيانات العميل' : 'Customer Details'}
                  </h4>
                </div>
                <p className="font-bold">{request.customerName}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Mail className="h-4 w-4 text-slate-400" />
                  <p>{request.customerEmail}</p>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Phone className="h-4 w-4 text-slate-400" />
                  <p>{request.customerPhone}</p>
                </div>
              </div>
              
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <Building className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'الشركة' : 'Company'}
                  </h4>
                </div>
                <p className="font-bold">{request.companyName}</p>
              </div>
            </div>
          </div>
          
          {/* تفاصيل الطلب */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'تفاصيل الطلب' : 'Request Details'}
            </h3>
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <MessageSquare className="h-5 w-5 text-primary-500" />
                <h4 className="font-medium">
                  {language === 'ar' ? 'الرسالة' : 'Message'}
                </h4>
              </div>
              <p className="whitespace-pre-line">{request.message}</p>
            </div>
          </div>
          
          {/* الإجراءات */}
          <div className="flex flex-wrap justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Printer className="h-5 w-5" />
              <span>{language === 'ar' ? 'طباعة الطلب' : 'Print Request'}</span>
            </Button>
            
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-5 w-5" />
              <span>{language === 'ar' ? 'تنزيل PDF' : 'Download PDF'}</span>
            </Button>
            
            <Button
              className="flex items-center gap-2"
            >
              <Send className="h-5 w-5" />
              <span>{language === 'ar' ? 'إرسال رد للعميل' : 'Send Response'}</span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
