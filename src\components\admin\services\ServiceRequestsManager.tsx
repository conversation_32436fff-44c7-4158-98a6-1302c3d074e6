'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Eye,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Calendar,
  Mail,
  Phone,
  Briefcase
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { ServiceRequestDetailsModal } from './ServiceRequestDetailsModal';

// نوع طلب الخدمة
interface ServiceRequest {
  id: string;
  serviceName: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  companyName: string;
  message: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: string;
}

// بيانات تجريبية لطلبات الخدمة
const mockServiceRequests: ServiceRequest[] = [
  {
    id: 'sr-001',
    serviceName: 'Inspection Services',
    customerName: 'Ahmed Ali',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 50 123 4567',
    companyName: 'Global Trading Co.',
    message: 'We need inspection services for our upcoming shipment from China.',
    status: 'pending',
    createdAt: '2023-06-15T10:30:00Z',
  },
  {
    id: 'sr-002',
    serviceName: 'Logistics Consulting',
    customerName: 'Sarah Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '****** 123 4567',
    companyName: 'Johnson Imports',
    message: 'Looking for logistics consulting to optimize our supply chain.',
    status: 'in-progress',
    createdAt: '2023-06-10T14:45:00Z',
  },
  {
    id: 'sr-003',
    serviceName: 'Sourcing Services',
    customerName: 'Mohammed Hassan',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 55 987 6543',
    companyName: 'Hassan Trading',
    message: 'Need help sourcing electronics components from Asian markets.',
    status: 'completed',
    createdAt: '2023-06-05T09:15:00Z',
  },
  {
    id: 'sr-004',
    serviceName: 'Quality Control',
    customerName: 'Li Wei',
    customerEmail: '<EMAIL>',
    customerPhone: '+86 123 4567 8901',
    companyName: 'Wei Manufacturing',
    message: 'Requesting quality control services for our production line.',
    status: 'cancelled',
    createdAt: '2023-06-01T11:20:00Z',
  },
  {
    id: 'sr-005',
    serviceName: 'Market Research',
    customerName: 'Fatima Al-Saud',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 54 765 4321',
    companyName: 'Al-Saud Enterprises',
    message: 'Need comprehensive market research for expanding into GCC markets.',
    status: 'pending',
    createdAt: '2023-06-18T08:00:00Z',
  },
];

// مكون إدارة طلبات الخدمات
export function ServiceRequestsManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof ServiceRequest>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // حالة تفاصيل الطلب
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null);
  const [showRequestDetails, setShowRequestDetails] = useState(false);

  // حالة البيانات
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load service requests from API
  const loadServiceRequests = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('/api/service-bookings');
      const result = await response.json();

      if (result.success) {
        // Transform API data to match component expectations
        const transformedRequests = result.data.bookings.map((booking: any) => ({
          id: booking.id,
          serviceName: booking.serviceName || booking.service_name,
          customerName: booking.customerName || booking.customer_name,
          customerEmail: booking.customerEmail || booking.customer_email,
          customerPhone: booking.customerPhone || booking.customer_phone,
          companyName: booking.companyName || booking.company_name,
          message: booking.message || '',
          status: booking.status || 'pending',
          createdAt: booking.createdAt || booking.created_at
        }));

        setServiceRequests(transformedRequests);
      } else {
        setError(result.error || 'Failed to load service requests');
        // Fallback to mock data if API fails
        setServiceRequests(mockServiceRequests);
      }
    } catch (error) {
      console.error('Error loading service requests:', error);
      setError('Failed to load service requests');
      // Fallback to mock data if API fails
      setServiceRequests(mockServiceRequests);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadServiceRequests();
  }, []);

  // تصفية طلبات الخدمة بناءً على البحث والحالة
  const filteredRequests = serviceRequests.filter((request) => {
    const searchRegex = new RegExp(searchQuery, 'i');
    const matchesSearch =
      searchRegex.test(request.serviceName) ||
      searchRegex.test(request.customerName) ||
      searchRegex.test(request.customerEmail) ||
      searchRegex.test(request.companyName);

    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // ترتيب طلبات الخدمة
  const sortedRequests = [...filteredRequests].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedRequests.length / itemsPerPage);

  // الحصول على طلبات الخدمة للصفحة الحالية
  const currentRequests = sortedRequests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // تغيير ترتيب الحقل
  const handleSort = (field: keyof ServiceRequest) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // عرض تفاصيل طلب الخدمة
  const handleViewRequest = (requestId: string) => {
    const request = serviceRequests.find(r => r.id === requestId);
    if (request) {
      setSelectedRequest(request);
      setShowRequestDetails(true);
    }
  };

  // تحديث حالة طلب الخدمة
  const handleUpdateStatus = async (requestId: string, status: string) => {
    try {
      const response = await fetch('/api/service-bookings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId: requestId,
          status: status
        })
      });

      const result = await response.json();

      if (result.success) {
        // Update local state
        setServiceRequests(prev =>
          prev.map(request =>
            request.id === requestId
              ? { ...request, status: status as any }
              : request
          )
        );

        // Update selected request if it's the one being updated
        if (selectedRequest && selectedRequest.id === requestId) {
          setSelectedRequest(prev => prev ? { ...prev, status: status as any } : null);
        }

        console.log('Service request status updated successfully');
      } else {
        console.error('Failed to update service request status:', result.error);
        setError(result.error || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating service request status:', error);
      setError('Failed to update status');
    }
  };

  // ترجمة حالة طلب الخدمة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'in-progress':
        return language === 'ar' ? 'قيد المعالجة' : 'In Progress';
      case 'completed':
        return language === 'ar' ? 'مكتمل' : 'Completed';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };

  // الحصول على لون حالة طلب الخدمة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-500';
      case 'in-progress':
        return 'text-blue-500';
      case 'completed':
        return 'text-green-500';
      case 'cancelled':
        return 'text-red-500';
      default:
        return 'text-slate-500';
    }
  };

  // الحصول على أيقونة حالة طلب الخدمة
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'in-progress':
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة طلبات الخدمات' : 'Service Requests Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar'
              ? 'إدارة ومتابعة طلبات الخدمات من العملاء'
              : 'Manage and track service requests from customers'}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <Card className="p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800">
            <div className="flex items-center text-red-600 dark:text-red-400">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                className="ml-auto"
                onClick={() => {
                  setError(null);
                  loadServiceRequests();
                }}
              >
                {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
              </Button>
            </div>
          </Card>
        )}

      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن طلبات الخدمات...' : 'Search service requests...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</option>
              <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
              <option value="in-progress">{language === 'ar' ? 'قيد المعالجة' : 'In Progress'}</option>
              <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
              <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
            </select>

            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>

            <Button variant="outline">
              <Filter className="h-5 w-5" />
              <span className="ml-2">{language === 'ar' ? 'تصفية' : 'Filter'}</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* جدول طلبات الخدمات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('id')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'رقم الطلب' : 'Request ID'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('serviceName')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الخدمة' : 'Service'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('customerName')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'العميل' : 'Customer'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('createdAt')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'التاريخ' : 'Date'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('status')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الحالة' : 'Status'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                      <span className="ml-3 text-slate-500 dark:text-slate-400">
                        {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
                      </span>
                    </div>
                  </td>
                </tr>
              ) : currentRequests.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="text-slate-500 dark:text-slate-400">
                      {language === 'ar' ? 'لا توجد طلبات خدمة' : 'No service requests found'}
                    </div>
                  </td>
                </tr>
              ) : (
                currentRequests.map((request) => (
                <tr
                  key={request.id}
                  className={cn(
                    "hover:bg-gray-50 dark:hover:bg-slate-700/50",
                    isDarkMode ? "bg-slate-800" : "bg-white"
                  )}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium">{request.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Briefcase className="h-5 w-5 text-primary-500 mr-3" />
                      <span>{request.serviceName}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="font-medium">{request.customerName}</div>
                    <div className="text-sm text-slate-500 dark:text-slate-400 flex items-center">
                      <Mail className="h-3 w-3 mr-1" />
                      {request.customerEmail}
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400 flex items-center">
                      <Phone className="h-3 w-3 mr-1" />
                      {request.customerPhone}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-slate-400" />
                      <span>
                        {new Date(request.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400">
                      {new Date(request.createdAt).toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={cn(
                      "flex items-center gap-2",
                      getStatusColor(request.status)
                    )}>
                      {getStatusIcon(request.status)}
                      <span>{getStatusText(request.status)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewRequest(request.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>

                      <select
                        className={cn(
                          "px-2 py-1 rounded-md border text-sm",
                          isDarkMode
                            ? "bg-slate-700 border-slate-600 text-white"
                            : "bg-white border-slate-300 text-slate-900"
                        )}
                        value={request.status}
                        onChange={(e) => handleUpdateStatus(request.id, e.target.value)}
                      >
                        <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                        <option value="in-progress">{language === 'ar' ? 'قيد المعالجة' : 'In Progress'}</option>
                        <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                        <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                      </select>
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* ترقيم الصفحات */}
        <div className={cn(
          "px-6 py-4 flex items-center justify-between border-t",
          isDarkMode ? "border-slate-700" : "border-gray-200"
        )}>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {language === 'ar'
              ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, sortedRequests.length)} من ${sortedRequests.length} طلب`
              : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, sortedRequests.length)} of ${sortedRequests.length} requests`}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>

      {/* نافذة تفاصيل طلب الخدمة */}
      {showRequestDetails && selectedRequest && (
        <ServiceRequestDetailsModal
          request={selectedRequest}
          onClose={() => setShowRequestDetails(false)}
          onUpdateStatus={handleUpdateStatus}
        />
      )}
    </>
  );
}
