'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Eye,
  ArrowUpDown,
  Check,
  X,
  Briefcase
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { services } from '../../../data/services';
import { Service } from '../../../types';
import { ServiceForm } from './ServiceForm';

// مكون إدارة الخدمات
export function ServicesManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof Service>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // حالة النموذج
  const [showServiceForm, setShowServiceForm] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  
  // تصفية الخدمات بناءً على البحث
  const filteredServices = services.filter((service) => {
    const searchRegex = new RegExp(searchQuery, 'i');
    return (
      searchRegex.test(service.name) ||
      searchRegex.test(service.name_ar || '') ||
      searchRegex.test(service.description) ||
      searchRegex.test(service.description_ar || '')
    );
  });
  
  // ترتيب الخدمات
  const sortedServices = [...filteredServices].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedServices.length / itemsPerPage);
  
  // الحصول على الخدمات للصفحة الحالية
  const currentServices = sortedServices.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof Service) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // تحرير خدمة
  const handleEditService = (service: Service) => {
    setEditingService(service);
    setShowServiceForm(true);
  };
  
  // إضافة خدمة جديدة
  const handleAddService = () => {
    setEditingService(null);
    setShowServiceForm(true);
  };
  
  // حذف خدمة
  const handleDeleteService = (serviceId: string | number) => {
    // هنا سيتم تنفيذ منطق حذف الخدمة
    console.log('Delete service:', serviceId);
    
    // في الإنتاج، سيتم استدعاء API لحذف الخدمة
    // وتحديث قائمة الخدمات
  };
  
  // عرض تفاصيل الخدمة
  const handleViewService = (serviceId: string | number) => {
    // هنا سيتم تنفيذ منطق عرض تفاصيل الخدمة
    console.log('View service:', serviceId);
    
    // في الإنتاج، سيتم الانتقال إلى صفحة تفاصيل الخدمة
    // أو فتح نافذة منبثقة لعرض التفاصيل
  };
  
  // حفظ الخدمة (إضافة أو تحديث)
  const handleSaveService = (service: Service) => {
    if (editingService) {
      // تحديث خدمة موجودة
      console.log('Update service:', service);
      
      // في الإنتاج، سيتم استدعاء API لتحديث الخدمة
      // وتحديث قائمة الخدمات
    } else {
      // إضافة خدمة جديدة
      console.log('Add service:', service);
      
      // في الإنتاج، سيتم استدعاء API لإضافة الخدمة
      // وتحديث قائمة الخدمات
    }
    
    setShowServiceForm(false);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة الخدمات' : 'Services Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة الخدمات وطلبات الخدمات'
              : 'Manage services and service requests'}
          </p>
        </div>
        
        <Button
          onClick={handleAddService}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة خدمة' : 'Add Service'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن الخدمات...' : 'Search services...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            
            <Button variant="outline">
              <Filter className="h-5 w-5" />
              <span className="ml-2">{language === 'ar' ? 'تصفية' : 'Filter'}</span>
            </Button>
          </div>
        </div>
      </Card>
      
      {/* جدول الخدمات */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'اسم الخدمة' : 'Service Name'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الميزات' : 'Features'}
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
              {currentServices.map((service) => (
                <tr 
                  key={service.id}
                  className={cn(
                    "hover:bg-gray-50 dark:hover:bg-slate-700/50",
                    isDarkMode ? "bg-slate-800" : "bg-white"
                  )}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Briefcase className="h-5 w-5 text-primary-500 mr-3" />
                      <div>
                        <div className="font-medium">
                          {language === 'ar' ? service.name_ar || service.name : service.name}
                        </div>
                        <div className="text-sm text-slate-500 dark:text-slate-400">
                          {service.slug}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm line-clamp-2">
                      {language === 'ar' ? service.description_ar || service.description : service.description}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      {(language === 'ar' ? service.features_ar || service.features : service.features).slice(0, 2).map((feature, index) => (
                        <div key={index} className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-1" />
                          <span className="line-clamp-1">{feature}</span>
                        </div>
                      ))}
                      {service.features.length > 2 && (
                        <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          {language === 'ar' 
                            ? `+${service.features.length - 2} ميزات أخرى`
                            : `+${service.features.length - 2} more features`}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewService(service.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditService(service)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeleteService(service.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        <div className={cn(
          "px-6 py-4 flex items-center justify-between border-t",
          isDarkMode ? "border-slate-700" : "border-gray-200"
        )}>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {language === 'ar'
              ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, sortedServices.length)} من ${sortedServices.length} خدمة`
              : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, sortedServices.length)} of ${sortedServices.length} services`}
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
      
      {/* نموذج إضافة/تحرير الخدمة */}
      {showServiceForm && (
        <ServiceForm
          service={editingService}
          onSave={handleSaveService}
          onCancel={() => setShowServiceForm(false)}
        />
      )}
    </div>
  );
}
