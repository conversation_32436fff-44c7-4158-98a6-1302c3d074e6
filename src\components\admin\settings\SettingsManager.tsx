'use client';

import { useState } from 'react';
import {
  Store,
  Truck,
  CreditCard,
  Globe,
  DollarSign,
  Shield,
  Tag,
  Mail,
  Search
} from 'lucide-react';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
// استيراد مكونات إعدادات المتجر
import { StoreSettings } from './store/StoreSettings';
import { PaymentSettings } from './store/PaymentSettings';

// استيراد مكونات إعدادات النظام
import { LanguageSettings } from './system/LanguageSettings';
import { CurrencySettings } from './system/CurrencySettings';
import { SecuritySettings } from './system/SecuritySettings';

// استيراد مكونات إعدادات التسويق
import { MarketingSettings } from '../settings/marketing/MarketingSettings';
import { EmailSettings } from '../settings/marketing/EmailSettings';
import { SEOSettings } from '../settings/marketing/SEOSettings';

// مكون إدارة الإعدادات
export function SettingsManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  // حالة التبويب النشط
  const [activeTab, setActiveTab] = useState<'store' | 'system' | 'marketing'>('store');

  // حالة القسم النشط
  const [activeSection, setActiveSection] = useState<string>('company');

  // تحديد القسم النشط بناءً على التبويب
  const handleTabChange = (tab: 'store' | 'system' | 'marketing') => {
    setActiveTab(tab);

    // تعيين القسم الافتراضي لكل تبويب
    switch (tab) {
      case 'store':
        setActiveSection('company');
        break;
      case 'system':
        setActiveSection('languages');
        break;
      case 'marketing':
        setActiveSection('promotions');
        break;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">
          {language === 'ar' ? 'الإعدادات والتكوين' : 'Settings & Configuration'}
        </h1>
        <p className="text-slate-600 dark:text-slate-400">
          {language === 'ar'
            ? 'إدارة إعدادات المتجر والنظام والتسويق'
            : 'Manage store, system, and marketing settings'}
        </p>
      </div>

      {/* علامات التبويب الرئيسية */}
      <div className={cn(
        "flex border-b",
        isDarkMode ? "border-slate-700" : "border-gray-200"
      )}>
        <button
          onClick={() => handleTabChange('store')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'store'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <Store className="h-5 w-5" />
          <span>{language === 'ar' ? 'إعدادات المتجر' : 'Store Settings'}</span>
        </button>

        <button
          onClick={() => handleTabChange('system')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'system'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <Globe className="h-5 w-5" />
          <span>{language === 'ar' ? 'إعدادات النظام' : 'System Settings'}</span>
        </button>

        <button
          onClick={() => handleTabChange('marketing')}
          className={cn(
            "flex items-center gap-2 px-4 py-2 font-medium",
            activeTab === 'marketing'
              ? isDarkMode
                ? "border-b-2 border-primary-500 text-primary-400"
                : "border-b-2 border-primary-500 text-primary-600"
              : isDarkMode
                ? "text-slate-400 hover:text-white"
                : "text-slate-600 hover:text-slate-900"
          )}
        >
          <Tag className="h-5 w-5" />
          <span>{language === 'ar' ? 'إعدادات التسويق' : 'Marketing Settings'}</span>
        </button>
      </div>

      {/* محتوى الإعدادات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* القائمة الجانبية */}
        <div className="md:col-span-1">
          <div className={cn(
            "rounded-lg overflow-hidden",
            isDarkMode ? "bg-slate-800" : "bg-white"
          )}>
            {activeTab === 'store' && (
              <div className="divide-y dark:divide-slate-700">
                <button
                  onClick={() => setActiveSection('company')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'company'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Store className="h-5 w-5" />
                  <span>{language === 'ar' ? 'معلومات الشركة' : 'Company Information'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('shipping')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'shipping'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Truck className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إعدادات الشحن' : 'Shipping Settings'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('payment')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'payment'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <CreditCard className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إعدادات الدفع' : 'Payment Settings'}</span>
                </button>
              </div>
            )}

            {activeTab === 'system' && (
              <div className="divide-y dark:divide-slate-700">
                <button
                  onClick={() => setActiveSection('languages')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'languages'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Globe className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إدارة اللغات' : 'Language Management'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('currencies')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'currencies'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <DollarSign className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إدارة العملات' : 'Currency Management'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('security')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'security'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Shield className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إعدادات الأمان' : 'Security Settings'}</span>
                </button>
              </div>
            )}

            {activeTab === 'marketing' && (
              <div className="divide-y dark:divide-slate-700">
                <button
                  onClick={() => setActiveSection('promotions')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'promotions'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Tag className="h-5 w-5" />
                  <span>{language === 'ar' ? 'الكوبونات والعروض' : 'Coupons & Promotions'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('email')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'email'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Mail className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إعدادات البريد الإلكتروني' : 'Email Settings'}</span>
                </button>
                <button
                  onClick={() => setActiveSection('seo')}
                  className={cn(
                    "w-full px-4 py-3 text-left flex items-center gap-3",
                    activeSection === 'seo'
                      ? isDarkMode
                        ? "bg-slate-700 text-primary-400"
                        : "bg-slate-100 text-primary-600"
                      : ""
                  )}
                >
                  <Search className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إعدادات SEO' : 'SEO Settings'}</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* محتوى الإعدادات */}
        <div className="md:col-span-3">
          {/* إعدادات المتجر */}
          {activeTab === 'store' && activeSection === 'company' && <StoreSettings />}
          {activeTab === 'store' && activeSection === 'shipping' && <StoreSettings />}
          {activeTab === 'store' && activeSection === 'payment' && <PaymentSettings />}

          {/* إعدادات النظام */}
          {activeTab === 'system' && activeSection === 'languages' && <LanguageSettings />}
          {activeTab === 'system' && activeSection === 'currencies' && <CurrencySettings />}
          {activeTab === 'system' && activeSection === 'security' && <SecuritySettings />}

          {/* إعدادات التسويق */}
          {activeTab === 'marketing' && activeSection === 'promotions' && <MarketingSettings />}
          {activeTab === 'marketing' && activeSection === 'email' && <EmailSettings />}
          {activeTab === 'marketing' && activeSection === 'seo' && <SEOSettings />}
        </div>
      </div>
    </div>
  );
}
