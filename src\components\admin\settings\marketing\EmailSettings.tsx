'use client';

import { useState } from 'react';
import { Save, Mail, Send, Plus, Trash2, Edit } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع قالب البريد الإلكتروني
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  subject_ar?: string;
  body: string;
  body_ar?: string;
  type: 'transactional' | 'marketing';
  isActive: boolean;
}

// مكون إعدادات البريد الإلكتروني
export function EmailSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات البريد الإلكتروني
  const [emailSettings, setEmailSettings] = useState({
    // إعدادات SMTP
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: 'password123',
    smtpEncryption: 'tls',
    senderName: 'CommercePro',
    senderEmail: '<EMAIL>',
    
    // إعدادات عامة
    enableEmailNotifications: true,
    enableAdminNotifications: true,
    adminEmail: '<EMAIL>',
    
    // إعدادات النشرة الإخبارية
    enableNewsletter: true,
    newsletterFrequency: 'weekly',
    
    // إعدادات التخصيص
    emailLogoUrl: '/images/logo.png',
    emailFooterText: 'CommercePro © 2023. All rights reserved.',
    emailFooterText_ar: 'كوميرس برو © 2023. جميع الحقوق محفوظة.',
    primaryColor: '#6366f1',
    
    // إعدادات الاختبار
    testEmail: ''
  });
  
  // حالة قوالب البريد الإلكتروني
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([
    {
      id: 'template1',
      name: 'Order Confirmation',
      subject: 'Your order #{{order_number}} has been confirmed',
      subject_ar: 'تم تأكيد طلبك رقم #{{order_number}}',
      body: 'Dear {{customer_name}},\n\nThank you for your order. Your order #{{order_number}} has been confirmed and is being processed.\n\nOrder Details:\n{{order_details}}\n\nThank you for shopping with us!',
      body_ar: 'عزيزي {{customer_name}}،\n\nشكرًا لطلبك. تم تأكيد طلبك رقم #{{order_number}} وجاري معالجته.\n\nتفاصيل الطلب:\n{{order_details}}\n\nشكرًا للتسوق معنا!',
      type: 'transactional',
      isActive: true
    },
    {
      id: 'template2',
      name: 'Shipping Confirmation',
      subject: 'Your order #{{order_number}} has been shipped',
      subject_ar: 'تم شحن طلبك رقم #{{order_number}}',
      body: 'Dear {{customer_name}},\n\nYour order #{{order_number}} has been shipped and is on its way to you.\n\nTracking Number: {{tracking_number}}\n\nEstimated Delivery: {{delivery_date}}\n\nThank you for shopping with us!',
      body_ar: 'عزيزي {{customer_name}}،\n\nتم شحن طلبك رقم #{{order_number}} وهو في طريقه إليك.\n\nرقم التتبع: {{tracking_number}}\n\nموعد التسليم المتوقع: {{delivery_date}}\n\nشكرًا للتسوق معنا!',
      type: 'transactional',
      isActive: true
    },
    {
      id: 'template3',
      name: 'Welcome Email',
      subject: 'Welcome to CommercePro!',
      subject_ar: 'مرحبًا بك في كوميرس برو!',
      body: 'Dear {{customer_name}},\n\nWelcome to CommercePro! We\'re excited to have you as a new customer.\n\nYour account has been created successfully. You can now start shopping and enjoy our products and services.\n\nBest regards,\nThe CommercePro Team',
      body_ar: 'عزيزي {{customer_name}}،\n\nمرحبًا بك في كوميرس برو! نحن متحمسون لانضمامك كعميل جديد.\n\nتم إنشاء حسابك بنجاح. يمكنك الآن بدء التسوق والاستمتاع بمنتجاتنا وخدماتنا.\n\nمع أطيب التحيات،\nفريق كوميرس برو',
      type: 'transactional',
      isActive: true
    },
    {
      id: 'template4',
      name: 'Monthly Newsletter',
      subject: 'CommercePro Newsletter - {{month}} {{year}}',
      subject_ar: 'النشرة الإخبارية لكوميرس برو - {{month}} {{year}}',
      body: 'Dear {{customer_name}},\n\nHere are our latest products and offers for this month:\n\n{{newsletter_content}}\n\nThank you for being a valued customer!\n\nTo unsubscribe, click here: {{unsubscribe_link}}',
      body_ar: 'عزيزي {{customer_name}}،\n\nإليك أحدث منتجاتنا وعروضنا لهذا الشهر:\n\n{{newsletter_content}}\n\nشكرًا لكونك عميلًا قيمًا!\n\nلإلغاء الاشتراك، انقر هنا: {{unsubscribe_link}}',
      type: 'marketing',
      isActive: true
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [testEmailSent, setTestEmailSent] = useState(false);
  
  // تحديث إعدادات البريد الإلكتروني
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setEmailSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseInt(value) 
          : value
    }));
  };
  
  // إضافة قالب بريد إلكتروني جديد
  const addEmailTemplate = () => {
    const newTemplate: EmailTemplate = {
      id: `template_${Date.now()}`,
      name: 'New Template',
      subject: '',
      subject_ar: '',
      body: '',
      body_ar: '',
      type: 'transactional',
      isActive: false
    };
    
    setEmailTemplates([...emailTemplates, newTemplate]);
  };
  
  // حذف قالب بريد إلكتروني
  const deleteEmailTemplate = (templateId: string) => {
    setEmailTemplates(emailTemplates.filter(template => template.id !== templateId));
  };
  
  // تحديث قالب بريد إلكتروني
  const updateEmailTemplate = (templateId: string, field: string, value: any) => {
    setEmailTemplates(emailTemplates.map(template => {
      if (template.id === templateId) {
        return { ...template, [field]: value };
      }
      return template;
    }));
  };
  
  // إرسال بريد إلكتروني اختباري
  const sendTestEmail = async () => {
    if (!emailSettings.testEmail) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق إرسال بريد إلكتروني اختباري
      console.log('Test email sent to:', emailSettings.testEmail);
      
      setTestEmailSent(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setTestEmailSent(false);
      }, 3000);
    } catch (error) {
      console.error('Error sending test email:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // حفظ إعدادات البريد الإلكتروني
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات البريد الإلكتروني
      console.log('Email settings saved:', { emailSettings, emailTemplates });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving email settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات البريد الإلكتروني' : 'Email Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات البريد الإلكتروني بنجاح' : 'Email settings saved successfully'}
          </div>
        )}
        
        {testEmailSent && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' 
              ? `تم إرسال بريد إلكتروني اختباري إلى ${emailSettings.testEmail}` 
              : `Test email sent to ${emailSettings.testEmail}`
            }
          </div>
        )}
        
        <div className="space-y-6">
          {/* إعدادات SMTP */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات SMTP' : 'SMTP Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'خادم SMTP' : 'SMTP Host'}
                </label>
                <Input
                  name="smtpHost"
                  value={emailSettings.smtpHost}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'منفذ SMTP' : 'SMTP Port'}
                </label>
                <Input
                  type="number"
                  name="smtpPort"
                  value={emailSettings.smtpPort}
                  onChange={handleSettingsChange}
                  min="1"
                  max="65535"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم المستخدم' : 'Username'}
                </label>
                <Input
                  name="smtpUsername"
                  value={emailSettings.smtpUsername}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'كلمة المرور' : 'Password'}
                </label>
                <Input
                  type="password"
                  name="smtpPassword"
                  value={emailSettings.smtpPassword}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'التشفير' : 'Encryption'}
                </label>
                <select
                  name="smtpEncryption"
                  value={emailSettings.smtpEncryption}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                >
                  <option value="none">{language === 'ar' ? 'بدون' : 'None'}</option>
                  <option value="ssl">SSL</option>
                  <option value="tls">TLS</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم المرسل' : 'Sender Name'}
                </label>
                <Input
                  name="senderName"
                  value={emailSettings.senderName}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'بريد المرسل الإلكتروني' : 'Sender Email'}
                </label>
                <Input
                  type="email"
                  name="senderEmail"
                  value={emailSettings.senderEmail}
                  onChange={handleSettingsChange}
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات عامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableEmailNotifications"
                  name="enableEmailNotifications"
                  checked={emailSettings.enableEmailNotifications}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableEmailNotifications" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل إشعارات البريد الإلكتروني' : 'Enable Email Notifications'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableAdminNotifications"
                  name="enableAdminNotifications"
                  checked={emailSettings.enableAdminNotifications}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!emailSettings.enableEmailNotifications}
                />
                <label htmlFor="enableAdminNotifications" className={cn(
                  "text-sm font-medium",
                  !emailSettings.enableEmailNotifications && "text-gray-400"
                )}>
                  {language === 'ar' ? 'تفعيل إشعارات المسؤول' : 'Enable Admin Notifications'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'بريد المسؤول الإلكتروني' : 'Admin Email'}
                </label>
                <Input
                  type="email"
                  name="adminEmail"
                  value={emailSettings.adminEmail}
                  onChange={handleSettingsChange}
                  disabled={!emailSettings.enableEmailNotifications || !emailSettings.enableAdminNotifications}
                  className={(!emailSettings.enableEmailNotifications || !emailSettings.enableAdminNotifications) ? "opacity-50" : ""}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNewsletter"
                  name="enableNewsletter"
                  checked={emailSettings.enableNewsletter}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!emailSettings.enableEmailNotifications}
                />
                <label htmlFor="enableNewsletter" className={cn(
                  "text-sm font-medium",
                  !emailSettings.enableEmailNotifications && "text-gray-400"
                )}>
                  {language === 'ar' ? 'تفعيل النشرة الإخبارية' : 'Enable Newsletter'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'تكرار النشرة الإخبارية' : 'Newsletter Frequency'}
                </label>
                <select
                  name="newsletterFrequency"
                  value={emailSettings.newsletterFrequency}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!emailSettings.enableEmailNotifications || !emailSettings.enableNewsletter}
                >
                  <option value="daily">{language === 'ar' ? 'يوميًا' : 'Daily'}</option>
                  <option value="weekly">{language === 'ar' ? 'أسبوعيًا' : 'Weekly'}</option>
                  <option value="biweekly">{language === 'ar' ? 'كل أسبوعين' : 'Bi-weekly'}</option>
                  <option value="monthly">{language === 'ar' ? 'شهريًا' : 'Monthly'}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* اختبار البريد الإلكتروني */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Send className="h-5 w-5" />
              {language === 'ar' ? 'اختبار البريد الإلكتروني' : 'Email Testing'}
            </h3>
            
            <div className="flex gap-2">
              <Input
                type="email"
                name="testEmail"
                value={emailSettings.testEmail}
                onChange={handleSettingsChange}
                placeholder={language === 'ar' ? 'أدخل بريد إلكتروني للاختبار' : 'Enter email for testing'}
                disabled={!emailSettings.enableEmailNotifications}
                className={!emailSettings.enableEmailNotifications ? "opacity-50" : ""}
              />
              <Button
                type="button"
                onClick={sendTestEmail}
                disabled={!emailSettings.testEmail || !emailSettings.enableEmailNotifications || isLoading}
                className="flex items-center gap-2"
              >
                <Send className="h-4 w-4" />
                <span>{language === 'ar' ? 'إرسال اختبار' : 'Send Test'}</span>
              </Button>
            </div>
          </div>
          
          {/* قوالب البريد الإلكتروني */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Mail className="h-5 w-5" />
                {language === 'ar' ? 'قوالب البريد الإلكتروني' : 'Email Templates'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addEmailTemplate}
                className="flex items-center gap-1"
                disabled={!emailSettings.enableEmailNotifications}
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة قالب' : 'Add Template'}</span>
              </Button>
            </div>
            
            {!emailSettings.enableEmailNotifications && (
              <div className={cn(
                "p-3 rounded-md mb-4",
                isDarkMode ? "bg-slate-700" : "bg-slate-100"
              )}>
                <p className="text-sm">
                  {language === 'ar'
                    ? 'إشعارات البريد الإلكتروني معطلة حاليًا. قم بتفعيل إشعارات البريد الإلكتروني في الإعدادات العامة لإدارة القوالب.'
                    : 'Email notifications are currently disabled. Enable email notifications in general settings to manage templates.'
                  }
                </p>
              </div>
            )}
            
            <div className="space-y-4">
              {emailTemplates.map((template) => (
                <Card key={template.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50",
                  !emailSettings.enableEmailNotifications && "opacity-60"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`active-${template.id}`}
                          checked={template.isActive}
                          onChange={(e) => updateEmailTemplate(template.id, 'isActive', e.target.checked)}
                          className="h-4 w-4 mr-2"
                          disabled={!emailSettings.enableEmailNotifications}
                        />
                        <label htmlFor={`active-${template.id}`} className={cn(
                          "text-sm font-medium",
                          !emailSettings.enableEmailNotifications && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'مفعّل' : 'Active'}
                        </label>
                      </div>
                      
                      <span className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        template.type === 'transactional'
                          ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                          : "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
                      )}>
                        {template.type === 'transactional'
                          ? language === 'ar' ? 'معاملات' : 'Transactional'
                          : language === 'ar' ? 'تسويقي' : 'Marketing'
                        }
                      </span>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => deleteEmailTemplate(template.id)}
                      className={cn(
                        "p-2 rounded-md",
                        isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                      )}
                      disabled={!emailSettings.enableEmailNotifications}
                    >
                      <Trash2 className={cn(
                        "h-5 w-5",
                        !emailSettings.enableEmailNotifications ? "text-gray-400" : "text-red-500"
                      )} />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'اسم القالب' : 'Template Name'}
                      </label>
                      <div className="flex items-center">
                        <Edit className="h-4 w-4 mr-2 text-slate-400" />
                        <Input
                          value={template.name}
                          onChange={(e) => updateEmailTemplate(template.id, 'name', e.target.value)}
                          disabled={!emailSettings.enableEmailNotifications}
                          className={!emailSettings.enableEmailNotifications ? "opacity-50" : ""}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'نوع القالب' : 'Template Type'}
                      </label>
                      <select
                        value={template.type}
                        onChange={(e) => updateEmailTemplate(template.id, 'type', e.target.value)}
                        className={cn(
                          "w-full px-3 py-2 rounded-md border",
                          isDarkMode 
                            ? "bg-slate-700 border-slate-600 text-white" 
                            : "bg-white border-gray-300 text-slate-900"
                        )}
                        disabled={!emailSettings.enableEmailNotifications}
                      >
                        <option value="transactional">{language === 'ar' ? 'معاملات' : 'Transactional'}</option>
                        <option value="marketing">{language === 'ar' ? 'تسويقي' : 'Marketing'}</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الموضوع (بالإنجليزية)' : 'Subject (English)'}
                      </label>
                      <Input
                        value={template.subject}
                        onChange={(e) => updateEmailTemplate(template.id, 'subject', e.target.value)}
                        disabled={!emailSettings.enableEmailNotifications}
                        className={!emailSettings.enableEmailNotifications ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الموضوع (بالعربية)' : 'Subject (Arabic)'}
                      </label>
                      <Input
                        value={template.subject_ar || ''}
                        onChange={(e) => updateEmailTemplate(template.id, 'subject_ar', e.target.value)}
                        dir="rtl"
                        disabled={!emailSettings.enableEmailNotifications}
                        className={!emailSettings.enableEmailNotifications ? "opacity-50" : ""}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
