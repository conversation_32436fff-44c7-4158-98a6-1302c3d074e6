'use client';

import { useState } from 'react';
import { Save, Plus, Trash2, Tag, Calendar, Percent } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع الكوبون
interface Coupon {
  id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minPurchase?: number;
  maxDiscount?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
  applicableProducts: 'all' | 'specific';
  productIds?: string[];
  applicableCategories: 'all' | 'specific';
  categoryIds?: string[];
}

// مكون إعدادات التسويق
export function MarketingSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات التسويق
  const [marketingSettings, setMarketingSettings] = useState({
    enableCoupons: true,
    enablePromotions: true,
    enableAbandonedCartEmails: true,
    abandonedCartEmailDelay: 24,
    enableProductReviewEmails: true,
    enableNewsletterPopup: true,
    newsletterPopupDelay: 30,
    enableReferralProgram: false,
    referralDiscountAmount: 10,
    referralDiscountType: 'percentage'
  });
  
  // حالة الكوبونات
  const [coupons, setCoupons] = useState<Coupon[]>([
    {
      id: 'coupon1',
      code: 'WELCOME10',
      type: 'percentage',
      value: 10,
      minPurchase: 100,
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      isActive: true,
      usageLimit: 1000,
      usageCount: 450,
      applicableProducts: 'all',
      applicableCategories: 'all'
    },
    {
      id: 'coupon2',
      code: 'SUMMER25',
      type: 'percentage',
      value: 25,
      minPurchase: 200,
      maxDiscount: 100,
      startDate: '2023-06-01',
      endDate: '2023-08-31',
      isActive: true,
      usageLimit: 500,
      usageCount: 320,
      applicableProducts: 'all',
      applicableCategories: 'all'
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات التسويق
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setMarketingSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseFloat(value) 
          : value
    }));
  };
  
  // إضافة كوبون جديد
  const addCoupon = () => {
    const today = new Date().toISOString().split('T')[0];
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    
    const newCoupon: Coupon = {
      id: `coupon_${Date.now()}`,
      code: '',
      type: 'percentage',
      value: 10,
      startDate: today,
      endDate: nextMonth.toISOString().split('T')[0],
      isActive: false,
      usageCount: 0,
      applicableProducts: 'all',
      applicableCategories: 'all'
    };
    
    setCoupons([...coupons, newCoupon]);
  };
  
  // حذف كوبون
  const deleteCoupon = (couponId: string) => {
    setCoupons(coupons.filter(coupon => coupon.id !== couponId));
  };
  
  // تحديث كوبون
  const updateCoupon = (couponId: string, field: string, value: any) => {
    setCoupons(coupons.map(coupon => {
      if (coupon.id === couponId) {
        return { ...coupon, [field]: value };
      }
      return coupon;
    }));
  };
  
  // حفظ إعدادات التسويق
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات التسويق
      console.log('Marketing settings saved:', { marketingSettings, coupons });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving marketing settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات الكوبونات والعروض' : 'Coupons & Promotions Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات التسويق بنجاح' : 'Marketing settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* الإعدادات العامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Tag className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableCoupons"
                  name="enableCoupons"
                  checked={marketingSettings.enableCoupons}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableCoupons" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل الكوبونات' : 'Enable Coupons'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enablePromotions"
                  name="enablePromotions"
                  checked={marketingSettings.enablePromotions}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enablePromotions" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل العروض الترويجية' : 'Enable Promotions'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableAbandonedCartEmails"
                  name="enableAbandonedCartEmails"
                  checked={marketingSettings.enableAbandonedCartEmails}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableAbandonedCartEmails" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل رسائل السلة المتروكة' : 'Enable Abandoned Cart Emails'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'تأخير رسائل السلة المتروكة (ساعات)' : 'Abandoned Cart Email Delay (hours)'}
                </label>
                <Input
                  type="number"
                  name="abandonedCartEmailDelay"
                  value={marketingSettings.abandonedCartEmailDelay}
                  onChange={handleSettingsChange}
                  min="1"
                  max="72"
                  disabled={!marketingSettings.enableAbandonedCartEmails}
                  className={!marketingSettings.enableAbandonedCartEmails ? "opacity-50" : ""}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNewsletterPopup"
                  name="enableNewsletterPopup"
                  checked={marketingSettings.enableNewsletterPopup}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableNewsletterPopup" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل نافذة النشرة الإخبارية' : 'Enable Newsletter Popup'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'تأخير نافذة النشرة الإخبارية (ثواني)' : 'Newsletter Popup Delay (seconds)'}
                </label>
                <Input
                  type="number"
                  name="newsletterPopupDelay"
                  value={marketingSettings.newsletterPopupDelay}
                  onChange={handleSettingsChange}
                  min="5"
                  max="120"
                  disabled={!marketingSettings.enableNewsletterPopup}
                  className={!marketingSettings.enableNewsletterPopup ? "opacity-50" : ""}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableReferralProgram"
                  name="enableReferralProgram"
                  checked={marketingSettings.enableReferralProgram}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableReferralProgram" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل برنامج الإحالة' : 'Enable Referral Program'}
                </label>
              </div>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'قيمة خصم الإحالة' : 'Referral Discount Amount'}
                  </label>
                  <Input
                    type="number"
                    name="referralDiscountAmount"
                    value={marketingSettings.referralDiscountAmount}
                    onChange={handleSettingsChange}
                    min="1"
                    max="100"
                    disabled={!marketingSettings.enableReferralProgram}
                    className={!marketingSettings.enableReferralProgram ? "opacity-50" : ""}
                  />
                </div>
                
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'نوع الخصم' : 'Discount Type'}
                  </label>
                  <select
                    name="referralDiscountType"
                    value={marketingSettings.referralDiscountType}
                    onChange={handleSettingsChange}
                    className={cn(
                      "w-full px-3 py-2 rounded-md border",
                      isDarkMode 
                        ? "bg-slate-700 border-slate-600 text-white" 
                        : "bg-white border-gray-300 text-slate-900",
                      !marketingSettings.enableReferralProgram && "opacity-50"
                    )}
                    disabled={!marketingSettings.enableReferralProgram}
                  >
                    <option value="percentage">{language === 'ar' ? 'نسبة مئوية' : 'Percentage'}</option>
                    <option value="fixed">{language === 'ar' ? 'مبلغ ثابت' : 'Fixed Amount'}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          {/* الكوبونات */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Percent className="h-5 w-5" />
                {language === 'ar' ? 'الكوبونات' : 'Coupons'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCoupon}
                className="flex items-center gap-1"
                disabled={!marketingSettings.enableCoupons}
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة كوبون' : 'Add Coupon'}</span>
              </Button>
            </div>
            
            {!marketingSettings.enableCoupons && (
              <div className={cn(
                "p-3 rounded-md mb-4",
                isDarkMode ? "bg-slate-700" : "bg-slate-100"
              )}>
                <p className="text-sm">
                  {language === 'ar'
                    ? 'الكوبونات معطلة حاليًا. قم بتفعيل الكوبونات في الإعدادات العامة لإدارة الكوبونات.'
                    : 'Coupons are currently disabled. Enable coupons in general settings to manage coupons.'
                  }
                </p>
              </div>
            )}
            
            <div className="space-y-4">
              {coupons.map((coupon) => (
                <Card key={coupon.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50",
                  !marketingSettings.enableCoupons && "opacity-60"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`active-${coupon.id}`}
                          checked={coupon.isActive}
                          onChange={(e) => updateCoupon(coupon.id, 'isActive', e.target.checked)}
                          className="h-4 w-4 mr-2"
                          disabled={!marketingSettings.enableCoupons}
                        />
                        <label htmlFor={`active-${coupon.id}`} className={cn(
                          "text-sm font-medium",
                          !marketingSettings.enableCoupons && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'مفعّل' : 'Active'}
                        </label>
                      </div>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => deleteCoupon(coupon.id)}
                      className={cn(
                        "p-2 rounded-md",
                        isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                      )}
                      disabled={!marketingSettings.enableCoupons}
                    >
                      <Trash2 className={cn(
                        "h-5 w-5",
                        !marketingSettings.enableCoupons ? "text-gray-400" : "text-red-500"
                      )} />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'كود الكوبون' : 'Coupon Code'}
                      </label>
                      <Input
                        value={coupon.code}
                        onChange={(e) => updateCoupon(coupon.id, 'code', e.target.value.toUpperCase())}
                        disabled={!marketingSettings.enableCoupons}
                        className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <label className="block text-sm font-medium mb-1">
                          {language === 'ar' ? 'قيمة الخصم' : 'Discount Value'}
                        </label>
                        <Input
                          type="number"
                          value={coupon.value}
                          onChange={(e) => updateCoupon(coupon.id, 'value', parseFloat(e.target.value))}
                          min="0"
                          step={coupon.type === 'percentage' ? '1' : '0.01'}
                          disabled={!marketingSettings.enableCoupons}
                          className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                        />
                      </div>
                      
                      <div className="flex-1">
                        <label className="block text-sm font-medium mb-1">
                          {language === 'ar' ? 'نوع الخصم' : 'Discount Type'}
                        </label>
                        <select
                          value={coupon.type}
                          onChange={(e) => updateCoupon(coupon.id, 'type', e.target.value)}
                          className={cn(
                            "w-full px-3 py-2 rounded-md border",
                            isDarkMode 
                              ? "bg-slate-700 border-slate-600 text-white" 
                              : "bg-white border-gray-300 text-slate-900",
                            !marketingSettings.enableCoupons && "opacity-50"
                          )}
                          disabled={!marketingSettings.enableCoupons}
                        >
                          <option value="percentage">{language === 'ar' ? 'نسبة مئوية (%)' : 'Percentage (%)'}</option>
                          <option value="fixed">{language === 'ar' ? 'مبلغ ثابت' : 'Fixed Amount'}</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'تاريخ البدء' : 'Start Date'}
                      </label>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-slate-400" />
                        <Input
                          type="date"
                          value={coupon.startDate}
                          onChange={(e) => updateCoupon(coupon.id, 'startDate', e.target.value)}
                          disabled={!marketingSettings.enableCoupons}
                          className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'تاريخ الانتهاء' : 'End Date'}
                      </label>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-slate-400" />
                        <Input
                          type="date"
                          value={coupon.endDate}
                          onChange={(e) => updateCoupon(coupon.id, 'endDate', e.target.value)}
                          disabled={!marketingSettings.enableCoupons}
                          className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الحد الأدنى للشراء' : 'Minimum Purchase'}
                      </label>
                      <Input
                        type="number"
                        value={coupon.minPurchase || ''}
                        onChange={(e) => updateCoupon(
                          coupon.id,
                          'minPurchase',
                          e.target.value ? parseFloat(e.target.value) : undefined
                        )}
                        min="0"
                        step="0.01"
                        placeholder={language === 'ar' ? 'لا يوجد' : 'None'}
                        disabled={!marketingSettings.enableCoupons}
                        className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                      />
                    </div>
                    
                    {coupon.type === 'percentage' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          {language === 'ar' ? 'الحد الأقصى للخصم' : 'Maximum Discount'}
                        </label>
                        <Input
                          type="number"
                          value={coupon.maxDiscount || ''}
                          onChange={(e) => updateCoupon(
                            coupon.id,
                            'maxDiscount',
                            e.target.value ? parseFloat(e.target.value) : undefined
                          )}
                          min="0"
                          step="0.01"
                          placeholder={language === 'ar' ? 'لا يوجد' : 'None'}
                          disabled={!marketingSettings.enableCoupons}
                          className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                        />
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'حد الاستخدام' : 'Usage Limit'}
                      </label>
                      <Input
                        type="number"
                        value={coupon.usageLimit || ''}
                        onChange={(e) => updateCoupon(
                          coupon.id,
                          'usageLimit',
                          e.target.value ? parseInt(e.target.value) : undefined
                        )}
                        min="1"
                        placeholder={language === 'ar' ? 'غير محدود' : 'Unlimited'}
                        disabled={!marketingSettings.enableCoupons}
                        className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'عدد مرات الاستخدام' : 'Usage Count'}
                      </label>
                      <Input
                        type="number"
                        value={coupon.usageCount}
                        onChange={(e) => updateCoupon(coupon.id, 'usageCount', parseInt(e.target.value))}
                        min="0"
                        disabled={!marketingSettings.enableCoupons}
                        className={!marketingSettings.enableCoupons ? "opacity-50" : ""}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
