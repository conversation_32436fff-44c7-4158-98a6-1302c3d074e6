'use client';

import { useState } from 'react';
import { Save, Search, Globe, FileText, Link } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// مكون إعدادات SEO
export function SEOSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات SEO
  const [seoSettings, setSeoSettings] = useState({
    // إعدادات عامة
    siteName: 'CommercePro',
    siteName_ar: 'كوميرس برو',
    siteDescription: 'Your complete business solution for retail, wholesale, production, and services.',
    siteDescription_ar: 'حلول الأعمال المتكاملة للتجزئة والجملة والإنتاج والخدمات.',
    siteKeywords: 'ecommerce, retail, wholesale, production, services',
    siteKeywords_ar: 'التجارة الإلكترونية، التجزئة، الجملة، الإنتاج، الخدمات',
    
    // إعدادات الروبوتات
    enableRobotsTxt: true,
    allowIndexing: true,
    allowFollowing: true,
    
    // إعدادات خريطة الموقع
    enableSitemap: true,
    sitemapChangeFrequency: 'weekly',
    sitemapPriority: 0.7,
    
    // إعدادات الوسائط الاجتماعية
    enableOpenGraph: true,
    ogImageUrl: '/images/og-image.jpg',
    enableTwitterCards: true,
    twitterCardType: 'summary_large_image',
    twitterUsername: '@commercepro',
    
    // إعدادات التحليلات
    googleAnalyticsId: '',
    enableGoogleTagManager: false,
    googleTagManagerId: '',
    
    // إعدادات هيكلة البيانات
    enableStructuredData: true,
    organizationType: 'Corporation',
    enableBreadcrumbs: true,
    enableProductSchema: true,
    
    // إعدادات متقدمة
    canonicalUrlBase: 'https://www.commercepro.com',
    enableHreflangTags: true
  });
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات SEO
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setSeoSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseFloat(value) 
          : value
    }));
  };
  
  // حفظ إعدادات SEO
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات SEO
      console.log('SEO settings saved:', seoSettings);
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving SEO settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات SEO' : 'SEO Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات SEO بنجاح' : 'SEO settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* إعدادات عامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم الموقع (بالإنجليزية)' : 'Site Name (English)'}
                </label>
                <Input
                  name="siteName"
                  value={seoSettings.siteName}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم الموقع (بالعربية)' : 'Site Name (Arabic)'}
                </label>
                <Input
                  name="siteName_ar"
                  value={seoSettings.siteName_ar}
                  onChange={handleSettingsChange}
                  dir="rtl"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وصف الموقع (بالإنجليزية)' : 'Site Description (English)'}
                </label>
                <textarea
                  name="siteDescription"
                  value={seoSettings.siteDescription}
                  onChange={handleSettingsChange}
                  rows={2}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border resize-none",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                />
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {language === 'ar' 
                    ? 'يوصى بألا يزيد عن 160 حرفًا'
                    : 'Recommended to be no more than 160 characters'
                  }
                </p>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وصف الموقع (بالعربية)' : 'Site Description (Arabic)'}
                </label>
                <textarea
                  name="siteDescription_ar"
                  value={seoSettings.siteDescription_ar}
                  onChange={handleSettingsChange}
                  rows={2}
                  dir="rtl"
                  className={cn(
                    "w-full px-3 py-2 rounded-md border resize-none",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الكلمات المفتاحية (بالإنجليزية)' : 'Keywords (English)'}
                </label>
                <Input
                  name="siteKeywords"
                  value={seoSettings.siteKeywords}
                  onChange={handleSettingsChange}
                />
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {language === 'ar' 
                    ? 'افصل بين الكلمات المفتاحية بفواصل'
                    : 'Separate keywords with commas'
                  }
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الكلمات المفتاحية (بالعربية)' : 'Keywords (Arabic)'}
                </label>
                <Input
                  name="siteKeywords_ar"
                  value={seoSettings.siteKeywords_ar}
                  onChange={handleSettingsChange}
                  dir="rtl"
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات الروبوتات */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Search className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات الروبوتات' : 'Robots Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableRobotsTxt"
                  name="enableRobotsTxt"
                  checked={seoSettings.enableRobotsTxt}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableRobotsTxt" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل ملف robots.txt' : 'Enable robots.txt'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowIndexing"
                  name="allowIndexing"
                  checked={seoSettings.allowIndexing}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!seoSettings.enableRobotsTxt}
                />
                <label htmlFor="allowIndexing" className={cn(
                  "text-sm font-medium",
                  !seoSettings.enableRobotsTxt && "text-gray-400"
                )}>
                  {language === 'ar' ? 'السماح بفهرسة الموقع' : 'Allow Site Indexing'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowFollowing"
                  name="allowFollowing"
                  checked={seoSettings.allowFollowing}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!seoSettings.enableRobotsTxt}
                />
                <label htmlFor="allowFollowing" className={cn(
                  "text-sm font-medium",
                  !seoSettings.enableRobotsTxt && "text-gray-400"
                )}>
                  {language === 'ar' ? 'السماح بتتبع الروابط' : 'Allow Link Following'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableSitemap"
                  name="enableSitemap"
                  checked={seoSettings.enableSitemap}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableSitemap" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل خريطة الموقع' : 'Enable Sitemap'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'تكرار التغيير' : 'Change Frequency'}
                </label>
                <select
                  name="sitemapChangeFrequency"
                  value={seoSettings.sitemapChangeFrequency}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!seoSettings.enableSitemap}
                >
                  <option value="always">{language === 'ar' ? 'دائمًا' : 'Always'}</option>
                  <option value="hourly">{language === 'ar' ? 'كل ساعة' : 'Hourly'}</option>
                  <option value="daily">{language === 'ar' ? 'يوميًا' : 'Daily'}</option>
                  <option value="weekly">{language === 'ar' ? 'أسبوعيًا' : 'Weekly'}</option>
                  <option value="monthly">{language === 'ar' ? 'شهريًا' : 'Monthly'}</option>
                  <option value="yearly">{language === 'ar' ? 'سنويًا' : 'Yearly'}</option>
                  <option value="never">{language === 'ar' ? 'أبدًا' : 'Never'}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الأولوية' : 'Priority'}
                </label>
                <Input
                  type="number"
                  name="sitemapPriority"
                  value={seoSettings.sitemapPriority}
                  onChange={handleSettingsChange}
                  min="0"
                  max="1"
                  step="0.1"
                  disabled={!seoSettings.enableSitemap}
                  className={!seoSettings.enableSitemap ? "opacity-50" : ""}
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات الوسائط الاجتماعية */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Link className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات الوسائط الاجتماعية' : 'Social Media Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableOpenGraph"
                  name="enableOpenGraph"
                  checked={seoSettings.enableOpenGraph}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableOpenGraph" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل Open Graph' : 'Enable Open Graph'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableTwitterCards"
                  name="enableTwitterCards"
                  checked={seoSettings.enableTwitterCards}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableTwitterCards" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل Twitter Cards' : 'Enable Twitter Cards'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'رابط صورة Open Graph' : 'Open Graph Image URL'}
                </label>
                <Input
                  name="ogImageUrl"
                  value={seoSettings.ogImageUrl}
                  onChange={handleSettingsChange}
                  disabled={!seoSettings.enableOpenGraph}
                  className={!seoSettings.enableOpenGraph ? "opacity-50" : ""}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'نوع Twitter Card' : 'Twitter Card Type'}
                </label>
                <select
                  name="twitterCardType"
                  value={seoSettings.twitterCardType}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!seoSettings.enableTwitterCards}
                >
                  <option value="summary">{language === 'ar' ? 'ملخص' : 'Summary'}</option>
                  <option value="summary_large_image">{language === 'ar' ? 'ملخص مع صورة كبيرة' : 'Summary with Large Image'}</option>
                  <option value="app">{language === 'ar' ? 'تطبيق' : 'App'}</option>
                  <option value="player">{language === 'ar' ? 'مشغل' : 'Player'}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم المستخدم في تويتر' : 'Twitter Username'}
                </label>
                <Input
                  name="twitterUsername"
                  value={seoSettings.twitterUsername}
                  onChange={handleSettingsChange}
                  disabled={!seoSettings.enableTwitterCards}
                  className={!seoSettings.enableTwitterCards ? "opacity-50" : ""}
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات التحليلات */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات التحليلات' : 'Analytics Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'معرف Google Analytics' : 'Google Analytics ID'}
                </label>
                <Input
                  name="googleAnalyticsId"
                  value={seoSettings.googleAnalyticsId}
                  onChange={handleSettingsChange}
                  placeholder="G-XXXXXXXXXX"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableGoogleTagManager"
                  name="enableGoogleTagManager"
                  checked={seoSettings.enableGoogleTagManager}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableGoogleTagManager" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل Google Tag Manager' : 'Enable Google Tag Manager'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'معرف Google Tag Manager' : 'Google Tag Manager ID'}
                </label>
                <Input
                  name="googleTagManagerId"
                  value={seoSettings.googleTagManagerId}
                  onChange={handleSettingsChange}
                  placeholder="GTM-XXXXXXX"
                  disabled={!seoSettings.enableGoogleTagManager}
                  className={!seoSettings.enableGoogleTagManager ? "opacity-50" : ""}
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات هيكلة البيانات */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات هيكلة البيانات' : 'Structured Data Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableStructuredData"
                  name="enableStructuredData"
                  checked={seoSettings.enableStructuredData}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableStructuredData" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل هيكلة البيانات' : 'Enable Structured Data'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'نوع المؤسسة' : 'Organization Type'}
                </label>
                <select
                  name="organizationType"
                  value={seoSettings.organizationType}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!seoSettings.enableStructuredData}
                >
                  <option value="Corporation">{language === 'ar' ? 'شركة' : 'Corporation'}</option>
                  <option value="Organization">{language === 'ar' ? 'مؤسسة' : 'Organization'}</option>
                  <option value="LocalBusiness">{language === 'ar' ? 'عمل محلي' : 'Local Business'}</option>
                  <option value="Store">{language === 'ar' ? 'متجر' : 'Store'}</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableBreadcrumbs"
                  name="enableBreadcrumbs"
                  checked={seoSettings.enableBreadcrumbs}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!seoSettings.enableStructuredData}
                />
                <label htmlFor="enableBreadcrumbs" className={cn(
                  "text-sm font-medium",
                  !seoSettings.enableStructuredData && "text-gray-400"
                )}>
                  {language === 'ar' ? 'تفعيل مسارات التنقل' : 'Enable Breadcrumbs'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableProductSchema"
                  name="enableProductSchema"
                  checked={seoSettings.enableProductSchema}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!seoSettings.enableStructuredData}
                />
                <label htmlFor="enableProductSchema" className={cn(
                  "text-sm font-medium",
                  !seoSettings.enableStructuredData && "text-gray-400"
                )}>
                  {language === 'ar' ? 'تفعيل مخطط المنتج' : 'Enable Product Schema'}
                </label>
              </div>
            </div>
          </div>
          
          {/* إعدادات متقدمة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Link className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات متقدمة' : 'Advanced Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'قاعدة الرابط القانوني' : 'Canonical URL Base'}
                </label>
                <Input
                  name="canonicalUrlBase"
                  value={seoSettings.canonicalUrlBase}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableHreflangTags"
                  name="enableHreflangTags"
                  checked={seoSettings.enableHreflangTags}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableHreflangTags" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل علامات hreflang' : 'Enable Hreflang Tags'}
                </label>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
