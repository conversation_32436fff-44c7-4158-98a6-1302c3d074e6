'use client';

import { useState } from 'react';
import { Save, Plus, Trash2, CreditCard, DollarSign, Check, X } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع طريقة الدفع
interface PaymentMethod {
  id: string;
  name: string;
  name_ar: string;
  type: 'credit_card' | 'bank_transfer' | 'cash_on_delivery' | 'paypal' | 'stripe' | 'mada' | 'apple_pay' | 'other';
  description: string;
  description_ar: string;
  isEnabled: boolean;
  isDefault: boolean;
  processingFee: number;
  processingFeeType: 'fixed' | 'percentage';
  minAmount?: number;
  maxAmount?: number;
  credentials?: Record<string, string>;
  icon?: string;
}

// مكون إعدادات الدفع
export function PaymentSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات الدفع
  const [paymentSettings, setPaymentSettings] = useState({
    currency: 'SAR',
    currencySymbol: '﷼',
    currencyPosition: 'after',
    thousandSeparator: ',',
    decimalSeparator: '.',
    numberOfDecimals: 2,
    taxEnabled: true,
    taxIncluded: true,
    taxRate: 15
  });
  
  // حالة طرق الدفع
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: 'credit_card',
      name: 'Credit Card',
      name_ar: 'بطاقة ائتمان',
      type: 'credit_card',
      description: 'Pay with Visa, Mastercard, or American Express',
      description_ar: 'ادفع باستخدام فيزا، ماستركارد، أو أمريكان إكسبريس',
      isEnabled: true,
      isDefault: true,
      processingFee: 2.9,
      processingFeeType: 'percentage',
      icon: '/images/payment/credit-card.png'
    },
    {
      id: 'mada',
      name: 'Mada',
      name_ar: 'مدى',
      type: 'mada',
      description: 'Pay with Mada debit card',
      description_ar: 'ادفع باستخدام بطاقة مدى',
      isEnabled: true,
      isDefault: false,
      processingFee: 1.5,
      processingFeeType: 'percentage',
      icon: '/images/payment/mada.png'
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      name_ar: 'تحويل بنكي',
      type: 'bank_transfer',
      description: 'Pay directly to our bank account',
      description_ar: 'ادفع مباشرة إلى حسابنا البنكي',
      isEnabled: true,
      isDefault: false,
      processingFee: 0,
      processingFeeType: 'fixed',
      icon: '/images/payment/bank-transfer.png'
    },
    {
      id: 'cash_on_delivery',
      name: 'Cash on Delivery',
      name_ar: 'الدفع عند الاستلام',
      type: 'cash_on_delivery',
      description: 'Pay when you receive your order',
      description_ar: 'ادفع عند استلام طلبك',
      isEnabled: true,
      isDefault: false,
      processingFee: 10,
      processingFeeType: 'fixed',
      icon: '/images/payment/cash-on-delivery.png'
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات الدفع
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setPaymentSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseFloat(value) 
          : value
    }));
  };
  
  // إضافة طريقة دفع جديدة
  const addPaymentMethod = () => {
    const newMethod: PaymentMethod = {
      id: `payment_${Date.now()}`,
      name: 'New Payment Method',
      name_ar: 'طريقة دفع جديدة',
      type: 'other',
      description: 'Description of the payment method',
      description_ar: 'وصف طريقة الدفع',
      isEnabled: true,
      isDefault: false,
      processingFee: 0,
      processingFeeType: 'fixed'
    };
    
    setPaymentMethods([...paymentMethods, newMethod]);
  };
  
  // حذف طريقة دفع
  const deletePaymentMethod = (methodId: string) => {
    setPaymentMethods(paymentMethods.filter(method => method.id !== methodId));
  };
  
  // تحديث طريقة دفع
  const updatePaymentMethod = (methodId: string, field: string, value: any) => {
    setPaymentMethods(paymentMethods.map(method => {
      if (method.id === methodId) {
        return { ...method, [field]: value };
      }
      return method;
    }));
  };
  
  // تعيين طريقة دفع كافتراضية
  const setDefaultPaymentMethod = (methodId: string) => {
    setPaymentMethods(paymentMethods.map(method => ({
      ...method,
      isDefault: method.id === methodId
    })));
  };
  
  // حفظ إعدادات الدفع
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات الدفع
      console.log('Payment settings saved:', { paymentSettings, paymentMethods });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving payment settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات الدفع' : 'Payment Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات الدفع بنجاح' : 'Payment settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* إعدادات العملة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات العملة' : 'Currency Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'العملة' : 'Currency'}
                </label>
                <select
                  name="currency"
                  value={paymentSettings.currency}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                >
                  <option value="SAR">{language === 'ar' ? 'ريال سعودي (SAR)' : 'Saudi Riyal (SAR)'}</option>
                  <option value="USD">{language === 'ar' ? 'دولار أمريكي (USD)' : 'US Dollar (USD)'}</option>
                  <option value="EUR">{language === 'ar' ? 'يورو (EUR)' : 'Euro (EUR)'}</option>
                  <option value="GBP">{language === 'ar' ? 'جنيه إسترليني (GBP)' : 'British Pound (GBP)'}</option>
                  <option value="AED">{language === 'ar' ? 'درهم إماراتي (AED)' : 'UAE Dirham (AED)'}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'رمز العملة' : 'Currency Symbol'}
                </label>
                <Input
                  name="currencySymbol"
                  value={paymentSettings.currencySymbol}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'موضع رمز العملة' : 'Currency Position'}
                </label>
                <select
                  name="currencyPosition"
                  value={paymentSettings.currencyPosition}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                >
                  <option value="before">{language === 'ar' ? 'قبل السعر ($100)' : 'Before price ($100)'}</option>
                  <option value="after">{language === 'ar' ? 'بعد السعر (100$)' : 'After price (100$)'}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'عدد الخانات العشرية' : 'Number of Decimals'}
                </label>
                <Input
                  type="number"
                  name="numberOfDecimals"
                  value={paymentSettings.numberOfDecimals}
                  onChange={handleSettingsChange}
                  min="0"
                  max="4"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'فاصل الآلاف' : 'Thousand Separator'}
                </label>
                <Input
                  name="thousandSeparator"
                  value={paymentSettings.thousandSeparator}
                  onChange={handleSettingsChange}
                  maxLength={1}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'فاصل العشري' : 'Decimal Separator'}
                </label>
                <Input
                  name="decimalSeparator"
                  value={paymentSettings.decimalSeparator}
                  onChange={handleSettingsChange}
                  maxLength={1}
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات الضريبة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات الضريبة' : 'Tax Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="taxEnabled"
                  name="taxEnabled"
                  checked={paymentSettings.taxEnabled}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="taxEnabled" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل الضريبة' : 'Enable Tax'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="taxIncluded"
                  name="taxIncluded"
                  checked={paymentSettings.taxIncluded}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!paymentSettings.taxEnabled}
                />
                <label htmlFor="taxIncluded" className={cn(
                  "text-sm font-medium",
                  !paymentSettings.taxEnabled && "text-gray-400"
                )}>
                  {language === 'ar' ? 'الأسعار تشمل الضريبة' : 'Prices Include Tax'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'نسبة الضريبة (%)' : 'Tax Rate (%)'}
                </label>
                <Input
                  type="number"
                  name="taxRate"
                  value={paymentSettings.taxRate}
                  onChange={handleSettingsChange}
                  min="0"
                  max="100"
                  step="0.01"
                  disabled={!paymentSettings.taxEnabled}
                  className={!paymentSettings.taxEnabled ? "opacity-50" : ""}
                />
              </div>
            </div>
          </div>
          
          {/* طرق الدفع */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                {language === 'ar' ? 'طرق الدفع' : 'Payment Methods'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPaymentMethod}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة طريقة' : 'Add Method'}</span>
              </Button>
            </div>
            
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <Card key={method.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`enabled-${method.id}`}
                          checked={method.isEnabled}
                          onChange={(e) => updatePaymentMethod(method.id, 'isEnabled', e.target.checked)}
                          className="h-4 w-4 mr-2"
                        />
                        <label htmlFor={`enabled-${method.id}`} className="text-sm font-medium">
                          {language === 'ar' ? 'مفعّل' : 'Enabled'}
                        </label>
                      </div>
                      
                      <div className="flex items-center ml-4">
                        <input
                          type="radio"
                          id={`default-${method.id}`}
                          checked={method.isDefault}
                          onChange={() => setDefaultPaymentMethod(method.id)}
                          className="h-4 w-4 mr-2"
                          disabled={!method.isEnabled}
                        />
                        <label htmlFor={`default-${method.id}`} className={cn(
                          "text-sm font-medium",
                          !method.isEnabled && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'افتراضي' : 'Default'}
                        </label>
                      </div>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => deletePaymentMethod(method.id)}
                      className={cn(
                        "p-2 rounded-md",
                        isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                      )}
                    >
                      <Trash2 className="h-5 w-5 text-red-500" />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الاسم (بالإنجليزية)' : 'Name (English)'}
                      </label>
                      <Input
                        value={method.name}
                        onChange={(e) => updatePaymentMethod(method.id, 'name', e.target.value)}
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الاسم (بالعربية)' : 'Name (Arabic)'}
                      </label>
                      <Input
                        value={method.name_ar}
                        onChange={(e) => updatePaymentMethod(method.id, 'name_ar', e.target.value)}
                        dir="rtl"
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'نوع طريقة الدفع' : 'Payment Method Type'}
                      </label>
                      <select
                        value={method.type}
                        onChange={(e) => updatePaymentMethod(method.id, 'type', e.target.value)}
                        className={cn(
                          "w-full px-3 py-2 rounded-md border",
                          isDarkMode 
                            ? "bg-slate-700 border-slate-600 text-white" 
                            : "bg-white border-gray-300 text-slate-900",
                          !method.isEnabled && "opacity-50"
                        )}
                        disabled={!method.isEnabled}
                      >
                        <option value="credit_card">{language === 'ar' ? 'بطاقة ائتمان' : 'Credit Card'}</option>
                        <option value="bank_transfer">{language === 'ar' ? 'تحويل بنكي' : 'Bank Transfer'}</option>
                        <option value="cash_on_delivery">{language === 'ar' ? 'الدفع عند الاستلام' : 'Cash on Delivery'}</option>
                        <option value="paypal">PayPal</option>
                        <option value="stripe">Stripe</option>
                        <option value="mada">{language === 'ar' ? 'مدى' : 'Mada'}</option>
                        <option value="apple_pay">Apple Pay</option>
                        <option value="other">{language === 'ar' ? 'أخرى' : 'Other'}</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'رسوم المعالجة' : 'Processing Fee'}
                      </label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          value={method.processingFee}
                          onChange={(e) => updatePaymentMethod(method.id, 'processingFee', parseFloat(e.target.value))}
                          min="0"
                          step="0.01"
                          disabled={!method.isEnabled}
                          className={cn(
                            "flex-1",
                            !method.isEnabled && "opacity-50"
                          )}
                        />
                        <select
                          value={method.processingFeeType}
                          onChange={(e) => updatePaymentMethod(method.id, 'processingFeeType', e.target.value)}
                          className={cn(
                            "px-3 py-2 rounded-md border",
                            isDarkMode 
                              ? "bg-slate-700 border-slate-600 text-white" 
                              : "bg-white border-gray-300 text-slate-900",
                            !method.isEnabled && "opacity-50"
                          )}
                          disabled={!method.isEnabled}
                        >
                          <option value="fixed">{language === 'ar' ? 'ثابت' : 'Fixed'}</option>
                          <option value="percentage">{language === 'ar' ? 'نسبة مئوية' : 'Percentage'}</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الوصف (بالإنجليزية)' : 'Description (English)'}
                      </label>
                      <Input
                        value={method.description}
                        onChange={(e) => updatePaymentMethod(method.id, 'description', e.target.value)}
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الوصف (بالعربية)' : 'Description (Arabic)'}
                      </label>
                      <Input
                        value={method.description_ar}
                        onChange={(e) => updatePaymentMethod(method.id, 'description_ar', e.target.value)}
                        dir="rtl"
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الحد الأدنى للطلب' : 'Minimum Order Amount'}
                      </label>
                      <Input
                        type="number"
                        value={method.minAmount || ''}
                        onChange={(e) => updatePaymentMethod(
                          method.id,
                          'minAmount',
                          e.target.value ? parseFloat(e.target.value) : undefined
                        )}
                        min="0"
                        step="0.01"
                        placeholder={language === 'ar' ? 'لا يوجد' : 'None'}
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'الحد الأقصى للطلب' : 'Maximum Order Amount'}
                      </label>
                      <Input
                        type="number"
                        value={method.maxAmount || ''}
                        onChange={(e) => updatePaymentMethod(
                          method.id,
                          'maxAmount',
                          e.target.value ? parseFloat(e.target.value) : undefined
                        )}
                        min="0"
                        step="0.01"
                        placeholder={language === 'ar' ? 'لا يوجد' : 'None'}
                        disabled={!method.isEnabled}
                        className={!method.isEnabled ? "opacity-50" : ""}
                      />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
