'use client';

import { useState } from 'react';
import { Save, Plus, Trash2, Truck, MapPin, DollarSign, Clock } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع منطقة الشحن
interface ShippingZone {
  id: string;
  name: string;
  name_ar: string;
  countries: string[];
  methods: ShippingMethod[];
}

// نوع طريقة الشحن
interface ShippingMethod {
  id: string;
  name: string;
  name_ar: string;
  cost: number;
  freeShippingThreshold?: number;
  estimatedDeliveryTime: string;
  isDefault: boolean;
}

// مكون إعدادات الشحن
export function ShippingSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات الشحن
  const [shippingSettings, setShippingSettings] = useState({
    enableShipping: true,
    defaultWeight: 1,
    weightUnit: 'kg',
    dimensionsUnit: 'cm',
    shippingOrigin: {
      country: 'Saudi Arabia',
      city: 'Riyadh',
      postalCode: '12345',
      address: '123 Main St'
    }
  });
  
  // حالة مناطق الشحن
  const [shippingZones, setShippingZones] = useState<ShippingZone[]>([
    {
      id: 'zone1',
      name: 'Domestic',
      name_ar: 'محلي',
      countries: ['Saudi Arabia'],
      methods: [
        {
          id: 'method1',
          name: 'Standard Shipping',
          name_ar: 'الشحن القياسي',
          cost: 15,
          freeShippingThreshold: 200,
          estimatedDeliveryTime: '2-3 days',
          isDefault: true
        },
        {
          id: 'method2',
          name: 'Express Shipping',
          name_ar: 'الشحن السريع',
          cost: 30,
          estimatedDeliveryTime: '1 day',
          isDefault: false
        }
      ]
    },
    {
      id: 'zone2',
      name: 'GCC Countries',
      name_ar: 'دول الخليج',
      countries: ['UAE', 'Kuwait', 'Bahrain', 'Qatar', 'Oman'],
      methods: [
        {
          id: 'method3',
          name: 'Standard International',
          name_ar: 'الشحن الدولي القياسي',
          cost: 50,
          freeShippingThreshold: 500,
          estimatedDeliveryTime: '3-5 days',
          isDefault: true
        },
        {
          id: 'method4',
          name: 'Express International',
          name_ar: 'الشحن الدولي السريع',
          cost: 100,
          estimatedDeliveryTime: '1-2 days',
          isDefault: false
        }
      ]
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات الشحن
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    // التعامل مع الحقول المتداخلة
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setShippingSettings(prev => ({
        ...prev,
        [section]: {
          ...prev[section as keyof typeof prev],
          [field]: value
        }
      }));
    } else {
      setShippingSettings(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      }));
    }
  };
  
  // إضافة منطقة شحن جديدة
  const addShippingZone = () => {
    const newZone: ShippingZone = {
      id: `zone${shippingZones.length + 1}`,
      name: 'New Zone',
      name_ar: 'منطقة جديدة',
      countries: [],
      methods: [
        {
          id: `method${Date.now()}`,
          name: 'Standard Shipping',
          name_ar: 'الشحن القياسي',
          cost: 0,
          estimatedDeliveryTime: '3-5 days',
          isDefault: true
        }
      ]
    };
    
    setShippingZones([...shippingZones, newZone]);
  };
  
  // حذف منطقة شحن
  const deleteShippingZone = (zoneId: string) => {
    setShippingZones(shippingZones.filter(zone => zone.id !== zoneId));
  };
  
  // تحديث منطقة شحن
  const updateShippingZone = (zoneId: string, field: string, value: any) => {
    setShippingZones(shippingZones.map(zone => {
      if (zone.id === zoneId) {
        return { ...zone, [field]: value };
      }
      return zone;
    }));
  };
  
  // إضافة طريقة شحن جديدة
  const addShippingMethod = (zoneId: string) => {
    const newMethod: ShippingMethod = {
      id: `method${Date.now()}`,
      name: 'New Method',
      name_ar: 'طريقة جديدة',
      cost: 0,
      estimatedDeliveryTime: '3-5 days',
      isDefault: false
    };
    
    setShippingZones(shippingZones.map(zone => {
      if (zone.id === zoneId) {
        return { ...zone, methods: [...zone.methods, newMethod] };
      }
      return zone;
    }));
  };
  
  // حذف طريقة شحن
  const deleteShippingMethod = (zoneId: string, methodId: string) => {
    setShippingZones(shippingZones.map(zone => {
      if (zone.id === zoneId) {
        return { ...zone, methods: zone.methods.filter(method => method.id !== methodId) };
      }
      return zone;
    }));
  };
  
  // تحديث طريقة شحن
  const updateShippingMethod = (zoneId: string, methodId: string, field: string, value: any) => {
    setShippingZones(shippingZones.map(zone => {
      if (zone.id === zoneId) {
        return {
          ...zone,
          methods: zone.methods.map(method => {
            if (method.id === methodId) {
              return { ...method, [field]: value };
            }
            return method;
          })
        };
      }
      return zone;
    }));
  };
  
  // حفظ إعدادات الشحن
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات الشحن
      console.log('Shipping settings saved:', { shippingSettings, shippingZones });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving shipping settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات الشحن' : 'Shipping Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات الشحن بنجاح' : 'Shipping settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* الإعدادات العامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Truck className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableShipping"
                  name="enableShipping"
                  checked={shippingSettings.enableShipping}
                  onChange={(e) => setShippingSettings(prev => ({ ...prev, enableShipping: e.target.checked }))}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableShipping" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل الشحن' : 'Enable Shipping'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الوزن الافتراضي (كجم)' : 'Default Weight (kg)'}
                </label>
                <Input
                  type="number"
                  name="defaultWeight"
                  value={shippingSettings.defaultWeight}
                  onChange={handleSettingsChange}
                  min="0"
                  step="0.1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وحدة الوزن' : 'Weight Unit'}
                </label>
                <select
                  name="weightUnit"
                  value={shippingSettings.weightUnit}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                >
                  <option value="kg">{language === 'ar' ? 'كيلوجرام (كجم)' : 'Kilogram (kg)'}</option>
                  <option value="g">{language === 'ar' ? 'جرام (جم)' : 'Gram (g)'}</option>
                  <option value="lb">{language === 'ar' ? 'رطل (رطل)' : 'Pound (lb)'}</option>
                  <option value="oz">{language === 'ar' ? 'أونصة (أوز)' : 'Ounce (oz)'}</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وحدة الأبعاد' : 'Dimensions Unit'}
                </label>
                <select
                  name="dimensionsUnit"
                  value={shippingSettings.dimensionsUnit}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                >
                  <option value="cm">{language === 'ar' ? 'سنتيمتر (سم)' : 'Centimeter (cm)'}</option>
                  <option value="m">{language === 'ar' ? 'متر (م)' : 'Meter (m)'}</option>
                  <option value="in">{language === 'ar' ? 'إنش (بوصة)' : 'Inch (in)'}</option>
                  <option value="ft">{language === 'ar' ? 'قدم (قدم)' : 'Foot (ft)'}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* عنوان الشحن الأصلي */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {language === 'ar' ? 'عنوان الشحن الأصلي' : 'Shipping Origin Address'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الدولة' : 'Country'}
                </label>
                <Input
                  name="shippingOrigin.country"
                  value={shippingSettings.shippingOrigin.country}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'المدينة' : 'City'}
                </label>
                <Input
                  name="shippingOrigin.city"
                  value={shippingSettings.shippingOrigin.city}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الرمز البريدي' : 'Postal Code'}
                </label>
                <Input
                  name="shippingOrigin.postalCode"
                  value={shippingSettings.shippingOrigin.postalCode}
                  onChange={handleSettingsChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'العنوان' : 'Address'}
                </label>
                <Input
                  name="shippingOrigin.address"
                  value={shippingSettings.shippingOrigin.address}
                  onChange={handleSettingsChange}
                />
              </div>
            </div>
          </div>
          
          {/* مناطق الشحن */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {language === 'ar' ? 'مناطق الشحن' : 'Shipping Zones'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addShippingZone}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة منطقة' : 'Add Zone'}</span>
              </Button>
            </div>
            
            <div className="space-y-4">
              {shippingZones.map((zone) => (
                <Card key={zone.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          {language === 'ar' ? 'اسم المنطقة (بالإنجليزية)' : 'Zone Name (English)'}
                        </label>
                        <Input
                          value={zone.name}
                          onChange={(e) => updateShippingZone(zone.id, 'name', e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          {language === 'ar' ? 'اسم المنطقة (بالعربية)' : 'Zone Name (Arabic)'}
                        </label>
                        <Input
                          value={zone.name_ar}
                          onChange={(e) => updateShippingZone(zone.id, 'name_ar', e.target.value)}
                          dir="rtl"
                        />
                      </div>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => deleteShippingZone(zone.id)}
                      className={cn(
                        "p-2 rounded-md ml-4",
                        isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                      )}
                    >
                      <Trash2 className="h-5 w-5 text-red-500" />
                    </button>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">
                      {language === 'ar' ? 'الدول' : 'Countries'}
                    </label>
                    <Input
                      value={zone.countries.join(', ')}
                      onChange={(e) => updateShippingZone(zone.id, 'countries', e.target.value.split(',').map(c => c.trim()))}
                      placeholder={language === 'ar' ? 'أدخل أسماء الدول مفصولة بفواصل' : 'Enter country names separated by commas'}
                    />
                  </div>
                  
                  {/* طرق الشحن */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-md font-medium">
                        {language === 'ar' ? 'طرق الشحن' : 'Shipping Methods'}
                      </h4>
                      
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addShippingMethod(zone.id)}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        <span>{language === 'ar' ? 'إضافة طريقة' : 'Add Method'}</span>
                      </Button>
                    </div>
                    
                    <div className="space-y-3">
                      {zone.methods.map((method) => (
                        <div key={method.id} className={cn(
                          "p-3 rounded-md",
                          isDarkMode ? "bg-slate-800" : "bg-white"
                        )}>
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <input
                                type="radio"
                                checked={method.isDefault}
                                onChange={() => {
                                  // تعيين هذه الطريقة كافتراضية وإلغاء الافتراضية من الطرق الأخرى
                                  const updatedMethods = zone.methods.map(m => ({
                                    ...m,
                                    isDefault: m.id === method.id
                                  }));
                                  updateShippingZone(zone.id, 'methods', updatedMethods);
                                }}
                                className="h-4 w-4"
                              />
                              <span className="text-sm font-medium">
                                {language === 'ar' ? 'افتراضي' : 'Default'}
                              </span>
                            </div>
                            
                            <button
                              type="button"
                              onClick={() => deleteShippingMethod(zone.id, method.id)}
                              className={cn(
                                "p-1 rounded-md",
                                isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                              )}
                              disabled={zone.methods.length <= 1}
                            >
                              <Trash2 className={cn(
                                "h-4 w-4",
                                zone.methods.length <= 1 ? "text-gray-400" : "text-red-500"
                              )} />
                            </button>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                              <label className="block text-xs font-medium mb-1">
                                {language === 'ar' ? 'الاسم (بالإنجليزية)' : 'Name (English)'}
                              </label>
                              <Input
                                value={method.name}
                                onChange={(e) => updateShippingMethod(zone.id, method.id, 'name', e.target.value)}
                                size="sm"
                              />
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium mb-1">
                                {language === 'ar' ? 'الاسم (بالعربية)' : 'Name (Arabic)'}
                              </label>
                              <Input
                                value={method.name_ar}
                                onChange={(e) => updateShippingMethod(zone.id, method.id, 'name_ar', e.target.value)}
                                dir="rtl"
                                size="sm"
                              />
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium mb-1">
                                {language === 'ar' ? 'تكلفة الشحن' : 'Shipping Cost'}
                              </label>
                              <div className="flex items-center">
                                <DollarSign className="h-4 w-4 mr-1 text-slate-400" />
                                <Input
                                  type="number"
                                  value={method.cost}
                                  onChange={(e) => updateShippingMethod(zone.id, method.id, 'cost', parseFloat(e.target.value))}
                                  min="0"
                                  step="0.01"
                                  size="sm"
                                />
                              </div>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium mb-1">
                                {language === 'ar' ? 'حد الشحن المجاني' : 'Free Shipping Threshold'}
                              </label>
                              <div className="flex items-center">
                                <DollarSign className="h-4 w-4 mr-1 text-slate-400" />
                                <Input
                                  type="number"
                                  value={method.freeShippingThreshold || ''}
                                  onChange={(e) => updateShippingMethod(
                                    zone.id,
                                    method.id,
                                    'freeShippingThreshold',
                                    e.target.value ? parseFloat(e.target.value) : undefined
                                  )}
                                  min="0"
                                  step="0.01"
                                  placeholder={language === 'ar' ? 'لا يوجد' : 'None'}
                                  size="sm"
                                />
                              </div>
                            </div>
                            
                            <div>
                              <label className="block text-xs font-medium mb-1">
                                {language === 'ar' ? 'وقت التوصيل المتوقع' : 'Estimated Delivery Time'}
                              </label>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1 text-slate-400" />
                                <Input
                                  value={method.estimatedDeliveryTime}
                                  onChange={(e) => updateShippingMethod(zone.id, method.id, 'estimatedDeliveryTime', e.target.value)}
                                  placeholder={language === 'ar' ? 'مثال: 3-5 أيام' : 'e.g. 3-5 days'}
                                  size="sm"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
