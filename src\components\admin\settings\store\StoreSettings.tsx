'use client';

import { useState } from 'react';
import { Save, Upload, MapPin, Phone, Mail, Globe, Clock, Info } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع معلومات الشركة
interface CompanyInfo {
  name: string;
  name_ar: string;
  logo: string;
  favicon: string;
  description: string;
  description_ar: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contact: {
    email: string;
    phone: string;
    whatsapp: string;
    website: string;
  };
  socialMedia: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
    youtube: string;
  };
  businessHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
  legalInfo: {
    taxId: string;
    registrationNumber: string;
    vatNumber: string;
  };
}

// مكون إعدادات المتجر
export function StoreSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة معلومات الشركة
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: 'CommercePro',
    name_ar: 'كوميرس برو',
    logo: '/images/logo.png',
    favicon: '/images/favicon.ico',
    description: 'Your complete business solution for retail, wholesale, production, and services.',
    description_ar: 'حلول الأعمال المتكاملة للتجزئة والجملة والإنتاج والخدمات.',
    address: {
      street: '123 Business Street',
      city: 'Riyadh',
      state: 'Riyadh Province',
      postalCode: '12345',
      country: 'Saudi Arabia'
    },
    contact: {
      email: '<EMAIL>',
      phone: '+966 12 345 6789',
      whatsapp: '+966 12 345 6789',
      website: 'www.commercepro.com'
    },
    socialMedia: {
      facebook: 'https://facebook.com/commercepro',
      twitter: 'https://twitter.com/commercepro',
      instagram: 'https://instagram.com/commercepro',
      linkedin: 'https://linkedin.com/company/commercepro',
      youtube: 'https://youtube.com/commercepro'
    },
    businessHours: {
      monday: '9:00 AM - 6:00 PM',
      tuesday: '9:00 AM - 6:00 PM',
      wednesday: '9:00 AM - 6:00 PM',
      thursday: '9:00 AM - 6:00 PM',
      friday: '9:00 AM - 6:00 PM',
      saturday: '10:00 AM - 4:00 PM',
      sunday: 'Closed'
    },
    legalInfo: {
      taxId: '**********',
      registrationNumber: 'REG987654321',
      vatNumber: 'VAT123456789'
    }
  });
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث حقل في معلومات الشركة
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // التعامل مع الحقول المتداخلة
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setCompanyInfo(prev => ({
        ...prev,
        [section]: {
          ...prev[section as keyof typeof prev],
          [field]: value
        }
      }));
    } else {
      setCompanyInfo(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // حفظ معلومات الشركة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ معلومات الشركة
      console.log('Company info saved:', companyInfo);
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving company info:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'معلومات الشركة' : 'Company Information'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ معلومات الشركة بنجاح' : 'Company information saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* معلومات أساسية */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Info className="h-5 w-5" />
              {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم الشركة (بالإنجليزية)' : 'Company Name (English)'}
                </label>
                <Input
                  name="name"
                  value={companyInfo.name}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم الشركة (بالعربية)' : 'Company Name (Arabic)'}
                </label>
                <Input
                  name="name_ar"
                  value={companyInfo.name_ar}
                  onChange={handleChange}
                  dir="rtl"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'شعار الشركة' : 'Company Logo'}
                </label>
                <div className="flex gap-2">
                  <Input
                    name="logo"
                    value={companyInfo.logo}
                    onChange={handleChange}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-shrink-0"
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
                {companyInfo.logo && (
                  <div className="mt-2 p-2 border rounded-md dark:border-slate-700 inline-block">
                    <img
                      src={companyInfo.logo}
                      alt="Logo Preview"
                      className="h-10"
                    />
                  </div>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'أيقونة الموقع' : 'Favicon'}
                </label>
                <div className="flex gap-2">
                  <Input
                    name="favicon"
                    value={companyInfo.favicon}
                    onChange={handleChange}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-shrink-0"
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وصف الشركة (بالإنجليزية)' : 'Company Description (English)'}
                </label>
                <textarea
                  name="description"
                  value={companyInfo.description}
                  onChange={handleChange}
                  rows={3}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border resize-none",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'وصف الشركة (بالعربية)' : 'Company Description (Arabic)'}
                </label>
                <textarea
                  name="description_ar"
                  value={companyInfo.description_ar}
                  onChange={handleChange}
                  rows={3}
                  dir="rtl"
                  className={cn(
                    "w-full px-3 py-2 rounded-md border resize-none",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                />
              </div>
            </div>
          </div>
          
          {/* معلومات العنوان */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {language === 'ar' ? 'معلومات العنوان' : 'Address Information'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الشارع' : 'Street'}
                </label>
                <Input
                  name="address.street"
                  value={companyInfo.address.street}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'المدينة' : 'City'}
                </label>
                <Input
                  name="address.city"
                  value={companyInfo.address.city}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'المنطقة/الولاية' : 'State/Province'}
                </label>
                <Input
                  name="address.state"
                  value={companyInfo.address.state}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الرمز البريدي' : 'Postal Code'}
                </label>
                <Input
                  name="address.postalCode"
                  value={companyInfo.address.postalCode}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الدولة' : 'Country'}
                </label>
                <Input
                  name="address.country"
                  value={companyInfo.address.country}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
          
          {/* معلومات الاتصال */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Phone className="h-5 w-5" />
              {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </label>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-slate-400" />
                  <Input
                    name="contact.email"
                    value={companyInfo.contact.email}
                    onChange={handleChange}
                    type="email"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                </label>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-2 text-slate-400" />
                  <Input
                    name="contact.phone"
                    value={companyInfo.contact.phone}
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'واتساب' : 'WhatsApp'}
                </label>
                <Input
                  name="contact.whatsapp"
                  value={companyInfo.contact.whatsapp}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}
                </label>
                <div className="flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-slate-400" />
                  <Input
                    name="contact.website"
                    value={companyInfo.contact.website}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* ساعات العمل */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {language === 'ar' ? 'ساعات العمل' : 'Business Hours'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الإثنين' : 'Monday'}
                </label>
                <Input
                  name="businessHours.monday"
                  value={companyInfo.businessHours.monday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الثلاثاء' : 'Tuesday'}
                </label>
                <Input
                  name="businessHours.tuesday"
                  value={companyInfo.businessHours.tuesday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الأربعاء' : 'Wednesday'}
                </label>
                <Input
                  name="businessHours.wednesday"
                  value={companyInfo.businessHours.wednesday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الخميس' : 'Thursday'}
                </label>
                <Input
                  name="businessHours.thursday"
                  value={companyInfo.businessHours.thursday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الجمعة' : 'Friday'}
                </label>
                <Input
                  name="businessHours.friday"
                  value={companyInfo.businessHours.friday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'السبت' : 'Saturday'}
                </label>
                <Input
                  name="businessHours.saturday"
                  value={companyInfo.businessHours.saturday}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الأحد' : 'Sunday'}
                </label>
                <Input
                  name="businessHours.sunday"
                  value={companyInfo.businessHours.sunday}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
