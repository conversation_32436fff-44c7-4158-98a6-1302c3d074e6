'use client';

import { useState } from 'react';
import { Save, Plus, Trash2, DollarSign, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع العملة
interface Currency {
  id: string;
  code: string;
  name: string;
  symbol: string;
  isEnabled: boolean;
  isDefault: boolean;
  exchangeRate: number;
  decimalPlaces: number;
  order: number;
}

// مكون إعدادات العملة
export function CurrencySettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات العملة
  const [currencySettings, setCurrencySettings] = useState({
    enableMultiCurrency: true,
    showCurrencySwitcher: true,
    autoUpdateExchangeRates: true,
    exchangeRateProvider: 'manual',
    exchangeRateApiKey: '',
    updateFrequency: 'daily'
  });
  
  // حالة العملات
  const [currencies, setCurrencies] = useState<Currency[]>([
    {
      id: 'sar',
      code: 'SAR',
      name: 'Saudi Riyal',
      symbol: '﷼',
      isEnabled: true,
      isDefault: true,
      exchangeRate: 1,
      decimalPlaces: 2,
      order: 1
    },
    {
      id: 'usd',
      code: 'USD',
      name: 'US Dollar',
      symbol: '$',
      isEnabled: true,
      isDefault: false,
      exchangeRate: 0.27,
      decimalPlaces: 2,
      order: 2
    },
    {
      id: 'eur',
      code: 'EUR',
      name: 'Euro',
      symbol: '€',
      isEnabled: true,
      isDefault: false,
      exchangeRate: 0.24,
      decimalPlaces: 2,
      order: 3
    },
    {
      id: 'gbp',
      code: 'GBP',
      name: 'British Pound',
      symbol: '£',
      isEnabled: false,
      isDefault: false,
      exchangeRate: 0.21,
      decimalPlaces: 2,
      order: 4
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات العملة
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setCurrencySettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };
  
  // إضافة عملة جديدة
  const addCurrency = () => {
    const newCurrency: Currency = {
      id: `currency_${Date.now()}`,
      code: '',
      name: '',
      symbol: '',
      isEnabled: false,
      isDefault: false,
      exchangeRate: 1,
      decimalPlaces: 2,
      order: currencies.length + 1
    };
    
    setCurrencies([...currencies, newCurrency]);
  };
  
  // حذف عملة
  const deleteCurrency = (currencyId: string) => {
    // لا يمكن حذف العملة الافتراضية
    if (currencies.find(curr => curr.id === currencyId)?.isDefault) {
      return;
    }
    
    setCurrencies(currencies.filter(curr => curr.id !== currencyId));
  };
  
  // تحديث عملة
  const updateCurrency = (currencyId: string, field: string, value: any) => {
    setCurrencies(currencies.map(curr => {
      if (curr.id === currencyId) {
        return { ...curr, [field]: value };
      }
      return curr;
    }));
  };
  
  // تعيين عملة كافتراضية
  const setDefaultCurrency = (currencyId: string) => {
    setCurrencies(currencies.map(curr => ({
      ...curr,
      isDefault: curr.id === currencyId,
      // إعادة تعيين سعر الصرف للعملة الافتراضية إلى 1
      exchangeRate: curr.id === currencyId ? 1 : curr.exchangeRate
    })));
  };
  
  // تغيير ترتيب العملة
  const changeCurrencyOrder = (currencyId: string, direction: 'up' | 'down') => {
    const currIndex = currencies.findIndex(curr => curr.id === currencyId);
    if (
      (direction === 'up' && currIndex === 0) || 
      (direction === 'down' && currIndex === currencies.length - 1)
    ) {
      return;
    }
    
    const newCurrencies = [...currencies];
    const targetIndex = direction === 'up' ? currIndex - 1 : currIndex + 1;
    
    // تبديل الترتيب
    [newCurrencies[currIndex].order, newCurrencies[targetIndex].order] = 
      [newCurrencies[targetIndex].order, newCurrencies[currIndex].order];
    
    // إعادة ترتيب المصفوفة
    setCurrencies([...newCurrencies].sort((a, b) => a.order - b.order));
  };
  
  // تحديث أسعار الصرف تلقائيًا
  const updateExchangeRates = async () => {
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // في الإنتاج، سيتم استدعاء API لتحديث أسعار الصرف
      // هنا نقوم بمحاكاة تحديث أسعار الصرف بقيم عشوائية
      const defaultCurrency = currencies.find(curr => curr.isDefault);
      
      if (defaultCurrency) {
        setCurrencies(currencies.map(curr => {
          if (curr.isDefault) {
            return curr; // لا تغيير في العملة الافتراضية
          }
          
          // توليد سعر صرف عشوائي بين 0.1 و 2
          const randomRate = Math.random() * 1.9 + 0.1;
          return {
            ...curr,
            exchangeRate: parseFloat(randomRate.toFixed(4))
          };
        }));
      }
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating exchange rates:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // حفظ إعدادات العملة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات العملة
      console.log('Currency settings saved:', { currencySettings, currencies });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving currency settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات العملة' : 'Currency Settings'}
          </h2>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              disabled={isLoading || !currencySettings.enableMultiCurrency || !currencySettings.autoUpdateExchangeRates}
              onClick={updateExchangeRates}
              className="flex items-center gap-2"
            >
              <DollarSign className="h-4 w-4" />
              <span>
                {language === 'ar' ? 'تحديث أسعار الصرف' : 'Update Exchange Rates'}
              </span>
            </Button>
            
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              <span>
                {isLoading
                  ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                  : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
                }
              </span>
            </Button>
          </div>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات العملة بنجاح' : 'Currency settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* الإعدادات العامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableMultiCurrency"
                  name="enableMultiCurrency"
                  checked={currencySettings.enableMultiCurrency}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableMultiCurrency" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل تعدد العملات' : 'Enable Multi-currency'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showCurrencySwitcher"
                  name="showCurrencySwitcher"
                  checked={currencySettings.showCurrencySwitcher}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!currencySettings.enableMultiCurrency}
                />
                <label htmlFor="showCurrencySwitcher" className={cn(
                  "text-sm font-medium",
                  !currencySettings.enableMultiCurrency && "text-gray-400"
                )}>
                  {language === 'ar' ? 'إظهار محول العملة' : 'Show Currency Switcher'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoUpdateExchangeRates"
                  name="autoUpdateExchangeRates"
                  checked={currencySettings.autoUpdateExchangeRates}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!currencySettings.enableMultiCurrency}
                />
                <label htmlFor="autoUpdateExchangeRates" className={cn(
                  "text-sm font-medium",
                  !currencySettings.enableMultiCurrency && "text-gray-400"
                )}>
                  {language === 'ar' ? 'تحديث أسعار الصرف تلقائيًا' : 'Auto-update Exchange Rates'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مزود أسعار الصرف' : 'Exchange Rate Provider'}
                </label>
                <select
                  name="exchangeRateProvider"
                  value={currencySettings.exchangeRateProvider}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!currencySettings.enableMultiCurrency || !currencySettings.autoUpdateExchangeRates}
                >
                  <option value="manual">{language === 'ar' ? 'يدوي' : 'Manual'}</option>
                  <option value="openexchangerates">Open Exchange Rates</option>
                  <option value="currencylayer">Currency Layer</option>
                  <option value="exchangerate-api">ExchangeRate-API</option>
                </select>
              </div>
              
              {currencySettings.exchangeRateProvider !== 'manual' && (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'مفتاح API' : 'API Key'}
                  </label>
                  <Input
                    type="password"
                    name="exchangeRateApiKey"
                    value={currencySettings.exchangeRateApiKey}
                    onChange={handleSettingsChange}
                    disabled={!currencySettings.enableMultiCurrency || !currencySettings.autoUpdateExchangeRates}
                    className={(!currencySettings.enableMultiCurrency || !currencySettings.autoUpdateExchangeRates) ? "opacity-50" : ""}
                  />
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'تكرار التحديث' : 'Update Frequency'}
                </label>
                <select
                  name="updateFrequency"
                  value={currencySettings.updateFrequency}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!currencySettings.enableMultiCurrency || !currencySettings.autoUpdateExchangeRates}
                >
                  <option value="hourly">{language === 'ar' ? 'كل ساعة' : 'Hourly'}</option>
                  <option value="daily">{language === 'ar' ? 'يوميًا' : 'Daily'}</option>
                  <option value="weekly">{language === 'ar' ? 'أسبوعيًا' : 'Weekly'}</option>
                  <option value="monthly">{language === 'ar' ? 'شهريًا' : 'Monthly'}</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* العملات المتاحة */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                {language === 'ar' ? 'العملات المتاحة' : 'Available Currencies'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCurrency}
                className="flex items-center gap-1"
                disabled={!currencySettings.enableMultiCurrency}
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة عملة' : 'Add Currency'}</span>
              </Button>
            </div>
            
            <div className="space-y-4">
              {currencies.map((curr) => (
                <Card key={curr.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`enabled-${curr.id}`}
                          checked={curr.isEnabled}
                          onChange={(e) => updateCurrency(curr.id, 'isEnabled', e.target.checked)}
                          className="h-4 w-4 mr-2"
                          disabled={curr.isDefault || !currencySettings.enableMultiCurrency}
                        />
                        <label htmlFor={`enabled-${curr.id}`} className={cn(
                          "text-sm font-medium",
                          (curr.isDefault || !currencySettings.enableMultiCurrency) && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'مفعّل' : 'Enabled'}
                        </label>
                      </div>
                      
                      <div className="flex items-center ml-4">
                        <input
                          type="radio"
                          id={`default-${curr.id}`}
                          checked={curr.isDefault}
                          onChange={() => setDefaultCurrency(curr.id)}
                          className="h-4 w-4 mr-2"
                          disabled={!curr.isEnabled || !currencySettings.enableMultiCurrency}
                        />
                        <label htmlFor={`default-${curr.id}`} className={cn(
                          "text-sm font-medium",
                          (!curr.isEnabled || !currencySettings.enableMultiCurrency) && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'افتراضي' : 'Default'}
                        </label>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button
                        type="button"
                        onClick={() => changeCurrencyOrder(curr.id, 'up')}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={
                          currencies.indexOf(curr) === 0 || 
                          !currencySettings.enableMultiCurrency
                        }
                      >
                        <ArrowUp className={cn(
                          "h-4 w-4",
                          currencies.indexOf(curr) === 0 || !currencySettings.enableMultiCurrency
                            ? "text-gray-400"
                            : "text-slate-600 dark:text-slate-300"
                        )} />
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => changeCurrencyOrder(curr.id, 'down')}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={
                          currencies.indexOf(curr) === currencies.length - 1 || 
                          !currencySettings.enableMultiCurrency
                        }
                      >
                        <ArrowDown className={cn(
                          "h-4 w-4",
                          currencies.indexOf(curr) === currencies.length - 1 || !currencySettings.enableMultiCurrency
                            ? "text-gray-400"
                            : "text-slate-600 dark:text-slate-300"
                        )} />
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => deleteCurrency(curr.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={curr.isDefault || !currencySettings.enableMultiCurrency}
                      >
                        <Trash2 className={cn(
                          "h-4 w-4",
                          curr.isDefault || !currencySettings.enableMultiCurrency
                            ? "text-gray-400"
                            : "text-red-500"
                        )} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'رمز العملة' : 'Currency Code'}
                      </label>
                      <Input
                        value={curr.code}
                        onChange={(e) => updateCurrency(curr.id, 'code', e.target.value.toUpperCase())}
                        disabled={curr.isDefault || !currencySettings.enableMultiCurrency}
                        className={cn(
                          (curr.isDefault || !currencySettings.enableMultiCurrency) && "opacity-50"
                        )}
                        maxLength={3}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'اسم العملة' : 'Currency Name'}
                      </label>
                      <Input
                        value={curr.name}
                        onChange={(e) => updateCurrency(curr.id, 'name', e.target.value)}
                        disabled={!currencySettings.enableMultiCurrency}
                        className={!currencySettings.enableMultiCurrency ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'رمز العملة (الرمز)' : 'Currency Symbol'}
                      </label>
                      <Input
                        value={curr.symbol}
                        onChange={(e) => updateCurrency(curr.id, 'symbol', e.target.value)}
                        disabled={!currencySettings.enableMultiCurrency}
                        className={!currencySettings.enableMultiCurrency ? "opacity-50" : ""}
                        maxLength={3}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'عدد الخانات العشرية' : 'Decimal Places'}
                      </label>
                      <Input
                        type="number"
                        value={curr.decimalPlaces}
                        onChange={(e) => updateCurrency(curr.id, 'decimalPlaces', parseInt(e.target.value))}
                        min="0"
                        max="4"
                        disabled={!currencySettings.enableMultiCurrency}
                        className={!currencySettings.enableMultiCurrency ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'سعر الصرف' : 'Exchange Rate'}
                      </label>
                      <Input
                        type="number"
                        value={curr.exchangeRate}
                        onChange={(e) => updateCurrency(curr.id, 'exchangeRate', parseFloat(e.target.value))}
                        min="0.0001"
                        step="0.0001"
                        disabled={curr.isDefault || !currencySettings.enableMultiCurrency}
                        className={cn(
                          (curr.isDefault || !currencySettings.enableMultiCurrency) && "opacity-50"
                        )}
                      />
                      {curr.isDefault && (
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          {language === 'ar' 
                            ? 'العملة الافتراضية دائمًا لها سعر صرف 1'
                            : 'Default currency always has exchange rate of 1'
                          }
                        </p>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
