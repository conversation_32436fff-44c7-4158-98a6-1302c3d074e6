'use client';

import { useState } from 'react';
import { Save, Plus, Trash2, Globe, Check, X, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// نوع اللغة
interface Language {
  id: string;
  code: string;
  name: string;
  nativeName: string;
  isEnabled: boolean;
  isDefault: boolean;
  isRTL: boolean;
  flagIcon?: string;
  order: number;
  translationProgress: number;
}

// مكون إعدادات اللغة
export function LanguageSettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات اللغة
  const [languageSettings, setLanguageSettings] = useState({
    enableMultiLanguage: true,
    showLanguageSwitcher: true,
    autoDetectLanguage: true,
    defaultLanguageForNewContent: 'ar'
  });
  
  // حالة اللغات
  const [languages, setLanguages] = useState<Language[]>([
    {
      id: 'ar',
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      isEnabled: true,
      isDefault: true,
      isRTL: true,
      flagIcon: '/images/flags/sa.svg',
      order: 1,
      translationProgress: 100
    },
    {
      id: 'en',
      code: 'en',
      name: 'English',
      nativeName: 'English',
      isEnabled: true,
      isDefault: false,
      isRTL: false,
      flagIcon: '/images/flags/us.svg',
      order: 2,
      translationProgress: 100
    },
    {
      id: 'fr',
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      isEnabled: false,
      isDefault: false,
      isRTL: false,
      flagIcon: '/images/flags/fr.svg',
      order: 3,
      translationProgress: 30
    }
  ]);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات اللغة
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setLanguageSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };
  
  // إضافة لغة جديدة
  const addLanguage = () => {
    const newLanguage: Language = {
      id: `lang_${Date.now()}`,
      code: '',
      name: '',
      nativeName: '',
      isEnabled: false,
      isDefault: false,
      isRTL: false,
      order: languages.length + 1,
      translationProgress: 0
    };
    
    setLanguages([...languages, newLanguage]);
  };
  
  // حذف لغة
  const deleteLanguage = (languageId: string) => {
    // لا يمكن حذف اللغة الافتراضية
    if (languages.find(lang => lang.id === languageId)?.isDefault) {
      return;
    }
    
    setLanguages(languages.filter(lang => lang.id !== languageId));
  };
  
  // تحديث لغة
  const updateLanguage = (languageId: string, field: string, value: any) => {
    setLanguages(languages.map(lang => {
      if (lang.id === languageId) {
        return { ...lang, [field]: value };
      }
      return lang;
    }));
  };
  
  // تعيين لغة كافتراضية
  const setDefaultLanguage = (languageId: string) => {
    setLanguages(languages.map(lang => ({
      ...lang,
      isDefault: lang.id === languageId
    })));
  };
  
  // تغيير ترتيب اللغة
  const changeLanguageOrder = (languageId: string, direction: 'up' | 'down') => {
    const langIndex = languages.findIndex(lang => lang.id === languageId);
    if (
      (direction === 'up' && langIndex === 0) || 
      (direction === 'down' && langIndex === languages.length - 1)
    ) {
      return;
    }
    
    const newLanguages = [...languages];
    const targetIndex = direction === 'up' ? langIndex - 1 : langIndex + 1;
    
    // تبديل الترتيب
    [newLanguages[langIndex].order, newLanguages[targetIndex].order] = 
      [newLanguages[targetIndex].order, newLanguages[langIndex].order];
    
    // إعادة ترتيب المصفوفة
    setLanguages([...newLanguages].sort((a, b) => a.order - b.order));
  };
  
  // حفظ إعدادات اللغة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات اللغة
      console.log('Language settings saved:', { languageSettings, languages });
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving language settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات اللغة' : 'Language Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات اللغة بنجاح' : 'Language settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* الإعدادات العامة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableMultiLanguage"
                  name="enableMultiLanguage"
                  checked={languageSettings.enableMultiLanguage}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableMultiLanguage" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل تعدد اللغات' : 'Enable Multi-language'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showLanguageSwitcher"
                  name="showLanguageSwitcher"
                  checked={languageSettings.showLanguageSwitcher}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!languageSettings.enableMultiLanguage}
                />
                <label htmlFor="showLanguageSwitcher" className={cn(
                  "text-sm font-medium",
                  !languageSettings.enableMultiLanguage && "text-gray-400"
                )}>
                  {language === 'ar' ? 'إظهار محول اللغة' : 'Show Language Switcher'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoDetectLanguage"
                  name="autoDetectLanguage"
                  checked={languageSettings.autoDetectLanguage}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!languageSettings.enableMultiLanguage}
                />
                <label htmlFor="autoDetectLanguage" className={cn(
                  "text-sm font-medium",
                  !languageSettings.enableMultiLanguage && "text-gray-400"
                )}>
                  {language === 'ar' ? 'الكشف التلقائي عن لغة المتصفح' : 'Auto-detect Browser Language'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اللغة الافتراضية للمحتوى الجديد' : 'Default Language for New Content'}
                </label>
                <select
                  name="defaultLanguageForNewContent"
                  value={languageSettings.defaultLanguageForNewContent}
                  onChange={handleSettingsChange}
                  className={cn(
                    "w-full px-3 py-2 rounded-md border",
                    isDarkMode 
                      ? "bg-slate-700 border-slate-600 text-white" 
                      : "bg-white border-gray-300 text-slate-900"
                  )}
                  disabled={!languageSettings.enableMultiLanguage}
                >
                  {languages
                    .filter(lang => lang.isEnabled)
                    .map(lang => (
                      <option key={lang.id} value={lang.code}>
                        {lang.name} ({lang.nativeName})
                      </option>
                    ))
                  }
                </select>
              </div>
            </div>
          </div>
          
          {/* اللغات المتاحة */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {language === 'ar' ? 'اللغات المتاحة' : 'Available Languages'}
              </h3>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addLanguage}
                className="flex items-center gap-1"
                disabled={!languageSettings.enableMultiLanguage}
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة لغة' : 'Add Language'}</span>
              </Button>
            </div>
            
            <div className="space-y-4">
              {languages.map((lang) => (
                <Card key={lang.id} className={cn(
                  "p-4",
                  isDarkMode ? "bg-slate-700" : "bg-gray-50"
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`enabled-${lang.id}`}
                          checked={lang.isEnabled}
                          onChange={(e) => updateLanguage(lang.id, 'isEnabled', e.target.checked)}
                          className="h-4 w-4 mr-2"
                          disabled={lang.isDefault || !languageSettings.enableMultiLanguage}
                        />
                        <label htmlFor={`enabled-${lang.id}`} className={cn(
                          "text-sm font-medium",
                          (lang.isDefault || !languageSettings.enableMultiLanguage) && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'مفعّل' : 'Enabled'}
                        </label>
                      </div>
                      
                      <div className="flex items-center ml-4">
                        <input
                          type="radio"
                          id={`default-${lang.id}`}
                          checked={lang.isDefault}
                          onChange={() => setDefaultLanguage(lang.id)}
                          className="h-4 w-4 mr-2"
                          disabled={!lang.isEnabled || !languageSettings.enableMultiLanguage}
                        />
                        <label htmlFor={`default-${lang.id}`} className={cn(
                          "text-sm font-medium",
                          (!lang.isEnabled || !languageSettings.enableMultiLanguage) && "text-gray-400"
                        )}>
                          {language === 'ar' ? 'افتراضي' : 'Default'}
                        </label>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button
                        type="button"
                        onClick={() => changeLanguageOrder(lang.id, 'up')}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={
                          languages.indexOf(lang) === 0 || 
                          !languageSettings.enableMultiLanguage
                        }
                      >
                        <ArrowUp className={cn(
                          "h-4 w-4",
                          languages.indexOf(lang) === 0 || !languageSettings.enableMultiLanguage
                            ? "text-gray-400"
                            : "text-slate-600 dark:text-slate-300"
                        )} />
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => changeLanguageOrder(lang.id, 'down')}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={
                          languages.indexOf(lang) === languages.length - 1 || 
                          !languageSettings.enableMultiLanguage
                        }
                      >
                        <ArrowDown className={cn(
                          "h-4 w-4",
                          languages.indexOf(lang) === languages.length - 1 || !languageSettings.enableMultiLanguage
                            ? "text-gray-400"
                            : "text-slate-600 dark:text-slate-300"
                        )} />
                      </button>
                      
                      <button
                        type="button"
                        onClick={() => deleteLanguage(lang.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-600" : "hover:bg-gray-200"
                        )}
                        disabled={lang.isDefault || !languageSettings.enableMultiLanguage}
                      >
                        <Trash2 className={cn(
                          "h-4 w-4",
                          lang.isDefault || !languageSettings.enableMultiLanguage
                            ? "text-gray-400"
                            : "text-red-500"
                        )} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'رمز اللغة' : 'Language Code'}
                      </label>
                      <Input
                        value={lang.code}
                        onChange={(e) => updateLanguage(lang.id, 'code', e.target.value)}
                        disabled={lang.isDefault || !languageSettings.enableMultiLanguage}
                        className={cn(
                          (lang.isDefault || !languageSettings.enableMultiLanguage) && "opacity-50"
                        )}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'اسم اللغة (بالإنجليزية)' : 'Language Name (English)'}
                      </label>
                      <Input
                        value={lang.name}
                        onChange={(e) => updateLanguage(lang.id, 'name', e.target.value)}
                        disabled={!languageSettings.enableMultiLanguage}
                        className={!languageSettings.enableMultiLanguage ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'اسم اللغة (الأصلي)' : 'Native Name'}
                      </label>
                      <Input
                        value={lang.nativeName}
                        onChange={(e) => updateLanguage(lang.id, 'nativeName', e.target.value)}
                        disabled={!languageSettings.enableMultiLanguage}
                        className={!languageSettings.enableMultiLanguage ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={`rtl-${lang.id}`}
                        checked={lang.isRTL}
                        onChange={(e) => updateLanguage(lang.id, 'isRTL', e.target.checked)}
                        className="h-4 w-4 mr-2"
                        disabled={!languageSettings.enableMultiLanguage}
                      />
                      <label htmlFor={`rtl-${lang.id}`} className={cn(
                        "text-sm font-medium",
                        !languageSettings.enableMultiLanguage && "text-gray-400"
                      )}>
                        {language === 'ar' ? 'من اليمين إلى اليسار (RTL)' : 'Right-to-Left (RTL)'}
                      </label>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'رابط أيقونة العلم' : 'Flag Icon URL'}
                      </label>
                      <Input
                        value={lang.flagIcon || ''}
                        onChange={(e) => updateLanguage(lang.id, 'flagIcon', e.target.value)}
                        placeholder="/images/flags/xx.svg"
                        disabled={!languageSettings.enableMultiLanguage}
                        className={!languageSettings.enableMultiLanguage ? "opacity-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        {language === 'ar' ? 'نسبة اكتمال الترجمة (%)' : 'Translation Progress (%)'}
                      </label>
                      <Input
                        type="number"
                        value={lang.translationProgress}
                        onChange={(e) => updateLanguage(lang.id, 'translationProgress', parseInt(e.target.value))}
                        min="0"
                        max="100"
                        disabled={!languageSettings.enableMultiLanguage}
                        className={!languageSettings.enableMultiLanguage ? "opacity-50" : ""}
                      />
                      <div className="mt-2 h-2 bg-gray-200 dark:bg-slate-600 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-primary-500"
                          style={{ width: `${lang.translationProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
