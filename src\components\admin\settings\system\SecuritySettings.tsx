'use client';

import { useState } from 'react';
import { Save, Shield, Lock, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { Input } from '../../../../components/ui/Input';
import { Card } from '../../../../components/ui/Card';
import { useLanguageStore } from '../../../../stores/languageStore';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../lib/utils';

// مكون إعدادات الأمان
export function SecuritySettings() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة إعدادات الأمان
  const [securitySettings, setSecuritySettings] = useState({
    // إعدادات كلمة المرور
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSpecialChars: true,
    passwordExpiryDays: 90,
    
    // إعدادات الجلسة
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    
    // إعدادات التحقق بخطوتين
    enable2FA: false,
    require2FAForAdmins: false,
    
    // إعدادات CSRF
    csrfProtection: true,
    
    // إعدادات التحكم في الوصول
    enableRateLimiting: true,
    apiRequestsPerMinute: 60,
    
    // إعدادات الخصوصية
    enablePrivacyMode: false,
    dataRetentionDays: 365,
    
    // إعدادات API
    apiKey: 'sk_test_' + Math.random().toString(36).substring(2, 15),
    apiKeyLastReset: new Date().toISOString()
  });
  
  // حالة عرض مفتاح API
  const [showApiKey, setShowApiKey] = useState(false);
  
  // حالة التحميل والنجاح
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // تحديث إعدادات الأمان
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setSecuritySettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseInt(value) 
          : value
    }));
  };
  
  // إعادة تعيين مفتاح API
  const resetApiKey = () => {
    setSecuritySettings(prev => ({
      ...prev,
      apiKey: 'sk_test_' + Math.random().toString(36).substring(2, 15),
      apiKeyLastReset: new Date().toISOString()
    }));
  };
  
  // حفظ إعدادات الأمان
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // هنا سيتم تنفيذ منطق حفظ إعدادات الأمان
      console.log('Security settings saved:', securitySettings);
      
      setSaveSuccess(true);
      
      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving security settings:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card className={cn(
        "p-6",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {language === 'ar' ? 'إعدادات الأمان' : 'Security Settings'}
          </h2>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>
              {isLoading
                ? language === 'ar' ? 'جاري الحفظ...' : 'Saving...'
                : language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'
              }
            </span>
          </Button>
        </div>
        
        {saveSuccess && (
          <div className={cn(
            "mb-6 p-3 rounded-md",
            isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
          )}>
            {language === 'ar' ? 'تم حفظ إعدادات الأمان بنجاح' : 'Security settings saved successfully'}
          </div>
        )}
        
        <div className="space-y-6">
          {/* إعدادات كلمة المرور */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Lock className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات كلمة المرور' : 'Password Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الحد الأدنى لطول كلمة المرور' : 'Minimum Password Length'}
                </label>
                <Input
                  type="number"
                  name="passwordMinLength"
                  value={securitySettings.passwordMinLength}
                  onChange={handleSettingsChange}
                  min="6"
                  max="32"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مدة انتهاء صلاحية كلمة المرور (بالأيام)' : 'Password Expiry (days)'}
                </label>
                <Input
                  type="number"
                  name="passwordExpiryDays"
                  value={securitySettings.passwordExpiryDays}
                  onChange={handleSettingsChange}
                  min="0"
                  max="365"
                />
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {language === 'ar' 
                    ? 'استخدم 0 لتعطيل انتهاء صلاحية كلمة المرور'
                    : 'Use 0 to disable password expiry'
                  }
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="passwordRequireUppercase"
                  name="passwordRequireUppercase"
                  checked={securitySettings.passwordRequireUppercase}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="passwordRequireUppercase" className="text-sm font-medium">
                  {language === 'ar' ? 'تتطلب أحرف كبيرة' : 'Require Uppercase Letters'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="passwordRequireLowercase"
                  name="passwordRequireLowercase"
                  checked={securitySettings.passwordRequireLowercase}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="passwordRequireLowercase" className="text-sm font-medium">
                  {language === 'ar' ? 'تتطلب أحرف صغيرة' : 'Require Lowercase Letters'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="passwordRequireNumbers"
                  name="passwordRequireNumbers"
                  checked={securitySettings.passwordRequireNumbers}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="passwordRequireNumbers" className="text-sm font-medium">
                  {language === 'ar' ? 'تتطلب أرقام' : 'Require Numbers'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="passwordRequireSpecialChars"
                  name="passwordRequireSpecialChars"
                  checked={securitySettings.passwordRequireSpecialChars}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="passwordRequireSpecialChars" className="text-sm font-medium">
                  {language === 'ar' ? 'تتطلب رموز خاصة' : 'Require Special Characters'}
                </label>
              </div>
            </div>
          </div>
          
          {/* إعدادات الجلسة */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات الجلسة' : 'Session Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مهلة الجلسة (بالدقائق)' : 'Session Timeout (minutes)'}
                </label>
                <Input
                  type="number"
                  name="sessionTimeout"
                  value={securitySettings.sessionTimeout}
                  onChange={handleSettingsChange}
                  min="5"
                  max="1440"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الحد الأقصى لمحاولات تسجيل الدخول' : 'Max Login Attempts'}
                </label>
                <Input
                  type="number"
                  name="maxLoginAttempts"
                  value={securitySettings.maxLoginAttempts}
                  onChange={handleSettingsChange}
                  min="3"
                  max="10"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مدة القفل (بالدقائق)' : 'Lockout Duration (minutes)'}
                </label>
                <Input
                  type="number"
                  name="lockoutDuration"
                  value={securitySettings.lockoutDuration}
                  onChange={handleSettingsChange}
                  min="5"
                  max="1440"
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات التحقق بخطوتين */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {language === 'ar' ? 'التحقق بخطوتين' : 'Two-Factor Authentication'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enable2FA"
                  name="enable2FA"
                  checked={securitySettings.enable2FA}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enable2FA" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل التحقق بخطوتين' : 'Enable Two-Factor Authentication'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="require2FAForAdmins"
                  name="require2FAForAdmins"
                  checked={securitySettings.require2FAForAdmins}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                  disabled={!securitySettings.enable2FA}
                />
                <label htmlFor="require2FAForAdmins" className={cn(
                  "text-sm font-medium",
                  !securitySettings.enable2FA && "text-gray-400"
                )}>
                  {language === 'ar' ? 'إلزامي للمسؤولين' : 'Required for Administrators'}
                </label>
              </div>
            </div>
          </div>
          
          {/* إعدادات الحماية */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات الحماية' : 'Protection Settings'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="csrfProtection"
                  name="csrfProtection"
                  checked={securitySettings.csrfProtection}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="csrfProtection" className="text-sm font-medium">
                  {language === 'ar' ? 'حماية CSRF' : 'CSRF Protection'}
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableRateLimiting"
                  name="enableRateLimiting"
                  checked={securitySettings.enableRateLimiting}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enableRateLimiting" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل تحديد معدل الطلبات' : 'Enable Rate Limiting'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'طلبات API لكل دقيقة' : 'API Requests Per Minute'}
                </label>
                <Input
                  type="number"
                  name="apiRequestsPerMinute"
                  value={securitySettings.apiRequestsPerMinute}
                  onChange={handleSettingsChange}
                  min="10"
                  max="1000"
                  disabled={!securitySettings.enableRateLimiting}
                  className={!securitySettings.enableRateLimiting ? "opacity-50" : ""}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enablePrivacyMode"
                  name="enablePrivacyMode"
                  checked={securitySettings.enablePrivacyMode}
                  onChange={handleSettingsChange}
                  className="h-4 w-4 mr-2"
                />
                <label htmlFor="enablePrivacyMode" className="text-sm font-medium">
                  {language === 'ar' ? 'تفعيل وضع الخصوصية' : 'Enable Privacy Mode'}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مدة الاحتفاظ بالبيانات (بالأيام)' : 'Data Retention Period (days)'}
                </label>
                <Input
                  type="number"
                  name="dataRetentionDays"
                  value={securitySettings.dataRetentionDays}
                  onChange={handleSettingsChange}
                  min="30"
                  max="3650"
                />
              </div>
            </div>
          </div>
          
          {/* إعدادات API */}
          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Lock className="h-5 w-5" />
              {language === 'ar' ? 'إعدادات API' : 'API Settings'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'مفتاح API' : 'API Key'}
                </label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      type={showApiKey ? "text" : "password"}
                      value={securitySettings.apiKey}
                      readOnly
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    >
                      {showApiKey ? (
                        <EyeOff className="h-5 w-5 text-slate-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-slate-400" />
                      )}
                    </button>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetApiKey}
                    className="flex items-center gap-1"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>{language === 'ar' ? 'إعادة تعيين' : 'Reset'}</span>
                  </Button>
                </div>
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {language === 'ar'
                    ? `آخر إعادة تعيين: ${new Date(securitySettings.apiKeyLastReset).toLocaleString('ar')}`
                    : `Last reset: ${new Date(securitySettings.apiKeyLastReset).toLocaleString()}`
                  }
                </p>
              </div>
              
              <div className={cn(
                "p-3 rounded-md",
                isDarkMode ? "bg-amber-900/20 text-amber-300" : "bg-amber-50 text-amber-600"
              )}>
                <p className="text-sm">
                  {language === 'ar'
                    ? 'تحذير: إعادة تعيين مفتاح API ستؤدي إلى إبطال جميع الطلبات التي تستخدم المفتاح القديم.'
                    : 'Warning: Resetting the API key will invalidate all requests using the old key.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </form>
  );
}
