'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Shield,
  Lock,
  Check,
  X,
  Users,
  Settings,
  ShoppingCart,
  Package,
  FileText,
  Image,
  Briefcase
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { RoleForm } from './RoleForm';

// نوع الدور
interface Role {
  id: string;
  name: string;
  name_ar?: string;
  description: string;
  description_ar?: string;
  permissions: Permission[];
  usersCount: number;
  isSystem: boolean;
  createdAt: string;
}

// نوع الإذن
interface Permission {
  id: string;
  name: string;
  name_ar?: string;
  module: string;
  action: 'view' | 'create' | 'edit' | 'delete' | 'manage';
}

// بيانات الأدوار (محاكاة)
const roles: Role[] = [
  {
    id: 'admin',
    name: 'Administrator',
    name_ar: 'مدير النظام',
    description: 'Full access to all system features and settings',
    description_ar: 'وصول كامل إلى جميع ميزات وإعدادات النظام',
    permissions: [
      { id: 'all', name: 'All Permissions', name_ar: 'جميع الصلاحيات', module: 'system', action: 'manage' }
    ],
    usersCount: 3,
    isSystem: true,
    createdAt: new Date().toISOString()
  },
  {
    id: 'manager',
    name: 'Manager',
    name_ar: 'مدير',
    description: 'Access to manage products, orders, and users',
    description_ar: 'وصول لإدارة المنتجات والطلبات والمستخدمين',
    permissions: [
      { id: 'products-manage', name: 'Manage Products', name_ar: 'إدارة المنتجات', module: 'products', action: 'manage' },
      { id: 'orders-manage', name: 'Manage Orders', name_ar: 'إدارة الطلبات', module: 'orders', action: 'manage' },
      { id: 'users-view', name: 'View Users', name_ar: 'عرض المستخدمين', module: 'users', action: 'view' },
      { id: 'content-view', name: 'View Content', name_ar: 'عرض المحتوى', module: 'content', action: 'view' }
    ],
    usersCount: 5,
    isSystem: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'editor',
    name: 'Content Editor',
    name_ar: 'محرر المحتوى',
    description: 'Access to manage blog posts and pages',
    description_ar: 'وصول لإدارة مقالات المدونة والصفحات',
    permissions: [
      { id: 'content-manage', name: 'Manage Content', name_ar: 'إدارة المحتوى', module: 'content', action: 'manage' },
      { id: 'media-manage', name: 'Manage Media', name_ar: 'إدارة الوسائط', module: 'media', action: 'manage' }
    ],
    usersCount: 2,
    isSystem: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'customer-service',
    name: 'Customer Service',
    name_ar: 'خدمة العملاء',
    description: 'Access to manage orders and customer inquiries',
    description_ar: 'وصول لإدارة الطلبات واستفسارات العملاء',
    permissions: [
      { id: 'orders-view', name: 'View Orders', name_ar: 'عرض الطلبات', module: 'orders', action: 'view' },
      { id: 'orders-edit', name: 'Edit Orders', name_ar: 'تعديل الطلبات', module: 'orders', action: 'edit' },
      { id: 'users-view', name: 'View Users', name_ar: 'عرض المستخدمين', module: 'users', action: 'view' }
    ],
    usersCount: 4,
    isSystem: false,
    createdAt: new Date().toISOString()
  },
  {
    id: 'customer',
    name: 'Customer',
    name_ar: 'عميل',
    description: 'Regular customer account with basic access',
    description_ar: 'حساب عميل عادي مع وصول أساسي',
    permissions: [],
    usersCount: 120,
    isSystem: true,
    createdAt: new Date().toISOString()
  }
];

// مكون إدارة الأدوار
export function RolesManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  // حالة الأدوار
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([]);
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  
  // تحديث الأدوار المصفاة عند تغيير البحث
  useEffect(() => {
    let filtered = [...roles];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(role => 
        role.name.toLowerCase().includes(query) || 
        (role.name_ar && role.name_ar.includes(query)) ||
        role.description.toLowerCase().includes(query)
      );
    }
    
    setFilteredRoles(filtered);
  }, [searchQuery, roles]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredRoles.length / itemsPerPage);
  
  // الحصول على الأدوار للصفحة الحالية
  const currentRoles = filteredRoles.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تحرير دور
  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setShowRoleForm(true);
  };
  
  // إضافة دور جديد
  const handleAddRole = () => {
    setEditingRole(null);
    setShowRoleForm(true);
  };
  
  // حذف دور
  const handleDeleteRole = (roleId: string) => {
    // هنا سيتم تنفيذ منطق حذف الدور
    console.log('Delete role:', roleId);
    
    // في الإنتاج، سيتم استدعاء API لحذف الدور
    // وتحديث قائمة الأدوار
  };
  
  // حفظ الدور (إضافة أو تحديث)
  const handleSaveRole = (role: Role) => {
    if (editingRole) {
      // تحديث دور موجود
      console.log('Update role:', role);
      
      // في الإنتاج، سيتم استدعاء API لتحديث الدور
      // وتحديث قائمة الأدوار
    } else {
      // إضافة دور جديد
      console.log('Add role:', role);
      
      // في الإنتاج، سيتم استدعاء API لإضافة الدور
      // وتحديث قائمة الأدوار
    }
    
    setShowRoleForm(false);
  };
  
  // الحصول على أيقونة الوحدة
  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'products':
        return <Package className="h-4 w-4" />;
      case 'orders':
        return <ShoppingCart className="h-4 w-4" />;
      case 'users':
        return <Users className="h-4 w-4" />;
      case 'content':
        return <FileText className="h-4 w-4" />;
      case 'media':
        return <Image className="h-4 w-4" />;
      case 'services':
        return <Briefcase className="h-4 w-4" />;
      case 'system':
        return <Settings className="h-4 w-4" />;
      default:
        return <Lock className="h-4 w-4" />;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة الأدوار والصلاحيات' : 'Roles & Permissions'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة أدوار المستخدمين وصلاحياتهم في النظام'
              : 'Manage user roles and their permissions in the system'}
          </p>
        </div>
        
        <Button
          onClick={handleAddRole}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة دور' : 'Add Role'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن الأدوار...' : 'Search roles...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </Card>
      
      {/* جدول الأدوار */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الدور' : 'Role'}
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'المستخدمين' : 'Users'}
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'الصلاحيات' : 'Permissions'}
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentRoles.map((role) => (
                <tr key={role.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center",
                        role.isSystem
                          ? "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
                          : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                      )}>
                        <Shield className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">{role.name}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          {role.name_ar}
                        </p>
                      </div>
                      {role.isSystem && (
                        <span className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
                        )}>
                          {language === 'ar' ? 'نظام' : 'System'}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-sm line-clamp-2">
                      {language === 'ar' && role.description_ar ? role.description_ar : role.description}
                    </p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-slate-400" />
                      <span>{role.usersCount}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {role.permissions.length > 0 ? (
                        role.permissions.slice(0, 3).map((permission) => (
                          <span
                            key={permission.id}
                            className={cn(
                              "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                              "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                            )}
                          >
                            {getModuleIcon(permission.module)}
                            <span className="truncate max-w-[100px]">
                              {language === 'ar' && permission.name_ar ? permission.name_ar : permission.name}
                            </span>
                          </span>
                        ))
                      ) : (
                        <span className="text-sm text-slate-500 dark:text-slate-400">
                          {language === 'ar' ? 'لا توجد صلاحيات' : 'No permissions'}
                        </span>
                      )}
                      {role.permissions.length > 3 && (
                        <span className={cn(
                          "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                          "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                        )}>
                          +{role.permissions.length - 3}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleEditRole(role)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                        disabled={role.isSystem}
                      >
                        <Edit className={cn(
                          "h-5 w-5",
                          role.isSystem ? "text-gray-400" : "text-yellow-500"
                        )} />
                      </button>
                      <button
                        onClick={() => handleDeleteRole(role.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                        disabled={role.isSystem}
                      >
                        <Trash2 className={cn(
                          "h-5 w-5",
                          role.isSystem ? "text-gray-400" : "text-red-500"
                        )} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredRoles.length)} من ${filteredRoles.length} دور`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredRoles.length)} of ${filteredRoles.length} roles`
              }
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>
      
      {/* نموذج إضافة/تحرير الدور */}
      {showRoleForm && (
        <RoleForm
          role={editingRole}
          onSave={handleSaveRole}
          onCancel={() => setShowRoleForm(false)}
        />
      )}
    </div>
  );
}
