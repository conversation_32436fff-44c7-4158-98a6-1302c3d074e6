'use client';

import { useState, useEffect } from 'react';
import { X, Upload, MapPin } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// نوع المستخدم
interface User {
  id: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phone: string;
  role?: 'admin' | 'customer' | 'vendor' | 'staff';
  status: 'active' | 'inactive' | 'blocked';
  createdAt: string;
  lastLogin?: string | null;
  avatar?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  ordersCount?: number;
  totalSpent?: number;
  wishlistCount?: number;
  lastOrderDate?: string | null;
}

interface UserFormProps {
  user: User | null;
  onSave: (user: User) => void;
  onCancel: () => void;
}

export function UserForm({ user, onSave, onCancel }: UserFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  // حالة النموذج
  const [formData, setFormData] = useState<Partial<User>>({
    id: '',
    name: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: 'customer',
    status: 'active',
    createdAt: new Date().toISOString(),
    lastLogin: null,
    avatar: '',
    address: {
      street: '',
      city: '',
      state: '',
      zip: '',
      country: ''
    },
    ordersCount: 0,
    totalSpent: 0,
    wishlistCount: 0,
    lastOrderDate: null
  });

  // تحميل بيانات المستخدم إذا كان موجودًا
  useEffect(() => {
    if (user) {
      setFormData({
        ...user,
      });
    }
  }, [user]);

  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // التعامل مع حقول العنوان المتداخلة
    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        address: {
          ...prev.address!,
          [addressField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // تحديث الصورة الرمزية
  const handleAvatarChange = (url: string) => {
    setFormData(prev => ({
      ...prev,
      avatar: url,
    }));
  };

  // حفظ المستخدم
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من صحة البيانات
    if ((!formData.name && (!formData.firstName || !formData.lastName)) || !formData.email) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }

    // إنشاء معرف جديد إذا كان مستخدم جديد
    const userData: User = {
      ...(formData as User),
      id: user?.id || `user-${Date.now()}`,
      createdAt: user?.createdAt || new Date().toISOString(),
      lastLogin: user?.lastLogin || null,
      // إذا كان لدينا firstName و lastName ولكن ليس لدينا name، قم بإنشاء name
      name: formData.name || (formData.firstName && formData.lastName ? `${formData.firstName} ${formData.lastName}` : undefined),
    };

    onSave(userData);
  };

  // ترجمة الأدوار
  const getRoleOptions = () => {
    return [
      { value: 'admin', label: language === 'ar' ? 'مدير' : 'Admin' },
      { value: 'customer', label: language === 'ar' ? 'عميل' : 'Customer' },
      { value: 'vendor', label: language === 'ar' ? 'بائع' : 'Vendor' },
      { value: 'staff', label: language === 'ar' ? 'موظف' : 'Staff' },
    ];
  };

  // ترجمة الحالات
  const getStatusOptions = () => {
    return [
      { value: 'active', label: language === 'ar' ? 'نشط' : 'Active' },
      { value: 'inactive', label: language === 'ar' ? 'غير نشط' : 'Inactive' },
      { value: 'blocked', label: language === 'ar' ? 'محظور' : 'Blocked' },
    ];
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-2xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {user
              ? language === 'ar' ? 'تحرير المستخدم' : 'Edit User'
              : language === 'ar' ? 'إضافة مستخدم جديد' : 'Add New User'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* الصورة الرمزية */}
          <div className="flex flex-col items-center">
            <div className="relative mb-4">
              <img
                src={formData.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name || 'User')}&background=random&size=128`}
                alt="Avatar"
                className="h-32 w-32 rounded-full object-cover border-4 border-white dark:border-slate-700 shadow-md"
              />
              <div className="absolute bottom-0 right-0">
                <div className="bg-primary-500 text-white p-2 rounded-full cursor-pointer">
                  <Upload className="h-5 w-5" />
                </div>
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Input
                placeholder={language === 'ar' ? 'رابط الصورة الرمزية' : 'Avatar URL'}
                value={formData.newAvatarUrl || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, newAvatarUrl: e.target.value }))}
                className="w-64"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (formData.newAvatarUrl) {
                    handleAvatarChange(formData.newAvatarUrl);
                    setFormData(prev => ({ ...prev, newAvatarUrl: '' }));
                  }
                }}
                disabled={!formData.newAvatarUrl}
              >
                {language === 'ar' ? 'تطبيق' : 'Apply'}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم الأول */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الاسم الأول' : 'First Name'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="firstName"
                value={formData.firstName || ''}
                onChange={handleChange}
                required
              />
            </div>

            {/* الاسم الأخير */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الاسم الأخير' : 'Last Name'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="lastName"
                value={formData.lastName || ''}
                onChange={handleChange}
                required
              />
            </div>

            {/* البريد الإلكتروني */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                <span className="text-red-500">*</span>
              </label>
              <Input
                name="email"
                type="email"
                value={formData.email || ''}
                onChange={handleChange}
                required
              />
            </div>

            {/* رقم الهاتف */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
              </label>
              <Input
                name="phone"
                value={formData.phone || ''}
                onChange={handleChange}
              />
            </div>

            {/* كلمة المرور */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'كلمة المرور' : 'Password'}
                {!user && <span className="text-red-500">*</span>}
              </label>
              <Input
                name="password"
                type="password"
                value={formData.password || ''}
                onChange={handleChange}
                required={!user}
                placeholder={user ? (language === 'ar' ? 'اتركه فارغًا للاحتفاظ بنفس كلمة المرور' : 'Leave empty to keep the same password') : ''}
              />
            </div>

            {/* الحالة */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {language === 'ar' ? 'الحالة' : 'Status'}
                <span className="text-red-500">*</span>
              </label>
              <select
                name="status"
                value={formData.status || 'active'}
                onChange={handleChange}
                required
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-300 text-slate-900"
                )}
              >
                {getStatusOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* العنوان */}
          <div>
            <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {language === 'ar' ? 'العنوان' : 'Address'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* الشارع */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الشارع' : 'Street'}
                </label>
                <Input
                  name="address.street"
                  value={formData.address?.street || ''}
                  onChange={handleChange}
                />
              </div>

              {/* المدينة */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'المدينة' : 'City'}
                </label>
                <Input
                  name="address.city"
                  value={formData.address?.city || ''}
                  onChange={handleChange}
                />
              </div>

              {/* الولاية/المنطقة */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الولاية/المنطقة' : 'State/Province'}
                </label>
                <Input
                  name="address.state"
                  value={formData.address?.state || ''}
                  onChange={handleChange}
                />
              </div>

              {/* الرمز البريدي */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'الرمز البريدي' : 'Postal Code'}
                </label>
                <Input
                  name="address.zip"
                  value={formData.address?.zip || ''}
                  onChange={handleChange}
                />
              </div>

              {/* البلد */}
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'البلد' : 'Country'}
                </label>
                <Input
                  name="address.country"
                  value={formData.address?.country || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>

          {/* ملاحظات */}
          <div>
            <label className="block text-sm font-medium mb-1">
              {language === 'ar' ? 'ملاحظات' : 'Notes'}
            </label>
            <textarea
              name="notes"
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
            />
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              {language === 'ar' ? 'حفظ المستخدم' : 'Save User'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
