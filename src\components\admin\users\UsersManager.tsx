'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  ArrowUpDown,
  ChevronLeft, 
  ChevronRight,
  Shield,
  User,
  UserCog,
  Mail,
  Calendar
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { User as UserType } from '../../../types';

// بيانات تجريبية للمستخدمين
const mockUsers: UserType[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'أحمد',
    lastName: 'المدير',
    role: 'admin',
    createdAt: '2023-01-01'
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    firstName: 'محمد',
    lastName: 'أحمد',
    role: 'user',
    createdAt: '2023-01-15'
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    firstName: 'سارة',
    lastName: 'علي',
    role: 'user',
    createdAt: '2023-02-01'
  },
  {
    id: 'user-4',
    email: '<EMAIL>',
    firstName: 'خالد',
    lastName: 'المدير',
    role: 'admin',
    createdAt: '2023-02-15'
  },
  {
    id: 'user-5',
    email: '<EMAIL>',
    firstName: 'فاطمة',
    lastName: 'حسن',
    role: 'user',
    createdAt: '2023-03-01'
  },
  {
    id: 'user-6',
    email: '<EMAIL>',
    firstName: 'عمر',
    lastName: 'محمود',
    role: 'user',
    createdAt: '2023-03-15'
  },
  {
    id: 'user-7',
    email: '<EMAIL>',
    firstName: 'نورة',
    lastName: 'سعيد',
    role: 'user',
    createdAt: '2023-04-01'
  },
  {
    id: 'user-8',
    email: '<EMAIL>',
    firstName: 'يوسف',
    lastName: 'المحرر',
    role: 'admin',
    createdAt: '2023-04-15'
  },
  {
    id: 'user-9',
    email: '<EMAIL>',
    firstName: 'ليلى',
    lastName: 'عبدالله',
    role: 'user',
    createdAt: '2023-05-01'
  },
  {
    id: 'user-10',
    email: '<EMAIL>',
    firstName: 'حسن',
    lastName: 'علي',
    role: 'user',
    createdAt: '2023-05-15'
  }
];

// مكون إدارة المستخدمين
export function UsersManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof UserType>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  
  // حالة المستخدمين
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([]);
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<UserType | null>(null);
  
  // تصفية وترتيب المستخدمين
  useEffect(() => {
    let result = [...mockUsers];
    
    // تطبيق البحث
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(user => 
        user.email.toLowerCase().includes(query) || 
        (user.firstName && user.firstName.toLowerCase().includes(query)) ||
        (user.lastName && user.lastName.toLowerCase().includes(query))
      );
    }
    
    // تطبيق تصفية الدور
    if (selectedRole) {
      result = result.filter(user => user.role === selectedRole);
    }
    
    // تطبيق الترتيب
    result.sort((a, b) => {
      if (sortField === 'createdAt') {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      }
      
      const fieldA = String(a[sortField] || '');
      const fieldB = String(b[sortField] || '');
      
      return sortDirection === 'asc' 
        ? fieldA.localeCompare(fieldB)
        : fieldB.localeCompare(fieldA);
    });
    
    setFilteredUsers(result);
  }, [searchQuery, selectedRole, sortField, sortDirection]);
  
  // حساب عدد الصفحات
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  
  // الحصول على المستخدمين للصفحة الحالية
  const currentUsers = filteredUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // تغيير ترتيب الحقل
  const handleSort = (field: keyof UserType) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // تحرير مستخدم
  const handleEditUser = (user: UserType) => {
    setEditingUser(user);
    setShowUserForm(true);
  };
  
  // إضافة مستخدم جديد
  const handleAddUser = () => {
    setEditingUser(null);
    setShowUserForm(true);
  };
  
  // حذف مستخدم
  const handleDeleteUser = (userId: string) => {
    // هنا سيتم تنفيذ منطق حذف المستخدم
    console.log('Delete user:', userId);
    
    // في الإنتاج، سيتم استدعاء API لحذف المستخدم
    // وتحديث قائمة المستخدمين
  };
  
  // عرض تفاصيل المستخدم
  const handleViewUser = (userId: string) => {
    // هنا سيتم تنفيذ منطق عرض تفاصيل المستخدم
    console.log('View user:', userId);
    
    // في الإنتاج، سيتم الانتقال إلى صفحة تفاصيل المستخدم
    // أو فتح نافذة منبثقة لعرض التفاصيل
  };
  
  // ترجمة دور المستخدم
  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin':
        return language === 'ar' ? 'مدير' : 'Admin';
      case 'user':
        return language === 'ar' ? 'مستخدم' : 'User';
      default:
        return role;
    }
  };
  
  // لون دور المستخدم
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'user':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };
  
  // أيقونة دور المستخدم
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'user':
        return <User className="h-4 w-4" />;
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة المستخدمين' : 'Users Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar' 
              ? 'إدارة المستخدمين والأدوار والصلاحيات'
              : 'Manage users, roles, and permissions'}
          </p>
        </div>
        
        <Button
          onClick={handleAddUser}
          className="flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          <span>{language === 'ar' ? 'إضافة مستخدم' : 'Add User'}</span>
        </Button>
      </div>
      
      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن المستخدمين...' : 'Search users...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="w-full md:w-64">
            <select
              value={selectedRole || ''}
              onChange={(e) => setSelectedRole(e.target.value || null)}
              className={cn(
                "w-full px-3 py-2 rounded-md border",
                isDarkMode 
                  ? "bg-slate-700 border-slate-600 text-white" 
                  : "bg-white border-gray-300 text-slate-900"
              )}
            >
              <option value="">
                {language === 'ar' ? 'جميع الأدوار' : 'All Roles'}
              </option>
              <option value="admin">
                {language === 'ar' ? 'مدير' : 'Admin'}
              </option>
              <option value="user">
                {language === 'ar' ? 'مستخدم' : 'User'}
              </option>
            </select>
          </div>
        </div>
      </Card>
      
      {/* جدول المستخدمين */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'المستخدم' : 'User'}
                </th>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('email')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-center">
                  {language === 'ar' ? 'الدور' : 'Role'}
                </th>
                <th className="px-6 py-3 text-left">
                  <button 
                    onClick={() => handleSort('createdAt')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'تاريخ التسجيل' : 'Registered'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y dark:divide-slate-700">
              {currentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-10 h-10 rounded-full flex items-center justify-center",
                        isDarkMode ? "bg-slate-700" : "bg-slate-200"
                      )}>
                        <span className="text-lg font-medium">
                          {user.firstName?.charAt(0) || user.email.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">
                          {user.firstName} {user.lastName}
                        </p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">
                          ID: {user.id}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-slate-400" />
                      {user.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs flex items-center justify-center gap-1 w-fit mx-auto",
                      getRoleColor(user.role)
                    )}>
                      {getRoleIcon(user.role)}
                      {getRoleText(user.role)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-slate-400" />
                      {new Date(user.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewUser(user.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>
                      <button
                        onClick={() => handleEditUser(user)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Edit className="h-5 w-5 text-yellow-500" />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* ترقيم الصفحات */}
        <div className="px-6 py-4 flex items-center justify-between border-t dark:border-slate-700">
          <div>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {language === 'ar'
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, filteredUsers.length)} من ${filteredUsers.length} مستخدم`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, filteredUsers.length)} of ${filteredUsers.length} users`
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={cn(
                "p-2 rounded-md",
                currentPage === 1 
                  ? "opacity-50 cursor-not-allowed" 
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            
            <span className="text-sm">
              {language === 'ar'
                ? `${currentPage} من ${totalPages}`
                : `${currentPage} of ${totalPages}`
              }
            </span>
            
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={cn(
                "p-2 rounded-md",
                currentPage === totalPages 
                  ? "opacity-50 cursor-not-allowed" 
                  : isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
              )}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
}
