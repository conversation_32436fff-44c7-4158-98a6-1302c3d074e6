'use client';

import { useState, useEffect } from 'react';
import { 
  X, 
  Plus, 
  Trash2, 
  Calendar, 
  DollarSign, 
  Truck, 
  FileText,
  Calculator
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// نوع عنصر عرض السعر
interface QuoteItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  discount?: number;
  total: number;
}

// نوع عرض السعر
interface Quote {
  id: string;
  wholesaleOrderId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  companyName: string;
  items: QuoteItem[];
  subtotal: number;
  discount: number;
  tax: number;
  shipping: number;
  total: number;
  notes: string;
  paymentTerms: string;
  shippingMethod: string;
  expiryDate: string;
  createdAt: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
}

interface QuoteFormProps {
  quote: Quote | null;
  wholesaleOrderId?: string;
  customerInfo?: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  onSave: (quote: Quote) => void;
  onCancel: () => void;
}

export function QuoteForm({ 
  quote, 
  wholesaleOrderId, 
  customerInfo, 
  onSave, 
  onCancel 
}: QuoteFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // حالة النموذج
  const [formData, setFormData] = useState<Partial<Quote>>({
    id: '',
    wholesaleOrderId: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    companyName: '',
    items: [],
    subtotal: 0,
    discount: 0,
    tax: 0,
    shipping: 0,
    total: 0,
    notes: '',
    paymentTerms: '',
    shippingMethod: '',
    expiryDate: '',
    createdAt: new Date().toISOString(),
    status: 'draft',
  });
  
  // تحميل بيانات عرض السعر إذا كانت موجودة
  useEffect(() => {
    if (quote) {
      setFormData({
        ...quote,
      });
    } else if (wholesaleOrderId || customerInfo) {
      setFormData(prev => ({
        ...prev,
        wholesaleOrderId: wholesaleOrderId || '',
        customerName: customerInfo?.name || '',
        customerEmail: customerInfo?.email || '',
        customerPhone: customerInfo?.phone || '',
        companyName: customerInfo?.company || '',
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 يوم من الآن
      }));
    }
  }, [quote, wholesaleOrderId, customerInfo]);
  
  // تحديث حقل في النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // إضافة عنصر جديد
  const addItem = () => {
    const newItem: QuoteItem = {
      id: `item-${Date.now()}`,
      name: '',
      price: 0,
      quantity: 1,
      discount: 0,
      total: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem],
    }));
  };
  
  // تحديث عنصر
  const updateItem = (index: number, field: keyof QuoteItem, value: string | number) => {
    const updatedItems = [...(formData.items || [])];
    
    // تحديث الحقل المحدد
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };
    
    // إعادة حساب المجموع للعنصر
    const item = updatedItems[index];
    const itemTotal = item.price * item.quantity;
    const itemDiscount = item.discount || 0;
    updatedItems[index].total = itemTotal - (itemTotal * itemDiscount / 100);
    
    setFormData(prev => ({
      ...prev,
      items: updatedItems,
    }));
    
    // إعادة حساب المجاميع
    calculateTotals(updatedItems);
  };
  
  // حذف عنصر
  const removeItem = (index: number) => {
    const updatedItems = [...(formData.items || [])];
    updatedItems.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      items: updatedItems,
    }));
    
    // إعادة حساب المجاميع
    calculateTotals(updatedItems);
  };
  
  // حساب المجاميع
  const calculateTotals = (items: QuoteItem[]) => {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(formData.discount?.toString() || '0');
    const shipping = parseFloat(formData.shipping?.toString() || '0');
    
    // حساب الضريبة (15% مثلاً)
    const taxRate = 0.15;
    const tax = (subtotal - discount) * taxRate;
    
    // المجموع الكلي
    const total = subtotal - discount + tax + shipping;
    
    setFormData(prev => ({
      ...prev,
      subtotal,
      tax,
      total,
    }));
  };
  
  // تنسيق العملة
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    });
  };
  
  // حفظ عرض السعر
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    if (!formData.customerName || !formData.companyName || !formData.expiryDate) {
      alert(language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }
    
    if (!formData.items || formData.items.length === 0) {
      alert(language === 'ar' ? 'يرجى إضافة عنصر واحد على الأقل' : 'Please add at least one item');
      return;
    }
    
    // التحقق من صحة العناصر
    const invalidItems = formData.items.filter(item => !item.name || item.price <= 0);
    if (invalidItems.length > 0) {
      alert(language === 'ar' ? 'يرجى ملء جميع بيانات العناصر بشكل صحيح' : 'Please fill all item details correctly');
      return;
    }
    
    // إنشاء معرف جديد إذا كان عرض سعر جديد
    const quoteData: Quote = {
      ...(formData as Quote),
      id: quote?.id || `quote-${Date.now()}`,
      createdAt: quote?.createdAt || new Date().toISOString(),
    };
    
    onSave(quoteData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <h2 className="text-xl font-bold">
            {quote
              ? language === 'ar' ? 'تحرير عرض السعر' : 'Edit Quote'
              : language === 'ar' ? 'إنشاء عرض سعر جديد' : 'Create New Quote'
            }
          </h2>
          <button
            onClick={onCancel}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* معلومات العميل */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'معلومات العميل' : 'Customer Information'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم العميل' : 'Customer Name'}
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  name="customerName"
                  value={formData.customerName || ''}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'اسم الشركة' : 'Company Name'}
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  name="companyName"
                  value={formData.companyName || ''}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </label>
                <Input
                  name="customerEmail"
                  type="email"
                  value={formData.customerEmail || ''}
                  onChange={handleChange}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                </label>
                <Input
                  name="customerPhone"
                  value={formData.customerPhone || ''}
                  onChange={handleChange}
                />
              </div>
            </div>
          </div>
          
          {/* عناصر عرض السعر */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium">
                {language === 'ar' ? 'عناصر عرض السعر' : 'Quote Items'}
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addItem}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                <span>{language === 'ar' ? 'إضافة عنصر' : 'Add Item'}</span>
              </Button>
            </div>
            
            {formData.items && formData.items.length > 0 ? (
              <div className="space-y-4">
                <div className={cn(
                  "grid grid-cols-12 gap-2 px-4 py-2 rounded-t-md text-sm font-medium",
                  isDarkMode ? "bg-slate-700" : "bg-slate-100"
                )}>
                  <div className="col-span-5">{language === 'ar' ? 'الوصف' : 'Description'}</div>
                  <div className="col-span-2 text-center">{language === 'ar' ? 'السعر' : 'Price'}</div>
                  <div className="col-span-1 text-center">{language === 'ar' ? 'الكمية' : 'Qty'}</div>
                  <div className="col-span-1 text-center">{language === 'ar' ? 'الخصم %' : 'Disc %'}</div>
                  <div className="col-span-2 text-center">{language === 'ar' ? 'المجموع' : 'Total'}</div>
                  <div className="col-span-1"></div>
                </div>
                
                {formData.items.map((item, index) => (
                  <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                    <div className="col-span-5">
                      <Input
                        value={item.name}
                        onChange={(e) => updateItem(index, 'name', e.target.value)}
                        placeholder={language === 'ar' ? 'اسم/وصف العنصر' : 'Item name/description'}
                        required
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.price}
                        onChange={(e) => updateItem(index, 'price', parseFloat(e.target.value))}
                        required
                      />
                    </div>
                    <div className="col-span-1">
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value))}
                        required
                      />
                    </div>
                    <div className="col-span-1">
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={item.discount || 0}
                        onChange={(e) => updateItem(index, 'discount', parseFloat(e.target.value))}
                      />
                    </div>
                    <div className="col-span-2 text-center font-medium">
                      {formatCurrency(item.total)}
                    </div>
                    <div className="col-span-1 flex justify-center">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Trash2 className="h-5 w-5 text-red-500" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={cn(
                "p-8 text-center rounded-md",
                isDarkMode ? "bg-slate-700" : "bg-slate-100"
              )}>
                <p className="text-slate-500 dark:text-slate-400 mb-4">
                  {language === 'ar'
                    ? 'لا توجد عناصر في عرض السعر'
                    : 'No items in the quote'
                  }
                </p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addItem}
                  className="flex items-center gap-2 mx-auto"
                >
                  <Plus className="h-4 w-4" />
                  <span>{language === 'ar' ? 'إضافة عنصر' : 'Add Item'}</span>
                </Button>
              </div>
            )}
          </div>
          
          {/* المجاميع والتفاصيل الإضافية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'تفاصيل إضافية' : 'Additional Details'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'شروط الدفع' : 'Payment Terms'}
                  </label>
                  <Input
                    name="paymentTerms"
                    value={formData.paymentTerms || ''}
                    onChange={handleChange}
                    placeholder={language === 'ar' ? 'مثال: 50% مقدم، 50% عند التسليم' : 'e.g. 50% advance, 50% on delivery'}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'طريقة الشحن' : 'Shipping Method'}
                  </label>
                  <Input
                    name="shippingMethod"
                    value={formData.shippingMethod || ''}
                    onChange={handleChange}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'تاريخ انتهاء العرض' : 'Expiry Date'}
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
                    <Input
                      name="expiryDate"
                      type="date"
                      value={formData.expiryDate || ''}
                      onChange={handleChange}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'ملاحظات' : 'Notes'}
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes || ''}
                    onChange={handleChange}
                    rows={4}
                    className={cn(
                      "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                      isDarkMode
                        ? "bg-slate-700 border-slate-600 text-white"
                        : "bg-white border-slate-300 text-slate-900"
                    )}
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'ملخص عرض السعر' : 'Quote Summary'}
              </h3>
              
              <div className={cn(
                "p-4 rounded-md space-y-3",
                isDarkMode ? "bg-slate-700" : "bg-slate-100"
              )}>
                <div className="flex justify-between">
                  <span>{language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}</span>
                  <span className="font-medium">{formatCurrency(formData.subtotal || 0)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span>{language === 'ar' ? 'الخصم' : 'Discount'}</span>
                  <div className="flex-1">
                    <Input
                      name="discount"
                      type="number"
                      min="0"
                      value={formData.discount || 0}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        setFormData(prev => ({ ...prev, discount: value }));
                        calculateTotals(formData.items || []);
                      }}
                      className="h-8 text-right"
                    />
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <span>{language === 'ar' ? 'الضريبة (15%)' : 'Tax (15%)'}</span>
                  <span className="font-medium">{formatCurrency(formData.tax || 0)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span>{language === 'ar' ? 'الشحن' : 'Shipping'}</span>
                  <div className="flex-1">
                    <Input
                      name="shipping"
                      type="number"
                      min="0"
                      value={formData.shipping || 0}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        setFormData(prev => ({ ...prev, shipping: value }));
                        calculateTotals(formData.items || []);
                      }}
                      className="h-8 text-right"
                    />
                  </div>
                </div>
                
                <div className="pt-3 border-t border-slate-200 dark:border-slate-600 flex justify-between text-lg font-bold">
                  <span>{language === 'ar' ? 'المجموع الكلي' : 'Total'}</span>
                  <span>{formatCurrency(formData.total || 0)}</span>
                </div>
                
                <div className="pt-3">
                  <label className="block text-sm font-medium mb-1">
                    {language === 'ar' ? 'حالة عرض السعر' : 'Quote Status'}
                  </label>
                  <select
                    name="status"
                    value={formData.status || 'draft'}
                    onChange={handleChange}
                    className={cn(
                      "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
                      isDarkMode
                        ? "bg-slate-700 border-slate-600 text-white"
                        : "bg-white border-slate-300 text-slate-900"
                    )}
                  >
                    <option value="draft">{language === 'ar' ? 'مسودة' : 'Draft'}</option>
                    <option value="sent">{language === 'ar' ? 'تم الإرسال' : 'Sent'}</option>
                    <option value="accepted">{language === 'ar' ? 'مقبول' : 'Accepted'}</option>
                    <option value="rejected">{language === 'ar' ? 'مرفوض' : 'Rejected'}</option>
                    <option value="expired">{language === 'ar' ? 'منتهي الصلاحية' : 'Expired'}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          {/* أزرار الإجراءات */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-2"
            >
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'حفظ عرض السعر' : 'Save Quote'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
