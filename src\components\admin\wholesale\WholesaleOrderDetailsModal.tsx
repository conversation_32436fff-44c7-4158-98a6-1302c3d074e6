'use client';

import { 
  X, 
  Building, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle,
  Send,
  Printer,
  Download,
  DollarSign,
  Truck,
  CreditCard
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';

// نوع عنصر طلب الجملة
interface WholesaleOrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

// نوع طلب الجملة
interface WholesaleOrder {
  id: string;
  company: {
    name: string;
    email: string;
    phone: string;
    contactPerson: string;
    taxNumber?: string;
  };
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: WholesaleOrderItem[];
  date: string;
  status: 'pending' | 'quoted' | 'approved' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'partial' | 'paid' | 'failed';
  paymentMethod: string;
  shippingMethod: string;
  trackingNumber?: string;
  total: number;
  subtotal: number;
  shipping: number;
  tax: number;
  discount: number;
  notes?: string;
  quoteExpiryDate?: string;
}

interface WholesaleOrderDetailsModalProps {
  order: WholesaleOrder;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: string) => void;
  onUpdatePaymentStatus: (orderId: string, status: string) => void;
}

export function WholesaleOrderDetailsModal({ 
  order, 
  onClose, 
  onUpdateStatus, 
  onUpdatePaymentStatus 
}: WholesaleOrderDetailsModalProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  
  // ترجمة حالة الطلب
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'quoted':
        return language === 'ar' ? 'تم تقديم عرض سعر' : 'Quoted';
      case 'approved':
        return language === 'ar' ? 'تمت الموافقة' : 'Approved';
      case 'processing':
        return language === 'ar' ? 'قيد المعالجة' : 'Processing';
      case 'shipped':
        return language === 'ar' ? 'تم الشحن' : 'Shipped';
      case 'delivered':
        return language === 'ar' ? 'تم التسليم' : 'Delivered';
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case 'quoted':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case 'approved':
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400";
      case 'processing':
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";
      case 'shipped':
        return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-400";
      case 'delivered':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case 'cancelled':
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };
  
  // ترجمة حالة الدفع
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'partial':
        return language === 'ar' ? 'مدفوع جزئياً' : 'Partially Paid';
      case 'paid':
        return language === 'ar' ? 'مدفوع' : 'Paid';
      case 'failed':
        return language === 'ar' ? 'فشل الدفع' : 'Failed';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الدفع
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case 'partial':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case 'paid':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case 'failed':
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };
  
  // تنسيق العملة
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className={cn(
        "w-full max-w-4xl max-h-[90vh] overflow-y-auto",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className={cn(
          "sticky top-0 z-10 px-6 py-4 flex items-center justify-between border-b",
          isDarkMode ? "bg-slate-800 border-slate-700" : "bg-white border-gray-200"
        )}>
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-bold">
              {language === 'ar' ? 'تفاصيل طلب الجملة' : 'Wholesale Order Details'}
            </h2>
            <span className="text-sm text-slate-500 dark:text-slate-400">
              {order.id}
            </span>
          </div>
          <button
            onClick={onClose}
            className={cn(
              "p-2 rounded-full",
              isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          {/* معلومات الطلب */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <Calendar className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'تاريخ الطلب' : 'Order Date'}
                </h3>
              </div>
              <p>
                {new Date(order.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <FileText className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'حالة الطلب' : 'Order Status'}
                </h3>
              </div>
              <div className="flex items-center justify-between">
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs",
                  getStatusColor(order.status)
                )}>
                  {getStatusText(order.status)}
                </span>
                
                <select
                  value={order.status}
                  onChange={(e) => onUpdateStatus(order.id, e.target.value)}
                  className={cn(
                    "px-2 py-1 rounded-md border text-sm",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white"
                      : "bg-white border-slate-300 text-slate-900"
                  )}
                >
                  <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                  <option value="quoted">{language === 'ar' ? 'تم تقديم عرض سعر' : 'Quoted'}</option>
                  <option value="approved">{language === 'ar' ? 'تمت الموافقة' : 'Approved'}</option>
                  <option value="processing">{language === 'ar' ? 'قيد المعالجة' : 'Processing'}</option>
                  <option value="shipped">{language === 'ar' ? 'تم الشحن' : 'Shipped'}</option>
                  <option value="delivered">{language === 'ar' ? 'تم التسليم' : 'Delivered'}</option>
                  <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                </select>
              </div>
              
              {order.quoteExpiryDate && (
                <div className="mt-3 text-sm">
                  <p className="text-slate-500 dark:text-slate-400">
                    {language === 'ar' ? 'تاريخ انتهاء العرض:' : 'Quote Expires:'}
                  </p>
                  <p className="font-medium">
                    {new Date(order.quoteExpiryDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
              )}
            </div>
            
            <div className={cn(
              "p-4 rounded-lg",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <div className="flex items-center gap-2 mb-3">
                <CreditCard className="h-5 w-5 text-primary-500" />
                <h3 className="font-medium">
                  {language === 'ar' ? 'حالة الدفع' : 'Payment Status'}
                </h3>
              </div>
              <div className="flex items-center justify-between">
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs",
                  getPaymentStatusColor(order.paymentStatus)
                )}>
                  {getPaymentStatusText(order.paymentStatus)}
                </span>
                
                <select
                  value={order.paymentStatus}
                  onChange={(e) => onUpdatePaymentStatus(order.id, e.target.value)}
                  className={cn(
                    "px-2 py-1 rounded-md border text-sm",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white"
                      : "bg-white border-slate-300 text-slate-900"
                  )}
                >
                  <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                  <option value="partial">{language === 'ar' ? 'مدفوع جزئياً' : 'Partially Paid'}</option>
                  <option value="paid">{language === 'ar' ? 'مدفوع' : 'Paid'}</option>
                  <option value="failed">{language === 'ar' ? 'فشل الدفع' : 'Failed'}</option>
                </select>
              </div>
              
              <div className="mt-3 text-sm">
                <p className="text-slate-500 dark:text-slate-400">
                  {language === 'ar' ? 'طريقة الدفع:' : 'Payment Method:'}
                </p>
                <p className="font-medium">{order.paymentMethod}</p>
              </div>
            </div>
          </div>
          
          {/* معلومات الشركة */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'معلومات الشركة' : 'Company Information'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <Building className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'بيانات الشركة' : 'Company Details'}
                  </h4>
                </div>
                <p className="font-bold">{order.company.name}</p>
                <div className="flex items-center gap-2 mt-2">
                  <User className="h-4 w-4 text-slate-400" />
                  <p>{order.company.contactPerson}</p>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Mail className="h-4 w-4 text-slate-400" />
                  <p>{order.company.email}</p>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Phone className="h-4 w-4 text-slate-400" />
                  <p>{order.company.phone}</p>
                </div>
                {order.company.taxNumber && (
                  <div className="flex items-center gap-2 mt-1">
                    <FileText className="h-4 w-4 text-slate-400" />
                    <p>
                      {language === 'ar' ? 'الرقم الضريبي: ' : 'Tax Number: '}
                      {order.company.taxNumber}
                    </p>
                  </div>
                )}
              </div>
              
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <div className="flex items-center gap-2 mb-3">
                  <MapPin className="h-5 w-5 text-primary-500" />
                  <h4 className="font-medium">
                    {language === 'ar' ? 'عنوان الشحن' : 'Shipping Address'}
                  </h4>
                </div>
                <p>{order.shippingAddress.street}</p>
                <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}</p>
                <p>{order.shippingAddress.country}</p>
                
                <div className="mt-3">
                  <p className="text-slate-500 dark:text-slate-400 text-sm">
                    {language === 'ar' ? 'طريقة الشحن:' : 'Shipping Method:'}
                  </p>
                  <p className="font-medium">{order.shippingMethod}</p>
                </div>
                
                {order.trackingNumber && (
                  <div className="mt-2">
                    <p className="text-slate-500 dark:text-slate-400 text-sm">
                      {language === 'ar' ? 'رقم التتبع:' : 'Tracking Number:'}
                    </p>
                    <p className="font-medium font-mono">{order.trackingNumber}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* عناصر الطلب */}
          <div>
            <h3 className="text-lg font-medium mb-3">
              {language === 'ar' ? 'عناصر الطلب' : 'Order Items'}
            </h3>
            <div className={cn(
              "rounded-lg overflow-hidden",
              isDarkMode ? "bg-slate-700" : "bg-slate-50"
            )}>
              <table className="w-full">
                <thead className={cn(
                  "text-xs uppercase",
                  isDarkMode ? "bg-slate-600 text-slate-300" : "bg-slate-200 text-slate-700"
                )}>
                  <tr>
                    <th className="px-6 py-3 text-left">
                      {language === 'ar' ? 'المنتج' : 'Product'}
                    </th>
                    <th className="px-6 py-3 text-center">
                      {language === 'ar' ? 'الكمية' : 'Quantity'}
                    </th>
                    <th className="px-6 py-3 text-right">
                      {language === 'ar' ? 'السعر' : 'Price'}
                    </th>
                    <th className="px-6 py-3 text-right">
                      {language === 'ar' ? 'المجموع' : 'Total'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y dark:divide-slate-600">
                  {order.items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <img 
                            src={item.image} 
                            alt={item.name}
                            className="w-12 h-12 rounded-md object-cover"
                          />
                          <div>
                            <p className="font-medium">{item.name}</p>
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              ID: {item.id}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-center">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 text-right">
                        {formatCurrency(item.price)}
                      </td>
                      <td className="px-6 py-4 text-right font-medium">
                        {formatCurrency(item.price * item.quantity)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className={cn(
                  "text-sm",
                  isDarkMode ? "bg-slate-600 text-white" : "bg-slate-100 text-slate-900"
                )}>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.subtotal)}
                    </td>
                  </tr>
                  {order.discount > 0 && (
                    <tr>
                      <td colSpan={3} className="px-6 py-3 text-right font-medium">
                        {language === 'ar' ? 'الخصم' : 'Discount'}
                      </td>
                      <td className="px-6 py-3 text-right font-medium text-green-500">
                        -{formatCurrency(order.discount)}
                      </td>
                    </tr>
                  )}
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'الشحن' : 'Shipping'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.shipping)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right font-medium">
                      {language === 'ar' ? 'الضريبة' : 'Tax'}
                    </td>
                    <td className="px-6 py-3 text-right font-medium">
                      {formatCurrency(order.tax)}
                    </td>
                  </tr>
                  <tr className="text-base">
                    <td colSpan={3} className="px-6 py-3 text-right font-bold">
                      {language === 'ar' ? 'المجموع الكلي' : 'Total'}
                    </td>
                    <td className="px-6 py-3 text-right font-bold">
                      {formatCurrency(order.total)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          
          {/* ملاحظات */}
          {order.notes && (
            <div>
              <h3 className="text-lg font-medium mb-3">
                {language === 'ar' ? 'ملاحظات' : 'Notes'}
              </h3>
              <div className={cn(
                "p-4 rounded-lg",
                isDarkMode ? "bg-slate-700" : "bg-slate-50"
              )}>
                <p>{order.notes}</p>
              </div>
            </div>
          )}
          
          {/* الإجراءات */}
          <div className="flex flex-wrap justify-end gap-3 pt-4 border-t border-gray-200 dark:border-slate-700">
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Printer className="h-5 w-5" />
              <span>{language === 'ar' ? 'طباعة الطلب' : 'Print Order'}</span>
            </Button>
            
            <Button
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-5 w-5" />
              <span>{language === 'ar' ? 'تنزيل PDF' : 'Download PDF'}</span>
            </Button>
            
            <Button
              className="flex items-center gap-2"
            >
              <Send className="h-5 w-5" />
              <span>{language === 'ar' ? 'إرسال إشعار للعميل' : 'Send Notification'}</span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
