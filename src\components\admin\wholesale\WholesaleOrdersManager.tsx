'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Eye,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Calendar,
  Mail,
  Phone,
  Handshake,
  FileText,
  DollarSign
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Card } from '../../../components/ui/Card';
import { useLanguageStore } from '../../../stores/languageStore';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../lib/utils';
import { WholesaleOrderDetailsModal } from './WholesaleOrderDetailsModal';

// نوع طلب الجملة
interface WholesaleOrder {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  companyName: string;
  products: {
    name: string;
    quantity: number;
  }[];
  totalAmount: number | null;
  status: 'pending' | 'quoted' | 'accepted' | 'rejected' | 'completed';
  createdAt: string;
  notes: string;
}

// بيانات تجريبية لطلبات الجملة
const mockWholesaleOrders: WholesaleOrder[] = [
  {
    id: 'wo-001',
    customerName: 'محمد العلي',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 50 123 4567',
    companyName: 'شركة العلي للتجارة',
    products: [
      { name: 'Smart Factory IoT Sensor Kit', quantity: 50 },
      { name: 'Industrial Control Panel', quantity: 20 }
    ],
    totalAmount: null,
    status: 'pending',
    createdAt: '2023-06-15T10:30:00Z',
    notes: 'يرغب العميل في الحصول على عرض سعر للكميات المذكورة مع إمكانية زيادة الكمية في المستقبل.'
  },
  {
    id: 'wo-002',
    customerName: 'Sarah Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '****** 123 4567',
    companyName: 'Johnson Imports',
    products: [
      { name: 'Quality Control System', quantity: 5 },
      { name: 'Warehouse Management Solution', quantity: 2 }
    ],
    totalAmount: 45000,
    status: 'quoted',
    createdAt: '2023-06-10T14:45:00Z',
    notes: 'Quote sent on June 12th. Awaiting customer response.'
  },
  {
    id: 'wo-003',
    customerName: 'أحمد الناصر',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 55 987 6543',
    companyName: 'مجموعة الناصر',
    products: [
      { name: 'Smart Factory IoT Sensor Kit', quantity: 100 },
      { name: 'Industrial Control Panel', quantity: 40 },
      { name: 'Quality Control System', quantity: 10 }
    ],
    totalAmount: 120000,
    status: 'accepted',
    createdAt: '2023-06-05T09:15:00Z',
    notes: 'تم قبول عرض السعر. جاري تجهيز الطلب للشحن.'
  },
  {
    id: 'wo-004',
    customerName: 'Li Wei',
    customerEmail: '<EMAIL>',
    customerPhone: '+86 123 4567 8901',
    companyName: 'Wei Manufacturing',
    products: [
      { name: 'Smart Factory IoT Sensor Kit', quantity: 200 }
    ],
    totalAmount: 150000,
    status: 'completed',
    createdAt: '2023-05-20T11:20:00Z',
    notes: 'Order completed and delivered on June 1st. Customer satisfied with products.'
  },
  {
    id: 'wo-005',
    customerName: 'فيصل العتيبي',
    customerEmail: '<EMAIL>',
    customerPhone: '+966 54 765 4321',
    companyName: 'شركة العتيبي للصناعات',
    products: [
      { name: 'Industrial Control Panel', quantity: 15 }
    ],
    totalAmount: 30000,
    status: 'rejected',
    createdAt: '2023-06-02T08:00:00Z',
    notes: 'رفض العميل عرض السعر بسبب ارتفاع التكلفة. يفضل البحث عن بدائل أقل تكلفة.'
  },
];

// مكون إدارة طلبات الجملة
export function WholesaleOrdersManager() {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof WholesaleOrder>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // حالة تفاصيل الطلب
  const [selectedOrder, setSelectedOrder] = useState<WholesaleOrder | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // تصفية طلبات الجملة بناءً على البحث والحالة
  const filteredOrders = mockWholesaleOrders.filter((order) => {
    const searchRegex = new RegExp(searchQuery, 'i');
    const matchesSearch =
      searchRegex.test(order.customerName) ||
      searchRegex.test(order.customerEmail) ||
      searchRegex.test(order.companyName) ||
      order.products.some(p => searchRegex.test(p.name));

    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // ترتيب طلبات الجملة
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // حساب عدد الصفحات
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage);

  // الحصول على طلبات الجملة للصفحة الحالية
  const currentOrders = sortedOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // تغيير ترتيب الحقل
  const handleSort = (field: keyof WholesaleOrder) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // عرض تفاصيل طلب الجملة
  const handleViewOrder = (orderId: string) => {
    // البحث عن الطلب بواسطة المعرف
    const order = mockWholesaleOrders.find(o => o.id === orderId);

    if (order) {
      // تحويل الطلب إلى الشكل المطلوب لمكون تفاصيل الطلب
      const formattedOrder = {
        id: order.id,
        company: {
          name: order.companyName,
          email: order.customerEmail,
          phone: order.customerPhone,
          contactPerson: order.customerName
        },
        shippingAddress: {
          street: "عنوان الشحن",
          city: "المدينة",
          state: "المنطقة",
          zip: "الرمز البريدي",
          country: "المملكة العربية السعودية"
        },
        items: order.products.map((product, index) => ({
          id: `PROD-${index + 1}`,
          name: product.name,
          price: order.totalAmount ? Math.round(order.totalAmount / order.products.reduce((sum, p) => sum + p.quantity, 0)) : 1000,
          quantity: product.quantity,
          image: '/images/products/placeholder.jpg'
        })),
        date: order.createdAt,
        status: order.status === 'accepted' ? 'approved' :
               order.status === 'completed' ? 'delivered' :
               order.status as any,
        paymentStatus: order.status === 'completed' ? 'paid' :
                      order.status === 'accepted' ? 'partial' : 'pending',
        paymentMethod: 'تحويل بنكي',
        shippingMethod: 'شحن قياسي',
        total: order.totalAmount || 0,
        subtotal: order.totalAmount ? order.totalAmount * 0.95 : 0,
        shipping: order.totalAmount ? order.totalAmount * 0.03 : 0,
        tax: order.totalAmount ? order.totalAmount * 0.02 : 0,
        discount: 0,
        notes: order.notes
      };

      setSelectedOrder(formattedOrder as any);
      setShowOrderDetails(true);
    }
  };

  // تحديث حالة طلب الجملة
  const handleUpdateStatus = (orderId: string, status: string) => {
    // هنا سيتم تنفيذ منطق تحديث حالة طلب الجملة
    console.log('Update wholesale order status:', orderId, status);

    // في الإنتاج، سيتم استدعاء API لتحديث حالة طلب الجملة
    // وتحديث قائمة طلبات الجملة
  };

  // تحديث حالة الدفع
  const handleUpdatePaymentStatus = (orderId: string, status: string) => {
    // هنا سيتم تنفيذ منطق تحديث حالة الدفع
    console.log('Update payment status:', orderId, status);

    // في الإنتاج، سيتم استدعاء API لتحديث حالة الدفع
    // وتحديث قائمة طلبات الجملة
  };

  // إنشاء عرض سعر
  const handleCreateQuote = (orderId: string) => {
    // هنا سيتم تنفيذ منطق إنشاء عرض سعر
    console.log('Create quote for wholesale order:', orderId);

    // في الإنتاج، سيتم الانتقال إلى صفحة إنشاء عرض سعر
    // أو فتح نافذة منبثقة لإنشاء عرض سعر
  };

  // ترجمة حالة طلب الجملة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending';
      case 'quoted':
        return language === 'ar' ? 'تم تقديم عرض' : 'Quoted';
      case 'accepted':
        return language === 'ar' ? 'مقبول' : 'Accepted';
      case 'rejected':
        return language === 'ar' ? 'مرفوض' : 'Rejected';
      case 'completed':
        return language === 'ar' ? 'مكتمل' : 'Completed';
      default:
        return status;
    }
  };

  // الحصول على لون حالة طلب الجملة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-500';
      case 'quoted':
        return 'text-blue-500';
      case 'accepted':
        return 'text-green-500';
      case 'rejected':
        return 'text-red-500';
      case 'completed':
        return 'text-purple-500';
      default:
        return 'text-slate-500';
    }
  };

  // الحصول على أيقونة حالة طلب الجملة
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'quoted':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'accepted':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-purple-500" />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">
            {language === 'ar' ? 'إدارة طلبات الجملة' : 'Wholesale Orders Management'}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {language === 'ar'
              ? 'إدارة ومتابعة طلبات الجملة وعروض الأسعار'
              : 'Manage and track wholesale orders and quotations'}
          </p>
        </div>

      {/* أدوات البحث والتصفية */}
      <Card className={cn(
        "p-4",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="text"
              placeholder={language === 'ar' ? 'البحث عن طلبات الجملة...' : 'Search wholesale orders...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</option>
              <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
              <option value="quoted">{language === 'ar' ? 'تم تقديم عرض' : 'Quoted'}</option>
              <option value="accepted">{language === 'ar' ? 'مقبول' : 'Accepted'}</option>
              <option value="rejected">{language === 'ar' ? 'مرفوض' : 'Rejected'}</option>
              <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
            </select>

            <select
              className={cn(
                "px-3 py-2 rounded-md border",
                isDarkMode
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900"
              )}
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>

            <Button variant="outline">
              <Filter className="h-5 w-5" />
              <span className="ml-2">{language === 'ar' ? 'تصفية' : 'Filter'}</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* جدول طلبات الجملة */}
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-slate-800" : "bg-white"
      )}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={cn(
              "text-xs uppercase",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-gray-50 text-slate-700"
            )}>
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('id')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'رقم الطلب' : 'Order ID'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('customerName')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'العميل' : 'Customer'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  {language === 'ar' ? 'المنتجات' : 'Products'}
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('createdAt')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'التاريخ' : 'Date'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={() => handleSort('status')}
                    className="flex items-center gap-1"
                  >
                    {language === 'ar' ? 'الحالة' : 'Status'}
                    <ArrowUpDown className="h-4 w-4" />
                  </button>
                </th>
                <th className="px-6 py-3 text-right">
                  {language === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-slate-700">
              {currentOrders.map((order) => (
                <tr
                  key={order.id}
                  className={cn(
                    "hover:bg-gray-50 dark:hover:bg-slate-700/50",
                    isDarkMode ? "bg-slate-800" : "bg-white"
                  )}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium">{order.id}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="font-medium">{order.customerName}</div>
                    <div className="text-sm text-slate-500 dark:text-slate-400">
                      {order.companyName}
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400 flex items-center">
                      <Mail className="h-3 w-3 mr-1" />
                      {order.customerEmail}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {order.products.slice(0, 2).map((product, index) => (
                        <div key={index} className="text-sm">
                          {product.name} <span className="text-slate-500 dark:text-slate-400">x{product.quantity}</span>
                        </div>
                      ))}
                      {order.products.length > 2 && (
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          {language === 'ar'
                            ? `+${order.products.length - 2} منتجات أخرى`
                            : `+${order.products.length - 2} more products`
                          }
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-slate-400" />
                      <span>
                        {new Date(order.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={cn(
                      "flex items-center gap-2",
                      getStatusColor(order.status)
                    )}>
                      {getStatusIcon(order.status)}
                      <span>{getStatusText(order.status)}</span>
                    </div>
                    {order.totalAmount && (
                      <div className="text-sm flex items-center mt-1 text-slate-500 dark:text-slate-400">
                        <DollarSign className="h-3 w-3 mr-1" />
                        {order.totalAmount.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        onClick={() => handleViewOrder(order.id)}
                        className={cn(
                          "p-1 rounded-md",
                          isDarkMode ? "hover:bg-slate-700" : "hover:bg-gray-100"
                        )}
                      >
                        <Eye className="h-5 w-5 text-blue-500" />
                      </button>

                      {order.status === 'pending' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCreateQuote(order.id)}
                          className="flex items-center gap-1"
                        >
                          <FileText className="h-4 w-4" />
                          <span>{language === 'ar' ? 'إنشاء عرض' : 'Create Quote'}</span>
                        </Button>
                      )}

                      <select
                        className={cn(
                          "px-2 py-1 rounded-md border text-sm",
                          isDarkMode
                            ? "bg-slate-700 border-slate-600 text-white"
                            : "bg-white border-slate-300 text-slate-900"
                        )}
                        value={order.status}
                        onChange={(e) => handleUpdateStatus(order.id, e.target.value)}
                      >
                        <option value="pending">{language === 'ar' ? 'قيد الانتظار' : 'Pending'}</option>
                        <option value="quoted">{language === 'ar' ? 'تم تقديم عرض' : 'Quoted'}</option>
                        <option value="accepted">{language === 'ar' ? 'مقبول' : 'Accepted'}</option>
                        <option value="rejected">{language === 'ar' ? 'مرفوض' : 'Rejected'}</option>
                        <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                      </select>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* ترقيم الصفحات */}
        <div className={cn(
          "px-6 py-4 flex items-center justify-between border-t",
          isDarkMode ? "border-slate-700" : "border-gray-200"
        )}>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {language === 'ar'
              ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, sortedOrders.length)} من ${sortedOrders.length} طلب`
              : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, sortedOrders.length)} of ${sortedOrders.length} orders`}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>

      {/* نافذة تفاصيل الطلب */}
      {showOrderDetails && selectedOrder && (
        <WholesaleOrderDetailsModal
          order={selectedOrder}
          onClose={() => setShowOrderDetails(false)}
          onUpdateStatus={handleUpdateStatus}
          onUpdatePaymentStatus={handleUpdatePaymentStatus}
        />
      )}
    </>
  );
}
