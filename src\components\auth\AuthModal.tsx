import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { useAuthStore } from '../../stores/authStore';
import { X, AlertCircle, CheckCircle, User, Mail, Lock, Info } from 'lucide-react';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { createDefaultAdminUser, createSecondAdminUser } from '../../services/AuthService';

interface AuthModalProps {
  onClose: () => void;
  mode?: 'sign-in' | 'sign-up';
}

export function AuthModal({ onClose, mode = 'sign-in' }: AuthModalProps) {
  const [isSignIn, setIsSignIn] = useState(mode === 'sign-in');
  const [formData, setFormData] = useState({
    email: mode === 'sign-in' ? '<EMAIL>' : '',
    password: mode === 'sign-in' ? 'password' : '',
    firstName: '',
    lastName: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { signIn, signUp, isLoading, user } = useAuthStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // إغلاق النافذة إذا كان المستخدم مسجل الدخول بالفعل
  useEffect(() => {
    if (user) {
      console.log('User authenticated, closing modal');
      // تأخير الإغلاق لتجنب مشاكل التوقيت
      setTimeout(() => {
        onClose();
      }, 1000);
    }
  }, [user, onClose]);

  // إعادة تعيين الخطأ عند تبديل وضع تسجيل الدخول/التسجيل
  useEffect(() => {
    setError(null);
    setSuccess(null);
  }, [isSignIn]);

  // إنشاء مستخدمين افتراضيين عند تحميل المكون
  useEffect(() => {
    const initUsers = async () => {
      await createDefaultAdminUser();
      await createSecondAdminUser();
      console.log('Default admin users created for testing');
    };

    initUsers();
  }, []);

  // التحقق من صحة النموذج
  const validateForm = () => {
    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      setError(t('auth.emailRequired'));
      return false;
    }

    // التحقق من تنسيق البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError(t('auth.invalidEmail'));
      return false;
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      setError(t('auth.passwordRequired'));
      return false;
    }

    // التحقق من طول كلمة المرور
    if (formData.password.length < 6) {
      setError(t('auth.passwordTooShort'));
      return false;
    }

    // التحقق من الاسم الأول والأخير في حالة التسجيل
    if (!isSignIn) {
      if (!formData.firstName) {
        setError(t('auth.firstNameRequired'));
        return false;
      }

      if (!formData.lastName) {
        setError(t('auth.lastNameRequired'));
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // التحقق من صحة النموذج
    if (!validateForm()) {
      return;
    }

    try {
      if (isSignIn) {
        // تسجيل الدخول
        console.log('Attempting to sign in with:', formData.email);
        setIsSubmitting(true);

        const { error } = await signIn(formData.email, formData.password);

        if (error) {
          console.error('Sign in error:', error);
          throw error;
        }

        console.log('Sign in successful, setting success message');
        setSuccess(t('auth.signInSuccess'));

        // إغلاق النافذة بعد تسجيل الدخول بنجاح
        console.log('Will close modal after delay');
        setTimeout(() => {
          console.log('Closing modal after successful sign in');
          onClose();

          // إعادة تحميل الصفحة بعد تسجيل الدخول بنجاح للتأكد من تحديث الحالة
          console.log('Reloading page to refresh auth state');
          window.location.reload();
        }, 1500);
      } else {
        // تسجيل حساب جديد
        console.log('Attempting to sign up with:', formData.email);
        setIsSubmitting(true);

        const { error } = await signUp(formData.email, formData.password, {
          firstName: formData.firstName,
          lastName: formData.lastName,
        });

        if (error) {
          console.error('Sign up error:', error);
          throw error;
        }

        setSuccess(t('auth.signUpSuccess'));

        // إغلاق النافذة بعد التسجيل بنجاح
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (err: any) {
      console.error('Auth error:', err);

      // تحسين رسائل الخطأ
      let errorMessage = err.message || t('auth.genericError');

      // تحويل رسائل الخطأ الشائعة إلى رسائل أكثر وضوحًا
      if (errorMessage.includes('بيانات تسجيل الدخول غير صحيحة')) {
        errorMessage = t('auth.invalidCredentials');
      } else if (errorMessage.includes('البريد الإلكتروني مستخدم بالفعل')) {
        errorMessage = t('auth.emailAlreadyInUse');
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center p-4 z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="w-full max-w-md"
          initial={{ scale: 0.9, y: 20, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          exit={{ scale: 0.9, y: 20, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <Card className={cn(
            "w-full p-8 relative overflow-hidden border-0 rounded-2xl",
            isDarkMode
              ? "bg-slate-800/95 text-white shadow-xl shadow-slate-900/30"
              : "bg-white/95 text-slate-900 shadow-xl shadow-slate-300/30"
          )}>
            {/* زخرفة خلفية */}
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 via-accent-500 to-secondary-500"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-primary-500/10 blur-3xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-accent-500/10 blur-3xl"></div>

            <motion.button
              onClick={onClose}
              className={cn(
                "absolute top-4 right-4 p-2 rounded-full transition-all duration-200 hover:bg-slate-200/20",
                isDarkMode ? "text-slate-400 hover:text-white" : "text-slate-400 hover:text-slate-700"
              )}
              aria-label={t('common.close')}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <X size={20} />
            </motion.button>

            <div className="text-center mb-8">
              <motion.div
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                <div className="mx-auto w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mb-4">
                  <User className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                </div>
                <h2 className="text-2xl font-bold mb-2">
                  {isSignIn ? t('auth.signIn') : t('auth.signUp')}
                </h2>
                <p className={cn(
                  "text-sm",
                  isDarkMode ? "text-slate-400" : "text-slate-500"
                )}>
                  {isSignIn
                    ? t('auth.signInSubtitle') || 'أهلاً بعودتك! سجل دخولك للوصول إلى حسابك'
                    : t('auth.signUpSubtitle') || 'انضم إلينا اليوم وابدأ رحلتك معنا'}
                </p>
              </motion.div>
            </div>

            <AnimatePresence>
              {error && (
                <motion.div
                  className={cn(
                    "p-4 rounded-lg mb-6 flex items-center border-l-4 border-red-500",
                    isDarkMode ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
                  )}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  <AlertCircle className={`${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 flex-shrink-0 animate-pulse`} />
                  <p className="text-sm font-medium">{error}</p>
                </motion.div>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {success && (
                <motion.div
                  className={cn(
                    "p-4 rounded-lg mb-6 flex items-center border-l-4 border-green-500",
                    isDarkMode ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
                  )}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  <CheckCircle className={`${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 flex-shrink-0`} />
                  <p className="text-sm font-medium">{success}</p>
                </motion.div>
              )}
            </AnimatePresence>

            {isSignIn && (
              <motion.div
                className={cn(
                  "p-4 rounded-lg mb-6 flex items-start border-l-4 border-blue-500",
                  isDarkMode ? "bg-blue-900/20 text-blue-300" : "bg-blue-50 text-blue-600"
                )}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Info className={`${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 flex-shrink-0 mt-0.5`} />
                <div className="text-sm">
                  <p className="font-medium mb-1">{currentLanguage === 'ar' ? 'بيانات تسجيل الدخول للاختبار:' : 'Test Login Credentials:'}</p>
                  <p className="mb-0.5">{currentLanguage === 'ar' ? 'البريد الإلكتروني: <EMAIL>' : 'Email: <EMAIL>'}</p>
                  <p>{currentLanguage === 'ar' ? 'كلمة المرور: password' : 'Password: password'}</p>
                </div>
              </motion.div>
            )}

            <motion.form
              onSubmit={handleSubmit}
              className="space-y-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {!isSignIn && (
                <motion.div
                  className="grid grid-cols-2 gap-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <div className="relative">
                    <label className={cn(
                      "block text-sm font-medium mb-2",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      {t('auth.firstName')}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <Input
                        value={formData.firstName}
                        onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                        required
                        className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                        placeholder={t('auth.firstNamePlaceholder') || "الاسم الأول"}
                      />
                    </div>
                  </div>
                  <div className="relative">
                    <label className={cn(
                      "block text-sm font-medium mb-2",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      {t('auth.lastName')}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <Input
                        value={formData.lastName}
                        onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                        required
                        className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                        placeholder={t('auth.lastNamePlaceholder') || "اسم العائلة"}
                      />
                    </div>
                  </div>
                </motion.div>
              )}

              <div className="relative">
                <label className={cn(
                  "block text-sm font-medium mb-2",
                  isDarkMode ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('auth.email')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-slate-400" />
                  </div>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    required
                    className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                    placeholder={t('auth.emailPlaceholder') || "أدخل بريدك الإلكتروني"}
                  />
                </div>
              </div>

              <div className="relative">
                <label className={cn(
                  "block text-sm font-medium mb-2",
                  isDarkMode ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-slate-400" />
                  </div>
                  <Input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    required
                    className={`pl-10 transition-all duration-300 focus:ring-primary-500 focus:border-primary-500 ${currentLanguage === 'ar' ? 'text-right pr-3' : ''}`}
                    placeholder={t('auth.passwordPlaceholder') || "أدخل كلمة المرور"}
                  />
                </div>
                {!isSignIn && (
                  <p className={cn(
                    "text-xs mt-2",
                    isDarkMode ? "text-slate-400" : "text-slate-500"
                  )}>
                    {t('auth.passwordRequirements') || "يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل"}
                  </p>
                )}
              </div>

              <motion.div
                className="pt-2"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="submit"
                  className="w-full py-2.5 text-base font-medium"
                  variant="primary"
                  size="lg"
                  disabled={isLoading || isSubmitting || !!success}
                  isLoading={isLoading || isSubmitting}
                >
                  {isLoading || isSubmitting
                    ? t('auth.processing')
                    : isSignIn
                      ? t('auth.signIn')
                      : t('auth.signUp')
                  }
                </Button>
              </motion.div>

              <div className="relative flex items-center justify-center my-6">
                <div className={cn(
                  "absolute inset-0 flex items-center",
                  currentLanguage === 'ar' ? "justify-end" : "justify-start"
                )}>
                  <span className={cn(
                    "w-1/3 border-t",
                    isDarkMode ? "border-slate-700" : "border-slate-300"
                  )}></span>
                </div>
                <div className="relative z-10 px-4 text-sm text-center">
                  <span className={cn(
                    isDarkMode ? "text-slate-400" : "text-slate-500"
                  )}>
                    {isSignIn ? t('auth.or') || "أو" : t('auth.or') || "أو"}
                  </span>
                </div>
                <div className={cn(
                  "absolute inset-0 flex items-center",
                  currentLanguage === 'ar' ? "justify-start" : "justify-end"
                )}>
                  <span className={cn(
                    "w-1/3 border-t",
                    isDarkMode ? "border-slate-700" : "border-slate-300"
                  )}></span>
                </div>
              </div>

              <motion.p
                className={cn(
                  "text-center text-sm",
                  isDarkMode ? "text-slate-400" : "text-slate-600"
                )}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                {isSignIn ? t('auth.noAccount') || "ليس لديك حساب؟" : t('auth.haveAccount') || "لديك حساب بالفعل؟"}
                <motion.button
                  type="button"
                  onClick={() => setIsSignIn(!isSignIn)}
                  className={cn(
                    `${currentLanguage === 'ar' ? 'mr-1' : 'ml-1'} font-medium underline-offset-2 hover:underline`,
                    isDarkMode ? "text-primary-400 hover:text-primary-300" : "text-primary-600 hover:text-primary-700"
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isSignIn ? t('auth.signUp') || "إنشاء حساب" : t('auth.signIn') || "تسجيل الدخول"}
                </motion.button>
              </motion.p>
            </motion.form>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
