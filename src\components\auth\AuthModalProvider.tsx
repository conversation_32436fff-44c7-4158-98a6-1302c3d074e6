import { ReactNode } from 'react';
import { useAuthModalStore } from '../../stores/authModalStore';
import { AuthModal } from './AuthModal';

interface AuthModalProviderProps {
  children: ReactNode;
}

export function AuthModalProvider({ children }: AuthModalProviderProps) {
  const { isOpen, closeModal } = useAuthModalStore();

  return (
    <>
      {children}
      {isOpen && <AuthModal onClose={closeModal} />}
    </>
  );
}
