import { useState } from 'react';
import { X, ShoppingCart } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { formatCurrency } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';

interface CartRecoveryModalProps {
  onClose: () => void;
}

export function CartRecoveryModal({ onClose }: CartRecoveryModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { items, clearCart } = useCartStore();

  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  const handleRecover = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      onClose();
    }, 1000);
  };

  const handleClear = () => {
    clearCart();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Recover Cart</h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600"
          >
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-center w-16 h-16 mx-auto bg-primary-50 text-primary-600 rounded-full">
            <ShoppingCart size={32} />
          </div>

          <p className="text-center text-slate-600">
            We found {items.length} items in your previous cart totaling{' '}
            {formatCurrency(total)}. Would you like to recover these items?
          </p>

          <div className="space-y-2">
            <Button
              className="w-full"
              onClick={handleRecover}
              disabled={isLoading}
            >
              {isLoading ? 'Recovering...' : 'Recover Cart'}
            </Button>
            <Button
              variant="outline"
              className="w-full"
              onClick={handleClear}
              disabled={isLoading}
            >
              Clear Cart
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}