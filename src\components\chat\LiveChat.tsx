import { useEffect } from 'react';
import { MessagesSquare } from 'lucide-react';

export function LiveChat() {
  useEffect(() => {
    // Initialize your preferred live chat service here
    // This is a placeholder for demonstration
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-live-chat-service.com/widget.js';
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <button
      className="fixed bottom-6 right-6 bg-primary-500 text-white p-4 rounded-full shadow-lg hover:bg-primary-600 transition-colors z-40"
      aria-label="Live Chat"
    >
      <MessagesSquare size={24} />
    </button>
  );
}