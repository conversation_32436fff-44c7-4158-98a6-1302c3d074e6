'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
}

export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        const metrics: Partial<PerformanceMetrics> = {
          pageLoadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        };

        // First Contentful Paint
        const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
        if (fcp) {
          metrics.firstContentfulPaint = fcp.startTime;
        }

        // Largest Contentful Paint
        if ('PerformanceObserver' in window) {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            metrics.largestContentfulPaint = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            metrics.cumulativeLayoutShift = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
            }
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        }

        // Log metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.group('🚀 Performance Metrics');
          console.log('Page Load Time:', metrics.pageLoadTime?.toFixed(2), 'ms');
          console.log('DOM Content Loaded:', metrics.domContentLoaded?.toFixed(2), 'ms');
          console.log('First Contentful Paint:', metrics.firstContentfulPaint?.toFixed(2), 'ms');
          console.log('Largest Contentful Paint:', metrics.largestContentfulPaint?.toFixed(2), 'ms');
          console.log('Cumulative Layout Shift:', metrics.cumulativeLayoutShift?.toFixed(4));
          console.log('First Input Delay:', metrics.firstInputDelay?.toFixed(2), 'ms');
          console.groupEnd();
        }

        // Send metrics to analytics (in production)
        if (process.env.NODE_ENV === 'production' && window.gtag) {
          window.gtag('event', 'page_performance', {
            page_load_time: Math.round(metrics.pageLoadTime || 0),
            dom_content_loaded: Math.round(metrics.domContentLoaded || 0),
            first_contentful_paint: Math.round(metrics.firstContentfulPaint || 0),
            largest_contentful_paint: Math.round(metrics.largestContentfulPaint || 0),
            cumulative_layout_shift: Math.round((metrics.cumulativeLayoutShift || 0) * 1000),
            first_input_delay: Math.round(metrics.firstInputDelay || 0),
          });
        }
      } catch (error) {
        console.warn('Performance monitoring error:', error);
      }
    };

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, []);

  return null; // This component doesn't render anything
}

// Type declaration for gtag
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
