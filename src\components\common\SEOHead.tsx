import Head from 'next/head';
import { useRouter } from 'next/router';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  locale?: 'ar' | 'en';
  alternateLocales?: Array<{ locale: string; url: string }>;
  structuredData?: object;
  noIndex?: boolean;
  canonical?: string;
}

export function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  locale = 'en',
  alternateLocales = [],
  structuredData,
  noIndex = false,
  canonical,
}: SEOProps) {
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://commercepro.com';
  
  // Default values
  const defaultTitle = locale === 'ar' 
    ? 'كوميرس برو - منصة التجارة الإلكترونية المتقدمة'
    : 'CommercePro - Advanced E-commerce Platform';
  
  const defaultDescription = locale === 'ar'
    ? 'منصة تجارة إلكترونية متقدمة تقدم حلول شاملة للأعمال التجارية مع دعم اللغة العربية والإنجليزية'
    : 'Advanced e-commerce platform providing comprehensive business solutions with Arabic and English language support';

  const defaultKeywords = locale === 'ar'
    ? ['تجارة إلكترونية', 'متجر إلكتروني', 'منصة تجارية', 'حلول أعمال', 'عربي', 'إنجليزي']
    : ['ecommerce', 'online store', 'business platform', 'commercial solutions', 'arabic', 'english'];

  // Construct final values
  const finalTitle = title ? `${title} | ${defaultTitle}` : defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalKeywords = [...defaultKeywords, ...keywords];
  const finalUrl = url || `${baseUrl}${router.asPath}`;
  const finalImage = image || `${baseUrl}/images/og-image.jpg`;
  const finalCanonical = canonical || finalUrl;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords.join(', ')} />
      <meta name="author" content="CommercePro Team" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Language and Direction */}
      <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* Alternate Language URLs */}
      {alternateLocales.map(({ locale: altLocale, url: altUrl }) => (
        <link
          key={altLocale}
          rel="alternate"
          hrefLang={altLocale}
          href={altUrl}
        />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:locale" content={locale === 'ar' ? 'ar_SA' : 'en_US'} />
      <meta property="og:site_name" content="CommercePro" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:site" content="@commercepro" />
      <meta name="twitter:creator" content="@commercepro" />
      
      {/* Robots Meta Tag */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Favicon and App Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#A855F7" />
      <meta name="msapplication-TileColor" content="#A855F7" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
    </Head>
  );
}

// Helper function to generate structured data for products
export function generateProductStructuredData(product: any, locale: 'ar' | 'en') {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: locale === 'ar' ? product.name_ar || product.name : product.name,
    description: locale === 'ar' ? product.description_ar || product.description : product.description,
    image: product.images?.[0] || '',
    sku: product.id,
    brand: {
      '@type': 'Brand',
      name: 'CommercePro',
    },
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'SAR',
      availability: product.stock > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'CommercePro',
      },
    },
    aggregateRating: product.rating && {
      '@type': 'AggregateRating',
      ratingValue: product.rating,
      reviewCount: product.review_count || 0,
    },
  };
}

// Helper function to generate structured data for services
export function generateServiceStructuredData(service: any, locale: 'ar' | 'en') {
  return {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: locale === 'ar' ? service.name_ar || service.name : service.name,
    description: locale === 'ar' ? service.description_ar || service.description : service.description,
    provider: {
      '@type': 'Organization',
      name: 'CommercePro',
    },
    serviceType: 'Business Service',
    areaServed: {
      '@type': 'Country',
      name: 'Saudi Arabia',
    },
  };
}
