import { useState } from 'react';
import { CurrencyInput } from 'react-currency-input-field';
import { Card } from '../ui/Card';

interface CurrencySelectorProps {
  onSelect: (currency: string) => void;
  onClose: () => void;
}

const currencies = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
];

export function CurrencySelector({ onSelect, onClose }: CurrencySelectorProps) {
  const [search, setSearch] = useState('');

  const filteredCurrencies = currencies.filter(
    currency =>
      currency.code.toLowerCase().includes(search.toLowerCase()) ||
      currency.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Select Currency</h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600"
          >
            <X size={20} />
          </button>
        </div>

        <input
          type="text"
          placeholder="Search currencies..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-full px-3 py-2 border rounded-md mb-4"
        />

        <div className="space-y-2 max-h-[60vh] overflow-y-auto">
          {filteredCurrencies.map((currency) => (
            <button
              key={currency.code}
              onClick={() => {
                onSelect(currency.code);
                onClose();
              }}
              className="w-full flex items-center justify-between p-3 hover:bg-slate-50 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-xl mr-3">{currency.symbol}</span>
                <span className="font-medium">{currency.code}</span>
              </div>
              <span className="text-slate-500">{currency.name}</span>
            </button>
          ))}
        </div>
      </Card>
    </div>
  );
}