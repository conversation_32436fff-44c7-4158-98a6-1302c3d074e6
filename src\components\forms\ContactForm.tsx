'use client';

import { useState } from 'react';
import { Mail, User, MessageSquare, Send, Phone, Building, Check } from 'lucide-react';
import { Form, FormField, FormSubmit } from '../ui/Form';
import { FormInput, validators } from '../ui/FormInput';
import { Button } from '../ui/Button';
import { useTranslation } from '../../translations';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';

interface ContactFormProps {
  className?: string;
  variant?: 'default' | 'card' | 'inline';
  onSuccess?: () => void;
}

export function ContactForm({ className, variant = 'default', onSuccess }: ContactFormProps) {
  const { t, currentLanguage } = useTranslation();
  const { direction } = useLanguageStore();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (values: Record<string, any>) => {
    setIsLoading(true);
    
    try {
      // محاكاة إرسال النموذج إلى API
      console.log('Form values:', values);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setIsSubmitted(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className={cn(
        "flex flex-col items-center justify-center p-8 text-center",
        variant === 'card' && "bg-white dark:bg-slate-800 rounded-lg shadow-lg",
        className
      )}>
        <div className="w-16 h-16 rounded-full bg-success-100 dark:bg-success-900 flex items-center justify-center mb-4">
          <Check className="w-8 h-8 text-success-600 dark:text-success-400" />
        </div>
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
          {currentLanguage === 'ar' ? 'تم إرسال رسالتك بنجاح!' : 'Message Sent Successfully!'}
        </h3>
        <p className="text-slate-600 dark:text-slate-300 mb-6">
          {currentLanguage === 'ar' 
            ? 'شكراً لتواصلك معنا. سنقوم بالرد عليك في أقرب وقت ممكن.' 
            : 'Thank you for contacting us. We will get back to you as soon as possible.'}
        </p>
        <Button 
          variant="outline" 
          onClick={() => setIsSubmitted(false)}
        >
          {currentLanguage === 'ar' ? 'إرسال رسالة أخرى' : 'Send Another Message'}
        </Button>
      </div>
    );
  }

  return (
    <div className={cn(
      variant === 'card' && "bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6",
      className
    )}>
      {variant !== 'inline' && (
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            {currentLanguage === 'ar' ? 'تواصل معنا' : 'Contact Us'}
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            {currentLanguage === 'ar' 
              ? 'نحن هنا للإجابة على أسئلتك ومساعدتك في كل ما تحتاجه.' 
              : 'We are here to answer your questions and help you with anything you need.'}
          </p>
        </div>
      )}
      
      <Form
        initialValues={{
          name: '',
          email: '',
          phone: '',
          company: '',
          subject: '',
          message: ''
        }}
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            name="name"
            label={currentLanguage === 'ar' ? 'الاسم' : 'Name'}
            placeholder={currentLanguage === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'}
            required
            leftIcon={<User />}
            validators={[validators.minLength(3)]}
          />
          
          <FormInput
            name="email"
            label={currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email'}
            placeholder={currentLanguage === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
            type="email"
            required
            leftIcon={<Mail />}
            validators={[validators.email]}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            name="phone"
            label={currentLanguage === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
            placeholder={currentLanguage === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'}
            leftIcon={<Phone />}
            validators={[validators.phone]}
          />
          
          <FormInput
            name="company"
            label={currentLanguage === 'ar' ? 'الشركة' : 'Company'}
            placeholder={currentLanguage === 'ar' ? 'أدخل اسم شركتك (اختياري)' : 'Enter your company name (optional)'}
            leftIcon={<Building />}
          />
        </div>
        
        <FormInput
          name="subject"
          label={currentLanguage === 'ar' ? 'الموضوع' : 'Subject'}
          placeholder={currentLanguage === 'ar' ? 'أدخل موضوع رسالتك' : 'Enter the subject of your message'}
          required
        />
        
        <FormField name="message" required validators={[validators.minLength(10)]}>
          {({ value, error, onChange, onBlur }) => (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'الرسالة' : 'Message'}
              </label>
              <div className="relative">
                <textarea
                  value={value || ''}
                  onChange={(e) => onChange(e.target.value)}
                  onBlur={onBlur}
                  placeholder={currentLanguage === 'ar' ? 'اكتب رسالتك هنا...' : 'Type your message here...'}
                  rows={5}
                  className={cn(
                    "w-full rounded-md border p-3 text-sm resize-y min-h-[120px]",
                    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500",
                    "transition-colors duration-200",
                    "dark:border-slate-700 dark:bg-slate-800 dark:text-white dark:placeholder:text-slate-500",
                    "border-slate-300 bg-white text-slate-900 placeholder:text-slate-400",
                    error && "border-error-500 focus-visible:ring-error-500"
                  )}
                  required
                />
                <MessageSquare className="absolute top-3 right-3 text-slate-400 dark:text-slate-500" />
              </div>
              {error && <p className="mt-1 text-sm text-error-500 dark:text-error-400">{error}</p>}
            </div>
          )}
        </FormField>
        
        <div className="flex justify-end mt-6">
          <FormSubmit>
            <Button
              type="submit"
              className="w-full md:w-auto"
              isLoading={isLoading}
            >
              <Send className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
              {currentLanguage === 'ar' ? 'إرسال الرسالة' : 'Send Message'}
            </Button>
          </FormSubmit>
        </div>
      </Form>
    </div>
  );
}
