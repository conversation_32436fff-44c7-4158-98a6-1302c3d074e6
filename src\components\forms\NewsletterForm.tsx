'use client';

import { useState } from 'react';
import { Mail, Send, Check, Bell, X } from 'lucide-react';
import { Form, FormSubmit } from '../ui/Form';
import { FormInput, validators } from '../ui/FormInput';
import { Button } from '../ui/Button';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';
import { HoverAnimation } from '../ui/animations/HoverAnimation';

interface NewsletterFormProps {
  className?: string;
  variant?: 'default' | 'inline' | 'card' | 'popup';
  title?: string;
  description?: string;
  onSuccess?: () => void;
  onClose?: () => void;
}

export function NewsletterForm({
  className,
  variant = 'default',
  title,
  description,
  onSuccess,
  onClose
}: NewsletterFormProps) {
  const { t, currentLanguage } = useTranslation();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const defaultTitle = currentLanguage === 'ar'
    ? 'اشترك في نشرتنا الإخبارية'
    : 'Subscribe to Our Newsletter';

  const defaultDescription = currentLanguage === 'ar'
    ? 'احصل على آخر العروض والمنتجات الجديدة مباشرة إلى بريدك الإلكتروني.'
    : 'Get the latest offers and new products directly to your inbox.';

  const handleSubmit = async (values: Record<string, any>) => {
    setIsLoading(true);
    
    try {
      // محاكاة إرسال النموذج إلى API
      console.log('Newsletter subscription:', values);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setIsSubmitted(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    if (isSubmitted) {
      return (
        <div className="text-center py-4">
          <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900 flex items-center justify-center mx-auto mb-4">
            <Check className="w-6 h-6 text-success-600 dark:text-success-400" />
          </div>
          <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
            {currentLanguage === 'ar' ? 'تم الاشتراك بنجاح!' : 'Successfully Subscribed!'}
          </h3>
          <p className="text-slate-600 dark:text-slate-300 mb-4">
            {currentLanguage === 'ar' 
              ? 'شكراً لاشتراكك في نشرتنا الإخبارية.' 
              : 'Thank you for subscribing to our newsletter.'}
          </p>
          {variant === 'popup' && onClose && (
            <Button 
              variant="outline" 
              onClick={onClose}
              size="sm"
            >
              {currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          )}
        </div>
      );
    }

    return (
      <>
        {(variant === 'default' || variant === 'card' || variant === 'popup') && (
          <div className={cn(
            "mb-4",
            variant === 'popup' ? "text-center" : ""
          )}>
            <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
              {title || defaultTitle}
            </h3>
            <p className="text-slate-600 dark:text-slate-300">
              {description || defaultDescription}
            </p>
          </div>
        )}
        
        <Form
          initialValues={{ email: '' }}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          {variant === 'inline' ? (
            <div className="flex flex-col sm:flex-row gap-3">
              <FormInput
                name="email"
                placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}
                type="email"
                required
                leftIcon={<Mail />}
                validators={[validators.email]}
                className="flex-1"
              />
              
              <FormSubmit>
                <Button
                  type="submit"
                  className="whitespace-nowrap"
                  isLoading={isLoading}
                >
                  {currentLanguage === 'ar' ? 'اشترك الآن' : 'Subscribe Now'}
                </Button>
              </FormSubmit>
            </div>
          ) : (
            <>
              <FormInput
                name="email"
                label={variant !== 'popup' ? (currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email') : undefined}
                placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}
                type="email"
                required
                leftIcon={<Mail />}
                validators={[validators.email]}
              />
              
              <FormSubmit>
                <Button
                  type="submit"
                  className={variant === 'popup' ? "w-full" : ""}
                  isLoading={isLoading}
                >
                  <Send className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {currentLanguage === 'ar' ? 'اشترك الآن' : 'Subscribe Now'}
                </Button>
              </FormSubmit>
            </>
          )}
        </Form>
      </>
    );
  };

  if (variant === 'popup') {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
        <HoverAnimation animation="fade">
          <div className="relative max-w-md w-full bg-white dark:bg-slate-800 rounded-lg shadow-xl p-6">
            {onClose && (
              <button
                onClick={onClose}
                className="absolute top-3 right-3 p-1 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
                aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
              >
                <X className="h-5 w-5 text-slate-500" />
              </button>
            )}
            
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 mx-auto mb-4">
              <Bell className="h-6 w-6" />
            </div>
            
            {renderContent()}
          </div>
        </HoverAnimation>
      </div>
    );
  }

  return (
    <div className={cn(
      variant === 'card' && "bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6",
      className
    )}>
      {renderContent()}
    </div>
  );
}
