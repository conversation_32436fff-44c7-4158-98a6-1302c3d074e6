import { useState } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { Send, X, AlertCircle, CheckCircle, Loader2, User, Mail, Phone, Building, MessageSquare } from 'lucide-react';
import { useLanguageStore } from '../../stores/languageStore';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

interface ServiceInquiryFormProps {
  serviceName: string;
  onClose: () => void;
}

interface FormData {
  fullName: string;
  email: string;
  phone: string;
  companyName: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export function ServiceInquiryForm({ serviceName, onClose }: ServiceInquiryFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    phone: '',
    companyName: '',
    message: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Full Name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = language === 'ar' ? 'الاسم الكامل مطلوب' : 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = language === 'ar' ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = language === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email format';
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = language === 'ar' ? 'رقم الهاتف مطلوب' : 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = language === 'ar' ? 'رقم الهاتف غير صحيح' : 'Invalid phone number';
    }

    // Company Name validation
    if (!formData.companyName.trim()) {
      newErrors.companyName = language === 'ar' ? 'اسم الشركة مطلوب' : 'Company name is required';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = language === 'ar' ? 'الرسالة مطلوبة' : 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = language === 'ar' ? 'الرسالة يجب أن تكون 10 أحرف على الأقل' : 'Message must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Form submitted:', { service: serviceName, ...formData });
      setIsSubmitted(true);

      // Auto close after success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Submission error:', error);
      setErrors({ submit: language === 'ar' ? 'حدث خطأ أثناء الإرسال' : 'Submission error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  if (isSubmitted) {
    return (
      <Card className="p-8 text-center">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
          {language === 'ar' ? 'تم إرسال طلبك بنجاح!' : 'Request Submitted Successfully!'}
        </h3>
        <p className="text-slate-600 dark:text-slate-300 mb-4">
          {language === 'ar'
            ? 'سنتواصل معك قريباً لمناقشة متطلباتك.'
            : 'We\'ll contact you soon to discuss your requirements.'}
        </p>
        <Button onClick={onClose} variant="primary">
          {language === 'ar' ? 'إغلاق' : 'Close'}
        </Button>
      </Card>
    );
  }

  return (
    <Card className="p-6 max-w-2xl w-full">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
          {language === 'ar' ? `طلب خدمة ${serviceName}` : `Request ${serviceName}`}
        </h3>
        <button
          onClick={onClose}
          className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
          disabled={isSubmitting}
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Full Name */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <User className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <Input
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            placeholder={language === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'}
            className={cn(
              "transition-all duration-300",
              errors.fullName && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.fullName && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.fullName}
            </p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <Mail className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email address'}
            className={cn(
              "transition-all duration-300",
              errors.email && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.email}
            </p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <Phone className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <Input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            placeholder={language === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'}
            className={cn(
              "transition-all duration-300",
              errors.phone && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.phone}
            </p>
          )}
        </div>

        {/* Company Name */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <Building className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'اسم الشركة' : 'Company Name'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <Input
            name="companyName"
            value={formData.companyName}
            onChange={handleChange}
            placeholder={language === 'ar' ? 'أدخل اسم شركتك' : 'Enter your company name'}
            className={cn(
              "transition-all duration-300",
              errors.companyName && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.companyName && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.companyName}
            </p>
          )}
        </div>

        {/* Message */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <MessageSquare className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'تفاصيل الطلب' : 'Service Requirements'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder={language === 'ar'
              ? 'يرجى وصف متطلبات الخدمة بالتفصيل...'
              : 'Please describe your service requirements in detail...'}
            className={cn(
              "w-full min-h-[120px] px-3 py-2 border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 resize-none",
              isDarkMode
                ? "bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:ring-primary-500 focus:border-primary-500"
                : "bg-white border-slate-200 text-slate-900 placeholder-slate-500 focus:ring-primary-500 focus:border-primary-500",
              errors.message && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.message}
            </p>
          )}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            {language === 'ar' ? 'إلغاء' : 'Cancel'}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            className="flex items-center gap-2 min-w-[120px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                {language === 'ar' ? 'جاري الإرسال...' : 'Submitting...'}
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                {language === 'ar' ? 'إرسال الطلب' : 'Submit Request'}
              </>
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
}