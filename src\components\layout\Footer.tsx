'use client';

import Link from 'next/link';
import { Mail, Phone, MapPin } from 'lucide-react';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';

export function Footer() {
  const currentYear = new Date().getFullYear();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t } = useTranslation();

  return (
    <footer className={cn(
      "text-slate-200 relative overflow-hidden",
      isDarkMode ? "bg-slate-950" : "bg-slate-900"
    )}>
      {/* زخارف الخلفية */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-accent-500 to-primary-500"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* نمط الشبكة */}
      <div className="absolute inset-0 bg-slate-800/5 [mask-image:linear-gradient(to_bottom,transparent,black)] z-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>

      <div className="container-custom py-16 md:py-20 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          {/* Company Info */}
          <div className="space-y-6">
            <Link
              href={`/${language}`}
              className="flex items-center space-x-2 group transition-transform duration-300 hover:scale-105"
            >
              {/* شعار ARTAL */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 500 500"
                width="36"
                height="36"
                className="text-primary-400 transition-transform duration-500 group-hover:rotate-6"
              >
                {/* مثلث برتقالي/أصفر (السهم) */}
                <path d="M120 400 L250 50 L120 150 Z" fill="url(#orange-gradient-footer)" />

                {/* حرف A بنفسجي/وردي */}
                <path d="M250 100 L350 400 L400 400 L300 100 Z" fill="url(#purple-gradient-footer)" />
                <path d="M270 300 L330 300 L320 330 L280 330 Z" fill="white" />

                {/* قاعدة مثلث أزرق/سماوي */}
                <path d="M200 350 L350 350 L275 450 Z" fill="url(#blue-gradient-footer)" />

                {/* التدرجات اللونية */}
                <defs>
                  <linearGradient id="orange-gradient-footer" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF5722" />
                    <stop offset="100%" stopColor="#FFC107" />
                  </linearGradient>

                  <linearGradient id="purple-gradient-footer" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#9C27B0" />
                    <stop offset="100%" stopColor="#E91E63" />
                  </linearGradient>

                  <linearGradient id="blue-gradient-footer" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#2196F3" />
                    <stop offset="100%" stopColor="#00BCD4" />
                  </linearGradient>
                </defs>
              </svg>
              <span className="font-bold text-2xl text-white">
                {language === 'ar' ? 'ارتال' : 'ARTAL'}
              </span>
            </Link>

            <div className="relative">
              <div className="absolute -left-5 top-0 w-1 h-full bg-gradient-to-b from-primary-500 to-accent-500 rounded-full"></div>
              <p className="text-slate-300 pl-4 text-base leading-relaxed">
                {language === 'ar'
                  ? 'حلول الأعمال المتكاملة للبيع بالتجزئة والجملة واحتياجات الإنتاج. نقدم منتجات وخدمات عالية الجودة لتلبية احتياجات عملائنا.'
                  : 'Your complete business solution for retail, wholesale, and production needs. We provide high-quality products and services to meet our customers\' requirements.'}
              </p>
            </div>

            <div className="flex space-x-4">
              <a
                href="#"
                className="w-10 h-10 rounded-full bg-slate-800/50 flex items-center justify-center text-slate-400 hover:text-white hover:bg-primary-600 transition-all duration-300 shadow-lg hover:shadow-primary-500/20 border border-slate-700 hover:border-primary-500 group"
                aria-label="Facebook"
              >
                <FaFacebook size={18} className="group-hover:scale-110 transition-transform duration-300" />
              </a>
              <a
                href="#"
                className="w-10 h-10 rounded-full bg-slate-800/50 flex items-center justify-center text-slate-400 hover:text-white hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/20 border border-slate-700 hover:border-blue-500 group"
                aria-label="Twitter"
              >
                <FaTwitter size={18} className="group-hover:scale-110 transition-transform duration-300" />
              </a>
              <a
                href="#"
                className="w-10 h-10 rounded-full bg-slate-800/50 flex items-center justify-center text-slate-400 hover:text-white hover:bg-pink-600 transition-all duration-300 shadow-lg hover:shadow-pink-500/20 border border-slate-700 hover:border-pink-500 group"
                aria-label="Instagram"
              >
                <FaInstagram size={18} className="group-hover:scale-110 transition-transform duration-300" />
              </a>
              <a
                href="#"
                className="w-10 h-10 rounded-full bg-slate-800/50 flex items-center justify-center text-slate-400 hover:text-white hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/20 border border-slate-700 hover:border-blue-500 group"
                aria-label="LinkedIn"
              >
                <FaLinkedin size={18} className="group-hover:scale-110 transition-transform duration-300" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <div className="relative inline-block mb-6">
              <h4 className="text-white font-bold text-xl mb-1">
                {language === 'ar' ? 'روابط سريعة' : 'Quick Links'}
              </h4>
              <div className="h-1 w-12 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></div>
            </div>

            <ul className="space-y-3">
              {[
                { href: `/${language}`, label: language === 'ar' ? 'الرئيسية' : 'Home' },
                { href: `/${language}/shop`, label: language === 'ar' ? 'المتجر' : 'Shop' },
                { href: `/${language}/production-lines`, label: language === 'ar' ? 'خطوط الإنتاج' : 'Production Lines' },
                { href: `/${language}/services`, label: language === 'ar' ? 'الخدمات' : 'Services' },
                { href: `/${language}/clearance`, label: language === 'ar' ? 'التصفية' : 'Clearance' },
                { href: `/${language}/blog`, label: language === 'ar' ? 'المدونة' : 'Blog' }
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-slate-300 hover:text-primary-400 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-1.5 h-1.5 rounded-full bg-slate-600 group-hover:bg-primary-500 mr-2 transition-all duration-300 group-hover:scale-125"></span>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Support */}
          <div>
            <div className="relative inline-block mb-6">
              <h4 className="text-white font-bold text-xl mb-1">
                {language === 'ar' ? 'دعم العملاء' : 'Customer Support'}
              </h4>
              <div className="h-1 w-12 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full"></div>
            </div>

            <ul className="space-y-3">
              {[
                { href: `/${language}/contact`, label: language === 'ar' ? 'اتصل بنا' : 'Contact Us' },
                { href: `/${language}/faq`, label: language === 'ar' ? 'الأسئلة الشائعة' : 'FAQ' },
                { href: `/${language}/shipping`, label: language === 'ar' ? 'معلومات الشحن' : 'Shipping Information' },
                { href: `/${language}/returns`, label: language === 'ar' ? 'الإرجاع والاستبدال' : 'Returns & Exchanges' },
                { href: `/${language}/privacy-policy`, label: language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy' },
                { href: `/${language}/terms-of-service`, label: language === 'ar' ? 'شروط الخدمة' : 'Terms of Service' }
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-slate-300 hover:text-accent-400 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-1.5 h-1.5 rounded-full bg-slate-600 group-hover:bg-accent-500 mr-2 transition-all duration-300 group-hover:scale-125"></span>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Information */}
          <div>
            <div className="relative inline-block mb-6">
              <h4 className="text-white font-bold text-xl mb-1">
                {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
              </h4>
              <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full"></div>
            </div>

            <ul className="space-y-5">
              <li className={`flex items-start ${language === 'ar' ? 'space-x-reverse' : 'space-x-3'} group`}>
                <div className="w-10 h-10 rounded-lg bg-slate-800/80 flex items-center justify-center text-primary-400 group-hover:text-white group-hover:bg-primary-600 transition-all duration-300 flex-shrink-0 border border-slate-700 group-hover:border-primary-500 shadow-sm">
                  <MapPin size={18} className="group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div>
                  <h5 className="text-white text-sm font-medium mb-1">{language === 'ar' ? 'العنوان' : 'Address'}</h5>
                  <span className="text-slate-300 text-sm leading-relaxed block">
                    {language === 'ar'
                      ? '١٢٣ شارع الأعمال، جناح ١٠٠، نيويورك، نيويورك ١٠٠٠١'
                      : '123 Business Street, Suite 100, New York, NY 10001'}
                  </span>
                </div>
              </li>

              <li className={`flex items-start ${language === 'ar' ? 'space-x-reverse' : 'space-x-3'} group`}>
                <div className="w-10 h-10 rounded-lg bg-slate-800/80 flex items-center justify-center text-accent-400 group-hover:text-white group-hover:bg-accent-600 transition-all duration-300 flex-shrink-0 border border-slate-700 group-hover:border-accent-500 shadow-sm">
                  <Phone size={18} className="group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div>
                  <h5 className="text-white text-sm font-medium mb-1">{language === 'ar' ? 'الهاتف' : 'Phone'}</h5>
                  <a
                    href="tel:+***********"
                    className="text-slate-300 text-sm hover:text-accent-400 transition-colors block"
                  >
                    {language === 'ar' ? '١-٢٣٤-٥٦٧-٨٩٠٠+' : '+****************'}
                  </a>
                </div>
              </li>

              <li className={`flex items-start ${language === 'ar' ? 'space-x-reverse' : 'space-x-3'} group`}>
                <div className="w-10 h-10 rounded-lg bg-slate-800/80 flex items-center justify-center text-blue-400 group-hover:text-white group-hover:bg-blue-600 transition-all duration-300 flex-shrink-0 border border-slate-700 group-hover:border-blue-500 shadow-sm">
                  <Mail size={18} className="group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div>
                  <h5 className="text-white text-sm font-medium mb-1">{language === 'ar' ? 'البريد الإلكتروني' : 'Email'}</h5>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-slate-300 text-sm hover:text-blue-400 transition-colors block"
                  >
                    <EMAIL>
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>

        {/* شريط الحقوق */}
        <div className={cn(
          "mt-12 pt-8 pb-4 border-t flex flex-col md:flex-row justify-between items-center gap-4",
          isDarkMode ? "border-slate-800/50" : "border-slate-800/30"
        )}>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-slate-800 flex items-center justify-center mr-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 500 500"
                width="20"
                height="20"
                className="text-primary-400"
              >
                <path d="M120 400 L250 50 L120 150 Z" fill="url(#orange-gradient-footer)" />
                <path d="M250 100 L350 400 L400 400 L300 100 Z" fill="url(#purple-gradient-footer)" />
                <path d="M270 300 L330 300 L320 330 L280 330 Z" fill="white" />
                <path d="M200 350 L350 350 L275 450 Z" fill="url(#blue-gradient-footer)" />
              </svg>
            </div>
            <p className="text-slate-400 text-sm">
              &copy; {currentYear} {language === 'ar' ? 'ارتال' : 'ARTAL'}.
              {language === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
            </p>
          </div>

          {/* روابط سريعة في الأسفل */}
          <div className="flex flex-wrap justify-center gap-x-6 gap-y-2">
            {[
              { href: `/${language}/privacy-policy`, label: language === 'ar' ? 'الخصوصية' : 'Privacy' },
              { href: `/${language}/terms-of-service`, label: language === 'ar' ? 'الشروط' : 'Terms' },
              { href: `/${language}/sitemap`, label: language === 'ar' ? 'خريطة الموقع' : 'Sitemap' },
              { href: `/${language}/contact`, label: language === 'ar' ? 'اتصل بنا' : 'Contact' }
            ].map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className="text-slate-400 hover:text-primary-400 text-sm transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}