'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import {
  Menu, X, ShoppingCart, User, Search, Heart, Tag, Languages,
  Truck, Warehouse, ClipboardCheck, PackageCheck, Award, Briefcase,
  ArrowRight
} from 'lucide-react';
import React from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { ThemeToggle } from '../ui/ThemeToggle';
import { cn } from '../../lib/utils';
// Define NavigationItem type
interface NavigationItem {
  title: string;
  href: string;
  icon?: any;
  description?: string;
  submenu?: {
    title: string;
    href: string;
    description?: string;
    icon?: any;
  }[];
}
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useThemeStore } from '../../stores/themeStore';
import { useAuthModalStore } from '../../stores/authModalStore';

const getNavigationItems = (language: string): NavigationItem[] => {
  // إضافة بادئة اللغة للروابط
  const langPrefix = `/${language}`;

  if (language === 'ar') {
    return [
      {
        title: 'الرئيسية',
        href: langPrefix,
      },
      {
        title: 'المتجر',
        href: `${langPrefix}/shop`,
      },
      {
        title: 'خطوط الإنتاج',
        href: `${langPrefix}/production-lines`,
      },
      {
        title: 'الخدمات',
        href: `${langPrefix}/services`,
        submenu: [
          {
            title: 'خدمات الفحص',
            href: `${langPrefix}/services/inspection`,
            description: 'مراقبة الجودة والتحقق من المنتج في كل مرحلة'
          },
          {
            title: 'حلول التخزين',
            href: `${langPrefix}/services/storage`,
            description: 'تخزين آمن وإدارة المخزون'
          },
          {
            title: 'الشحن والخدمات اللوجستية',
            href: `${langPrefix}/services/shipping`,
            description: 'حلول الشحن العالمية وسلسلة التوريد'
          },
          {
            title: 'إدارة الطلبات',
            href: `${langPrefix}/services/order-management`,
            description: 'معالجة مبسطة لطلبات الجملة والطلبات الكبيرة'
          },
          {
            title: 'خدمات الشهادات',
            href: `${langPrefix}/services/certification`,
            description: 'شهادات المنتج والمساعدة في الامتثال'
          },
          {
            title: 'الخدمات الاستشارية',
            href: `${langPrefix}/services/consulting`,
            description: 'استشارات خبيرة في الأعمال والعمليات'
          },
        ],
      },
      {
        title: 'المدونة',
        href: `${langPrefix}/blog`,
      },
    ];
  } else {
    return [
      {
        title: 'Home',
        href: langPrefix,
      },
      {
        title: 'Shop',
        href: `${langPrefix}/shop`,
      },
      {
        title: 'Production Lines',
        href: `${langPrefix}/production-lines`,
      },
      {
        title: 'Services',
        href: `${langPrefix}/services`,
        submenu: [
          {
            title: 'Inspection Services',
            href: `${langPrefix}/services/inspection`,
            description: 'Quality control and product verification at every stage',
            icon: ClipboardCheck
          },
          {
            title: 'Storage Solutions',
            href: `${langPrefix}/services/storage`,
            description: 'Secure warehousing and inventory management',
            icon: Warehouse
          },
          {
            title: 'Shipping & Logistics',
            href: `${langPrefix}/services/shipping`,
            description: 'Global shipping and supply chain solutions',
            icon: Truck
          },
          {
            title: 'Order Management',
            href: `${langPrefix}/services/order-management`,
            description: 'Streamlined wholesale and bulk order processing',
            icon: PackageCheck
          },
          {
            title: 'Certification Services',
            href: `${langPrefix}/services/certification`,
            description: 'Product certification and compliance assistance',
            icon: Award
          },
          {
            title: 'Consulting Services',
            href: `${langPrefix}/services/consulting`,
            description: 'Expert business and operational consulting',
            icon: Briefcase
          },
        ],
      },
      {
        title: 'Blog',
        href: `${langPrefix}/blog`,
      },
    ];
  }
};

export function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const pathname = usePathname();
  const router = useRouter();
  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { language, setLanguage } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  const { openModal } = useAuthModalStore();

  // استخدام قائمة التنقل المناسبة للغة الحالية
  const navigation = getNavigationItems(language);

  const cartItemsCount = cartStore.getTotalItems();
  const wishlistItemsCount = wishlistStore.getWishlistCount();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsOpen(false);
    setShowSearch(false);
  }, [pathname]);

  const toggleSubmenu = (title: string) => {
    // إضافة تأثير انتقالي عند تبديل القائمة الفرعية
    if (activeSubmenu === title) {
      // إغلاق القائمة الفرعية بتأخير بسيط للحصول على تأثير انتقالي أفضل
      setTimeout(() => {
        setActiveSubmenu(null);
      }, 50);
    } else {
      setActiveSubmenu(title);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/${language}/shop?search=${encodeURIComponent(searchQuery.trim())}`);
      setShowSearch(false);
      setSearchQuery('');
    }
  };

  const handleAuthAction = () => {
    if (user) {
      router.push(`/${language}/account`);
    } else {
      openModal('sign-in');
    }
  };

  const toggleLanguage = () => {
    // تحديد اللغة الجديدة (العربية هي الافتراضية)
    const newLanguage = language === 'en' ? 'ar' : 'en';

    // تحديث اللغة في المتجر
    setLanguage(newLanguage);

    // استخراج المسار الحالي بدون اللغة
    let currentPath = pathname || '/';

    // إذا كان المسار يبدأ بـ /ar/ أو /en/، قم بإزالة جزء اللغة
    const langRegex = /^\/(ar|en)(\/|$)/;
    if (langRegex.test(currentPath)) {
      currentPath = currentPath.replace(langRegex, '/');
      // إذا أصبح المسار فارغًا، اجعله "/"
      if (currentPath === '') currentPath = '/';
    }

    // إنشاء المسار الجديد مع اللغة الجديدة
    const newPath = `/${newLanguage}${currentPath === '/' ? '' : currentPath}`;

    // عرض رسالة تأكيد للمستخدم (اختياري)
    if (language === 'ar') {
      console.log('Switching to English language');
    } else {
      console.log('جاري التحويل إلى اللغة العربية');
    }

    // التنقل إلى المسار الجديد
    window.location.href = newPath;
  };

  return (
    <div
      className={cn(
        'fixed w-full top-0 z-50 transition-all duration-500',
        isScrolled
          ? isDarkMode
            ? 'bg-slate-900/90 shadow-lg shadow-slate-900/10 py-2 backdrop-blur-md'
            : 'bg-white/90 shadow-lg shadow-slate-200/20 py-2 backdrop-blur-md'
          : 'bg-transparent py-4'
      )}
      dir={language === 'ar' ? 'rtl' : 'ltr'}
    >
      {/* شريط علوي للإعلانات والعروض الخاصة */}
      {!isScrolled && (
        <div className={cn(
          "absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 via-accent-500 to-primary-500"
        )}></div>
      )}

      <div className="container-custom">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link
            href={`/${language}`}
            className={`flex items-center ${language === 'ar' ? 'space-x-reverse' : 'space-x-2'} transition-all duration-300 hover:scale-105 group relative`}
          >
            <span className="sr-only">{language === 'ar' ? 'ارتال' : 'ARTAL'}</span>
            {/* تأثير توهج خلف الشعار */}
            <div className="absolute -inset-2 bg-gradient-to-r from-primary-500/20 to-accent-500/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            {/* استخدام SVG مباشرة بدلاً من الصورة */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 500 500"
              width="36"
              height="36"
              className="transition-transform duration-500 group-hover:rotate-6 relative z-10"
            >
              {/* مثلث برتقالي/أصفر (السهم) */}
              <path d="M120 400 L250 50 L120 150 Z" fill="url(#orange-gradient)" className="transform origin-center transition-transform duration-700 group-hover:scale-105" />

              {/* حرف A بنفسجي/وردي */}
              <path d="M250 100 L350 400 L400 400 L300 100 Z" fill="url(#purple-gradient)" className="transform origin-center transition-transform duration-700 group-hover:scale-105" />
              <path d="M270 300 L330 300 L320 330 L280 330 Z" fill="white" className="transform origin-center transition-transform duration-700 group-hover:scale-105" />

              {/* قاعدة مثلث أزرق/سماوي */}
              <path d="M200 350 L350 350 L275 450 Z" fill="url(#blue-gradient)" className="transform origin-center transition-transform duration-700 group-hover:scale-105" />

              {/* التدرجات اللونية */}
              <defs>
                <linearGradient id="orange-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#FF5722" />
                  <stop offset="100%" stopColor="#FFC107" />
                </linearGradient>

                <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#9C27B0" />
                  <stop offset="100%" stopColor="#E91E63" />
                </linearGradient>

                <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#2196F3" />
                  <stop offset="100%" stopColor="#00BCD4" />
                </linearGradient>
              </defs>
            </svg>
            <div className="relative">
              <span className={`font-bold text-xl hidden sm:block transition-all duration-300 ${isDarkMode ? 'text-white' : ''} relative z-10`}>
                {language === 'ar' ? 'ارتال' : 'ARTAL'}
              </span>
              {/* خط تحت الاسم */}
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-accent-500 group-hover:w-full transition-all duration-500"></div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className={`hidden lg:flex items-center ${language === 'ar' ? 'space-x-reverse space-x-6' : 'space-x-6'} mx-6`}>
            {navigation.map((item) => (
              <div
                key={item.title}
                className="relative group py-1"
                onMouseEnter={() => item.submenu && toggleSubmenu(item.title)}
                onMouseLeave={() => item.submenu && toggleSubmenu(item.title)}
              >
                <Link
                  href={item.href}
                  className={cn(
                    'font-medium text-base tracking-wide transition-all duration-300 px-3 py-1.5 rounded-md relative group-hover:text-primary-600 dark:group-hover:text-primary-400',
                    // تأثير الخط المتحرك تحت العنصر
                    'after:absolute after:bottom-0 after:left-1/2 after:right-1/2 after:h-0.5 after:bg-gradient-to-r after:from-primary-500 after:to-accent-500 after:rounded-full after:transition-all after:duration-500',
                    'hover:after:left-3 hover:after:right-3',
                    // تأثير الخلفية عند التحويم
                    'hover:bg-slate-50/80 dark:hover:bg-slate-800/30',
                    // حالة العنصر النشط
                    pathname === item.href || (pathname && pathname.startsWith(`${item.href}/`))
                      ? 'text-primary-600 dark:text-primary-400 after:left-3 after:right-3 font-semibold'
                      : isScrolled
                      ? isDarkMode ? 'text-slate-200' : 'text-slate-800'
                      : isDarkMode ? 'text-slate-100' : 'text-slate-700'
                  )}
                >
                  <span className="relative z-10">{item.title}</span>
                </Link>

                {/* القائمة الفرعية */}
                {item.submenu && (
                  <div
                    className={cn(
                      'absolute left-0 mt-2 w-80 rounded-xl shadow-xl bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border border-slate-100 dark:border-slate-700 transition-all duration-300 transform origin-top-right z-50',
                      language === 'ar' ? 'right-0 left-auto origin-top-left' : 'left-0 origin-top-right',
                      activeSubmenu === item.title
                        ? 'opacity-100 scale-100 translate-y-0'
                        : 'opacity-0 scale-95 translate-y-2 pointer-events-none'
                    )}
                  >
                    {/* شريط علوي متدرج */}
                    <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>

                    <div className="p-4 grid grid-cols-1 gap-2">
                      <h3 className={`text-sm font-semibold px-3 py-2 border-b border-slate-100 dark:border-slate-700 mb-2 ${language === 'ar' ? 'text-right' : 'text-left'} text-primary-600 dark:text-primary-400`}>
                        {language === 'ar' ? 'خدماتنا' : 'Our Services'}
                      </h3>

                      {item.submenu.map((subItem) => (
                        <Link
                          key={subItem.title}
                          href={subItem.href}
                          className={`flex items-center gap-3 px-3 py-3 text-sm rounded-lg transition-all duration-300 hover:bg-slate-50 dark:hover:bg-slate-700/50 group ${language === 'ar' ? 'flex-row-reverse text-right hover:-translate-x-1' : 'text-left hover:translate-x-1'}`}
                        >
                          {subItem.icon && (
                            <div className="flex-shrink-0 w-9 h-9 flex items-center justify-center rounded-lg bg-primary-50 dark:bg-slate-700 text-primary-500 dark:text-primary-400 group-hover:bg-primary-100 dark:group-hover:bg-slate-600 transition-colors shadow-sm">
                              {React.createElement(subItem.icon, { size: 18 })}
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-slate-800 dark:text-slate-200 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                              {subItem.title}
                            </div>
                            {subItem.description && (
                              <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5 line-clamp-1 group-hover:text-slate-700 dark:group-hover:text-slate-300 transition-colors">
                                {subItem.description}
                              </div>
                            )}
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Right side items */}
          <div className="flex items-center space-x-3">
            {/* Special Offers */}
            <Link
              href={`/${language}/clearance`}
              className={cn(
                'hidden md:flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 text-white transition-all duration-300 group relative overflow-hidden',
                isScrolled ? 'shadow-md' : 'shadow-lg shadow-accent-500/20'
              )}
            >
              {/* تأثير الخلفية المتحركة */}
              <span className="absolute inset-0 bg-gradient-to-r from-accent-600 to-accent-700 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></span>
              {/* تأثير التوهج */}
              <span className="absolute -inset-10 bg-accent-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 animate-pulse"></span>

              <Tag size={16} className="relative z-10 group-hover:rotate-12 transition-transform duration-300" />
              <span className="text-sm font-medium relative z-10">{language === 'ar' ? 'عروض خاصة' : 'Special Offers'}</span>

              {/* تأثير السهم المتحرك */}
              <span className={`absolute ${language === 'ar' ? 'left-3 group-hover:left-2' : 'right-3 group-hover:right-2'} top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10`}>
                <ArrowRight className={`w-4 h-4 ${language === 'ar' ? 'rotate-180' : ''}`} />
              </span>
            </Link>

            {/* Theme Toggle - تحسين مبدل السمة */}
            <div className="relative group">
              <ThemeToggle
                variant="menu"
                size="md"
                className={cn(
                  'relative z-10 p-2 rounded-full transition-all duration-300 hover:bg-slate-100/80 dark:hover:bg-slate-800/50',
                  isDarkMode
                    ? 'text-slate-200 hover:text-primary-400'
                    : isScrolled ? 'text-slate-700 hover:text-primary-600' : 'text-slate-700 hover:text-primary-600'
                )}
              />
              {/* تأثير توهج خلف الزر */}
              <span className="absolute inset-0 bg-primary-500/10 dark:bg-primary-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            </div>

            {/* Language Switcher - تحسين مبدل اللغة */}
            <button
              className={cn(
                'flex items-center gap-1.5 px-3 py-1.5 rounded-full transition-all duration-300 border relative overflow-hidden group',
                language === 'ar'
                  ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary-600 border-primary-200 hover:border-primary-300 dark:from-primary-900/30 dark:to-primary-800/30 dark:text-primary-400 dark:border-primary-800/50'
                  : 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 border-blue-200 hover:border-blue-300 dark:from-blue-900/30 dark:to-blue-800/30 dark:text-blue-400 dark:border-blue-800/50',
                isScrolled ? 'shadow-md' : 'shadow-lg',
                language === 'ar' ? 'shadow-primary-500/10' : 'shadow-blue-500/10'
              )}
              onClick={toggleLanguage}
              aria-label="Switch Language"
            >
              <Languages size={18} className="flex-shrink-0 relative z-10 group-hover:rotate-12 transition-transform duration-300" />
              <span className="text-sm font-semibold relative z-10">
                {language === 'ar' ? 'English' : 'العربية'}
              </span>
              {/* تأثير التوهج عند التحويم */}
              <span className="absolute inset-0 bg-white/40 dark:bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-0"></span>
              {/* شارة اللغة الافتراضية */}
              {language === 'ar' && (
                <span className="absolute top-0 right-0 bg-primary-500 text-white text-[10px] px-1.5 py-0.5 rounded-bl-md font-medium shadow-sm">الافتراضية</span>
              )}
            </button>

            {/* Search - تحسين البحث */}
            <div className="relative group">
              <button
                className={cn(
                  'p-2 rounded-full transition-all duration-300 hover:bg-slate-100/80 dark:hover:bg-slate-800/50 relative z-10',
                  isDarkMode
                    ? 'text-slate-200 hover:text-primary-400'
                    : isScrolled ? 'text-slate-700 hover:text-primary-600' : 'text-slate-700 hover:text-primary-600'
                )}
                onClick={() => setShowSearch(!showSearch)}
                aria-label="Search"
              >
                <Search size={20} className="group-hover:scale-110 transition-transform duration-300" />
              </button>
              {/* تأثير توهج خلف الزر */}
              <span className="absolute inset-0 bg-primary-500/10 dark:bg-primary-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>

              {/* مربع البحث المحسن */}
              {showSearch && (
                <div className="absolute right-0 mt-2 w-80 bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-xl shadow-xl p-4 z-50 border border-slate-200 dark:border-slate-700 transform transition-all duration-300 origin-top-right">
                  {/* شريط علوي متدرج */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>

                  <form onSubmit={handleSearch} className="flex gap-2">
                    <Input
                      type="search"
                      placeholder={language === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1 border-slate-200 dark:border-slate-700 focus:ring-2 focus:ring-primary-500/30"
                      autoFocus
                    />
                    <Button type="submit" size="sm" variant="primary" className="shadow-md hover:shadow-lg transition-shadow">
                      <Search size={16} className="mr-1" />
                      {language === 'ar' ? 'بحث' : 'Search'}
                    </Button>
                  </form>

                  {/* اقتراحات البحث السريع */}
                  <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                    <p className="text-xs text-slate-500 dark:text-slate-400 mb-2">{language === 'ar' ? 'بحث سريع:' : 'Quick search:'}</p>
                    <div className="flex flex-wrap gap-2">
                      {['Electronics', 'Clothing', 'Furniture', 'Tools'].map((term) => (
                        <button
                          key={term}
                          className="px-2 py-1 text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-md hover:bg-primary-50 dark:hover:bg-primary-900/30 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                          onClick={() => {
                            setSearchQuery(term);
                            router.push(`/${language}/shop?search=${encodeURIComponent(term)}`);
                            setShowSearch(false);
                          }}
                        >
                          {term}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Wishlist - تحسين قائمة الرغبات */}
            <div className="relative group">
              <Link
                href={`/${language}/shop/wishlist`}
                className={cn(
                  'p-2 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-slate-100/80 dark:hover:bg-slate-800/50 relative z-10',
                  isDarkMode
                    ? 'text-slate-200 hover:text-primary-400'
                    : isScrolled ? 'text-slate-700 hover:text-primary-600' : 'text-slate-700 hover:text-primary-600'
                )}
                aria-label="Wishlist"
              >
                <Heart size={20} className="group-hover:scale-110 transition-transform duration-300" />
              </Link>
              {/* تأثير توهج خلف الزر */}
              <span className="absolute inset-0 bg-primary-500/10 dark:bg-primary-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>

              {/* شارة العدد المحسنة */}
              {wishlistItemsCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-gradient-to-br from-accent-500 to-accent-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-md shadow-accent-500/20 border border-white/20 transition-all duration-300">
                  {wishlistItemsCount}
                </span>
              )}
            </div>

            {/* Cart - تحسين سلة التسوق */}
            <div className="relative group">
              <Link
                href={`/${language}/cart`}
                className={cn(
                  'p-2 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-slate-100/80 dark:hover:bg-slate-800/50 relative z-10',
                  isDarkMode
                    ? 'text-slate-200 hover:text-primary-400'
                    : isScrolled ? 'text-slate-700 hover:text-primary-600' : 'text-slate-700 hover:text-primary-600'
                )}
                aria-label="Shopping Cart"
              >
                <ShoppingCart size={20} className="group-hover:scale-110 transition-transform duration-300" />
              </Link>
              {/* تأثير توهج خلف الزر */}
              <span className="absolute inset-0 bg-primary-500/10 dark:bg-primary-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>

              {/* شارة العدد المحسنة */}
              {cartItemsCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-gradient-to-br from-primary-500 to-primary-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-md shadow-primary-500/20 border border-white/20 transition-all duration-300">
                  {cartItemsCount}
                </span>
              )}
            </div>

            {/* Account - تحسين زر الحساب */}
            <div className="relative group">
              <button
                onClick={handleAuthAction}
                className={cn(
                  'p-2 rounded-full flex items-center justify-center transition-all duration-300 relative z-10',
                  user
                    ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 hover:bg-primary-100 dark:hover:bg-primary-800/50 shadow-sm'
                    : 'hover:bg-slate-100/80 dark:hover:bg-slate-800/50',
                  isDarkMode && !user
                    ? 'text-slate-200 hover:text-primary-400'
                    : !user && (isScrolled ? 'text-slate-700 hover:text-primary-600' : 'text-slate-700 hover:text-primary-600')
                )}
                aria-label={user ? 'Account' : 'Sign In'}
              >
                <User size={20} className="group-hover:scale-110 transition-transform duration-300" />
              </button>
              {/* تأثير توهج خلف الزر */}
              <span className={cn(
                "absolute inset-0 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300",
                user ? 'bg-primary-500/20 dark:bg-primary-500/30' : 'bg-primary-500/10 dark:bg-primary-500/20'
              )}></span>

              {/* شارة تسجيل الدخول */}
              {user && (
                <span className="absolute -top-1 -right-1 bg-green-500 w-2.5 h-2.5 rounded-full border border-white dark:border-slate-800 shadow-sm"></span>
              )}
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className={cn(
              "lg:hidden p-2 rounded-full transition-colors",
              isDarkMode
                ? "hover:bg-slate-800 text-slate-300"
                : "hover:bg-slate-100 text-slate-900"
            )}
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle Menu"
          >
            {isOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          'fixed inset-0 z-40 lg:hidden transition-transform duration-300 transform',
          isDarkMode ? 'bg-slate-900 text-white' : 'bg-white text-slate-900',
          isOpen ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <div className="p-4 h-full overflow-y-auto">
          <div className="flex items-center justify-between mb-8">
            <Link href={`/${language}`} className="flex items-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary-500"
              >
                <path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"></path>
                <path d="M8 6h8"></path>
                <path d="M8 10h8"></path>
                <path d="M8 14h4"></path>
              </svg>
              <span className="font-bold text-xl">{language === 'ar' ? 'كوميرس برو' : 'CommercePro'}</span>
            </Link>
            <button
              className={cn(
                "p-2 rounded-full transition-colors",
                isDarkMode
                  ? "hover:bg-slate-800 text-slate-300"
                  : "hover:bg-slate-100 text-slate-900"
              )}
              onClick={() => setIsOpen(false)}
              aria-label="Close Menu"
            >
              <X size={24} />
            </button>
          </div>

          {/* Mobile Special Offers */}
          <Link
            href={`/${language}/clearance`}
            className="flex items-center gap-2 px-4 py-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-lg mb-4 relative overflow-hidden"
            onClick={() => setIsOpen(false)}
          >
            <Tag size={20} className="relative z-10" />
            <span className="font-medium relative z-10">{language === 'ar' ? 'عروض خاصة' : 'Special Offers'}</span>
            <span className="absolute inset-0 bg-white/10 opacity-0 hover:opacity-100 transition-opacity duration-300 -z-10" />
          </Link>

          <nav className="space-y-1">
            {navigation.map((item) => (
              <div key={item.title} className="py-2">
                <div
                  className="flex items-center justify-between py-2"
                  onClick={() => item.submenu && toggleSubmenu(item.title)}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      'text-lg font-medium',
                      pathname === item.href
                        ? 'text-primary-500'
                        : isDarkMode ? 'text-slate-200' : 'text-slate-900'
                    )}
                  >
                    {item.title}
                  </Link>
                  {item.submenu && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={cn(
                        'transition-transform duration-200',
                        activeSubmenu === item.title ? 'rotate-180' : ''
                      )}
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  )}
                </div>
                {item.submenu && activeSubmenu === item.title && (
                  <div className={cn(
                    "mt-2 space-y-2 p-3 rounded-lg",
                    isDarkMode ? "bg-slate-800/50" : "bg-slate-50",
                    language === 'ar' ? "mr-4 pr-4 border-r-2" : "ml-4 pl-4 border-l-2",
                    isDarkMode ? "border-slate-700" : "border-slate-200"
                  )}>
                    {item.submenu.map((subItem) => (
                      <Link
                        key={subItem.title}
                        href={subItem.href}
                        className={cn(
                          "flex items-center gap-3 py-2.5 px-2 rounded-md hover:bg-white dark:hover:bg-slate-700/50 transition-colors group",
                          isDarkMode ? "text-slate-300" : "text-slate-700",
                          language === 'ar' ? "flex-row-reverse text-right" : "text-left"
                        )}
                        onClick={() => setIsOpen(false)}
                      >
                        {subItem.icon && (
                          <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center rounded-full bg-primary-50 dark:bg-slate-700 text-primary-500 dark:text-primary-400 group-hover:bg-primary-100 dark:group-hover:bg-slate-600 transition-colors">
                            {React.createElement(subItem.icon, { size: 14 })}
                          </div>
                        )}
                        <div>
                          <div className="font-medium group-hover:text-primary-500">
                            {subItem.title}
                          </div>
                          {subItem.description && (
                            <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5 line-clamp-1">
                              {subItem.description}
                            </div>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          <div className="mt-8 space-y-4">
            {/* Mobile Language Switcher */}
            <button
              className={cn(
                'flex items-center justify-center gap-2 w-full py-3 rounded-lg transition-all duration-300 border-2 mb-4 relative overflow-hidden group',
                language === 'ar'
                  ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary-600 border-primary-200 hover:border-primary-300'
                  : 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 border-blue-200 hover:border-blue-300'
              )}
              onClick={() => {
                toggleLanguage();
                setIsOpen(false);
              }}
            >
              <Languages size={20} className="flex-shrink-0 relative z-10 group-hover:rotate-12 transition-transform duration-300" />
              <span className="font-medium relative z-10">
                {language === 'ar'
                  ? 'تغيير اللغة إلى الإنجليزية'
                  : 'تغيير إلى اللغة العربية (الافتراضية)'}
              </span>
              <span className="absolute inset-0 bg-white/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-0" />
              {language === 'ar' && (
                <span className="absolute top-0 right-0 bg-primary-500 text-white text-xs px-2 py-0.5 rounded-bl-md font-medium">الافتراضية</span>
              )}
            </button>

            <Button
              className="w-full"
              variant="primary"
              onClick={() => {
                openModal('sign-in');
                setIsOpen(false);
              }}
            >
              {user
                ? (language === 'ar' ? 'الحساب' : 'Account')
                : (language === 'ar' ? 'تسجيل الدخول' : 'Sign In')}
            </Button>
            {!user && (
              <Button
                className="w-full"
                variant="outline"
                onClick={() => {
                  openModal('sign-up');
                  setIsOpen(false);
                }}
              >
                {language === 'ar' ? 'إنشاء حساب' : 'Sign Up'}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Auth Modal is now handled by AuthModalProvider */}
    </div>
  );
}