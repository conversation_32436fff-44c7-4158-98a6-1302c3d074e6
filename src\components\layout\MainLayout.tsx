'use client';

import { ReactNode, useEffect, useState } from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { ChatWidget } from '../chat/ChatWidget';
import { PageTransition } from '../ui/PageTransition';
import { MobileNavBar } from '../ui/MobileNavBar';
import { SkipLink } from '../ui/SkipLink';
import { AccessibilityMenu } from '../ui/AccessibilityMenu';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';

interface MainLayoutProps {
  children: ReactNode;
  fullWidth?: boolean;
}

export function MainLayout({ children, fullWidth = false }: MainLayoutProps) {
  const { resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t } = useTranslation();
  const [isMobile, setIsMobile] = useState(false);

  // التحقق مما إذا كان الجهاز محمولاً
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // التحقق عند التحميل
    checkMobile();

    // التحقق عند تغيير حجم النافذة
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return (
    <div className={cn(
      "min-h-screen flex flex-col transition-colors duration-300",
      resolvedTheme === 'dark' ? 'bg-slate-950 text-slate-100' : 'bg-white text-slate-900'
    )}>
      {/* رابط تخطي التنقل لتحسين إمكانية الوصول */}
      <SkipLink href="#main-content">
        {language === 'ar' ? 'تخطي إلى المحتوى الرئيسي' : 'Skip to main content'}
      </SkipLink>

      <Header />
      <main
        id="main-content"
        className={cn(
          "flex-grow pb-16 md:pb-20", // تحسين المسافات السفلية فقط
          isMobile && "pb-24", // إضافة تباعد أسفل للأجهزة المحمولة لتجنب تداخل المحتوى مع شريط التنقل
          fullWidth ? "" : "pt-24 md:pt-28" // إضافة تباعد علوي فقط للصفحات العادية وليس للصفحات ذات العرض الكامل
        )}
        tabIndex={-1} // يسمح بالتركيز عند الانتقال إليه
      >
        <PageTransition>
          <div className={fullWidth ? '' : 'container-custom'}>
            {children}
          </div>
        </PageTransition>
      </main>
      <Footer />
      <ChatWidget />
      {isMobile && <MobileNavBar />}

      {/* قائمة إمكانية الوصول */}
      <AccessibilityMenu />
    </div>
  );
}