'use client';

import { useState, useEffect, useRef } from 'react';
import { X, Mail, Gift, ShoppingCart, Tag, ArrowRight, Check } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { EnhancedImage } from '../ui/EnhancedImage';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useTheme } from 'next-themes';
import { cn, formatCurrency } from '../../lib/utils';
import { Product } from '../../types/index';
import { useCartStore } from '../../stores/cartStore';
import { Badge } from '../ui/Badge';
import { HoverAnimation } from '../ui/animations/HoverAnimation';

interface ExitIntentPopupProps {
  delay?: number;
  sessionKey?: string;
  cookieDays?: number;
  onClose: () => void;
  product: Product;
}

export function ExitIntentPopup({
  delay = 5000,
  sessionKey = 'exit_popup_shown',
  cookieDays = 7,
  onClose,
  product
}: ExitIntentPopupProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const popupRef = useRef<HTMLDivElement>(null);

  const { language } = useLanguageStore();
  const { t, currentLanguage } = useTranslation();
  const { resolvedTheme } = useTheme();
  const cartStore = useCartStore();

  useEffect(() => {
    const hasSeenPopup =
      sessionStorage.getItem(sessionKey) === 'true' ||
      document.cookie.includes(`${sessionKey}=true`);

    if (hasSeenPopup) {
      onClose();
      return;
    }

    const timer = setTimeout(() => {
      document.addEventListener('mouseout', handleMouseLeave);
    }, delay);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mouseout', handleMouseLeave);
    };
  }, [delay, sessionKey, onClose]);

  const handleMouseLeave = (e: MouseEvent) => {
    if (e.clientY <= 0 && !isVisible) {
      setIsVisible(true);
      document.removeEventListener('mouseout', handleMouseLeave);
      sessionStorage.setItem(sessionKey, 'true');
      if (cookieDays > 0) {
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + cookieDays);
        document.cookie = `${sessionKey}=true; expires=${expiryDate.toUTCString()}; path=/`;
      }
    }
  };

  const internalHandleClose = () => {
    setIsVisible(false);
    onClose();
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(e.target as Node)) {
        internalHandleClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, internalHandleClose]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError(language === 'ar' ? 'يرجى إدخال بريدك الإلكتروني' : 'Please enter your email');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // هنا يمكن إضافة رمز لإرسال البريد الإلكتروني إلى API
      // await api.subscribeToNewsletter(email);

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsSubmitted(true);
    } catch (err) {
      setError(language === 'ar' ? 'حدث خطأ. يرجى المحاولة مرة أخرى.' : 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isVisible) {
    return null;
  }

  // إضافة المنتج إلى سلة التسوق
  const handleAddToCart = () => {
    cartStore.addItem({
      id: product.id,
      name: product.name,
      name_ar: product.name_ar,
      price: product.price,
      image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',
      quantity: 1,
    });

    // إغلاق النافذة المنبثقة بعد الإضافة
    setTimeout(() => {
      internalHandleClose();
    }, 500);
  };

  // حساب نسبة الخصم إذا كان هناك سعر مقارنة
  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price
    ? Math.round((1 - product.price / product.compareAtPrice) * 100)
    : product.discount || 0;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4">
      <HoverAnimation animation="fade">
        <div
          ref={popupRef}
          className={cn(
            "relative max-w-lg w-full rounded-xl shadow-2xl overflow-hidden",
            resolvedTheme === 'dark' ? "bg-slate-900" : "bg-white",
            "border border-slate-200 dark:border-slate-700"
          )}
        >
          {/* زر الإغلاق */}
          <button
            onClick={internalHandleClose}
            className="absolute top-4 right-4 z-10 p-1.5 rounded-full bg-white/80 dark:bg-slate-800/80 hover:bg-white dark:hover:bg-slate-700 transition-colors shadow-sm"
            aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
          >
            <X size={18} className="text-slate-700 dark:text-slate-200" />
          </button>

          {!isSubmitted ? (
            <div className="grid grid-cols-1 md:grid-cols-2">
              {/* صورة المنتج */}
              <div className="relative aspect-square bg-slate-100 dark:bg-slate-800">
                {product.images && product.images[0] && (
                  <EnhancedImage
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    objectFit="contain"
                    progressive={true}
                    placeholder="shimmer"
                    containerClassName="w-full h-full"
                  />
                )}

                {/* شارات المنتج */}
                <div className="absolute top-4 left-4 flex flex-col gap-1.5">
                  {discountPercentage > 0 && (
                    <Badge variant="destructive" size="md">
                      {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}
                    </Badge>
                  )}
                  {product.isNew && (
                    <Badge variant="primary" size="md">
                      {currentLanguage === 'ar' ? 'جديد' : 'New'}
                    </Badge>
                  )}
                </div>
              </div>

              {/* تفاصيل المنتج ونموذج الاشتراك */}
              <div className="p-6 flex flex-col">
                <div className="mb-4">
                  <h2 className="text-xl font-bold mb-1 text-slate-900 dark:text-white">
                    {currentLanguage === 'ar' ? 'عرض خاص لك!' : 'Special Offer For You!'}
                  </h2>

                  <div className="mb-3">
                    <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">
                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                    </h3>

                    <div className="flex items-baseline mt-2">
                      <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                        {formatCurrency(product.price)}
                      </span>
                      {product.compareAtPrice && product.compareAtPrice > product.price && (
                        <span className="ml-2 text-sm text-slate-500 line-through">
                          {formatCurrency(product.compareAtPrice)}
                        </span>
                      )}
                    </div>
                  </div>

                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                    {currentLanguage === 'ar'
                      ? 'لا تفوت هذا العرض الرائع! اشترك في نشرتنا الإخبارية للحصول على خصم إضافي.'
                      : 'Don\'t miss this amazing offer! Subscribe to our newsletter for an additional discount.'}
                  </p>

                  {/* أزرار الإجراءات */}
                  <div className="flex gap-2 mb-6">
                    <Button
                      variant="primary"
                      className="flex-1 flex items-center justify-center gap-1"
                      onClick={handleAddToCart}
                    >
                      <ShoppingCart className="h-4 w-4" />
                      <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>
                    </Button>

                    <Button
                      variant="outline"
                      className="flex items-center justify-center"
                      onClick={internalHandleClose}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* نموذج الاشتراك */}
                <div className="mt-auto">
                  <div className="border-t border-slate-200 dark:border-slate-700 pt-4 mb-4">
                    <p className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                      {currentLanguage === 'ar'
                        ? 'اشترك للحصول على خصم إضافي 10%'
                        : 'Subscribe for an additional 10% discount'}
                    </p>

                    <form onSubmit={handleSubmit}>
                      <div className="mb-3">
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
                          <Input
                            type="email"
                            placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className={cn(
                              "pl-10 py-2 text-sm",
                              error ? "border-error-500 focus:ring-error-500" : ""
                            )}
                          />
                        </div>
                        {error && <p className="mt-1 text-xs text-error-500">{error}</p>}
                      </div>

                      <Button
                        type="submit"
                        variant="secondary"
                        className="w-full text-sm"
                        size="sm"
                        isLoading={isLoading}
                      >
                        {currentLanguage === 'ar' ? 'احصل على الخصم' : 'Get Discount'}
                      </Button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-success-100 dark:bg-success-900 text-success-600 dark:text-success-300 mb-4">
                <Check className="h-8 w-8" />
              </div>
              <h2 className="text-2xl font-bold mb-2 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'شكراً لك!' : 'Thank You!'}
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-2">
                {currentLanguage === 'ar'
                  ? 'تم تسجيل بريدك الإلكتروني بنجاح.'
                  : 'Your email has been registered successfully.'}
              </p>
              <p className="text-primary-600 dark:text-primary-400 font-medium mb-6">
                {currentLanguage === 'ar'
                  ? 'رمز الخصم الخاص بك: WELCOME10'
                  : 'Your discount code: WELCOME10'}
              </p>

              <div className="flex gap-3 justify-center">
                <Button
                  variant="primary"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                </Button>
                <Button
                  variant="outline"
                  onClick={internalHandleClose}
                >
                  {currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </HoverAnimation>
    </div>
  );
}
