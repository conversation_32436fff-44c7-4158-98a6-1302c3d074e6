'use client';

import { useState, useEffect } from 'react';
import { Award, Gift, TrendingUp, ShoppingBag, ChevronRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Progress } from '../ui/Progress';
import { useLanguageStore } from '../../stores/languageStore';
import { useAuthStore } from '../../stores/authStore';
import { useAuthModalStore } from '../../stores/authModalStore';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';

// نموذج بيانات برنامج الولاء
interface LoyaltyData {
  points: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  nextTierPoints: number;
  availableRewards: Reward[];
  pointsHistory: PointHistory[];
}

interface Reward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: 'discount' | 'freeShipping' | 'gift' | 'exclusive';
  code?: string;
}

interface PointHistory {
  id: string;
  date: string;
  points: number;
  type: 'earned' | 'spent';
  description: string;
}

// بيانات تجريبية لبرنامج الولاء
const mockLoyaltyData: LoyaltyData = {
  points: 750,
  tier: 'silver',
  nextTierPoints: 1500,
  availableRewards: [
    {
      id: 'reward1',
      name: 'خصم 10%',
      description: 'خصم 10% على طلبك التالي',
      pointsCost: 500,
      type: 'discount',
      code: 'LOYAL10'
    },
    {
      id: 'reward2',
      name: 'شحن مجاني',
      description: 'شحن مجاني على طلبك التالي',
      pointsCost: 300,
      type: 'freeShipping',
      code: 'FREESHIP'
    },
    {
      id: 'reward3',
      name: 'هدية حصرية',
      description: 'هدية حصرية مع طلبك التالي',
      pointsCost: 1000,
      type: 'gift'
    }
  ],
  pointsHistory: [
    {
      id: 'history1',
      date: '2023-10-15',
      points: 250,
      type: 'earned',
      description: 'طلب #12345'
    },
    {
      id: 'history2',
      date: '2023-09-28',
      points: 500,
      type: 'earned',
      description: 'طلب #12300'
    },
    {
      id: 'history3',
      date: '2023-09-10',
      points: -300,
      type: 'spent',
      description: 'استبدال مكافأة: شحن مجاني'
    }
  ]
};

// تعريف مستويات برنامج الولاء
const loyaltyTiers = {
  bronze: {
    min: 0,
    max: 999,
    color: 'bg-amber-700',
    textColor: 'text-amber-700',
    name: { ar: 'برونزي', en: 'Bronze' }
  },
  silver: {
    min: 1000,
    max: 2499,
    color: 'bg-slate-400',
    textColor: 'text-slate-400',
    name: { ar: 'فضي', en: 'Silver' }
  },
  gold: {
    min: 2500,
    max: 4999,
    color: 'bg-yellow-500',
    textColor: 'text-yellow-500',
    name: { ar: 'ذهبي', en: 'Gold' }
  },
  platinum: {
    min: 5000,
    max: Infinity,
    color: 'bg-cyan-500',
    textColor: 'text-cyan-500',
    name: { ar: 'بلاتيني', en: 'Platinum' }
  }
};

export function LoyaltyProgram() {
  const [activeTab, setActiveTab] = useState<'overview' | 'rewards' | 'history'>('overview');
  const [loyaltyData, setLoyaltyData] = useState<LoyaltyData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { language } = useLanguageStore();
  const { user } = useAuthStore();
  const { openModal } = useAuthModalStore();

  // جلب بيانات برنامج الولاء
  useEffect(() => {
    const fetchLoyaltyData = async () => {
      setIsLoading(true);
      try {
        // هنا يمكن استبدال هذا بطلب API حقيقي
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLoyaltyData(mockLoyaltyData);
      } catch (error) {
        console.error('Error fetching loyalty data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchLoyaltyData();
    }
  }, [user]);

  // إذا لم يكن المستخدم مسجل الدخول
  if (!user) {
    return (
      <Card className="p-8 text-center">
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="inline-flex justify-center items-center w-24 h-24 rounded-full bg-primary-50 dark:bg-primary-900/20 mb-6">
            <Award className="h-12 w-12 text-primary-500" />
          </div>
          <h2 className="text-2xl font-bold mb-3 text-slate-900 dark:text-white">
            {language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program'}
          </h2>
          <p className="text-slate-600 dark:text-slate-300 mb-6 max-w-lg mx-auto">
            {language === 'ar'
              ? 'سجل الدخول أو أنشئ حسابًا للانضمام إلى برنامج الولاء واكسب النقاط واستمتع بالمكافآت الحصرية.'
              : 'Sign in or create an account to join our loyalty program, earn points, and enjoy exclusive rewards.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <HoverAnimation animation="scale">
              <Button onClick={() => openModal('sign-in')} size="lg">
                {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
              </Button>
            </HoverAnimation>
            <HoverAnimation animation="scale">
              <Button variant="outline" onClick={() => openModal('sign-up')} size="lg">
                {language === 'ar' ? 'إنشاء حساب' : 'Create Account'}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      </Card>
    );
  }

  // أثناء تحميل البيانات
  if (isLoading || !loyaltyData) {
    return (
      <Card className="p-8">
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-1/3 mb-6"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-5/6"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-4/6"></div>
            <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-1/4 mt-6"></div>
          </div>
        </ScrollAnimation>
      </Card>
    );
  }

  // حساب نسبة التقدم نحو المستوى التالي
  const currentTier = loyaltyTiers[loyaltyData.tier];
  const progress = Math.min(
    Math.round((loyaltyData.points / loyaltyData.nextTierPoints) * 100),
    100
  );

  return (
    <Card className="overflow-hidden">
      {/* رأس البطاقة */}
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="bg-gradient-to-r from-primary-600 to-primary-800 p-6 text-white">
          <div className="flex flex-col sm:flex-row items-center justify-between mb-4 gap-3">
            <div className="flex items-center gap-3">
              <Award className="h-8 w-8" />
              <h2 className="text-2xl font-bold">
                {language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program'}
              </h2>
            </div>
            <HoverAnimation animation="pulse">
              <div className="px-4 py-1 rounded-full bg-white/20 backdrop-blur-sm">
                <span className="font-medium">
                  {language === 'ar'
                    ? `المستوى: ${loyaltyTiers[loyaltyData.tier].name.ar}`
                    : `Tier: ${loyaltyTiers[loyaltyData.tier].name.en}`}
                </span>
              </div>
            </HoverAnimation>
          </div>

          <div className="mb-2">
            <div className="flex justify-between mb-1 text-sm">
              <span>
                {language === 'ar' ? 'النقاط الحالية:' : 'Current Points:'}
              </span>
              <span className="font-medium">
                {loyaltyData.points} / {loyaltyData.nextTierPoints}
              </span>
            </div>
            <Progress value={progress} className="h-2 bg-white/20" indicatorClassName={currentTier.color} />
          </div>

          <p className="text-sm text-white/80">
            {language === 'ar'
              ? `${loyaltyData.nextTierPoints - loyaltyData.points} نقطة متبقية للوصول إلى المستوى التالي`
              : `${loyaltyData.nextTierPoints - loyaltyData.points} points to reach the next tier`}
          </p>
        </div>
      </ScrollAnimation>

      {/* علامات التبويب */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="border-b dark:border-slate-700">
          <div className="flex">
            {(['overview', 'rewards', 'history'] as const).map((tab) => (
              <HoverAnimation key={tab} animation="lift">
                <button
                  onClick={() => setActiveTab(tab)}
                  className={cn(
                    "flex-1 py-4 px-4 text-center font-medium transition-colors",
                    activeTab === tab
                      ? "border-b-2 border-primary-500 text-primary-600 dark:text-primary-400"
                      : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
                  )}
                >
                  {language === 'ar'
                    ? tab === 'overview' ? 'نظرة عامة' : tab === 'rewards' ? 'المكافآت' : 'السجل'
                    : tab === 'overview' ? 'Overview' : tab === 'rewards' ? 'Rewards' : 'History'}
                </button>
              </HoverAnimation>
            ))}
          </div>
        </div>
      </ScrollAnimation>

      {/* محتوى علامة التبويب النشطة */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <Gift className="h-5 w-5 text-primary-500" />
                  <h3 className="font-medium text-slate-900 dark:text-white">
                    {language === 'ar' ? 'النقاط المتاحة' : 'Available Points'}
                  </h3>
                </div>
                <p className="text-2xl font-bold text-slate-900 dark:text-white">
                  {loyaltyData.points}
                </p>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <TrendingUp className="h-5 w-5 text-primary-500" />
                  <h3 className="font-medium text-slate-900 dark:text-white">
                    {language === 'ar' ? 'المستوى الحالي' : 'Current Tier'}
                  </h3>
                </div>
                <p className={`text-2xl font-bold ${currentTier.textColor}`}>
                  {language === 'ar' ? currentTier.name.ar : currentTier.name.en}
                </p>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <ShoppingBag className="h-5 w-5 text-primary-500" />
                  <h3 className="font-medium text-slate-900 dark:text-white">
                    {language === 'ar' ? 'المكافآت المتاحة' : 'Available Rewards'}
                  </h3>
                </div>
                <p className="text-2xl font-bold text-slate-900 dark:text-white">
                  {loyaltyData.availableRewards.length}
                </p>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4 text-slate-900 dark:text-white">
                {language === 'ar' ? 'كيفية كسب المزيد من النقاط' : 'How to Earn More Points'}
              </h3>
              <ul className="space-y-3">
                {[
                  { text: { ar: 'اكسب 1 نقطة لكل 10 ريال تنفقه', en: 'Earn 1 point for every $10 spent' } },
                  { text: { ar: 'اكسب 50 نقطة عند إكمال ملف التعريف الخاص بك', en: 'Earn 50 points for completing your profile' } },
                  { text: { ar: 'اكسب 100 نقطة عند كتابة مراجعة للمنتج', en: 'Earn 100 points for writing a product review' } },
                  { text: { ar: 'اكسب 200 نقطة عند إحالة صديق', en: 'Earn 200 points for referring a friend' } }
                ].map((item, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <ChevronRight className="h-5 w-5 text-primary-500 flex-shrink-0" />
                    <span className="text-slate-700 dark:text-slate-300">
                      {language === 'ar' ? item.text.ar : item.text.en}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'rewards' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {loyaltyData.availableRewards.map((reward) => (
                <Card key={reward.id} className="p-4 border dark:border-slate-700">
                  <div className="flex justify-between mb-2">
                    <h3 className="font-medium text-slate-900 dark:text-white">
                      {reward.name}
                    </h3>
                    <span className="text-sm font-medium bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 px-2 py-0.5 rounded-full">
                      {reward.pointsCost} {language === 'ar' ? 'نقطة' : 'points'}
                    </span>
                  </div>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    {reward.description}
                  </p>
                  <Button
                    size="sm"
                    variant={loyaltyData.points >= reward.pointsCost ? "primary" : "outline"}
                    disabled={loyaltyData.points < reward.pointsCost}
                    className="w-full"
                  >
                    {language === 'ar' ? 'استبدال' : 'Redeem'}
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b dark:border-slate-700">
                    <th className="py-3 px-4 text-left text-sm font-medium text-slate-500 dark:text-slate-400">
                      {language === 'ar' ? 'التاريخ' : 'Date'}
                    </th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-slate-500 dark:text-slate-400">
                      {language === 'ar' ? 'الوصف' : 'Description'}
                    </th>
                    <th className="py-3 px-4 text-right text-sm font-medium text-slate-500 dark:text-slate-400">
                      {language === 'ar' ? 'النقاط' : 'Points'}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {loyaltyData.pointsHistory.map((item) => (
                    <tr key={item.id} className="border-b dark:border-slate-700">
                      <td className="py-3 px-4 text-sm text-slate-700 dark:text-slate-300">
                        {new Date(item.date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </td>
                      <td className="py-3 px-4 text-sm text-slate-700 dark:text-slate-300">
                        {item.description}
                      </td>
                      <td className={`py-3 px-4 text-sm font-medium text-right ${
                        item.type === 'earned'
                          ? 'text-success-600 dark:text-success-400'
                          : 'text-error-600 dark:text-error-400'
                      }`}>
                        {item.type === 'earned' ? '+' : ''}{item.points}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
