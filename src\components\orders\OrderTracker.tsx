import { useState } from 'react';
import { Package, Truck, CheckCircle, Clock, X } from 'lucide-react';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

interface OrderTrackerProps {
  onClose: () => void;
}

export function OrderTracker({ onClose }: OrderTrackerProps) {
  const [orderNumber, setOrderNumber] = useState('');
  const [trackingResult, setTrackingResult] = useState<any>(null);

  const handleTrack = () => {
    setTrackingResult({
      status: 'in_transit',
      estimatedDelivery: '2025-03-20',
      currentLocation: 'New York Distribution Center',
      events: [
        {
          date: '2025-03-15',
          time: '14:30',
          location: 'New York Distribution Center',
          status: 'Package in transit'
        },
        {
          date: '2025-03-14',
          time: '09:15',
          location: 'Los Angeles Warehouse',
          status: 'Package processed'
        },
        {
          date: '2025-03-13',
          time: '16:45',
          location: 'Los Angeles Warehouse',
          status: 'Order confirmed'
        }
      ]
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="max-w-2xl w-full p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">Track Your Order</h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex gap-4 mb-8">
          <Input
            type="text"
            placeholder="Enter order number"
            value={orderNumber}
            onChange={(e) => setOrderNumber(e.target.value)}
            className="flex-1"
          />
          <Button onClick={handleTrack}>
            Track Order
          </Button>
        </div>

        {trackingResult && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500">Estimated Delivery</p>
                <p className="font-medium">{trackingResult.estimatedDelivery}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-slate-500">Current Location</p>
                <p className="font-medium">{trackingResult.currentLocation}</p>
              </div>
            </div>

            <div className="relative">
              <div className="absolute left-6 top-0 h-full w-0.5 bg-slate-200"></div>
              <div className="space-y-8">
                {trackingResult.events.map((event: any, index: number) => (
                  <div key={index} className="flex gap-4">
                    <div className="relative z-10">
                      {index === 0 ? (
                        <div className="w-12 h-12 rounded-full bg-primary-500 text-white flex items-center justify-center">
                          <Package size={24} />
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-slate-100 text-slate-500 flex items-center justify-center">
                          <Clock size={24} />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{event.status}</p>
                      <p className="text-sm text-slate-500">
                        {event.location} - {event.date} {event.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}