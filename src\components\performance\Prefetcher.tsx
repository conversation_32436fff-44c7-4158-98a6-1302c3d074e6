'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { products } from '../../data/products';
import { services } from '../../data/services';
import { clearanceItems } from '../../data/clearanceItems';
import { blogPosts } from '../../data/blogPosts';
import api from '../../lib/api';
import { defaultCache } from '../../lib/localCache';

/**
 * مكون لتحميل البيانات المسبق لتحسين الأداء
 * يقوم بتحميل البيانات المتوقع استخدامها في الصفحات التالية
 */
export function Prefetcher() {
  const pathname = usePathname();
  const queryClient = useQueryClient();

  // تحميل البيانات المسبق بناءً على المسار الحالي
  useEffect(() => {
    const prefetchData = async () => {
      const path = pathname;

      // الصفحة الرئيسية
      if (path === '/') {
        // تحميل المنتجات المميزة مسبقًا
        const featuredProducts = products.filter(product => product.featured).slice(0, 4);
        queryClient.setQueryData(['featured-products'], featuredProducts);

        // تحميل عناصر التصفية مسبقًا
        queryClient.setQueryData(['clearance-items'], clearanceItems.slice(0, 3));

        // تخزين البيانات في التخزين المؤقت المحلي
        defaultCache.set('featured-products', featuredProducts);
        defaultCache.set('clearance-items', clearanceItems.slice(0, 3));
      }

      // صفحة المتجر
      else if (path === '/shop') {
        // تحميل جميع المنتجات مسبقًا
        queryClient.setQueryData(['products'], products);
        defaultCache.set('products', products);
      }

      // صفحة المنتج
      else if (path.startsWith('/shop/product/')) {
        const slug = path.split('/').pop();
        const product = products.find(p => p.slug === slug);

        if (product) {
          // تحميل المنتج الحالي مسبقًا
          queryClient.setQueryData(['product', slug], product);

          // تحميل المنتجات ذات الصلة مسبقًا
          const relatedProducts = products
            .filter(p => p.category === product.category && p.id !== product.id)
            .slice(0, 4);

          queryClient.setQueryData(['related-products', slug], relatedProducts);

          // تخزين البيانات في التخزين المؤقت المحلي
          defaultCache.set(`product-${slug}`, product);
          defaultCache.set(`related-products-${slug}`, relatedProducts);
        }
      }

      // صفحة الخدمات
      else if (path === '/services') {
        // تحميل جميع الخدمات مسبقًا
        queryClient.setQueryData(['services'], services);
        defaultCache.set('services', services);
      }

      // صفحة المدونة
      else if (path === '/blog') {
        // تحميل منشورات المدونة مسبقًا
        queryClient.setQueryData(['blog-posts'], blogPosts);
        defaultCache.set('blog-posts', blogPosts);
      }

      // صفحة منشور المدونة
      else if (path.startsWith('/blog/')) {
        const slug = path.split('/').pop();
        const post = blogPosts.find(p => p.slug === slug);

        if (post) {
          // تحميل المنشور الحالي مسبقًا
          queryClient.setQueryData(['blog-post', slug], post);

          // تحميل المنشورات ذات الصلة مسبقًا
          const relatedPosts = blogPosts
            .filter(p => p.categories.some(cat => post.categories.includes(cat)) && p.id !== post.id)
            .slice(0, 3);

          queryClient.setQueryData(['related-posts', slug], relatedPosts);

          // تخزين البيانات في التخزين المؤقت المحلي
          defaultCache.set(`blog-post-${slug}`, post);
          defaultCache.set(`related-posts-${slug}`, relatedPosts);
        }
      }

      // تحميل البيانات من API خارجي للاختبار
      if (path === '/test-query') {
        try {
          const response = await api.get('https://jsonplaceholder.typicode.com/todos/1');
          queryClient.setQueryData(['test', '1'], response.data);
          defaultCache.set('test-1', response.data);
        } catch (error) {
          console.error('Error prefetching test data:', error);
        }
      }
    };

    prefetchData();
  }, [pathname, queryClient]);

  // هذا المكون لا يعرض أي شيء في واجهة المستخدم
  return null;
}
