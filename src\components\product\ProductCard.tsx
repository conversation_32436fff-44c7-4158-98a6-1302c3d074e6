'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Heart, ShoppingCart, Eye, Star } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { EnhancedImage } from '../ui/EnhancedImage';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useThemeStore } from '../../stores/themeStore';
import { formatCurrency, cn } from '../../lib/utils';
import { Product } from '../../types';
import { useTranslation } from '../../translations';
import { HoverAnimation } from '../ui/animations/HoverAnimation';

interface ProductCardProps {
  product: Product;
  index?: number;
  className?: string;
  showQuickView?: boolean;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  onQuickView?: (product: Product) => void;
  onAddToCart?: (product: Product) => void;
  onToggleWishlist?: (product: Product) => void;
}

export function ProductCard({
  product,
  index = 0,
  className = '',
  showQuickView = true,
  showAddToCart = true,
  showWishlist = true,
  onQuickView,
  onAddToCart,
  onToggleWishlist,
}: ProductCardProps) {
  const { t, currentLanguage } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const [imageError, setImageError] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onAddToCart) {
      onAddToCart(product);
    } else {
      cartStore.addItem({
        id: product.id,
        name: product.name,
        name_ar: product.name_ar,
        price: product.price,
        image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',
        quantity: 1,
      });
    }
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onToggleWishlist) {
      onToggleWishlist(product);
    } else {
      if (wishlistStore.isInWishlist(product.id)) {
        wishlistStore.removeItem(product.id);
      } else {
        wishlistStore.addItem(product);
      }
    }
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onQuickView) {
      onQuickView(product);
    }
  };

  // Fallback image if the product image fails to load
  const fallbackImage = `/images/product-placeholder-${isDarkMode ? 'dark' : 'light'}.svg`;

  // Use the first product image or fallback if there are no images
  const productImage = imageError || !product.images || product.images.length === 0
    ? fallbackImage
    : product.images[0];

  // تحديد ما إذا كان المنتج في المخزون
  const isInStock = product.stock > 0;

  // حساب نسبة الخصم إذا كان هناك سعر مقارنة
  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price
    ? Math.round((1 - product.price / product.compareAtPrice) * 100)
    : product.discount || 0;

  return (
    <HoverAnimation animation="lift">
      <Card
        className={cn(
          "group flex flex-col h-full overflow-hidden rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-slate-800",
          className
        )}
      >
        <div className="relative">
          <Link href={`/shop/${product.slug}`}>
            <div className="relative w-full h-48 overflow-hidden">
              <EnhancedImage
                src={productImage}
                alt={product.name}
                fill={true}
                objectFit="cover"
                progressive={true}
                placeholder="shimmer"
                className="transition-transform duration-500 group-hover:scale-105"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                priority={index < 4}
                onError={() => setImageError(true)}
              />
            </div>
          </Link>

          {/* Product badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
            {product.isNew && (
              <span className="px-2 py-1 text-xs font-semibold bg-blue-500 text-white rounded-full shadow-sm">
                {currentLanguage === 'ar' ? 'جديد' : 'New'}
              </span>
            )}
            {discountPercentage > 0 && (
              <span className="px-2 py-1 text-xs font-semibold bg-red-500 text-white rounded-full shadow-sm">
                {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}
              </span>
            )}
            {!isInStock && (
              <span className="px-2 py-1 text-xs font-semibold bg-gray-500 text-white rounded-full shadow-sm">
                {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}
              </span>
            )}
            {product.featured && (
              <span className="px-2 py-1 text-xs font-semibold bg-amber-500 text-white rounded-full shadow-sm">
                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
              </span>
            )}
          </div>

          {/* Action buttons */}
          <div className="absolute top-2 right-2 flex flex-col gap-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {showWishlist && (
              <Button
                variant="icon"
                size="sm"
                className={cn(
                  "p-1.5 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110",
                  wishlistStore.isInWishlist(product.id)
                    ? "bg-primary-500 text-white"
                    : "bg-white text-slate-700 dark:bg-slate-700 dark:text-white"
                )}
                onClick={handleToggleWishlist}
                aria-label={t('shop.addToWishlist')}
              >
                <Heart
                  className={cn(
                    "h-4 w-4",
                    wishlistStore.isInWishlist(product.id) && "fill-current"
                  )}
                />
              </Button>
            )}

            {showQuickView && onQuickView && (
              <Button
                variant="icon"
                size="sm"
                className="p-1.5 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110"
                onClick={handleQuickView}
                aria-label={t('shop.quickView')}
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Overlay on hover */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>

        </div>

        <div className="p-4 flex flex-col flex-grow">
          {/* Category & Rating */}
          <div className="flex items-center justify-between mb-2">
            {product.category && (
              <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                {product.category}
              </span>
            )}

            <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
              <Star className={`h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`} />
              <span>
                {product.rating?.toFixed(1) ?? 'N/A'}
                ({product.reviewCount ?? 0})
              </span>
            </div>
          </div>

          {/* Product name */}
          <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1">
              {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
            </h3>
          </Link>

          {/* Product description */}
          <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm">
            {currentLanguage === 'ar'
              ? (product.description_ar || product.description)
              : product.description}
          </p>

          {/* Price & Stock */}
          <div className="flex items-center justify-between mb-3 mt-auto">
            <div className="flex items-baseline gap-1">
              <span className="text-lg font-bold text-slate-900 dark:text-white">
                {formatCurrency(product.price)}
              </span>
              {product.compareAtPrice && product.compareAtPrice > product.price && (
                <span className="text-sm text-slate-500 line-through">
                  {formatCurrency(product.compareAtPrice)}
                </span>
              )}
            </div>

            <div className="text-xs font-medium">
              {isInStock ? (
                <span className="text-green-600 dark:text-green-400">
                  {currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}
                </span>
              ) : (
                <span className="text-red-600 dark:text-red-400">
                  {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}
                </span>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2">
            {/* Add to cart button */}
            {showAddToCart && (
              <Button
                variant="primary"
                size="sm"
                className="flex-1 rounded-md"
                onClick={handleAddToCart}
                disabled={!isInStock}
                aria-label={t('shop.addToCart')}
              >
                <ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>
              </Button>
            )}

            {/* Quick view button for mobile */}
            {showQuickView && onQuickView && (
              <Button
                variant="outline"
                size="sm"
                className="rounded-md"
                onClick={handleQuickView}
                aria-label={t('shop.quickView')}
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </Card>
    </HoverAnimation>
  );
}
