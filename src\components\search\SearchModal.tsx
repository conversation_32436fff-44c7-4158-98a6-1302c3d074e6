import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Search, Tag } from 'lucide-react';
import Fuse from 'fuse.js';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { products } from '../../data/products';
import { formatCurrency } from '../../lib/utils';

interface SearchModalProps {
  onClose: () => void;
}

const fuse = new Fuse(products, {
  keys: ['name', 'description', 'category', 'tags'],
  threshold: 0.3,
});

export function SearchModal({ onClose }: SearchModalProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState(products);
  const navigate = useNavigate();

  useEffect(() => {
    if (query) {
      setResults(fuse.search(query).map(result => result.item));
    } else {
      setResults(products);
    }
  }, [query]);

  const handleSelect = (slug: string) => {
    navigate(`/shop/product/${slug}`);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-20 p-4 z-50">
      <Card className="max-w-2xl w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Search Products</h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600"
          >
            <X size={20} />
          </button>
        </div>

        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
          <Input
            type="search"
            placeholder="Search products..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10"
            autoFocus
          />
        </div>

        <div className="space-y-4 max-h-[60vh] overflow-y-auto">
          {results.map((product) => (
            <div
              key={product.id}
              className="flex items-center gap-4 p-4 hover:bg-slate-50 rounded-lg cursor-pointer"
              onClick={() => handleSelect(product.slug)}
            >
              <img
                src={product.images[0]}
                alt={product.name}
                className="w-16 h-16 object-cover rounded"
              />
              <div className="flex-1">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-slate-600 line-clamp-1">{product.description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm font-medium">{formatCurrency(product.price)}</span>
                  <span className="text-xs text-slate-500">{product.category}</span>
                  {product.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center text-xs bg-primary-50 text-primary-700 px-2 py-0.5 rounded"
                    >
                      <Tag size={12} className="mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}

          {results.length === 0 && (
            <div className="text-center py-8">
              <Search className="mx-auto h-12 w-12 text-slate-300 mb-4" />
              <p className="text-slate-600">No products found matching your search.</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}