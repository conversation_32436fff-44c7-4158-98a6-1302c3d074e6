'use client';

import { useEffect, useState } from 'react';
import api from '../../lib/api';

// مكون لإدارة CSRF Token
export function CsrfToken() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // وظيفة لجلب CSRF Token من الخادم
    const fetchCsrfToken = async () => {
      try {
        // في بيئة الإنتاج، سيتم استدعاء نقطة نهاية حقيقية
        // هنا نقوم بمحاكاة الاستجابة للتطوير المحلي
        const mockToken = `csrf-${Math.random().toString(36).substring(2, 15)}`;

        // إنشاء عنصر meta لتخزين CSRF Token
        let metaTag = document.querySelector('meta[name="csrf-token"]');

        if (!metaTag) {
          metaTag = document.createElement('meta');
          metaTag.setAttribute('name', 'csrf-token');
          document.head.appendChild(metaTag);
        }

        metaTag.setAttribute('content', mockToken);
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    };

    fetchCsrfToken();
  }, []);

  // هذا المكون لا يعرض أي شيء في واجهة المستخدم
  return null;
}
