'use client';

interface OrganizationSchemaProps {
  url: string;
  logo?: string;
  name?: string;
  description?: string;
  sameAs?: string[];
}

export function generateOrganizationSchema({
  url,
  logo = '/logo.svg',
  name = 'ARTAL',
  description = 'منصة تجارية متكاملة تقدم خدمات البيع بالتجزئة والجملة وخطوط الإنتاج وخدمات الأعمال والمزيد.',
  sameAs = []
}: OrganizationSchemaProps) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://artal.com';

  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    'url': url,
    'logo': logo.startsWith('http') ? logo : `${baseUrl}${logo}`,
    'name': name,
    'description': description,
    'sameAs': sameAs,
    'contactPoint': {
      '@type': 'ContactPoint',
      'telephone': '+966-123-456789',
      'contactType': 'customer service',
      'availableLanguage': ['Arabic', 'English']
    }
  };

  return organizationSchema;
}

export function OrganizationSchema(props: OrganizationSchemaProps) {
  const organizationSchema = generateOrganizationSchema(props);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
    />
  );
}
