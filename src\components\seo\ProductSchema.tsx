'use client';

import { Product, Review } from '../../types';

interface ProductSchemaProps {
  product: Product;
  url: string;
}

export function generateProductSchema(product: Product, url: string) {
  // تحويل التقييمات إلى تنسيق Schema.org
  const reviews = product.reviews?.map((review: Review) => ({
    '@type': 'Review',
    'reviewRating': {
      '@type': 'Rating',
      'ratingValue': review.rating,
      'bestRating': '5'
    },
    'author': {
      '@type': 'Person',
      'name': review.userName
    },
    'reviewBody': review.comment,
    'datePublished': review.createdAt
  })) || [];

  // إنشاء كائن Schema.org للمنتج
  const productSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    'name': product.name,
    'image': product.images,
    'description': product.description,
    'sku': product.id,
    'mpn': product.id,
    'brand': {
      '@type': 'Brand',
      'name': 'CommercePro'
    },
    'offers': {
      '@type': 'Offer',
      'url': url,
      'priceCurrency': 'SAR', // يمكن تغييره حسب العملة المستخدمة
      'price': product.price,
      'priceValidUntil': new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
      'itemCondition': 'https://schema.org/NewCondition',
      'availability': product.stock > 0 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock'
    }
  };

  // إضافة التقييمات إذا كانت موجودة
  if (product.rating && reviews.length > 0) {
    Object.assign(productSchema, {
      'aggregateRating': {
        '@type': 'AggregateRating',
        'ratingValue': product.rating,
        'reviewCount': reviews.length
      },
      'review': reviews
    });
  }

  return productSchema;
}

export function ProductSchema({ product, url }: ProductSchemaProps) {
  const productSchema = generateProductSchema(product, url);
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(productSchema) }}
    />
  );
}
