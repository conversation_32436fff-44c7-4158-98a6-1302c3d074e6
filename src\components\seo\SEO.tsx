'use client';

import { useEffect } from 'react';
import Head from 'next/head';
import { usePathname } from 'next/navigation';
import { useLanguageStore } from '../../stores/languageStore';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image';
  canonicalUrl?: string;
  noIndex?: boolean;
  structuredData?: Record<string, any>;
  children?: React.ReactNode;
}

export function SEO({
  title,
  description,
  keywords,
  ogImage = '/images/og-image.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  noIndex = false,
  structuredData,
  children,
}: SEOProps) {
  const pathname = usePathname();
  const { language } = useLanguageStore();

  // تحديد العناوين والأوصاف حسب اللغة
  const defaultTitle = language === 'ar'
    ? 'ارتال | حلول الأعمال المتكاملة'
    : 'ARTAL | Your Complete Business Solution';

  const defaultDescription = language === 'ar'
    ? 'منصة تجارية متكاملة تقدم خدمات البيع بالتجزئة والجملة وخطوط الإنتاج وخدمات الأعمال والمزيد.'
    : 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.';

  const pageTitle = title ? `${title} | ${defaultTitle.split(' | ')[0]}` : defaultTitle;
  const pageDescription = description || defaultDescription;

  // تحديد الرابط القانوني
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://artal.com';
  const currentUrl = canonicalUrl || `${baseUrl}${pathname}`;

  // تحويل بيانات Schema.org إلى نص JSON
  const structuredDataJSON = structuredData
    ? JSON.stringify(structuredData)
    : null;

  return (
    <>
      <Head>
        {/* العناوين الأساسية */}
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        {keywords && <meta name="keywords" content={keywords} />}

        {/* الروابط القانونية */}
        <link rel="canonical" href={currentUrl} />

        {/* Open Graph */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content={ogType} />
        <meta property="og:url" content={currentUrl} />
        <meta property="og:image" content={ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`} />
        <meta property="og:site_name" content={defaultTitle.split(' | ')[0]} />
        <meta property="og:locale" content={language === 'ar' ? 'ar_SA' : 'en_US'} />

        {/* Twitter Card */}
        <meta name="twitter:card" content={twitterCard} />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        <meta name="twitter:image" content={ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`} />

        {/* إضافة علامة noindex إذا كان مطلوبًا */}
        {noIndex && <meta name="robots" content="noindex, nofollow" />}

        {/* بيانات منظمة Schema.org */}
        {structuredDataJSON && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: structuredDataJSON }}
          />
        )}
      </Head>
      {children}
    </>
  );
}
