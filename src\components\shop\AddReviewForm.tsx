import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Star, Upload, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { cn } from '../../lib/utils';
import { useTranslation } from '../../translations';

// تعريف مخطط التحقق من صحة النموذج
const reviewSchema = z.object({
  title: z.string().min(3, 'العنوان قصير جدًا').max(100, 'العنوان طويل جدًا'),
  comment: z.string().min(10, 'المراجعة قصيرة جدًا').max(1000, 'المراجعة طويلة جدًا'),
  rating: z.number().min(1, 'يرجى اختيار تقييم').max(5),
});

type ReviewFormData = z.infer<typeof reviewSchema>;

interface AddReviewFormProps {
  productId: string;
  productName: string;
  onSubmit: (data: ReviewFormData & { images: string[] }) => void;
  onCancel: () => void;
}

export function AddReviewForm({
  productId,
  productName,
  onSubmit,
  onCancel,
}: AddReviewFormProps) {
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [images, setImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { user } = useAuthStore();
  const { t } = useTranslation();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      title: '',
      comment: '',
      rating: 0,
    },
  });
  
  // معالجة تقديم النموذج
  const handleFormSubmit = async (data: ReviewFormData) => {
    if (rating === 0) {
      return; // يجب اختيار تقييم
    }
    
    setIsSubmitting(true);
    
    try {
      // إرسال البيانات مع الصور
      await onSubmit({
        ...data,
        rating,
        images,
      });
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // معالجة تحميل الصور
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    
    // التحقق من عدد الصور (الحد الأقصى 5)
    if (images.length + files.length > 5) {
      alert(t('product.maxImagesError'));
      return;
    }
    
    // تحويل الملفات إلى URLs
    Array.from(files).forEach((file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImages((prev) => [...prev, e.target!.result as string]);
        }
      };
      reader.readAsDataURL(file);
    });
  };
  
  // إزالة صورة
  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };
  
  return (
    <Card className="p-6">
      <h3 className="text-xl font-bold mb-4">{t('product.writeReview')}</h3>
      <p className="mb-6">
        {t('product.reviewingProduct')}: <span className="font-semibold">{productName}</span>
      </p>
      
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        {/* تقييم النجوم */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">{t('product.rating')} *</label>
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => setRating(star)}
                onMouseEnter={() => setHoverRating(star)}
                onMouseLeave={() => setHoverRating(0)}
                className="text-2xl mr-1 focus:outline-none"
              >
                <Star
                  size={32}
                  className={cn(
                    'transition-colors',
                    (hoverRating || rating) >= star
                      ? 'text-yellow-400 fill-yellow-400'
                      : 'text-gray-300'
                  )}
                />
              </button>
            ))}
            <span className="ml-2 text-sm">
              {rating > 0 ? `${rating}/5` : t('product.selectRating')}
            </span>
          </div>
          {rating === 0 && (
            <p className="text-red-500 text-sm mt-1">{t('product.ratingRequired')}</p>
          )}
        </div>
        
        {/* عنوان المراجعة */}
        <div className="mb-4">
          <label htmlFor="title" className="block mb-2 font-medium">
            {t('product.reviewTitle')} *
          </label>
          <Input
            id="title"
            placeholder={t('product.reviewTitlePlaceholder')}
            {...register('title')}
            error={errors.title?.message}
          />
        </div>
        
        {/* نص المراجعة */}
        <div className="mb-6">
          <label htmlFor="comment" className="block mb-2 font-medium">
            {t('product.reviewComment')} *
          </label>
          <textarea
            id="comment"
            rows={5}
            className={cn(
              'w-full px-3 py-2 border rounded-md',
              errors.comment ? 'border-red-500' : 'border-gray-300',
              'focus:outline-none focus:ring-2 focus:ring-primary-500'
            )}
            placeholder={t('product.reviewCommentPlaceholder')}
            {...register('comment')}
          />
          {errors.comment && (
            <p className="text-red-500 text-sm mt-1">{errors.comment.message}</p>
          )}
        </div>
        
        {/* تحميل الصور */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">{t('product.uploadImages')}</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {images.map((image, index) => (
              <div key={index} className="relative w-20 h-20">
                <img
                  src={image}
                  alt={`Uploaded ${index + 1}`}
                  className="w-full h-full object-cover rounded"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                >
                  <X size={14} />
                </button>
              </div>
            ))}
            
            {images.length < 5 && (
              <label className="w-20 h-20 border-2 border-dashed rounded flex items-center justify-center cursor-pointer">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={handleImageUpload}
                />
                <Upload size={24} className="text-gray-400" />
              </label>
            )}
          </div>
          <p className="text-sm text-gray-500">
            {t('product.maxImages')} (JPG, PNG)
          </p>
        </div>
        
        {/* أزرار الإرسال والإلغاء */}
        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || rating === 0}
            isLoading={isSubmitting}
          >
            {t('product.submitReview')}
          </Button>
        </div>
      </form>
    </Card>
  );
}
