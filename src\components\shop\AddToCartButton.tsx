'use client';

import { useState } from 'react';
import { ShoppingCart, CheckCircle } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { useCartStore } from '../../stores/cartStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useAuthModalStore } from '../../stores/authModalStore';
import { useTranslation } from '../../translations';
import { Product } from '../../types/index'; 
import { Button } from '../ui/Button';

interface AddToCartButtonProps {
  product: Product;
}

export const AddToCartButton = ({ product }: AddToCartButtonProps) => {
  const { user } = useAuthStore();
  const { addItem } = useCartStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const { openModal } = useAuthModalStore();
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = async () => {
    if (!user) {
      openModal('sign-in');
      return;
    }

    setIsAdding(true);
    addItem(product, 1); 

    setTimeout(() => {
      setIsAdding(false);
    }, 1000);
  };

  return (
    <Button
      className="w-full flex items-center justify-center"
      onClick={handleAddToCart}
      // Revert to using product.stock based on new lint errors
      disabled={isAdding || product.stock === 0}
    >
      {isAdding ? (
        <span className="flex items-center">
          <CheckCircle className={`w-4 h-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {currentLanguage === 'ar' ? 'تمت الإضافة!' : 'Added!'}
        </span>
      ) : (
        <span className="flex items-center">
          <ShoppingCart className={`w-4 h-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {t('products.addToCart')}
        </span>
      )}
    </Button>
  );
};
