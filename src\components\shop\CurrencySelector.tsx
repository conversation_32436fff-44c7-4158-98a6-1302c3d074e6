import { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Currency } from '../../types';
import { useCurrencyStore } from '../../stores/currencyStore';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';

// قائمة العملات المدعومة
const supportedCurrencies: Currency[] = [
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    rate: 1,
    default: true,
  },
  {
    code: 'SAR',
    name: 'Saudi Riyal',
    symbol: '﷼',
    rate: 3.75,
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    rate: 7.25,
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    rate: 0.93,
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    rate: 0.79,
  },
];

interface CurrencySelectorProps {
  className?: string;
  showName?: boolean;
  showSymbol?: boolean;
  variant?: 'dropdown' | 'buttons' | 'select';
}

export function CurrencySelector({
  className,
  showName = false,
  showSymbol = true,
  variant = 'dropdown',
}: CurrencySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { currency, setCurrency } = useCurrencyStore();
  const { isDarkMode } = useThemeStore();
  
  // الحصول على العملة الحالية
  const currentCurrency = supportedCurrencies.find(c => c.code === currency) || supportedCurrencies[0];
  
  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = () => {
      setIsOpen(false);
    };
    
    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isOpen]);
  
  // تغيير العملة
  const handleCurrencyChange = (code: string) => {
    setCurrency(code);
    setIsOpen(false);
  };
  
  // عرض القائمة المنسدلة
  if (variant === 'dropdown') {
    return (
      <div className={cn("relative", className)}>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className={cn(
            "flex items-center gap-1 px-2 py-1 rounded",
            isDarkMode ? "hover:bg-slate-800" : "hover:bg-slate-100"
          )}
        >
          {showSymbol && <span>{currentCurrency.symbol}</span>}
          <span>{currentCurrency.code}</span>
          {showName && <span className="hidden sm:inline">({currentCurrency.name})</span>}
          <ChevronDown size={16} className={cn(
            "transition-transform",
            isOpen ? "transform rotate-180" : ""
          )} />
        </button>
        
        {isOpen && (
          <div className={cn(
            "absolute right-0 mt-1 w-48 rounded-md shadow-lg z-10",
            isDarkMode ? "bg-slate-800 border border-slate-700" : "bg-white border border-slate-200"
          )}>
            <div className="py-1">
              {supportedCurrencies.map((curr) => (
                <button
                  key={curr.code}
                  onClick={() => handleCurrencyChange(curr.code)}
                  className={cn(
                    "flex items-center w-full px-4 py-2 text-sm",
                    currency === curr.code
                      ? isDarkMode
                        ? "bg-slate-700 text-white"
                        : "bg-slate-100 text-slate-900"
                      : isDarkMode
                        ? "text-slate-200 hover:bg-slate-700"
                        : "text-slate-700 hover:bg-slate-50"
                  )}
                >
                  <span className="mr-2">{curr.symbol}</span>
                  <span>{curr.code}</span>
                  {showName && <span className="ml-1 text-xs text-slate-500">({curr.name})</span>}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
  
  // عرض أزرار
  if (variant === 'buttons') {
    return (
      <div className={cn("flex gap-1", className)}>
        {supportedCurrencies.map((curr) => (
          <button
            key={curr.code}
            onClick={() => handleCurrencyChange(curr.code)}
            className={cn(
              "px-2 py-1 text-sm rounded",
              currency === curr.code
                ? isDarkMode
                  ? "bg-primary-600 text-white"
                  : "bg-primary-500 text-white"
                : isDarkMode
                  ? "bg-slate-800 text-slate-200 hover:bg-slate-700"
                  : "bg-slate-100 text-slate-700 hover:bg-slate-200"
            )}
          >
            {showSymbol && <span className="mr-1">{curr.symbol}</span>}
            <span>{curr.code}</span>
          </button>
        ))}
      </div>
    );
  }
  
  // عرض قائمة منسدلة
  return (
    <select
      value={currency}
      onChange={(e) => setCurrency(e.target.value)}
      className={cn(
        "px-2 py-1 rounded text-sm",
        isDarkMode
          ? "bg-slate-800 border-slate-700 text-slate-200"
          : "bg-white border-slate-300 text-slate-700",
        className
      )}
    >
      {supportedCurrencies.map((curr) => (
        <option key={curr.code} value={curr.code}>
          {showSymbol && `${curr.symbol} `}
          {curr.code}
          {showName && ` (${curr.name})`}
        </option>
      ))}
    </select>
  );
}
