'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Star, ShoppingCart, Heart, ArrowRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { EnhancedImage } from '../ui/EnhancedImage';
import { Badge } from '../ui/Badge';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { formatCurrency, cn } from '../../lib/utils';
import { Product } from '../../types';
import { HoverAnimation } from '../ui/animations/HoverAnimation';

interface FeaturedProductProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
  onToggleWishlist?: (product: Product) => void;
}

export function FeaturedProduct({
  product,
  onAddToCart,
  onToggleWishlist
}: FeaturedProductProps) {
  const { t, locale } = useTranslation();
  const { language } = useLanguageStore();
  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const [imageError, setImageError] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [showAddedToCart, setShowAddedToCart] = useState(false);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isInStock) return;
    
    setIsAddingToCart(true);

    // محاكاة تأخير الإضافة إلى السلة
    setTimeout(() => {
      // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
      if (onAddToCart) {
        onAddToCart(product);
      } else {
        cartStore.addItem(product, 1);
      }
      
      setIsAddingToCart(false);
      setShowAddedToCart(true);
      
      // إخفاء رسالة "تمت الإضافة" بعد 2 ثوانٍ
      setTimeout(() => {
        setShowAddedToCart(false);
      }, 2000);
    }, 500);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onToggleWishlist) {
      onToggleWishlist(product);
    } else {
      if (wishlistStore.isInWishlist(product.id)) {
        wishlistStore.removeItem(product.id);
      } else {
        wishlistStore.addItem(product);
      }
    }
  };

  // Fallback image if the product image fails to load
  const fallbackImage = `/images/product-placeholder-light.svg`;

  // Use the first product image or fallback if there are no images
  const productImage = imageError || !product.images || product.images.length === 0
    ? fallbackImage
    : product.images[0];

  // تحديد ما إذا كان المنتج في المخزون
  const isInStock = product.stock > 0;

  // حساب نسبة الخصم إذا كان هناك سعر مقارنة
  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price
    ? Math.round((1 - product.price / product.compareAtPrice) * 100)
    : 0;

  // تحديد ما إذا كان المنتج في السلة
  const isInCart = cartStore.isProductInCart(product.id);

  // تحديد ما إذا كان المنتج في المفضلة
  const isInWishlist = wishlistStore.isInWishlist(product.id);

  return (
    <Card className="overflow-hidden border border-primary-100 dark:border-primary-800">
      {/* عنوان القسم */}
      <div className="bg-primary-50 dark:bg-primary-900/30 p-3 border-b border-primary-100 dark:border-primary-800">
        <h3 className="font-semibold text-primary-800 dark:text-primary-300">
          {currentLanguage === 'ar' ? 'منتج مميز' : 'Featured Product'}
        </h3>
      </div>

      {/* صورة المنتج */}
      <div className="relative p-4">
        <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
          <div className="relative aspect-square overflow-hidden rounded-lg mb-4">
            <EnhancedImage
              src={productImage}
              alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
              fill={true}
              objectFit="cover"
              progressive={true}
              placeholder="shimmer"
              className="transition-transform duration-500 hover:scale-105"
              onError={() => setImageError(true)}
            />
            
            {/* شارات المنتج */}
            {discountPercentage > 0 && (
              <Badge variant="error" className="absolute top-2 left-2">
                {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}
              </Badge>
            )}
          </div>
        </Link>

        {/* معلومات المنتج */}
        <div className="space-y-2">
          {/* التقييم */}
          <div className="flex items-center text-sm text-yellow-500 mb-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={cn(
                  "h-4 w-4",
                  i < Math.floor(product.rating || 0) ? "fill-current" : "text-slate-300 dark:text-slate-600"
                )}
              />
            ))}
            <span className="ml-1 text-slate-600 dark:text-slate-300 text-xs">
              ({product.reviewCount || 0})
            </span>
          </div>

          {/* اسم المنتج */}
          <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
            <h4 className="font-semibold text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
              {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
            </h4>
          </Link>

          {/* السعر */}
          <div className="flex items-baseline gap-2">
            <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
              {formatCurrency(product.price)}
            </span>
            {product.compareAtPrice && product.compareAtPrice > product.price && (
              <span className="text-sm text-slate-500 line-through">
                {formatCurrency(product.compareAtPrice)}
              </span>
            )}
          </div>

          {/* وصف مختصر */}
          <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-2">
            {currentLanguage === 'ar'
              ? (product.description_ar || product.description)
              : product.description}
          </p>

          {/* أزرار الإجراءات */}
          <div className="flex gap-2 pt-2">
            <Button
              variant={isInCart || showAddedToCart ? "success" : "primary"}
              size="sm"
              className="flex-1"
              onClick={handleAddToCart}
              disabled={!isInStock || isAddingToCart || isInCart}
            >
              {isAddingToCart ? (
                <span className="flex items-center">
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                  {currentLanguage === 'ar' ? 'جاري...' : 'Adding...'}
                </span>
              ) : isInCart || showAddedToCart ? (
                <span className="flex items-center">
                  <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {currentLanguage === 'ar' ? 'في السلة' : 'In Cart'}
                </span>
              ) : (
                <span className="flex items-center">
                  <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                </span>
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              className={cn(
                "p-2",
                isInWishlist && "text-primary-600 border-primary-600 dark:text-primary-400 dark:border-primary-400"
              )}
              onClick={handleToggleWishlist}
              aria-label={isInWishlist 
                ? (currentLanguage === 'ar' ? 'إزالة من المفضلة' : 'Remove from Wishlist')
                : (currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to Wishlist')
              }
            >
              <Heart
                className={cn(
                  "h-4 w-4",
                  isInWishlist && "fill-current"
                )}
              />
            </Button>
          </div>
        </div>
      </div>

      {/* رابط عرض جميع المنتجات المميزة */}
      <div className="p-3 bg-primary-50/50 dark:bg-primary-900/20 border-t border-primary-100 dark:border-primary-800">
        <Link 
          href={`/${currentLanguage}/shop?featured=true`}
          className="text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center justify-center"
        >
          {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
          <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
        </Link>
      </div>
    </Card>
  );
}
