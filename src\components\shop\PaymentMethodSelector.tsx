import { useState } from 'react';
import { Check, CreditCard, AlertCircle } from 'lucide-react';
import { PaymentMethod } from '../../types';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';
import { useTranslation } from '../../translations';
import { useCurrencyStore } from '../../stores/currencyStore';

// طرق الدفع المدعومة
const paymentMethods: PaymentMethod[] = [
  {
    id: 'credit_card',
    name: 'Credit Card',
    type: 'credit_card',
    icon: '/images/payment/credit-card.svg',
    supportedCurrencies: ['USD', 'EUR', 'GBP', 'SAR', 'CNY'],
    processingFee: 2.9,
    processingFeeType: 'percentage',
    enabled: true,
  },
  {
    id: 'paypal',
    name: 'PayPal',
    type: 'paypal',
    icon: '/images/payment/paypal.svg',
    supportedCurrencies: ['USD', 'EUR', 'GBP'],
    processingFee: 3.5,
    processingFeeType: 'percentage',
    enabled: true,
  },
  {
    id: 'stripe',
    name: 'Stripe',
    type: 'stripe',
    icon: '/images/payment/stripe.svg',
    supportedCurrencies: ['USD', 'EUR', 'GBP', 'SAR'],
    processingFee: 2.9,
    processingFeeType: 'percentage',
    enabled: true,
  },
  {
    id: 'mada',
    name: 'Mada',
    type: 'mada',
    icon: '/images/payment/mada.svg',
    supportedCurrencies: ['SAR'],
    processingFee: 1.5,
    processingFeeType: 'percentage',
    enabled: true,
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    type: 'bank_transfer',
    icon: '/images/payment/bank.svg',
    supportedCurrencies: ['USD', 'EUR', 'GBP', 'SAR', 'CNY'],
    enabled: true,
  },
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    type: 'cash_on_delivery',
    icon: '/images/payment/cash.svg',
    supportedCurrencies: ['USD', 'SAR', 'CNY'],
    processingFee: 5,
    processingFeeType: 'fixed',
    enabled: true,
  },
];

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onSelect: (methodId: string) => void;
  className?: string;
}

export function PaymentMethodSelector({
  selectedMethod,
  onSelect,
  className,
}: PaymentMethodSelectorProps) {
  const { isDarkMode } = useThemeStore();
  const { t } = useTranslation();
  const { currency } = useCurrencyStore();
  
  // تصفية طرق الدفع حسب العملة المحددة
  const filteredMethods = paymentMethods.filter(
    method => method.enabled && method.supportedCurrencies.includes(currency)
  );
  
  // التحقق من وجود طريقة الدفع المحددة في القائمة المصفاة
  const isSelectedMethodAvailable = filteredMethods.some(method => method.id === selectedMethod);
  
  // إذا كانت طريقة الدفع المحددة غير متوفرة، اختر الطريقة الأولى
  if (!isSelectedMethodAvailable && filteredMethods.length > 0) {
    onSelect(filteredMethods[0].id);
  }
  
  // حساب رسوم المعالجة
  const getProcessingFee = (method: PaymentMethod, amount: number = 100): number => {
    if (!method.processingFee) return 0;
    
    if (method.processingFeeType === 'percentage') {
      return (amount * method.processingFee) / 100;
    }
    
    return method.processingFee;
  };
  
  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-4">{t('checkout.paymentMethod')}</h3>
      
      {filteredMethods.length === 0 ? (
        <div className={cn(
          "p-4 rounded-md flex items-center",
          isDarkMode ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
        )}>
          <AlertCircle className="mr-2 h-5 w-5" />
          <p>{t('checkout.noPaymentMethodsAvailable')}</p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredMethods.map((method) => (
            <div
              key={method.id}
              className={cn(
                "relative p-4 rounded-md border cursor-pointer transition-colors",
                selectedMethod === method.id
                  ? isDarkMode
                    ? "border-primary-500 bg-primary-900/20"
                    : "border-primary-500 bg-primary-50"
                  : isDarkMode
                    ? "border-slate-700 hover:border-slate-600"
                    : "border-slate-200 hover:border-slate-300",
              )}
              onClick={() => onSelect(method.id)}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 mr-4 w-12 h-12 flex items-center justify-center">
                  {/* Fallback icon if image is not available */}
                  <CreditCard className={cn(
                    "h-8 w-8",
                    isDarkMode ? "text-slate-400" : "text-slate-600"
                  )} />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium">{method.name}</h4>
                  
                  {method.processingFee ? (
                    <p className="text-sm text-slate-500">
                      {method.processingFeeType === 'percentage'
                        ? `${t('checkout.processingFee')}: ${method.processingFee}%`
                        : `${t('checkout.processingFee')}: $${method.processingFee.toFixed(2)}`}
                    </p>
                  ) : (
                    <p className="text-sm text-slate-500">
                      {t('checkout.noProcessingFee')}
                    </p>
                  )}
                </div>
                
                {selectedMethod === method.id && (
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center",
                    isDarkMode ? "bg-primary-500" : "bg-primary-500"
                  )}>
                    <Check className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
