import { Filter, Search, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { HoverAnimation } from '../ui/animations';
import { formatCurrency, cn } from '../../lib/utils';
import { ProductCategory, ProductFiltersState } from '../../types/index';

interface ProductFiltersProps {
  filters: ProductFiltersState;
  setFilters: React.Dispatch<React.SetStateAction<ProductFiltersState>>;
  resetFilters: () => void;
  maxPrice: number;
  productCategories: ProductCategory[];
  t: (key: string) => string; // Translation function
  currentLanguage: 'en' | 'ar';
  showMobileFilters: boolean;
  setShowMobileFilters: (show: boolean) => void;
}

export function ProductFilters({
  filters,
  setFilters,
  resetFilters,
  maxPrice,
  productCategories,
  t,
  currentLanguage,
  showMobileFilters,
  setShowMobileFilters,
}: ProductFiltersProps) {
  return (
    <div className="mb-8 p-6 bg-white dark:bg-slate-800 rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
          {t('shop.filters.title')}
        </h2>
        <Button variant="ghost" onClick={() => setShowMobileFilters(!showMobileFilters)} className="md:hidden">
          <Filter className="h-5 w-5 mr-2" /> {showMobileFilters ? t('shop.filters.hide') : t('shop.filters.show')}
        </Button>
      </div>
      <div className={`${showMobileFilters ? 'block' : 'hidden'} md:grid md:grid-cols-2 lg:grid-cols-4 gap-6`}>
        {/* Category Filter */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            {t('shop.filters.category')}
          </label>
          <select
            id="category"
            value={filters.category}
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            className="w-full p-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100"
          >
            <option value="all">{t('shop.filters.allCategories')}</option>
            {productCategories.map(category => (
              <option key={category.id} value={category.id}>{category.name[currentLanguage]}</option>
            ))}
          </select>
        </div>

        {/* Price Range Filter */}
        <div>
          <label htmlFor="priceRangeMax" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            {t('shop.filters.priceRange')} ({formatCurrency(filters.priceRange.min)} - {formatCurrency(filters.priceRange.max)})
          </label>
          <input
            type="range"
            id="priceRangeMax"
            min="0"
            max={maxPrice}
            value={filters.priceRange.max}
            onChange={(e) => setFilters({ ...filters, priceRange: { ...filters.priceRange, max: parseInt(e.target.value) } })}
            className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400"
          />
          <input
            type="range"
            id="priceRangeMin"
            min="0"
            max={maxPrice}
            value={filters.priceRange.min}
            onChange={(e) => setFilters({ ...filters, priceRange: { ...filters.priceRange, min: parseInt(e.target.value) } })}
            className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400 mt-2"
          />
        </div>

        {/* Stock & Sale Filters */}
        <div className="space-y-2">
          <div className="flex items-center">
            <input
              id="inStock"
              type="checkbox"
              checked={filters.inStock}
              onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}
              className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 bg-white dark:bg-slate-700"
            />
            <label htmlFor="inStock" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
              {t('shop.filters.inStockOnly')}
            </label>
          </div>
          <div className="flex items-center">
            <input
              id="onSale"
              type="checkbox"
              checked={filters.onSale}
              onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}
              className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 bg-white dark:bg-slate-700"
            />
            <label htmlFor="onSale" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
              {t('shop.filters.onSaleOnly')}
            </label>
          </div>
        </div>

        {/* Search Input */}
        <div className="relative">
          <label htmlFor="search" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            {t('shop.filters.search')}
          </label>
          <Input
            id="search"
            type="text"
            placeholder={t('shop.filters.searchPlaceholder')}
            value={filters.searchQuery}
            onChange={(e) => setFilters({ ...filters, searchQuery: e.target.value })}
            className="pl-10 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 border-slate-300 dark:border-slate-600"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400 dark:text-slate-500 mt-3" />
          {filters.searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setFilters({ ...filters, searchQuery: '' })}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 mt-3 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300"
            >
              <X size={18} />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-6 flex justify-end">
        <HoverAnimation animation="scale">
          <Button variant="outline" onClick={resetFilters} className="text-slate-700 dark:text-slate-300 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700">
            {t('shop.filters.reset')}
          </Button>
        </HoverAnimation>
      </div>
    </div>
  );
}
