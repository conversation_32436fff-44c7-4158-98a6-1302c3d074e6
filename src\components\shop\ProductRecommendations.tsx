'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRight, ShoppingCart, Heart } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { LazyImage } from '../ui/LazyImage';
import { Product } from '../../types';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { formatCurrency } from '../../lib/utils';
import { useTranslation } from '../../translations';
import { SmoothTransition } from '../ui/animations/SmoothTransition';

interface ProductRecommendationsProps {
  currentProductId: string;
  products: Product[];
  type?: 'similar' | 'complementary' | 'frequently-bought-together' | 'recently-viewed';
  title?: string;
  maxItems?: number;
}

export function ProductRecommendations({
  currentProductId,
  products,
  type = 'similar',
  title,
  maxItems = 4,
}: ProductRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Product[]>([]);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { t } = useTranslation();

  // تحديد العنوان بناءً على النوع
  const getTitle = () => {
    if (title) return title;

    switch (type) {
      case 'similar':
        return t('product.similarProducts');
      case 'complementary':
        return t('product.complementaryProducts');
      case 'frequently-bought-together':
        return t('product.frequentlyBoughtTogether');
      case 'recently-viewed':
        return t('product.recentlyViewed');
      default:
        return t('product.recommendedProducts');
    }
  };

  // الحصول على التوصيات بناءً على النوع
  useEffect(() => {
    // استبعاد المنتج الحالي
    const filteredProducts = products.filter(p => p.id !== currentProductId);

    switch (type) {
      case 'similar':
        // البحث عن المنتجات المشابهة (نفس الفئة أو العلامات)
        const currentProduct = products.find(p => p.id === currentProductId);
        if (currentProduct) {
          const similarProducts = filteredProducts
            .filter(p =>
              p.category === currentProduct.category ||
              p.tags.some(tag => currentProduct.tags.includes(tag))
            )
            .slice(0, maxItems);
          setRecommendations(similarProducts);
        }
        break;

      case 'complementary':
        // في الحالة الحقيقية، ستكون هناك علاقة بين المنتجات المكملة
        // هنا نستخدم منتجات عشوائية للتوضيح
        const randomProducts = [...filteredProducts]
          .sort(() => 0.5 - Math.random())
          .slice(0, maxItems);
        setRecommendations(randomProducts);
        break;

      case 'frequently-bought-together':
        // في الحالة الحقيقية، ستكون هناك بيانات عن المنتجات التي تُشترى معًا
        // هنا نستخدم منتجات عشوائية للتوضيح
        const randomFrequent = [...filteredProducts]
          .sort(() => 0.5 - Math.random())
          .slice(0, maxItems);
        setRecommendations(randomFrequent);
        break;

      case 'recently-viewed':
        // في الحالة الحقيقية، ستكون هناك سجل للمنتجات التي تمت مشاهدتها مؤخرًا
        // هنا نستخدم منتجات عشوائية للتوضيح
        const recentlyViewed = [...filteredProducts]
          .sort(() => 0.5 - Math.random())
          .slice(0, maxItems);
        setRecommendations(recentlyViewed);
        break;

      default:
        setRecommendations(filteredProducts.slice(0, maxItems));
    }
  }, [currentProductId, products, type, maxItems]);

  // إضافة منتج إلى السلة
  const addToCart = (product: Product, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    cartStore.addItem(product, 1);
  };

  // إضافة منتج إلى المفضلة
  const toggleWishlist = (product: Product, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (wishlistStore.isInWishlist(product.id)) {
      wishlistStore.removeItem(product.id);
    } else {
      wishlistStore.addItem(product);
    }
  };

  if (recommendations.length === 0) {
    return null;
  }

  return (
    <div className="mt-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold">{getTitle()}</h2>
        <Link href="/shop" className="text-primary-500 hover:underline flex items-center">
          {t('common.viewAll')}
          <ArrowRight size={16} className="ml-1" />
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product, index) => (
          <SmoothTransition
            key={product.id}
            type="fade"
            delay={index * 0.1}
          >
            <Card className="group h-full flex flex-col">
              <Link href={`/shop/product/${product.slug}`} className="block">
                <div className="relative aspect-square overflow-hidden">
                  <LazyImage
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />

                  {/* أزرار السلة والمفضلة */}
                  <div className="absolute top-2 right-2 flex flex-col gap-2">
                    <button
                      onClick={(e) => toggleWishlist(product, e)}
                      className="p-2 rounded-full bg-white/80 hover:bg-white text-gray-700 hover:text-primary-500 transition-colors"
                    >
                      <Heart
                        size={18}
                        className={wishlistStore.isInWishlist(product.id) ? 'fill-primary-500 text-primary-500' : ''}
                      />
                    </button>
                    <button
                      onClick={(e) => addToCart(product, e)}
                      className="p-2 rounded-full bg-white/80 hover:bg-white text-gray-700 hover:text-primary-500 transition-colors"
                    >
                      <ShoppingCart size={18} />
                    </button>
                  </div>

                  {/* شارة الخصم */}
                  {product.compareAtPrice && (
                    <div className="absolute top-2 left-2 bg-accent-500 text-white text-xs font-bold px-2 py-1 rounded">
                      {Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)}% {t('product.off')}
                    </div>
                  )}
                </div>

                <div className="p-4 flex-1 flex flex-col">
                  <h3 className="font-semibold mb-1 line-clamp-2">{product.name}</h3>

                  <div className="mt-auto pt-2">
                    <div className="flex items-center justify-between">
                      <div>
                        {product.compareAtPrice ? (
                          <div className="flex items-center gap-2">
                            <span className="font-bold">{formatCurrency(product.price)}</span>
                            <span className="text-sm text-gray-500 line-through">
                              {formatCurrency(product.compareAtPrice)}
                            </span>
                          </div>
                        ) : (
                          <span className="font-bold">{formatCurrency(product.price)}</span>
                        )}
                      </div>

                      {product.rating && (
                        <div className="flex items-center">
                          <span className="text-yellow-500">★</span>
                          <span className="text-sm ml-1">{product.rating.toFixed(1)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            </Card>
          </SmoothTransition>
        ))}
      </div>
    </div>
  );
}
