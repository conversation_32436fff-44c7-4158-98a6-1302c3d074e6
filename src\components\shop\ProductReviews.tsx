import { useState } from 'react';
import { Star, ThumbsUp, Check, Image as ImageIcon, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { LazyImage } from '../ui/LazyImage';
import { Review } from '../../types/index';
import { useAuthStore } from '../../stores/authStore';
import { cn } from '../../lib/utils';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';

interface ProductReviewsProps {
  productId: string;
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
  onAddReview?: () => void;
}

export function ProductReviews({
  productId,
  reviews,
  averageRating,
  totalReviews,
  onAddReview,
}: ProductReviewsProps) {
  const [activeFilter, setActiveFilter] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<'recent' | 'helpful'>('recent');
  const [expandedReview, setExpandedReview] = useState<string | null>(null);
  const [expandedImage, setExpandedImage] = useState<string | null>(null);
  
  const { user } = useAuthStore();
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  const { language } = useLanguageStore();
  const { t } = useTranslation();
  
  // تصفية المراجعات حسب التقييم
  const filteredReviews = activeFilter
    ? reviews.filter((review) => review.rating === activeFilter)
    : reviews;
  
  // ترتيب المراجعات
  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (sortBy === 'recent') {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    return b.helpful - a.helpful;
  });
  
  // حساب عدد المراجعات لكل تقييم
  const ratingCounts = [5, 4, 3, 2, 1].map((rating) => {
    const count = reviews.filter((review) => review.rating === rating).length;
    const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
    return { rating, count, percentage };
  });
  
  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  // عرض النجوم
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={16}
            className={cn(
              star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300',
              'mr-0.5'
            )}
          />
        ))}
      </div>
    );
  };
  
  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold mb-6">{t('product.reviews')} ({totalReviews})</h2>
      
      {/* ملخص التقييمات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div className="md:col-span-1">
          <Card className="p-6">
            <div className="text-center">
              <div className="text-5xl font-bold mb-2">{averageRating.toFixed(1)}</div>
              <div className="flex justify-center mb-2">
                {renderStars(Math.round(averageRating))}
              </div>
              <div className="text-sm text-gray-500 mb-4">
                {totalReviews} {t('product.reviews')}
              </div>
              
              {user ? (
                <Button
                  onClick={onAddReview}
                  className="w-full"
                >
                  {t('product.writeReview')}
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {/* تنفيذ تسجيل الدخول */}}
                >
                  {t('auth.signInToReview')}
                </Button>
              )}
            </div>
          </Card>
        </div>
        
        <div className="md:col-span-2">
          <Card className="p-6">
            <div className="space-y-3">
              {ratingCounts.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center">
                  <div className="w-12 text-sm">{rating} {t('product.stars')}</div>
                  <div className="flex-1 mx-3">
                    <div className="h-2 rounded-full bg-gray-200 overflow-hidden">
                      <div
                        className="h-full bg-yellow-400"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                  <div className="w-10 text-sm text-right">{count}</div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
      
      {/* أدوات التصفية والترتيب */}
      <div className="flex flex-wrap justify-between items-center mb-6">
        <div className="flex flex-wrap gap-2 mb-4 md:mb-0">
          <Button
            variant={activeFilter === null ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setActiveFilter(null)}
          >
            {t('product.allReviews')}
          </Button>
          {[5, 4, 3, 2, 1].map((rating) => (
            <Button
              key={rating}
              variant={activeFilter === rating ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setActiveFilter(rating)}
            >
              {rating} {t('product.stars')}
            </Button>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm">{t('product.sortBy')}:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'recent' | 'helpful')}
            className={cn(
              'rounded border p-1 text-sm',
              isDarkMode ? 'bg-slate-800 border-slate-700' : 'bg-white border-slate-300'
            )}
          >
            <option value="recent">{t('product.mostRecent')}</option>
            <option value="helpful">{t('product.mostHelpful')}</option>
          </select>
        </div>
      </div>
      
      {/* قائمة المراجعات */}
      {sortedReviews.length > 0 ? (
        <div className="space-y-6">
          {sortedReviews.map((review) => (
            <Card key={review.id} className="p-6">
              <div className="flex items-start">
                {review.userAvatar ? (
                  <LazyImage
                    src={review.userAvatar}
                    alt={review.userName}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                ) : (
                  <div className={cn(
                    'w-12 h-12 rounded-full mr-4 flex items-center justify-center text-lg font-semibold',
                    isDarkMode ? 'bg-slate-700' : 'bg-slate-200'
                  )}>
                    {review.userName.charAt(0).toUpperCase()}
                  </div>
                )}
                
                <div className="flex-1">
                  <div className="flex flex-wrap items-center gap-2 mb-1">
                    <span className="font-semibold">{review.userName}</span>
                    {review.verified && (
                      <span className="inline-flex items-center text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                        <Check size={12} className="mr-1" />
                        {t('product.verifiedPurchase')}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center mb-2">
                    {renderStars(review.rating)}
                    <span className="mx-2 text-sm text-gray-500">
                      {formatDate(review.createdAt)}
                    </span>
                  </div>
                  
                  <h4 className="font-semibold mb-2">{review.title}</h4>
                  
                  <div className="mb-4">
                    {review.comment.length > 300 && expandedReview !== review.id ? (
                      <>
                        <p>{review.comment.substring(0, 300)}...</p>
                        <button
                          onClick={() => setExpandedReview(review.id)}
                          className="text-primary-500 hover:underline mt-1 text-sm"
                        >
                          {t('product.readMore')}
                        </button>
                      </>
                    ) : (
                      <p>{review.comment}</p>
                    )}
                    
                    {expandedReview === review.id && (
                      <button
                        onClick={() => setExpandedReview(null)}
                        className="text-primary-500 hover:underline mt-1 text-sm"
                      >
                        {t('product.showLess')}
                      </button>
                    )}
                  </div>
                  
                  {/* صور المراجعة */}
                  {review.images && review.images.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {review.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setExpandedImage(image)}
                          className="relative w-16 h-16 overflow-hidden rounded border"
                        >
                          <LazyImage
                            src={image}
                            alt={`Review image ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                  
                  <div className="flex items-center text-sm">
                    <button
                      className="inline-flex items-center text-gray-500 hover:text-primary-500"
                      onClick={() => {/* تنفيذ زيادة عدد المفيد */}}
                    >
                      <ThumbsUp size={14} className="mr-1" />
                      {review.helpful > 0 ? `${review.helpful} ${t('product.foundHelpful')}` : t('product.helpful')}
                    </button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-6 text-center">
          <p>{t('product.noReviews')}</p>
        </Card>
      )}
      
      {/* عرض الصورة المكبرة */}
      {expandedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
          <button
            onClick={() => setExpandedImage(null)}
            className="absolute top-4 right-4 text-white"
          >
            <X size={24} />
          </button>
          <img
            src={expandedImage}
            alt="Review image"
            className="max-w-full max-h-[90vh] object-contain"
          />
        </div>
      )}
    </div>
  );
}
