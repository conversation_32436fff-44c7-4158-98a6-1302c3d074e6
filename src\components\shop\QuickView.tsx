'use client';

import { useState, useEffect } from 'react';
import { X, ShoppingCart, Heart, Star, ArrowRight, ChevronRight, ChevronLeft, Check, Info, Share2 } from 'lucide-react';
import Link from 'next/link';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { AuthModal } from '../auth/AuthModal';
import { Product } from '../../types/index';
import { useTranslation } from '../../translations';
import { EnhancedImage } from '../ui/EnhancedImage';
import { Badge } from '../ui/Badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '../ui/Dialog';

interface QuickViewProps {
  product: Product | null;
  onClose: () => void;
  onAddToCart?: (product: Product) => void;
  onToggleWishlist?: (product: Product) => void;
}

export function QuickView({ product, onClose, onAddToCart, onToggleWishlist: onToggleWishlistProp }: QuickViewProps) {
  const isOpen = !!product; // إذا كان هناك منتج، فإن النافذة مفتوحة
  const { t, currentLanguage } = useTranslation();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();

  // إعادة تعيين الحالة عند تغيير المنتج
  useEffect(() => {
    if (isOpen) {
      setCurrentImageIndex(0);
      setQuantity(1);
      setSelectedColor(null);
      setSelectedSize(null);
      setImageError(false);
      setIsAdding(false);
    }
  }, [isOpen, product?.id]);

  // إذا لم يكن هناك منتج، لا تعرض شيئًا
  if (!product) return null;

  // التحقق من وجود صور للمنتج
  const hasImages = product.images && product.images.length > 0;

  // الصورة الحالية أو الصورة الاحتياطية
  const currentImage = imageError || !hasImages
    ? `/images/product-placeholder-light.svg`
    : product.images[currentImageIndex];

  // زيادة الكمية
  const incrementQuantity = () => {
    if (quantity < (product.stock || 10)) {
      setQuantity(quantity + 1);
    }
  };

  // إنقاص الكمية
  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  // إضافة المنتج إلى سلة التسوق
  const handleAddToCart = () => {
    if (!product) return;

    // التحقق من توفر المنتج في المخزون
    if (product.stock <= 0) {
      console.warn(`المنتج ${product.name} غير متوفر في المخزون`);
      return;
    }

    // يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول
    setIsAdding(true);

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onAddToCart) {
      onAddToCart(product);

      // إظهار تأثير الإضافة وإغلاق النافذة
      setTimeout(() => {
        setIsAdding(false);
        // إغلاق النافذة المنبثقة بعد الإضافة
        setTimeout(() => {
          onClose();
          setQuantity(1); // إعادة تعيين الكمية
        }, 500);
      }, 1000);
    } else {
      // تجهيز بيانات المنتج للإضافة إلى السلة
      const cartItem = {
        ...product,
        quantity: quantity,
        color: selectedColor,
        size: selectedSize,
      };

      // إضافة المنتج إلى السلة
      cartStore.addItem(cartItem, quantity);

      // إظهار تأثير الإضافة وإغلاق النافذة
      setTimeout(() => {
        setIsAdding(false);
        // إغلاق النافذة المنبثقة بعد الإضافة
        setTimeout(() => {
          onClose();
          setQuantity(1); // إعادة تعيين الكمية
        }, 500);
      }, 1000);
    }

    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('add_to_cart_from_quickview', { product_id: product.id, product_name: product.name, quantity });
  };

  // إضافة المنتج إلى المفضلة أو إزالته منها
  const toggleWishlist = () => {
    if (!product) return;

    // التحقق من تسجيل الدخول قبل إضافة المنتج إلى المفضلة
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي
    if (onToggleWishlistProp) {
      onToggleWishlistProp(product);
    } else {
      const wishlist = wishlistStore;

      // التحقق مما إذا كان المنتج موجوداً بالفعل في المفضلة
      if (wishlist.isInWishlist(product.id)) {
        // إزالة المنتج من المفضلة
        wishlist.removeItem(product.id);
        console.log(`تمت إزالة ${product.name} من المفضلة`);
      } else {
        // إضافة المنتج إلى المفضلة
        wishlist.addItem(product);
        console.log(`تمت إضافة ${product.name} إلى المفضلة`);
      }
    }

    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('toggle_wishlist_from_quickview', { product_id: product.id, product_name: product.name });
  };

  // التنقل بين صور المنتج
  const navigateImages = (direction: 'next' | 'prev') => {
    if (!hasImages) return;

    if (direction === 'next') {
      setCurrentImageIndex((prev) => (prev + 1) % product.images.length);
    } else {
      setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);
    }
  };

  // تحديد ما إذا كان المنتج في المخزون
  const isInStock = product.stock > 0;

  // الألوان المتاحة (يمكن تحديثها لاحقًا لتكون ديناميكية)
  const availableColors = product.colors || ['#000000', '#FFFFFF', '#0066CC', '#FF4500'];

  // الأحجام المتاحة (يمكن تحديثها لاحقًا لتكون ديناميكية)
  const availableSizes = product.sizes || ['S', 'M', 'L', 'XL'];

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="flex justify-between items-center">
            <DialogTitle className="text-xl font-bold text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
            </DialogTitle>
            <DialogClose className="absolute right-4 top-4">
              <X className="h-5 w-5 text-slate-500 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white transition-colors" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Product Images */}
            <div className="relative">
              <div className="relative aspect-square rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800">
                <EnhancedImage
                  src={currentImage}
                  alt={product.name}
                  fill={true}
                  objectFit="contain"
                  progressive={true}
                  placeholder="shimmer"
                  className="object-center"
                  onError={() => setImageError(true)}
                />

                {/* Image navigation buttons */}
                {hasImages && product.images.length > 1 && (
                  <>
                    <button
                      onClick={() => navigateImages('prev')}
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 rounded-full p-1.5 shadow-md hover:bg-white dark:hover:bg-slate-700 transition-colors"
                      aria-label="Previous image"
                    >
                      <ChevronLeft className="h-5 w-5 text-slate-700 dark:text-slate-200" />
                    </button>
                    <button
                      onClick={() => navigateImages('next')}
                      className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 rounded-full p-1.5 shadow-md hover:bg-white dark:hover:bg-slate-700 transition-colors"
                      aria-label="Next image"
                    >
                      <ChevronRight className="h-5 w-5 text-slate-700 dark:text-slate-200" />
                    </button>
                  </>
                )}

                {/* Product badges */}
                <div className="absolute top-2 left-2 flex flex-col gap-1">
                  {product.isNew && (
                    <Badge variant="primary" className="text-xs">
                      {currentLanguage === 'ar' ? 'جديد' : 'New'}
                    </Badge>
                  )}
                  {product.discount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {currentLanguage === 'ar' ? `${product.discount}% خصم` : `${product.discount}% OFF`}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Thumbnail images */}
              {hasImages && product.images.length > 1 && (
                <div className="flex mt-4 gap-2 overflow-x-auto pb-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={cn(
                        "relative w-16 h-16 rounded-md overflow-hidden border-2 transition-all",
                        index === currentImageIndex
                          ? "border-primary-500 shadow-md"
                          : "border-transparent hover:border-slate-300 dark:hover:border-slate-600"
                      )}
                    >
                      <EnhancedImage
                        src={image}
                        alt={`${product.name} - Image ${index + 1}`}
                        fill={true}
                        objectFit="cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="flex flex-col">
              {/* Category & Rating */}
              <div className="flex items-center justify-between mb-4">
                {product.category && (
                  <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                    {product.category}
                  </span>
                )}

                <div className="flex items-center">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={cn(
                          "h-4 w-4",
                          star <= (product.rating || 0)
                            ? "text-yellow-400 fill-yellow-400"
                            : "text-slate-300 dark:text-slate-600"
                        )}
                      />
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                    {product.rating?.toFixed(1) || '0.0'} ({product.reviewCount || 0} {t('shop.reviews')})
                  </span>
                </div>
              </div>

              {/* Price */}
              <div className="flex items-baseline mb-4">
                <span className="text-2xl font-bold text-slate-900 dark:text-white">
                  {formatCurrency(product.price)}
                </span>
                {product.compareAtPrice && product.compareAtPrice > product.price && (
                  <span className="ml-2 text-sm text-slate-500 line-through">
                    {formatCurrency(product.compareAtPrice)}
                  </span>
                )}
              </div>

              {/* Description */}
              <p className="text-slate-600 dark:text-slate-300 mb-6 text-sm">
                {currentLanguage === 'ar'
                  ? (product.description_ar || product.description)
                  : product.description}
              </p>

              {/* Color selection */}
              {availableColors.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                    {currentLanguage === 'ar' ? 'اللون' : 'Color'}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {availableColors.map((color) => (
                      <button
                        key={color}
                        onClick={() => setSelectedColor(color)}
                        className={cn(
                          "w-8 h-8 rounded-full border-2 transition-all flex items-center justify-center",
                          selectedColor === color
                            ? "border-primary-500 shadow-sm"
                            : "border-slate-200 dark:border-slate-700"
                        )}
                        style={{ backgroundColor: color }}
                        aria-label={`Select color: ${color}`}
                      >
                        {selectedColor === color && (
                          <Check className="h-4 w-4 text-white drop-shadow-md" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Size selection */}
              {availableSizes.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                    {currentLanguage === 'ar' ? 'الحجم' : 'Size'}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {availableSizes.map((size) => (
                      <button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={cn(
                          "px-3 py-1 rounded-md text-sm font-medium transition-all",
                          selectedSize === size
                            ? "bg-primary-500 text-white shadow-sm"
                            : "bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700"
                        )}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center border border-slate-300 dark:border-slate-600 rounded-md">
                  <button
                    onClick={decrementQuantity}
                    disabled={quantity <= 1}
                    className="px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 disabled:opacity-50"
                  >
                    -
                  </button>
                  <span className="px-3 py-2 text-slate-900 dark:text-white min-w-[40px] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={incrementQuantity}
                    disabled={quantity >= (product.stock || 10)}
                    className="px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 disabled:opacity-50"
                  >
                    +
                  </button>
                </div>

                <Button
                  variant="primary"
                  className="flex-1 flex items-center justify-center gap-2"
                  onClick={handleAddToCart}
                  disabled={!isInStock || isAdding}
                >
                  {isAdding ? (
                    <span>{currentLanguage === 'ar' ? 'تمت الإضافة!' : 'Added to Cart!'}</span>
                  ) : (
                    <>
                      <ShoppingCart className="h-4 w-4" />
                      <span>{isInStock ? t('shop.addToCart') : t('shop.outOfStock')}</span>
                    </>
                  )}
                </Button>
              </div>

              {/* Stock status */}
              <div className="flex items-center gap-2 mb-4">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  isInStock ? "bg-green-500" : "bg-red-500"
                )} />
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {isInStock
                    ? `${currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock'} (${product.stock})`
                    : currentLanguage === 'ar' ? 'غير متوفر' : 'Out of Stock'
                  }
                </span>
              </div>

              {/* Action buttons */}
              <div className="flex gap-2 mt-auto">
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "flex items-center gap-1",
                    user && useWishlistStore.getState().isInWishlist(product.id) && "text-red-500"
                  )}
                  onClick={toggleWishlist}
                >
                  <Heart className={cn(
                    "h-4 w-4",
                    user && useWishlistStore.getState().isInWishlist(product.id) && "fill-current"
                  )} />
                  <span>
                    {user && useWishlistStore.getState().isInWishlist(product.id)
                      ? currentLanguage === 'ar' ? 'تمت الإضافة للمفضلة' : 'Added to Wishlist'
                      : currentLanguage === 'ar' ? 'أضف للمفضلة' : 'Add to Wishlist'
                    }
                  </span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Share2 className="h-4 w-4" />
                  <span>{currentLanguage === 'ar' ? 'مشاركة' : 'Share'}</span>
                </Button>
              </div>

              {/* View full details link */}
              <Link
                href={`/${currentLanguage}/shop/${product.slug}`}
                className="mt-4 text-primary-600 dark:text-primary-400 text-sm font-medium hover:underline flex items-center"
              >
                <Info className="h-4 w-4 mr-1" />
                {currentLanguage === 'ar' ? 'عرض التفاصيل الكاملة' : 'View Full Details'}
              </Link>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {showAuthModal && (
        <AuthModal onClose={() => setShowAuthModal(false)} />
      )}
    </>
  );
}
