'use client';

import { useState } from 'react';
import { Eye } from 'lucide-react';
import { Product } from '../../types/index';
import { QuickView } from './QuickView';

interface QuickViewButtonProps {
  product: Product;
}

export const QuickViewButton = ({ product }: QuickViewButtonProps) => {
  const [showQuickView, setShowQuickView] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowQuickView(true)}
        className="p-2 rounded-full bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-300 shadow-lg hover:scale-110 transition-all duration-300"
        aria-label="Quick view"
      >
        <Eye size={20} />
      </button>
      {showQuickView && (
        <QuickView product={product} onClose={() => setShowQuickView(false)} />
      )}
    </>
  );
};
