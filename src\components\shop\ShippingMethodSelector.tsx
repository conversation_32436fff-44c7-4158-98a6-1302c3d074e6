import { useState, useEffect } from 'react';
import { Check, Truck, Clock, AlertCircle } from 'lucide-react';
import { ShippingMethod } from '../../types';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';
import { useTranslation } from '../../translations';
import { formatCurrency } from '../../lib/utils';
import { useCurrencyStore } from '../../stores/currencyStore';

// طرق الشحن المدعومة
const shippingMethods: ShippingMethod[] = [
  {
    id: 'standard',
    name: 'Standard Shipping',
    description: '5-7 business days',
    price: 10,
    estimatedDelivery: '5-7 business days',
    countries: ['*'], // متاح لجميع البلدان
    icon: 'truck',
    enabled: true,
  },
  {
    id: 'express',
    name: 'Express Shipping',
    description: '2-3 business days',
    price: 25,
    estimatedDelivery: '2-3 business days',
    countries: ['US', 'CA', 'UK', 'EU', 'SA', 'AE', 'CN'],
    icon: 'fast-delivery',
    enabled: true,
  },
  {
    id: 'overnight',
    name: 'Overnight Shipping',
    description: 'Next business day',
    price: 50,
    estimatedDelivery: 'Next business day',
    countries: ['US', 'CA', 'UK', 'SA', 'AE'],
    icon: 'overnight',
    enabled: true,
  },
  {
    id: 'international',
    name: 'International Shipping',
    description: '7-14 business days',
    price: 35,
    estimatedDelivery: '7-14 business days',
    countries: ['*'],
    icon: 'globe',
    enabled: true,
  },
  {
    id: 'free',
    name: 'Free Shipping',
    description: 'For orders over $100',
    price: 0,
    estimatedDelivery: '7-10 business days',
    countries: ['US', 'CA', 'UK', 'EU', 'SA', 'AE'],
    icon: 'gift',
    enabled: true,
  },
];

interface ShippingMethodSelectorProps {
  selectedMethod: string;
  onSelect: (methodId: string) => void;
  country: string;
  orderTotal: number;
  className?: string;
}

export function ShippingMethodSelector({
  selectedMethod,
  onSelect,
  country,
  orderTotal,
  className,
}: ShippingMethodSelectorProps) {
  const { isDarkMode } = useThemeStore();
  const { t } = useTranslation();
  const { currency } = useCurrencyStore();
  
  // تصفية طرق الشحن حسب البلد
  const filteredMethods = shippingMethods.filter(
    method => method.enabled && (method.countries.includes('*') || method.countries.includes(country))
  );
  
  // تطبيق قواعد الشحن المجاني
  const availableMethods = filteredMethods.map(method => {
    // إذا كان الشحن المجاني متاحًا للطلبات التي تزيد عن 100 دولار
    if (method.id === 'free' && orderTotal < 100) {
      return { ...method, enabled: false };
    }
    return method;
  }).filter(method => method.enabled);
  
  // التحقق من وجود طريقة الشحن المحددة في القائمة المصفاة
  const isSelectedMethodAvailable = availableMethods.some(method => method.id === selectedMethod);
  
  // إذا كانت طريقة الشحن المحددة غير متوفرة، اختر الطريقة الأولى
  useEffect(() => {
    if (!isSelectedMethodAvailable && availableMethods.length > 0) {
      onSelect(availableMethods[0].id);
    }
  }, [isSelectedMethodAvailable, availableMethods, onSelect]);
  
  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-4">{t('checkout.shippingMethod')}</h3>
      
      {availableMethods.length === 0 ? (
        <div className={cn(
          "p-4 rounded-md flex items-center",
          isDarkMode ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
        )}>
          <AlertCircle className="mr-2 h-5 w-5" />
          <p>{t('checkout.noShippingMethodsAvailable')}</p>
        </div>
      ) : (
        <div className="space-y-3">
          {availableMethods.map((method) => (
            <div
              key={method.id}
              className={cn(
                "relative p-4 rounded-md border cursor-pointer transition-colors",
                selectedMethod === method.id
                  ? isDarkMode
                    ? "border-primary-500 bg-primary-900/20"
                    : "border-primary-500 bg-primary-50"
                  : isDarkMode
                    ? "border-slate-700 hover:border-slate-600"
                    : "border-slate-200 hover:border-slate-300",
              )}
              onClick={() => onSelect(method.id)}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0 mr-4 w-12 h-12 flex items-center justify-center">
                  <Truck className={cn(
                    "h-8 w-8",
                    isDarkMode ? "text-slate-400" : "text-slate-600"
                  )} />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{method.name}</h4>
                    <span className="font-semibold">
                      {method.price === 0 ? t('checkout.free') : formatCurrency(method.price, currency)}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm text-slate-500 mt-1">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{method.estimatedDelivery}</span>
                  </div>
                </div>
                
                {selectedMethod === method.id && (
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center",
                    isDarkMode ? "bg-primary-500" : "bg-primary-500"
                  )}>
                    <Check className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
