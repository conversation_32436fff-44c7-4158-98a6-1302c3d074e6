'use client';

import { Chevron<PERSON>eft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { ScrollAnimation } from '../ui/animations';

interface ShopFooterProps {
  totalProducts: number;
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

export function ShopFooter({
  totalProducts,
  currentPage,
  itemsPerPage,
  onPageChange
}: ShopFooterProps) {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();


  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(totalProducts / itemsPerPage);

  // إنشاء مصفوفة أرقام الصفحات
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // إذا كان إجمالي عدد الصفحات أقل من أو يساوي الحد الأقصى، عرض جميع الصفحات
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // إذا كان إجمالي عدد الصفحات أكبر من الحد الأقصى
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // إضافة "..." إذا لزم الأمر
      if (startPage > 1) {
        pageNumbers.unshift(-1); // استخدام -1 لتمثيل "..."
        pageNumbers.unshift(1); // دائمًا إضافة الصفحة الأولى
      }

      if (endPage < totalPages) {
        pageNumbers.push(-2); // استخدام -2 لتمثيل "..." الثاني
        pageNumbers.push(totalPages); // دائمًا إضافة الصفحة الأخيرة
      }
    }

    return pageNumbers;
  };



  return (
    <div className="mt-12">
      {/* ترقيم الصفحات */}
      {totalPages > 1 && (
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="flex justify-center my-8">
            <div className={cn(
              "flex items-center gap-1 rounded-lg p-1",
              "bg-white dark:bg-slate-800 shadow-md"
            )}>
              {/* زر الصفحة الأولى */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onPageChange(1)}
                disabled={currentPage === 1}
                className="h-9 w-9"
                aria-label={currentLanguage === 'ar' ? 'الصفحة الأولى' : 'First page'}
              >
                <ChevronsLeft className={cn("h-4 w-4", isRTL && "rotate-180")} />
              </Button>

              {/* زر الصفحة السابقة */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="h-9 w-9"
                aria-label={currentLanguage === 'ar' ? 'الصفحة السابقة' : 'Previous page'}
              >
                <ChevronLeft className={cn("h-4 w-4", isRTL && "rotate-180")} />
              </Button>

              {/* أرقام الصفحات */}
              {getPageNumbers().map((pageNumber, index) => (
                pageNumber < 0 ? (
                  // عرض "..." للصفحات المحذوفة
                  <span key={`ellipsis-${index}`} className="px-2 text-slate-500 dark:text-slate-400">
                    ...
                  </span>
                ) : (
                  // زر رقم الصفحة
                  <Button
                    key={`page-${pageNumber}`}
                    variant={currentPage === pageNumber ? "default" : "ghost"}
                    onClick={() => onPageChange(pageNumber)}
                    className={cn(
                      "h-9 w-9 rounded-md",
                      currentPage === pageNumber && "bg-primary-500 text-white hover:bg-primary-600"
                    )}
                  >
                    {pageNumber}
                  </Button>
                )
              ))}

              {/* زر الصفحة التالية */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="h-9 w-9"
                aria-label={currentLanguage === 'ar' ? 'الصفحة التالية' : 'Next page'}
              >
                <ChevronRight className={cn("h-4 w-4", isRTL && "rotate-180")} />
              </Button>

              {/* زر الصفحة الأخيرة */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onPageChange(totalPages)}
                disabled={currentPage === totalPages}
                className="h-9 w-9"
                aria-label={currentLanguage === 'ar' ? 'الصفحة الأخيرة' : 'Last page'}
              >
                <ChevronsRight className={cn("h-4 w-4", isRTL && "rotate-180")} />
              </Button>
            </div>
          </div>
        </ScrollAnimation>
      )}


    </div>
  );
}
