'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Search, Tag, ArrowRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';

interface ShopHeaderProps {
  onSearch: (query: string) => void;
  onCategorySelect: (category: string) => void;
  searchQuery: string;
  selectedCategory: string;
}

export function ShopHeader({
  onSearch,
  onCategorySelect,
  searchQuery,
  selectedCategory
}: ShopHeaderProps) {
  const router = useRouter();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث البحث المحلي عند تغيير البحث الخارجي
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // معالجة البحث
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  return (
    <div className="mb-6">

      {/* فئات المنتجات */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="mb-6 bg-gradient-to-br from-white via-slate-50/50 to-white dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 rounded-xl shadow-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
          <div className="flex items-center justify-between mb-5">
            <h2 className="text-xl font-bold text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'}
            </h2>
            <Link href={`/${currentLanguage}/shop/categories`} className="text-primary-600 dark:text-primary-400 flex items-center text-sm hover:underline font-medium bg-primary-50 dark:bg-primary-900/30 px-3 py-2 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors">
              {currentLanguage === 'ar' ? 'عرض الكل' : 'View all'}
              <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
            </Link>
          </div>

          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
            {productCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => onCategorySelect(category.id)}
                className={cn(
                  "relative h-20 rounded-xl overflow-hidden group transition-all duration-300",
                  "border-2 border-slate-200/60 dark:border-slate-700/60 hover:border-primary-300/80 dark:hover:border-primary-600/80",
                  "hover:shadow-lg hover:shadow-primary-500/10 dark:hover:shadow-primary-400/10",
                  "hover:-translate-y-1",
                  selectedCategory === category.id
                    ? "ring-2 ring-primary-500/50 border-primary-500 dark:border-primary-400 shadow-lg shadow-primary-500/20"
                    : ""
                )}
              >
                {/* صورة الفئة */}
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{ backgroundImage: `url(${category.image})` }}
                ></div>

                {/* طبقة التراكب المحسنة */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/10 group-hover:from-black/90 group-hover:via-black/50 transition-all duration-300"></div>

                {/* تأثير الضوء */}
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* اسم الفئة */}
                <div className="absolute inset-0 flex items-center justify-center p-3 z-10">
                  <span className="text-white text-center font-semibold text-sm leading-tight drop-shadow-lg group-hover:scale-105 transition-transform duration-300">
                    {currentLanguage === 'ar' ? category.name.ar : category.name.en}
                  </span>
                </div>

                {/* أيقونة التحديد */}
                {selectedCategory === category.id && (
                  <div className="absolute top-2 right-2 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center z-20 shadow-lg animate-pulse">
                    <Tag className="w-3 h-3 text-white" />
                  </div>
                )}

                {/* مؤشر الحالة */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
            ))}
          </div>
        </div>
      </ScrollAnimation>
    </div>
  );
}
