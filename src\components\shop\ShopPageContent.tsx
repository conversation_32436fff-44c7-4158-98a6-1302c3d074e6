'use client';

import { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ShoppingCart, Heart, Star, ArrowRight, X, Search,
  Grid, List, SlidersHorizontal, Tag, Filter, ChevronDown,
  ArrowUpDown, ArrowDownUp, CheckCircle, Eye, Truck, Package
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { AuthModal } from '../../components/auth/AuthModal';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { products, productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { Product, ProductFiltersState, SortOption } from '../../types/index';
import { useAuthenticatedAction } from '../../hooks/useAuthenticatedAction';
import { ProductFilters } from '../../components/shop/ProductFilters';

export const ShopPageContent = () => {
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>('featured');
  const [isLoading, setIsLoading] = useState(true);
  const [showSortDropdown, setShowSortDropdown] = useState(false);

  const maxPrice = useMemo(() => products.reduce((max, p) => p.price > max ? p.price : max, 0), [products]);

  const [filters, setFilters] = useState<ProductFiltersState>({
    category: 'all',
    priceRange: { min: 0, max: maxPrice || 50000 },
    inStock: false,
    onSale: false,
    featured: false,
    searchQuery: ''
  });

  // تحديث الفلاتر عند تغير السعر الأقصى
  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        max: maxPrice || 50000
      }
    }));
  }, [maxPrice]);

  // تحقق من وجود معلمات URL عند تحميل الصفحة
  useEffect(() => {
    // محاكاة قراءة معلمات URL (في التطبيق الحقيقي، استخدم router.query)
    const urlParams = new URLSearchParams(window.location.search);
    const featuredParam = urlParams.get('featured');

    if (featuredParam === 'true') {
      setFilters(prevFilters => ({
        ...prevFilters,
        featured: true
      }));
      setSortOption('featured');
    }
  }, []);

  // محاكاة تحميل البيانات
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { theme, resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تصفية المنتجات حسب الفلاتر
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      if (filters.category !== 'all' && product.category !== filters.category) return false;
      if (filters.inStock && product.stock === 0) return false;
      if (filters.onSale && !product.compareAtPrice) return false;
      if (filters.featured && !product.featured) return false;
      if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) return false;
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        return (
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.category.toLowerCase().includes(query) ||
          product.tags.some(tag => tag.toLowerCase().includes(query))
        );
      }
      return true;
    });
  }, [filters]);

  // ترتيب المنتجات حسب الخيار المحدد
  const sortedProducts = useMemo(() => {
    let sorted = [...filteredProducts];

    switch (sortOption) {
      case 'featured':
        return sorted.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
      case 'newest':
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case 'price-asc':
        return sorted.sort((a, b) => a.price - b.price);
      case 'price-desc':
        return sorted.sort((a, b) => b.price - a.price);
      case 'popular':
        return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      default:
        return sorted;
    }
  }, [filteredProducts, sortOption]);

  const handleUnauthenticated = () => {
    setShowAuthModal(true);
  };

  const handleAddToCart = useAuthenticatedAction((product: Product) => {
    cartStore.addItem(product, 1);
    // إظهار رسالة نجاح (يمكن تنفيذها باستخدام مكتبة toast)
    console.log(`${product.name} added to cart`);
  }, handleUnauthenticated);

  const handleWholesaleInquiry = useAuthenticatedAction((product: Product) => {
    setSelectedProduct(product);
    setShowWholesaleForm(true);
  }, handleUnauthenticated);

  const toggleWishlist = useAuthenticatedAction((product: Product) => {
    if (wishlistStore.isInWishlist(product.id)) {
      wishlistStore.removeItem(product.id);
    } else {
      wishlistStore.addItem(product);
    }
  }, handleUnauthenticated);

  const handleQuickView = (product: Product) => {
    setQuickViewProduct(product);
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      priceRange: { min: 0, max: maxPrice || 50000 },
      inStock: false,
      onSale: false,
      featured: false,
      searchQuery: ''
    });
    setSortOption('featured');
    setShowMobileFilters(false);
  };

  // تبديل وضع العرض (شبكة/قائمة)
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };

  // التحقق من وجود منتجات مميزة
  const hasFeaturedProducts = useMemo(() => {
    return products.some(product => product.featured);
  }, [products]);

  // الحصول على المنتجات المميزة
  const featuredProducts = useMemo(() => {
    return products.filter(product => product.featured).slice(0, 4);
  }, [products]);

  return (
    <div className="container-custom py-8">
      {/* Hero Section */}
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="mb-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4">
            {currentLanguage === 'ar' ? 'متجر ارتال' : 'ARTAL Shop'}
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            {currentLanguage === 'ar'
              ? 'تسوق أحدث المنتجات عالية الجودة بأفضل الأسعار. شحن سريع وخدمة عملاء ممتازة.'
              : 'Shop the latest high-quality products at the best prices. Fast shipping and excellent customer service.'}
          </p>
        </div>
      </ScrollAnimation>

      {/* Featured Products Section */}
      {hasFeaturedProducts && (
        <ScrollAnimation animation="fade" delay={0.2} className="mb-12">
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-6 md:p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary-300/20 dark:bg-primary-500/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-accent-300/20 dark:bg-accent-500/10 rounded-full blur-3xl"></div>

            <div className="flex flex-col md:flex-row justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-2 flex items-center">
                  <Tag className="mr-2 text-primary-500" size={24} />
                  {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
                </h2>
                <p className="text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar'
                    ? 'اكتشف منتجاتنا المميزة المختارة خصيصًا لك'
                    : 'Discover our featured products specially curated for you'}
                </p>
              </div>

              <Button
                variant="outline"
                className="mt-4 md:mt-0 border-primary-200 dark:border-primary-800 hover:bg-primary-50 dark:hover:bg-primary-900/30"
                onClick={() => setFilters(prev => ({ ...prev, featured: !prev.featured }))}
              >
                {filters.featured
                  ? (currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'Show All Products')
                  : (currentLanguage === 'ar' ? 'تصفية المنتجات المميزة فقط' : 'Filter Featured Only')}
                <Filter className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>

            {/* Featured Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {featuredProducts.map((product, index) => (
                <Link
                  key={product.id}
                  href={`/${currentLanguage}/shop/${product.slug}`}
                  className="group bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden flex flex-col h-full transform hover:-translate-y-1"
                >
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <EnhancedImage
                      src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                      alt={product.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                      priority={true}
                    />

                    {/* Sale Badge */}
                    {product.compareAtPrice && product.compareAtPrice > product.price && (
                      <div className="absolute top-2 right-2 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                          {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-3 flex flex-col flex-grow">
                    <h3 className="font-semibold text-slate-900 dark:text-white line-clamp-1 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                    </h3>

                    <div className="flex items-center justify-between mt-2">
                      <span className="font-bold text-primary-600 dark:text-primary-400">
                        {formatCurrency(product.price)}
                      </span>

                      <div className="flex items-center text-sm text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="ml-1 text-slate-600 dark:text-slate-300">
                          {product.rating?.toFixed(1) || '4.5'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* View All Featured Button */}
            <div className="mt-6 text-center">
              <Button
                variant="primary"
                className="px-6"
                onClick={() => setFilters(prev => ({ ...prev, featured: true }))}
              >
                {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>
          </div>
        </ScrollAnimation>
      )}

      {/* Shop Controls */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 bg-white dark:bg-slate-800 p-4 rounded-xl shadow-md">
        <div className="flex items-center mb-4 md:mb-0">
          <span className="text-slate-600 dark:text-slate-300 mr-2">
            {currentLanguage === 'ar'
              ? `${sortedProducts.length} منتج`
              : `${sortedProducts.length} Products`}
          </span>

          <div className="flex items-center ml-4 space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
              }`}
              aria-label={viewMode === 'grid' ? 'Grid View' : 'List View'}
            >
              <Grid size={18} />
            </button>

            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
              }`}
              aria-label={viewMode === 'list' ? 'List View' : 'Grid View'}
            >
              <List size={18} />
            </button>
          </div>
        </div>

        <div className="relative">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowSortDropdown(!showSortDropdown)}
          >
            <SlidersHorizontal size={16} />
            {currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort by'}:
            <span className="font-medium">
              {sortOption === 'featured' && (currentLanguage === 'ar' ? 'المميزة' : 'Featured')}
              {sortOption === 'newest' && (currentLanguage === 'ar' ? 'الأحدث' : 'Newest')}
              {sortOption === 'price-asc' && (currentLanguage === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High')}
              {sortOption === 'price-desc' && (currentLanguage === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low')}
              {sortOption === 'popular' && (currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular')}
            </span>
            <ChevronDown size={16} className={`transition-transform ${showSortDropdown ? 'rotate-180' : ''}`} />
          </Button>

          {showSortDropdown && (
            <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-slate-800 rounded-md shadow-lg z-20 border border-slate-200 dark:border-slate-700">
              <div className="py-1">
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'featured' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('featured');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'المميزة' : 'Featured'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'newest' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('newest');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'الأحدث' : 'Newest'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-asc' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('price-asc');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-desc' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('price-desc');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'popular' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('popular');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Products Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar with Filters */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-md p-4 sticky top-24">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
              </h3>

              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset'}
              </Button>
            </div>

            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <Input
                  type="text"
                  placeholder={currentLanguage === 'ar' ? 'ابحث عن منتجات...' : 'Search products...'}
                  value={filters.searchQuery}
                  onChange={(e) => setFilters({ ...filters, searchQuery: e.target.value })}
                  className="pr-10"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
              </div>
            </div>

            {/* Categories */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="category-all"
                    type="radio"
                    checked={filters.category === 'all'}
                    onChange={() => setFilters({ ...filters, category: 'all' })}
                    className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                  />
                  <label htmlFor="category-all" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}
                  </label>
                </div>

                {productCategories.map((category) => (
                  <div key={category} className="flex items-center">
                    <input
                      id={`category-${category}`}
                      type="radio"
                      checked={filters.category === category}
                      onChange={() => setFilters({ ...filters, category: category })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                    />
                    <label htmlFor={`category-${category}`} className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {category}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Price Range */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                {currentLanguage === 'ar' ? 'نطاق السعر' : 'Price Range'}
              </h3>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {formatCurrency(filters.priceRange.min)}
                </span>
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {formatCurrency(filters.priceRange.max)}
                </span>
              </div>
              <input
                type="range"
                min={0}
                max={maxPrice}
                value={filters.priceRange.max}
                onChange={(e) => setFilters({
                  ...filters,
                  priceRange: { ...filters.priceRange, max: parseInt(e.target.value) }
                })}
                className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Availability Filters */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                {currentLanguage === 'ar' ? 'التوفر' : 'Availability'}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="inStock"
                    type="checkbox"
                    checked={filters.inStock}
                    onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}
                    className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="inStock" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'متوفر في المخزون فقط' : 'In Stock Only'}
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="onSale"
                    type="checkbox"
                    checked={filters.onSale}
                    onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}
                    className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="onSale" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'العروض فقط' : 'On Sale Only'}
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="featured"
                    type="checkbox"
                    checked={filters.featured}
                    onChange={(e) => setFilters({ ...filters, featured: e.target.checked })}
                    className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="featured" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'المنتجات المميزة فقط' : 'Featured Products Only'}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="lg:col-span-3">
          {isLoading ? (
            <div className="flex items-center justify-center py-20">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="mt-4 text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'جاري تحميل المنتجات...' : 'Loading products...'}
                </p>
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            <ScrollStagger className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-6 md:p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary-300/20 dark:bg-primary-500/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-accent-300/20 dark:bg-accent-500/10 rounded-full blur-3xl"></div>

            <div className="flex flex-col md:flex-row justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-2 flex items-center">
                  <Tag className="mr-2 text-primary-500" size={24} />
                  {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
                </h2>
                <p className="text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar'
                    ? 'اكتشف منتجاتنا المميزة المختارة خصيصًا لك'
                    : 'Discover our featured products specially curated for you'}
                </p>
              </div>

              <Button
                variant="outline"
                className="mt-4 md:mt-0 border-primary-200 dark:border-primary-800 hover:bg-primary-50 dark:hover:bg-primary-900/30"
                onClick={() => setFilters(prev => ({ ...prev, featured: !prev.featured }))}
              >
                {filters.featured
                  ? (currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'Show All Products')
                  : (currentLanguage === 'ar' ? 'تصفية المنتجات المميزة فقط' : 'Filter Featured Only')}
                <Filter className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>

            {/* Featured Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {featuredProducts.map((product, index) => (
                <Link
                  key={product.id}
                  href={`/${currentLanguage}/shop/${product.slug}`}
                  className="group bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden flex flex-col h-full transform hover:-translate-y-1"
                >
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <EnhancedImage
                      src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                      alt={product.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                      priority={true}
                    />

                    {/* Sale Badge */}
                    {product.compareAtPrice && product.compareAtPrice > product.price && (
                      <div className="absolute top-2 right-2 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                          {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-3 flex flex-col flex-grow">
                    <h3 className="font-semibold text-slate-900 dark:text-white line-clamp-1 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                    </h3>

                    <div className="flex items-center justify-between mt-2">
                      <span className="font-bold text-primary-600 dark:text-primary-400">
                        {formatCurrency(product.price)}
                      </span>

                      <div className="flex items-center text-sm text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="ml-1 text-slate-600 dark:text-slate-300">
                          {product.rating?.toFixed(1) || '4.5'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* View All Featured Button */}
            <div className="mt-6 text-center">
              <Button
                variant="primary"
                className="px-6"
                onClick={() => setFilters(prev => ({ ...prev, featured: true }))}
              >
                {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>
          </div>
        </ScrollAnimation>
      )}
    </div>
  );
};
