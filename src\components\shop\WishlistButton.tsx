'use client';

import { Heart } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthModalStore } from '../../stores/authModalStore';
import { Product } from '../../types/index'; 

interface WishlistButtonProps {
  product: Product;
}

export const WishlistButton = ({ product }: WishlistButtonProps) => {
  const { user } = useAuthStore();
  const { addItem, removeItem, isInWishlist } = useWishlistStore();
  const { openModal } = useAuthModalStore();

  const handleWishlist = () => {
    if (!user) {
      openModal('sign-in');
      return;
    }
    const productIdStr = String(product.id);

    if (isInWishlist(productIdStr)) {
      removeItem(productIdStr);
    } else {
      addItem(product); 
    }
  };

  return (
    <button
      onClick={handleWishlist}
      className={`p-2 rounded-full shadow-lg transition-all duration-300 ${
        isInWishlist(String(product.id))
          ? 'bg-primary-500 text-white'
          : 'bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-300 hover:scale-110'
      }`}
      aria-label="Add to wishlist"
    >
      <Heart
        size={20}
        className={isInWishlist(String(product.id)) ? 'fill-current' : ''}
      />
    </button>
  );
};
