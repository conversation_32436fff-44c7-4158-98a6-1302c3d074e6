import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, ZoomIn, ZoomOut, Sun, Moon, Type } from 'lucide-react';
import { useThemeStore } from '../../stores/themeStore';
import { Button } from './Button';
import { AccessibleIcon } from './AccessibleIcon';

/**
 * قائمة إمكانية الوصول التي توفر خيارات لتحسين تجربة المستخدم
 * مثل تغيير حجم الخط وتباين الألوان والوضع الليلي
 */
export function AccessibilityMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const { isDarkMode, toggleTheme } = useThemeStore();
  const [fontSize, setFontSize] = useState(100);

  // تغيير حجم الخط
  const changeFontSize = (size: number) => {
    const newSize = Math.max(80, Math.min(150, fontSize + size));
    setFontSize(newSize);
    document.documentElement.style.fontSize = `${newSize}%`;
  };

  // زيادة تباين الألوان
  const toggleHighContrast = () => {
    document.documentElement.classList.toggle('high-contrast');
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <Button
        variant="primary"
        className="rounded-full w-12 h-12 flex items-center justify-center shadow-lg"
        onClick={() => setIsOpen(!isOpen)}
        accessibilityLabel="فتح قائمة إمكانية الوصول"
      >
        <AccessibleIcon
          icon={<Settings className="w-5 h-5" />}
          label="إعدادات إمكانية الوصول"
        />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-16 left-0 bg-white dark:bg-slate-800 rounded-lg shadow-xl p-4 w-64 border border-slate-200 dark:border-slate-700"
          >
            <h3 className="text-lg font-medium mb-3 text-slate-900 dark:text-white">
              إعدادات إمكانية الوصول
            </h3>

            <div className="space-y-4">
              {/* تغيير حجم الخط */}
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                  حجم الخط: {fontSize}%
                </p>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => changeFontSize(-10)}
                    accessibilityLabel="تصغير حجم الخط"
                  >
                    <AccessibleIcon
                      icon={<ZoomOut className="w-4 h-4" />}
                      label="تصغير"
                    />
                  </Button>
                  <div className="h-2 bg-slate-200 dark:bg-slate-700 rounded-full flex-1 relative">
                    <div
                      className="absolute top-0 left-0 h-full bg-primary-500 rounded-full"
                      style={{ width: `${((fontSize - 80) / 70) * 100}%` }}
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => changeFontSize(10)}
                    accessibilityLabel="تكبير حجم الخط"
                  >
                    <AccessibleIcon
                      icon={<ZoomIn className="w-4 h-4" />}
                      label="تكبير"
                    />
                  </Button>
                </div>
              </div>

              {/* تبديل الوضع الليلي */}
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                  الوضع المظلم
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={toggleTheme}
                  accessibilityLabel={isDarkMode ? "تفعيل الوضع النهاري" : "تفعيل الوضع الليلي"}
                >
                  <span>
                    {isDarkMode ? "تفعيل الوضع النهاري" : "تفعيل الوضع الليلي"}
                  </span>
                  <AccessibleIcon
                    icon={isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                    label={isDarkMode ? "شمس" : "قمر"}
                  />
                </Button>
              </div>

              {/* تبديل التباين العالي */}
              <div>
                <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                  تباين عالي
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={toggleHighContrast}
                  accessibilityLabel="تبديل وضع التباين العالي"
                >
                  <span>تفعيل التباين العالي</span>
                  <AccessibleIcon
                    icon={<Type className="w-4 h-4" />}
                    label="تباين"
                  />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
