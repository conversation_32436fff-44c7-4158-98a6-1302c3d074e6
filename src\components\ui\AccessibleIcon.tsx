import { ReactElement, cloneElement } from 'react';
import { VisuallyHidden } from './VisuallyHidden';

interface AccessibleIconProps {
  /** الأيقونة المراد عرضها */
  icon: ReactElement;
  /** وصف الأيقونة للقارئات الشاشة */
  label: string;
}

/**
 * مكون لجعل الأيقونات متاحة لقارئات الشاشة
 */
export function AccessibleIcon({ icon, label }: AccessibleIconProps) {
  return (
    <>
      {cloneElement(icon, {
        'aria-hidden': 'true',
        focusable: 'false',
      })}
      <VisuallyHidden>{label}</VisuallyHidden>
    </>
  );
}
