import { ReactNode, useEffect, useState } from 'react';
import { motion, useAnimation, Variants } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { cn } from '../../lib/utils';

interface AnimatedElementProps {
  children: ReactNode;
  className?: string;
  animation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'scale' | 'rotate' | 'bounce';
  delay?: number;
  duration?: number;
  threshold?: number;
  once?: boolean;
}

export const AnimatedElement = ({
  children,
  className,
  animation = 'fade',
  delay = 0,
  duration = 0.5,
  threshold = 0.1,
  once = true,
}: AnimatedElementProps) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({ threshold, triggerOnce: once });
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    } else if (!once) {
      controls.start('hidden');
    }
  }, [controls, inView, once]);

  const getVariants = (): Variants => {
    switch (animation) {
      case 'fade':
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration, delay } },
        };
      case 'slide-up':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { duration, delay } },
        };
      case 'slide-down':
        return {
          hidden: { opacity: 0, y: -20 },
          visible: { opacity: 1, y: 0, transition: { duration, delay } },
        };
      case 'slide-left':
        return {
          hidden: { opacity: 0, x: 20 },
          visible: { opacity: 1, x: 0, transition: { duration, delay } },
        };
      case 'slide-right':
        return {
          hidden: { opacity: 0, x: -20 },
          visible: { opacity: 1, x: 0, transition: { duration, delay } },
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.9 },
          visible: { opacity: 1, scale: 1, transition: { duration, delay } },
        };
      case 'rotate':
        return {
          hidden: { opacity: 0, rotate: -5, scale: 0.9 },
          visible: { opacity: 1, rotate: 0, scale: 1, transition: { duration, delay } },
        };
      case 'bounce':
        return {
          hidden: { opacity: 0, scale: 0.3 },
          visible: {
            opacity: 1,
            scale: 1,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 260,
              damping: 20,
            },
          },
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration, delay } },
        };
    }
  };

  // إذا كنا في وضع العميل، استخدم Framer Motion
  if (isClient) {
    return (
      <motion.div
        ref={ref}
        initial="hidden"
        animate={controls}
        variants={getVariants()}
        className={className}
      >
        {children}
      </motion.div>
    );
  }

  // إذا كنا في وضع الخادم، عرض المحتوى بدون حركة
  return <div className={cn(className)}>{children}</div>;
};

// مكونات مساعدة للاستخدام السريع
export const FadeIn = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="fade" {...props} />
);

export const SlideUp = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="slide-up" {...props} />
);

export const SlideDown = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="slide-down" {...props} />
);

export const SlideLeft = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="slide-left" {...props} />
);

export const SlideRight = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="slide-right" {...props} />
);

export const ScaleIn = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="scale" {...props} />
);

export const RotateIn = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="rotate" {...props} />
);

export const BounceIn = (props: Omit<AnimatedElementProps, 'animation'>) => (
  <AnimatedElement animation="bounce" {...props} />
);
