import { cn } from '../../lib/utils';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'destructive' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
}

export function Badge({
  children,
  variant = 'default',
  size = 'md',
  className,
  onClick,
}: BadgeProps) {
  const baseStyles = "inline-flex items-center justify-center font-medium transition-colors";
  
  const variantStyles = {
    default: "bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-600",
    primary: "bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-900/50",
    secondary: "bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300 hover:bg-secondary-200 dark:hover:bg-secondary-900/50",
    success: "bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300 hover:bg-success-200 dark:hover:bg-success-900/50",
    warning: "bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300 hover:bg-warning-200 dark:hover:bg-warning-900/50",
    destructive: "bg-destructive-100 text-destructive-800 dark:bg-destructive-900/30 dark:text-destructive-300 hover:bg-destructive-200 dark:hover:bg-destructive-900/50",
    outline: "border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-800",
  };
  
  const sizeStyles = {
    sm: "text-xs px-2 py-0.5 rounded-full",
    md: "text-sm px-2.5 py-0.5 rounded-full",
    lg: "text-base px-3 py-1 rounded-full",
  };
  
  return (
    <span
      className={cn(
        baseStyles,
        variantStyles[variant],
        sizeStyles[size],
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      {children}
    </span>
  );
}
