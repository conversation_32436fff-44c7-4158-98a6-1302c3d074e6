'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { useLanguageStore } from '../../stores/languageStore';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();

  return (
    <nav 
      className={cn(
        "flex items-center space-x-1 text-sm",
        language === 'ar' && "space-x-reverse",
        className
      )}
      aria-label={language === 'ar' ? 'مسار التنقل' : 'Breadcrumb'}
    >
      {/* Home Icon */}
      <Link
        href={`/${language}`}
        className={cn(
          "flex items-center text-slate-500 hover:text-slate-700 transition-colors duration-200",
          isDarkMode ? "text-slate-400 hover:text-slate-200" : "text-slate-500 hover:text-slate-700"
        )}
        aria-label={language === 'ar' ? 'الرئيسية' : 'Home'}
      >
        <Home className="h-4 w-4" />
      </Link>

      {/* Breadcrumb Items */}
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {/* Separator */}
          <ChevronRight 
            className={cn(
              "h-4 w-4 mx-2 text-slate-400",
              language === 'ar' && "rotate-180"
            )} 
          />
          
          {/* Breadcrumb Item */}
          {item.href && !item.current ? (
            <Link
              href={item.href}
              className={cn(
                "text-slate-500 hover:text-slate-700 transition-colors duration-200 truncate max-w-[200px]",
                isDarkMode ? "text-slate-400 hover:text-slate-200" : "text-slate-500 hover:text-slate-700"
              )}
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={cn(
                "text-slate-900 font-medium truncate max-w-[200px]",
                isDarkMode ? "text-slate-100" : "text-slate-900"
              )}
              aria-current={item.current ? 'page' : undefined}
            >
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  );
}

// Helper function to generate breadcrumb items for services
export function generateServiceBreadcrumbs(
  serviceName?: string,
  serviceSlug?: string,
  language: 'ar' | 'en' = 'en'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: language === 'ar' ? 'الخدمات' : 'Services',
      href: `/${language}/services`
    }
  ];

  if (serviceName) {
    items.push({
      label: serviceName,
      href: serviceSlug ? `/${language}/services/${serviceSlug}` : undefined,
      current: true
    });
  }

  return items;
}

// Helper function to generate breadcrumb items for products
export function generateProductBreadcrumbs(
  productName?: string,
  productSlug?: string,
  category?: string,
  language: 'ar' | 'en' = 'en'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: language === 'ar' ? 'المتجر' : 'Shop',
      href: `/${language}/shop`
    }
  ];

  if (category) {
    items.push({
      label: category,
      href: `/${language}/shop?category=${encodeURIComponent(category)}`
    });
  }

  if (productName) {
    items.push({
      label: productName,
      href: productSlug ? `/${language}/shop/product/${productSlug}` : undefined,
      current: true
    });
  }

  return items;
}

// Helper function to generate breadcrumb items for production lines
export function generateProductionLineBreadcrumbs(
  lineName?: string,
  lineSlug?: string,
  language: 'ar' | 'en' = 'en'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: language === 'ar' ? 'خطوط الإنتاج' : 'Production Lines',
      href: `/${language}/production-lines`
    }
  ];

  if (lineName) {
    items.push({
      label: lineName,
      href: lineSlug ? `/${language}/production-lines/${lineSlug}` : undefined,
      current: true
    });
  }

  return items;
}

// Helper function to generate breadcrumb items for blog
export function generateBlogBreadcrumbs(
  postTitle?: string,
  postSlug?: string,
  language: 'ar' | 'en' = 'en'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: language === 'ar' ? 'المدونة' : 'Blog',
      href: `/${language}/blog`
    }
  ];

  if (postTitle) {
    items.push({
      label: postTitle,
      href: postSlug ? `/${language}/blog/${postSlug}` : undefined,
      current: true
    });
  }

  return items;
}

// Helper function to generate breadcrumb items for account pages
export function generateAccountBreadcrumbs(
  pageName?: string,
  pageSlug?: string,
  language: 'ar' | 'en' = 'en'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [
    {
      label: language === 'ar' ? 'حسابي' : 'My Account',
      href: `/${language}/account`
    }
  ];

  if (pageName) {
    items.push({
      label: pageName,
      href: pageSlug ? `/${language}/account/${pageSlug}` : undefined,
      current: true
    });
  }

  return items;
}
