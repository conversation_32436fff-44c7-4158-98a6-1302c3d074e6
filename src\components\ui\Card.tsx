import { HTMLAttributes, forwardRef } from 'react';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, shadow = 'sm', border = true, ...props }, ref) => {
    const { isDarkMode } = useThemeStore();

    const shadows = {
      none: '',
      sm: isDarkMode ? 'shadow-sm shadow-slate-900/50' : 'shadow-sm hover:shadow-md transition-shadow duration-300',
      md: isDarkMode ? 'shadow-md shadow-slate-900/50' : 'shadow-md hover:shadow-lg transition-shadow duration-300',
      lg: isDarkMode ? 'shadow-lg shadow-slate-900/50' : 'shadow-lg hover:shadow-xl transition-shadow duration-300',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-lg overflow-hidden transition-all duration-300',
          isDarkMode
            ? 'bg-slate-900 backdrop-blur-sm bg-opacity-90'
            : 'bg-white backdrop-blur-sm bg-opacity-95',
          border && (isDarkMode
            ? 'border border-slate-800/80'
            : 'border border-slate-200/80'),
          shadows[shadow],
          'hover:translate-y-[-2px]',
          className
        )}
        {...props}
      />
    );
  }
);

Card.displayName = 'Card';

const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('p-6', className)}
      {...props}
    />
  )
);

CardHeader.displayName = 'CardHeader';

const CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn('text-xl font-semibold', className)}
      {...props}
    />
  )
);

CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => {
    const { isDarkMode } = useThemeStore();

    return (
      <p
        ref={ref}
        className={cn(
          'text-sm',
          isDarkMode ? 'text-slate-400' : 'text-slate-500',
          className
        )}
        {...props}
      />
    );
  }
);

CardDescription.displayName = 'CardDescription';

const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('p-6 pt-0', className)}
      {...props}
    />
  )
);

CardContent.displayName = 'CardContent';

const CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('p-6 pt-0 flex items-center', className)}
      {...props}
    />
  )
);

CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };