'use client';

import { useState } from 'react';
import Image from 'next/image';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';

interface EnhancedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  containerClassName?: string;
  priority?: boolean;
  effect?: 'none' | 'zoom' | 'fade' | 'blur' | 'tilt' | 'shine';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  aspectRatio?: '1:1' | '4:3' | '16:9' | '21:9' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill' | 'none';
  quality?: number;
  loading?: 'eager' | 'lazy';
  fill?: boolean;
  sizes?: string;
  onClick?: () => void;
  progressive?: boolean;
  placeholder?: 'blur' | 'empty' | 'shimmer';
  onError?: () => void;
}

export function EnhancedImage({
  src,
  alt,
  width,
  height,
  className,
  containerClassName,
  priority = false,
  effect = 'none',
  rounded = 'md',
  aspectRatio = 'auto',
  objectFit = 'cover',
  quality = 85,
  loading = 'lazy',
  fill = false,
  sizes,
  onClick,
  progressive = false,
  placeholder = 'blur',
  onError,
}: EnhancedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const { isDarkMode } = useThemeStore();

  // تعيين نسبة العرض إلى الارتفاع
  const getAspectRatio = () => {
    switch (aspectRatio) {
      case '1:1': return 'aspect-square';
      case '4:3': return 'aspect-[4/3]';
      case '16:9': return 'aspect-[16/9]';
      case '21:9': return 'aspect-[21/9]';
      default: return '';
    }
  };

  // تعيين حجم التقريب
  const getRoundedSize = () => {
    switch (rounded) {
      case 'none': return '';
      case 'sm': return 'rounded-sm';
      case 'md': return 'rounded-md';
      case 'lg': return 'rounded-lg';
      case 'full': return 'rounded-full';
      default: return 'rounded-md';
    }
  };

  // تعيين نوع التأثير
  const getEffectStyles = () => {
    switch (effect) {
      case 'zoom':
        return 'group-hover:scale-110 transition-transform duration-500';
      case 'fade':
        return 'opacity-90 group-hover:opacity-100 transition-opacity duration-300';
      case 'blur':
        return 'blur-[2px] group-hover:blur-0 transition-all duration-300';
      case 'tilt':
        return '';
      case 'shine':
        return 'relative';
      default:
        return '';
    }
  };

  // تعيين نوع الاحتواء
  const getObjectFit = () => {
    switch (objectFit) {
      case 'cover': return 'object-cover';
      case 'contain': return 'object-contain';
      case 'fill': return 'object-fill';
      case 'none': return 'object-none';
      default: return 'object-cover';
    }
  };



  // تأثير العنصر النائب
  const renderPlaceholder = () => {
    if (!isLoaded) {
      switch (placeholder) {
        case 'blur':
          return (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
            </div>
          );
        case 'shimmer':
          return (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
          );
        default:
          return null;
      }
    }
    return null;
  };

  // تحسين تجربة المستخدم عند تحميل الصور
  const handleImageLoad = () => {
    setIsLoaded(true);
    // تأخير إظهار الصورة قليلاً لتجنب الوميض
    if (progressive) {
      setTimeout(() => {
        const imageElement = document.getElementById(`enhanced-image-${src.split('/').pop()}`);
        if (imageElement) {
          imageElement.classList.remove('opacity-0');
          imageElement.classList.add('opacity-100');
        }
      }, 50);
    }
  };

  // معالجة خطأ تحميل الصورة
  const handleImageError = () => {
    setHasError(true);
    if (onError) onError();
    console.warn(`Failed to load image: ${src}`);
  };

  // استخدام صورة احتياطية في حالة الخطأ
  const fallbackSrc = hasError ?
    `/images/placeholder-${isDarkMode ? 'dark' : 'light'}.svg` :
    src;

  // تحميل الصورة مسبقًا للتحقق من صحتها
  const preloadImage = (url: string) => {
    if (typeof window !== 'undefined' && !hasError) {
      const img = document.createElement('img');
      img.src = url;
      img.onerror = handleImageError;
    }
  };

  // تحميل الصورة مسبقًا إذا كانت ذات أولوية
  if (priority && typeof window !== 'undefined') {
    preloadImage(src);
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden group',
        getAspectRatio(),
        getRoundedSize(),
        isDarkMode ? 'bg-slate-800' : 'bg-slate-100',
        onClick && 'cursor-pointer',
        containerClassName
      )}
      onClick={onClick}
    >
      {/* الصورة الرئيسية */}
      <Image
        id={`enhanced-image-${src.split('/').pop()}`}
        src={fallbackSrc}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          getObjectFit(),
          getEffectStyles(),
          'transition-all duration-300',
          !isLoaded && progressive ? 'opacity-0' : 'opacity-100',
          className
        )}
        priority={priority}
        quality={quality}
        loading={priority ? 'eager' : loading}
        fill={fill}
        sizes={sizes || (fill ? '100vw' : undefined)}
        onLoad={handleImageLoad}
        onError={handleImageError}
        unoptimized={hasError} // عدم تحسين الصور الاحتياطية
      />

      {/* تأثير التحميل */}
      {renderPlaceholder()}
    </div>
  );
}


