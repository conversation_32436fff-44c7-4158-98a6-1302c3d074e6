import { ReactNode } from 'react';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';
import { SmoothTransition } from './animations/SmoothTransition';
import { Card } from './Card';

interface FeatureCardProps {
  icon?: ReactNode;
  title: string;
  description: string;
  className?: string;
  iconClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  animated?: boolean;
  animationDelay?: number;
  variant?: 'default' | 'bordered' | 'gradient' | 'minimal';
  onClick?: () => void;
}

export function FeatureCard({
  icon,
  title,
  description,
  className,
  iconClassName,
  titleClassName,
  descriptionClassName,
  animated = true,
  animationDelay = 0,
  variant = 'default',
  onClick,
}: FeatureCardProps) {
  const { isDarkMode } = useThemeStore();

  const getVariantStyles = () => {
    switch (variant) {
      case 'bordered':
        return cn(
          'border-2',
          isDarkMode
            ? 'border-slate-800 hover:border-primary-600'
            : 'border-slate-200 hover:border-primary-500',
          'transition-colors duration-300'
        );
      case 'gradient':
        return cn(
          'bg-gradient-to-br',
          isDarkMode
            ? 'from-slate-900 to-slate-800 border-slate-800'
            : 'from-white to-slate-50 border-slate-200',
          'hover:shadow-lg transition-all duration-300'
        );
      case 'minimal':
        return cn(
          'border-0 shadow-none',
          isDarkMode
            ? 'bg-transparent hover:bg-slate-900/50'
            : 'bg-transparent hover:bg-slate-50',
          'transition-colors duration-300'
        );
      default:
        return '';
    }
  };

  const content = (
    <Card
      className={cn(
        'p-6 h-full',
        getVariantStyles(),
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {icon && (
        <div className={cn(
          'mb-4 flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 text-white',
          iconClassName
        )}>
          {icon}
        </div>
      )}
      
      <h3 className={cn(
        'text-xl font-semibold mb-2',
        isDarkMode ? 'text-white' : 'text-slate-900',
        titleClassName
      )}>
        {title}
      </h3>
      
      <p className={cn(
        'text-sm',
        isDarkMode ? 'text-slate-300' : 'text-slate-600',
        descriptionClassName
      )}>
        {description}
      </p>
    </Card>
  );

  if (animated) {
    return (
      <SmoothTransition type="scale" delay={animationDelay} duration={0.4}>
        {content}
      </SmoothTransition>
    );
  }

  return content;
}
