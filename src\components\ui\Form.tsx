'use client';

import React, { createContext, useContext, useState, ReactNode, FormEvent } from 'react';
import { cn } from '../../lib/utils';

// تعريف نوع البيانات للحقول
export type FormFieldType = {
  name: string;
  value: any;
  error?: string;
  touched?: boolean;
  required?: boolean;
  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];
};

// تعريف سياق النموذج
type FormContextType = {
  values: Record<string, any>;
  errors: Record<string, string | undefined>;
  touched: Record<string, boolean>;
  setFieldValue: (name: string, value: any) => void;
  setFieldError: (name: string, error?: string) => void;
  setFieldTouched: (name: string, touched?: boolean) => void;
  registerField: (field: FormFieldType) => void;
  unregisterField: (name: string) => void;
  validateField: (name: string) => boolean;
  validateForm: () => boolean;
  resetForm: () => void;
  submitForm: () => void;
  isSubmitting: boolean;
};

// إنشاء سياق النموذج
const FormContext = createContext<FormContextType | undefined>(undefined);

// Hook لاستخدام سياق النموذج
export const useForm = () => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useForm must be used within a FormProvider');
  }
  return context;
};

// خصائص مكون النموذج
interface FormProps {
  children: ReactNode;
  initialValues?: Record<string, any>;
  onSubmit?: (values: Record<string, any>) => void | Promise<void>;
  onReset?: () => void;
  className?: string;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
}

// مكون النموذج
export function Form({
  children,
  initialValues = {},
  onSubmit,
  onReset,
  className,
  validateOnChange = true,
  validateOnBlur = true,
  validateOnSubmit = true,
}: FormProps) {
  const [values, setValues] = useState<Record<string, any>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [fields, setFields] = useState<Record<string, FormFieldType>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تسجيل حقل جديد
  const registerField = (field: FormFieldType) => {
    setFields(prev => ({
      ...prev,
      [field.name]: field
    }));

    // تعيين القيمة الأولية إذا لم تكن موجودة
    if (values[field.name] === undefined) {
      setFieldValue(field.name, field.value);
    }
  };

  // إلغاء تسجيل حقل
  const unregisterField = (name: string) => {
    setFields(prev => {
      const newFields = { ...prev };
      delete newFields[name];
      return newFields;
    });
  };

  // تعيين قيمة حقل
  const setFieldValue = (name: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));

    // التحقق من صحة الحقل عند التغيير إذا كان مطلوبًا
    if (validateOnChange && touched[name]) {
      validateField(name);
    }
  };

  // تعيين خطأ حقل
  const setFieldError = (name: string, error?: string) => {
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // تعيين حالة لمس الحقل
  const setFieldTouched = (name: string, isTouched: boolean = true) => {
    setTouched(prev => ({
      ...prev,
      [name]: isTouched
    }));

    // التحقق من صحة الحقل عند فقدان التركيز إذا كان مطلوبًا
    if (validateOnBlur && isTouched) {
      validateField(name);
    }
  };

  // التحقق من صحة حقل
  const validateField = (name: string): boolean => {
    const field = fields[name];
    if (!field) return true;

    const value = values[name];
    let error: string | undefined;

    // التحقق من الحقل المطلوب
    if (field.required && (value === undefined || value === null || value === '')) {
      error = 'هذا الحقل مطلوب';
    }
    // تنفيذ المصادقات المخصصة
    else if (field.validators && field.validators.length > 0) {
      for (const validator of field.validators) {
        const validationError = validator(value, values);
        if (validationError) {
          error = validationError;
          break;
        }
      }
    }

    setFieldError(name, error);
    return !error;
  };

  // التحقق من صحة النموذج بالكامل
  const validateForm = (): boolean => {
    let isValid = true;

    // التحقق من جميع الحقول المسجلة
    Object.keys(fields).forEach(fieldName => {
      const fieldIsValid = validateField(fieldName);
      if (!fieldIsValid) {
        isValid = false;
      }

      // تعيين جميع الحقول كملموسة
      setFieldTouched(fieldName, true);
    });

    return isValid;
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});

    if (onReset) {
      onReset();
    }
  };

  // تقديم النموذج
  const submitForm = async () => {
    // التحقق من صحة النموذج قبل التقديم إذا كان مطلوبًا
    if (validateOnSubmit) {
      const isValid = validateForm();
      if (!isValid) return;
    }

    if (onSubmit) {
      setIsSubmitting(true);
      try {
        await onSubmit(values);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // معالج تقديم النموذج
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    submitForm();
  };

  // قيمة سياق النموذج
  const formContextValue: FormContextType = {
    values,
    errors,
    touched,
    setFieldValue,
    setFieldError,
    setFieldTouched,
    registerField,
    unregisterField,
    validateField,
    validateForm,
    resetForm,
    submitForm,
    isSubmitting
  };

  return (
    <FormContext.Provider value={formContextValue}>
      <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>
        {children}
      </form>
    </FormContext.Provider>
  );
}

// مكون حقل النموذج
interface FormFieldProps {
  name: string;
  children: ReactNode | ((props: {
    value: any;
    error?: string;
    touched?: boolean;
    onChange: (value: any) => void;
    onBlur: () => void;
  }) => ReactNode);
  initialValue?: any;
  required?: boolean;
  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];
}

export function FormField({
  name,
  children,
  initialValue = '',
  required = false,
  validators = [],
}: FormFieldProps) {
  const {
    values,
    errors,
    touched,
    setFieldValue,
    setFieldTouched,
    registerField,
    unregisterField
  } = useForm();

  // تسجيل الحقل عند التحميل وإلغاء التسجيل عند التفريغ
  React.useEffect(() => {
    registerField({
      name,
      value: initialValue,
      required,
      validators
    });

    return () => {
      unregisterField(name);
    };
  }, [name, initialValue, required, validators.length]);

  // معالجات الأحداث
  const handleChange = (value: any) => {
    setFieldValue(name, value);
  };

  const handleBlur = () => {
    setFieldTouched(name, true);
  };

  // تقديم الأطفال
  if (typeof children === 'function') {
    return children({
      value: values[name],
      error: errors[name],
      touched: touched[name],
      onChange: handleChange,
      onBlur: handleBlur
    });
  }

  return children;
}

// مكون زر تقديم النموذج
interface FormSubmitProps {
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function FormSubmit({ children, className, disabled }: FormSubmitProps) {
  const { isSubmitting } = useForm();

  // بدلاً من استخدام زر، نستخدم div لتجنب مشكلة تداخل الأزرار
  return (
    <div className={className}>
      {React.cloneElement(children as React.ReactElement, {
        type: "submit",
        disabled: disabled || isSubmitting
      })}
    </div>
  );
}

// مكون زر إعادة تعيين النموذج
interface FormResetProps {
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function FormReset({ children, className, disabled }: FormResetProps) {
  const { resetForm, isSubmitting } = useForm();

  // بدلاً من استخدام زر، نستخدم div لتجنب مشكلة تداخل الأزرار
  return (
    <div className={className}>
      {React.cloneElement(children as React.ReactElement, {
        type: "button",
        disabled: disabled || isSubmitting,
        onClick: resetForm
      })}
    </div>
  );
}
