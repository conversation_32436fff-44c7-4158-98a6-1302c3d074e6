'use client';

import { ReactNode } from 'react';
import { Input, InputProps } from './Input';
import { FormField } from './Form';
import { cn } from '../../lib/utils';

export interface FormInputProps extends Omit<InputProps, 'onChange' | 'onBlur' | 'value' | 'error'> {
  name: string;
  label?: string;
  initialValue?: string;
  required?: boolean;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];
}

export function FormInput({
  name,
  label,
  initialValue = '',
  required = false,
  helperText,
  leftIcon,
  rightIcon,
  validators = [],
  ...props
}: FormInputProps) {
  return (
    <FormField
      name={name}
      initialValue={initialValue}
      required={required}
      validators={validators}
    >
      {({ value, error, onChange, onBlur }) => (
        <Input
          id={name}
          name={name}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          error={error}
          label={label}
          helperText={!error ? helperText : undefined}
          leftIcon={leftIcon}
          rightIcon={rightIcon}
          required={required}
          {...props}
        />
      )}
    </FormField>
  );
}

// مصادقات شائعة الاستخدام
export const validators = {
  // التحقق من البريد الإلكتروني
  email: (value: string) => {
    if (!value) return undefined;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? undefined : 'يرجى إدخال بريد إلكتروني صالح';
  },

  // التحقق من الحد الأدنى للطول
  minLength: (min: number) => (value: string) => {
    if (!value) return undefined;
    return value.length >= min ? undefined : `يجب أن يكون الطول ${min} أحرف على الأقل`;
  },

  // التحقق من الحد الأقصى للطول
  maxLength: (max: number) => (value: string) => {
    if (!value) return undefined;
    return value.length <= max ? undefined : `يجب أن لا يتجاوز الطول ${max} أحرف`;
  },

  // التحقق من النطاق
  range: (min: number, max: number) => (value: number) => {
    if (value === undefined || value === null) return undefined;
    return value >= min && value <= max ? undefined : `يجب أن تكون القيمة بين ${min} و ${max}`;
  },

  // التحقق من تطابق الحقول
  matches: (matchField: string, message?: string) => (value: any, formValues: Record<string, any>) => {
    return value === formValues[matchField] ? undefined : message || 'الحقول غير متطابقة';
  },

  // التحقق من الرقم
  number: (value: any) => {
    if (value === undefined || value === null || value === '') return undefined;
    return !isNaN(Number(value)) ? undefined : 'يرجى إدخال رقم صالح';
  },

  // التحقق من الرقم الموجب
  positiveNumber: (value: any) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = Number(value);
    return !isNaN(num) && num > 0 ? undefined : 'يرجى إدخال رقم موجب';
  },

  // التحقق من رقم الهاتف
  phone: (value: string) => {
    if (!value) return undefined;
    const phoneRegex = /^\+?[0-9]{8,15}$/;
    return phoneRegex.test(value) ? undefined : 'يرجى إدخال رقم هاتف صالح';
  },

  // التحقق من كلمة المرور القوية
  strongPassword: (value: string) => {
    if (!value) return undefined;

    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    if (value.length < 8) {
      return 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    }

    if (!(hasUpperCase && hasLowerCase && hasNumbers)) {
      return 'يجب أن تحتوي كلمة المرور على أحرف كبيرة وصغيرة وأرقام';
    }

    if (!hasSpecialChar) {
      return 'يجب أن تحتوي كلمة المرور على حرف خاص واحد على الأقل';
    }

    return undefined;
  },

  // التحقق من URL
  url: (value: string) => {
    if (!value) return undefined;
    try {
      new URL(value);
      return undefined;
    } catch {
      return 'يرجى إدخال رابط صالح';
    }
  }
};
