import { LabelHTMLAttributes, forwardRef } from 'react';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';

export interface FormLabelProps extends LabelHTMLAttributes<HTMLLabelElement> {
  /** نص التسمية */
  children: React.ReactNode;
  /** هل الحقل إلزامي */
  required?: boolean;
  /** رسالة توضيحية إضافية */
  hint?: string;
}

export const FormLabel = forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, children, required, hint, ...props }, ref) => {
    const { isDarkMode } = useThemeStore();

    return (
      <div className="flex flex-col space-y-1.5">
        <label
          ref={ref}
          className={cn(
            'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
            isDarkMode ? 'text-slate-200' : 'text-slate-900',
            className
          )}
          {...props}
        >
          {children}
          {required && <span className="text-error-500 mr-1">*</span>}
        </label>
        {hint && (
          <p className={cn(
            'text-xs',
            isDarkMode ? 'text-slate-400' : 'text-slate-500'
          )}>
            {hint}
          </p>
        )}
      </div>
    );
  }
);

FormLabel.displayName = 'FormLabel';
