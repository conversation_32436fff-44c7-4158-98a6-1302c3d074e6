import { ButtonHTMLAttributes, forwardRef, ReactNode } from 'react';
import Link from 'next/link';
import { cn } from '../../lib/utils';
import { motion } from 'framer-motion';

export interface HeroButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  href?: string; // استخدام href بدلاً من to لتوافق أفضل مع Next.js
  children: ReactNode;
}

const HeroButton = forwardRef<HTMLButtonElement, HeroButtonProps>(
  ({ className, variant = 'primary', size = 'lg', isLoading, children, disabled, href, ...props }, ref) => {
    const baseStyles = 'group inline-flex items-center justify-center font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none rounded-md relative overflow-hidden';

    const variants = {
      primary: 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus-visible:ring-primary-500 shadow-lg hover:shadow-xl',
      secondary: 'bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus-visible:ring-secondary-500 shadow-lg hover:shadow-xl',
      accent: 'bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 focus-visible:ring-accent-500 shadow-lg hover:shadow-xl',
      outline: 'border-2 border-white bg-transparent text-white backdrop-blur-sm hover:bg-white/10 focus-visible:ring-white shadow-lg hover:shadow-xl',
    };

    const sizes = {
      sm: 'h-9 px-3 text-xs',
      md: 'h-10 px-4 text-sm',
      lg: 'h-12 px-8 text-lg',
    };

    const classNames = cn(
      baseStyles,
      variants[variant],
      sizes[size],
      isLoading && 'opacity-70',
      className
    );

    // تحديد محتوى الزر
    const buttonContent = (
      <>
        {isLoading ? (
          <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent relative z-10" />
        ) : null}

        {typeof children === 'string' ? (
          <span className="relative z-10">{children}</span>
        ) : (
          <span className="relative z-10">{children}</span>
        )}

        {/* تأثير التحويم للأزرار الملونة - تم إصلاح مشكلة اختفاء النص */}
        {(variant === 'primary' || variant === 'secondary' || variant === 'accent') && (
          <span className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
        )}
      </>
    );

    // إذا كان هناك href، استخدم Link
    if (href) {
      return (
        <motion.div whileTap={{ scale: 0.98 }}>
          <Link
            href={href}
            className={classNames}
            {...props}
          >
            {buttonContent}
          </Link>
        </motion.div>
      );
    }

    // استخدم button إذا لم يكن هناك href
    return (
      <motion.div whileTap={{ scale: 0.98 }}>
        <button
          ref={ref}
          className={classNames}
          disabled={disabled || isLoading}
          {...props}
        >
          {buttonContent}
        </button>
      </motion.div>
    );
  }
);

HeroButton.displayName = 'HeroButton';

export { HeroButton };
