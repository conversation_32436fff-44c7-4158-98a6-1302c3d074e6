import { ButtonHTMLAttributes, forwardRef, ElementType, ReactNode } from 'react';
import Link from 'next/link';
import { cn } from '../../lib/utils';
import { motion } from 'framer-motion';
import { VisuallyHidden } from './VisuallyHidden';

export interface IconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon: ReactNode;
  isLoading?: boolean;
  as?: ElementType;
  to?: string;
  label: string;
  showLabel?: boolean;
  labelPosition?: 'left' | 'right' | 'bottom';
}

const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    icon,
    isLoading,
    children,
    disabled,
    as,
    to,
    label,
    showLabel = false,
    labelPosition = 'right',
    ...props
  }, ref) => {
    const baseStyles = 'group inline-flex items-center justify-center font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none rounded-md relative overflow-hidden';

    const variants = {
      primary: 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus-visible:ring-primary-500 shadow-md hover:shadow-lg',
      secondary: 'bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus-visible:ring-secondary-500 shadow-md hover:shadow-lg',
      accent: 'bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 focus-visible:ring-accent-500 shadow-md hover:shadow-lg',
      outline: 'border-2 border-slate-300 bg-transparent hover:border-primary-500 hover:text-primary-600 focus-visible:ring-primary-500 dark:border-slate-600 dark:hover:border-primary-400 dark:hover:text-primary-400',
      ghost: 'bg-transparent hover:bg-slate-100 focus-visible:ring-slate-500 dark:hover:bg-slate-800',
    };

    const sizes = {
      sm: showLabel ? 'h-8 px-2 text-xs' : 'h-8 w-8 p-1.5 text-xs',
      md: showLabel ? 'h-10 px-3 text-sm' : 'h-10 w-10 p-2 text-sm',
      lg: showLabel ? 'h-12 px-4 text-base' : 'h-12 w-12 p-2.5 text-base',
    };

    const classNames = cn(
      baseStyles,
      variants[variant],
      sizes[size],
      isLoading && 'opacity-70',
      className
    );

    // تحديد محتوى الزر
    const buttonContent = (
      <>
        {isLoading ? (
          <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        ) : (
          <div className={cn(
            'flex',
            showLabel && labelPosition === 'left' && 'flex-row-reverse',
            showLabel && labelPosition === 'bottom' && 'flex-col gap-1',
            showLabel && labelPosition !== 'bottom' && 'items-center gap-2'
          )}>
            <span className="relative z-10">{icon}</span>
            
            {showLabel && (
              <span className="relative z-10 whitespace-nowrap">{label}</span>
            )}
            
            {!showLabel && (
              <VisuallyHidden>{label}</VisuallyHidden>
            )}
          </div>
        )}

        {/* تأثير التحويم للأزرار الملونة - تم إصلاح مشكلة اختفاء النص */}
        {(variant === 'primary' || variant === 'secondary' || variant === 'accent') && (
          <span className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
        )}
      </>
    );

    // If 'as' prop is provided, render the component as that element type
    if (as === Link && to) {
      return (
        <motion.div whileTap={{ scale: 0.95 }}>
          <Link
            href={to}
            className={classNames}
          >
            {buttonContent}
          </Link>
        </motion.div>
      );
    }

    // Default to button
    return (
      <motion.div whileTap={{ scale: 0.95 }}>
        <button
          ref={ref}
          className={classNames}
          disabled={disabled || isLoading}
          aria-label={label}
          {...props}
        >
          {buttonContent}
        </button>
      </motion.div>
    );
  }
);

IconButton.displayName = 'IconButton';

export { IconButton };
