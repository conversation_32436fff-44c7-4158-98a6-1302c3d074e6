import { InputHTMLAttributes, forwardRef, ReactNode } from 'react';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';
import { useTheme } from 'next-themes';

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    error,
    label,
    helperText,
    leftIcon,
    rightIcon,
    fullWidth = true,
    variant = 'default',
    size = 'md',
    ...props
  }, ref) => {
    const { isDarkMode } = useThemeStore();
    const { resolvedTheme } = useTheme();
    const isDark = resolvedTheme === 'dark' || isDarkMode;

    // تحديد أحجام الحقل
    const sizeClasses = {
      sm: 'h-8 text-xs px-2 py-1',
      md: 'h-10 text-sm px-3 py-2',
      lg: 'h-12 text-base px-4 py-3',
    };

    // تحديد أنماط الحقل
    const variantClasses = {
      default: cn(
        'border rounded-md',
        isDark
          ? 'border-slate-700 bg-slate-800 text-white placeholder:text-slate-500'
          : 'border-slate-300 bg-white text-slate-900 placeholder:text-slate-400'
      ),
      filled: cn(
        'border-b-2 rounded-t-md',
        isDark
          ? 'border-slate-600 bg-slate-700/50 text-white placeholder:text-slate-400'
          : 'border-slate-300 bg-slate-100 text-slate-900 placeholder:text-slate-500'
      ),
      outlined: cn(
        'bg-transparent border-2 rounded-md',
        isDark
          ? 'border-slate-600 text-white placeholder:text-slate-500'
          : 'border-slate-300 text-slate-900 placeholder:text-slate-400'
      ),
    };

    // تحديد أنماط الأيقونات
    const iconSizeClasses = {
      sm: 'h-3.5 w-3.5',
      md: 'h-4 w-4',
      lg: 'h-5 w-5',
    };

    // تحديد المسافة البادئة للحقل عند وجود أيقونة
    const paddingWithIcon = {
      left: leftIcon ? (size === 'sm' ? 'pl-7' : size === 'md' ? 'pl-9' : 'pl-11') : '',
      right: rightIcon ? (size === 'sm' ? 'pr-7' : size === 'md' ? 'pr-9' : 'pr-11') : '',
    };

    return (
      <div className={cn("relative", fullWidth ? "w-full" : "w-auto")}>
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              "block text-sm font-medium mb-1.5",
              isDark ? "text-slate-300" : "text-slate-700",
              error && "text-error-500 dark:text-error-400"
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className={cn(
              "absolute left-0 inset-y-0 flex items-center justify-center",
              size === 'sm' ? 'pl-2' : size === 'md' ? 'pl-3' : 'pl-4'
            )}>
              <span className={cn(
                "text-slate-400",
                iconSizeClasses[size]
              )}>
                {leftIcon}
              </span>
            </div>
          )}

          <input
            ref={ref}
            className={cn(
              'w-full file:border-0 file:bg-transparent file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200',
              sizeClasses[size],
              variantClasses[variant],
              paddingWithIcon.left,
              paddingWithIcon.right,
              error && 'border-error-500 focus-visible:ring-error-500',
              className
            )}
            {...props}
          />

          {rightIcon && (
            <div className={cn(
              "absolute right-0 inset-y-0 flex items-center justify-center",
              size === 'sm' ? 'pr-2' : size === 'md' ? 'pr-3' : 'pr-4'
            )}>
              <span className={cn(
                "text-slate-400",
                iconSizeClasses[size]
              )}>
                {rightIcon}
              </span>
            </div>
          )}
        </div>

        {(error || helperText) && (
          <p className={cn(
            "mt-1.5 text-xs",
            error
              ? "text-error-500 dark:text-error-400"
              : isDark ? "text-slate-400" : "text-slate-500"
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };