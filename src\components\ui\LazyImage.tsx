import { useState, useEffect, useRef, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';

interface LazyImageProps extends React.HTMLAttributes<HTMLDivElement> {
  src: string;
  alt: string;
  className?: string;
  imgClassName?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  placeholderColor?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  blur?: boolean;
  animation?: 'fade' | 'zoom' | 'slide' | 'none';
  aspectRatio?: string;
  loading?: 'lazy' | 'eager';
  sizes?: string;
  srcSet?: string;
  fallbackSrc?: string;
  onLoadingComplete?: () => void;
  lowResSrc?: string; // مصدر الصورة منخفضة الدقة للتحميل التدريجي
  blurhash?: string; // رمز Blurhash للصورة
  progressive?: boolean; // تفعيل التحميل التدريجي
}

export const LazyImage = forwardRef<HTMLDivElement, LazyImageProps>(({
  src,
  alt,
  className = '',
  imgClassName = '',
  objectFit = 'cover',
  placeholderColor,
  width,
  height,
  priority = false,
  blur = true,
  animation = 'fade',
  aspectRatio,
  loading = 'lazy',
  sizes,
  srcSet,
  fallbackSrc,
  onLoadingComplete,
  lowResSrc,
  blurhash,
  progressive = false,
  ...props
}, ref) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [error, setError] = useState(false);
  const [isLowResLoaded, setIsLowResLoaded] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);
  const { isDarkMode } = useThemeStore();

  // تحديد لون العنصر النائب بناءً على الوضع المظلم
  const defaultPlaceholderColor = isDarkMode ? 'bg-slate-800' : 'bg-slate-200';
  const actualPlaceholderColor = placeholderColor || defaultPlaceholderColor;

  useEffect(() => {
    // إذا كانت الصورة ذات أولوية، قم بتحميلها على الفور
    if (priority) {
      setIsInView(true);
      return;
    }

    // إنشاء مراقب التقاطع لتحميل الصورة عندما تكون في العرض
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    }, {
      rootMargin: '200px', // بدء تحميل الصور قبل 200 بكسل من دخولها إلى العرض
      threshold: 0.01
    });

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [priority]);

  // معالجة حدث تحميل الصورة
  const handleImageLoad = () => {
    setIsLoaded(true);
    if (onLoadingComplete) {
      onLoadingComplete();
    }
  };

  // معالجة حدث خطأ الصورة
  const handleImageError = () => {
    setError(true);
    if (fallbackSrc) {
      // استخدام الصورة البديلة في حالة الخطأ
      console.warn(`Failed to load image: ${src}, using fallback image instead.`);
    } else {
      console.error(`Failed to load image: ${src}`);
    }
  };

  // تحديد فئة object-fit
  const objectFitClass = `object-${objectFit}`;

  // تحديد رسوم متحركة للصورة
  const getAnimationProps = () => {
    switch (animation) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          transition: { duration: 0.5 }
        };
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.9 },
          animate: { opacity: 1, scale: 1 },
          transition: { duration: 0.5 }
        };
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          transition: { duration: 0.5 }
        };
      case 'none':
      default:
        return {};
    }
  };

  // تحديد نسبة العرض إلى الارتفاع
  const aspectRatioStyle = aspectRatio ? { aspectRatio } : {};

  // معالجة تحميل الصورة منخفضة الدقة
  const handleLowResImageLoad = () => {
    setIsLowResLoaded(true);
  };

  // إنشاء عنصر Blurhash إذا كان متوفرًا
  const renderBlurhash = () => {
    if (blurhash) {
      return (
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(data:image/svg+xml;base64,${blurhash})`,
            opacity: isLoaded ? 0 : 1,
            transition: 'opacity 0.5s ease-in-out'
          }}
        />
      );
    }
    return null;
  };

  return (
    <div
      ref={(node) => {
        // دمج المراجع
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
        if (imgRef.current !== node) {
          imgRef.current = node;
        }
      }}
      className={cn('relative overflow-hidden', className)}
      style={{
        ...aspectRatioStyle,
        width: width ? `${width}px` : undefined,
        height: height ? `${height}px` : undefined,
      }}
      {...props}
    >
      <AnimatePresence>
        {isInView && (
          <>
            {/* الصورة الرئيسية عالية الدقة */}
            <motion.img
              key="image"
              src={error && fallbackSrc ? fallbackSrc : src}
              alt={alt}
              width={width}
              height={height}
              loading={loading}
              sizes={sizes}
              srcSet={srcSet}
              className={cn(
                'w-full h-full transition-opacity duration-500',
                objectFitClass,
                isLoaded ? 'opacity-100' : 'opacity-0',
                imgClassName
              )}
              onLoad={handleImageLoad}
              onError={handleImageError}
              {...getAnimationProps()}
              style={{ zIndex: 20 }}
            />

            {/* الصورة منخفضة الدقة للتحميل التدريجي */}
            {progressive && lowResSrc && !isLoaded && (
              <motion.img
                key="lowres-image"
                src={lowResSrc}
                alt={alt}
                width={width}
                height={height}
                className={cn(
                  'absolute inset-0 w-full h-full transition-opacity duration-500',
                  objectFitClass,
                  isLowResLoaded ? 'opacity-100 blur-sm' : 'opacity-0',
                  imgClassName
                )}
                onLoad={handleLowResImageLoad}
                style={{ zIndex: 10 }}
              />
            )}

            {/* عنصر Blurhash إذا كان متوفرًا */}
            {blurhash && renderBlurhash()}

            {/* العنصر النائب أثناء التحميل */}
            {!isLoaded && !isLowResLoaded && blur && !blurhash && (
              <motion.div
                key="placeholder"
                className={cn(
                  'absolute inset-0 animate-pulse',
                  actualPlaceholderColor
                )}
                initial={{ opacity: 1 }}
                animate={{ opacity: isLoaded ? 0 : 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                style={{ zIndex: 5 }}
              />
            )}
          </>
        )}

        {!isInView && (
          <div className={cn(
            'w-full h-full animate-pulse',
            actualPlaceholderColor
          )} />
        )}
      </AnimatePresence>
    </div>
  );
});

LazyImage.displayName = 'LazyImage';
