import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useThemeStore } from '../../stores/themeStore';
import { NavigationItem } from '../../types';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navigation: NavigationItem[];
}

export function MobileMenu({ isOpen, onClose, navigation }: MobileMenuProps) {
  const location = useLocation();
  const { isDarkMode } = useThemeStore();
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

  // Close the menu when the route changes
  useEffect(() => {
    if (isOpen) {
      onClose();
    }
  }, [location.pathname, isOpen, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const toggleSubmenu = (label: string) => {
    setOpenSubmenu(openSubmenu === label ? null : label);
  };

  const menuVariants = {
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    closed: { opacity: 0, x: -20 },
    open: { opacity: 1, x: 0 },
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Menu */}
          <motion.div
            className={cn(
              "fixed top-0 left-0 bottom-0 w-[280px] z-50 overflow-y-auto",
              isDarkMode ? "bg-slate-900" : "bg-white"
            )}
            variants={menuVariants}
            initial="closed"
            animate="open"
            exit="closed"
          >
            <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-800">
              <Link to="/" className="flex items-center space-x-2" onClick={onClose}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary-500"
                >
                  <path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"></path>
                  <path d="M8 6h8"></path>
                  <path d="M8 10h8"></path>
                  <path d="M8 14h4"></path>
                </svg>
                <span className="font-bold text-xl">CommercePro</span>
              </Link>
              <button
                onClick={onClose}
                className={cn(
                  "p-2 rounded-full",
                  isDarkMode 
                    ? "hover:bg-slate-800 text-slate-400 hover:text-white" 
                    : "hover:bg-slate-100 text-slate-500 hover:text-slate-900"
                )}
                aria-label="Close menu"
              >
                <X size={20} />
              </button>
            </div>

            <nav className="p-4">
              <ul className="space-y-1">
                {navigation.map((item) => (
                  <motion.li key={item.label} variants={itemVariants}>
                    {item.children ? (
                      <div>
                        <button
                          onClick={() => toggleSubmenu(item.label)}
                          className={cn(
                            "flex items-center justify-between w-full p-3 rounded-md transition-colors",
                            isDarkMode
                              ? "hover:bg-slate-800 text-slate-200"
                              : "hover:bg-slate-100 text-slate-900",
                            openSubmenu === item.label && (
                              isDarkMode ? "bg-slate-800" : "bg-slate-100"
                            )
                          )}
                        >
                          <span>{item.label}</span>
                          {openSubmenu === item.label ? (
                            <ChevronDown size={18} />
                          ) : (
                            <ChevronRight size={18} />
                          )}
                        </button>
                        <AnimatePresence>
                          {openSubmenu === item.label && (
                            <motion.ul
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: 'auto', opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              transition={{ duration: 0.2 }}
                              className="ml-4 mt-1 space-y-1 overflow-hidden"
                            >
                              {item.children.map((child) => (
                                <motion.li key={child.label} variants={itemVariants}>
                                  <Link
                                    to={child.href}
                                    className={cn(
                                      "block p-3 rounded-md transition-colors",
                                      isDarkMode
                                        ? "hover:bg-slate-800 text-slate-300"
                                        : "hover:bg-slate-100 text-slate-700",
                                      location.pathname === child.href && (
                                        isDarkMode 
                                          ? "bg-slate-800 text-primary-400" 
                                          : "bg-slate-100 text-primary-600"
                                      )
                                    )}
                                    onClick={onClose}
                                  >
                                    {child.label}
                                  </Link>
                                </motion.li>
                              ))}
                            </motion.ul>
                          )}
                        </AnimatePresence>
                      </div>
                    ) : (
                      <Link
                        to={item.href}
                        className={cn(
                          "block p-3 rounded-md transition-colors",
                          isDarkMode
                            ? "hover:bg-slate-800 text-slate-200"
                            : "hover:bg-slate-100 text-slate-900",
                          location.pathname === item.href && (
                            isDarkMode 
                              ? "bg-slate-800 text-primary-400" 
                              : "bg-slate-100 text-primary-600"
                          )
                        )}
                        onClick={onClose}
                      >
                        {item.label}
                      </Link>
                    )}
                  </motion.li>
                ))}
              </ul>
            </nav>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
