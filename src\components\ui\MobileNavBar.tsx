'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Home, ShoppingBag, Layers, Briefcase, Phone, Menu, X, Heart, ShoppingCart, User } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useTheme } from 'next-themes';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';

interface MobileNavBarProps {
  className?: string;
}

export function MobileNavBar({ className }: MobileNavBarProps) {
  const pathname = usePathname();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { language } = useLanguageStore();
  const { items: cartItems } = useCartStore();
  const { items: wishlistItems } = useWishlistStore();
  const [isVisible, setIsVisible] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  // التحقق من حالة التمرير لإظهار/إخفاء شريط التنقل
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // إظهار شريط التنقل عند التمرير لأعلى أو عند الوصول إلى أعلى الصفحة
      if (currentScrollY < lastScrollY || currentScrollY < 50) {
        setIsVisible(true);
      }
      // إخفاء شريط التنقل عند التمرير لأسفل
      else if (currentScrollY > 100 && currentScrollY > lastScrollY) {
        setIsVisible(false);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollY]);

  // قائمة عناصر التنقل حسب اللغة
  const navItems = language === 'ar' ? [
    { path: `/${language}`, label: 'الرئيسية', icon: <Home size={20} /> },
    { path: `/${language}/shop`, label: 'المتجر', icon: <ShoppingBag size={20} /> },
    { path: `/${language}/production-lines`, label: 'خطوط الإنتاج', icon: <Layers size={20} /> },
    { path: `/${language}/services`, label: 'الخدمات', icon: <Briefcase size={20} /> },
    { path: `/${language}/contact`, label: 'اتصل بنا', icon: <Phone size={20} /> },
  ] : [
    { path: `/${language}`, label: 'Home', icon: <Home size={20} /> },
    { path: `/${language}/shop`, label: 'Shop', icon: <ShoppingBag size={20} /> },
    { path: `/${language}/production-lines`, label: 'Production Lines', icon: <Layers size={20} /> },
    { path: `/${language}/services`, label: 'Services', icon: <Briefcase size={20} /> },
    { path: `/${language}/contact`, label: 'Contact', icon: <Phone size={20} /> },
  ];
  const isActive = (path: string) => {
    if (pathname === null) {
      return false;
    }
    if (path === `/${language}`) {
      return pathname === `/${language}`;
    }
    return pathname.startsWith(path);
  };

  return (
    <>
      {isVisible && (
        <div
          className={cn(
            'fixed bottom-0 left-0 right-0 z-50 lg:hidden animate-slide-up',
            currentIsDark ? 'bg-slate-900 border-t border-slate-800' : 'bg-white border-t border-slate-200',
            'shadow-lg',
            className
          )}
        >
          <div className="flex items-center justify-around h-16">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={cn(
                  'flex flex-col items-center justify-center w-full h-full text-xs font-medium transition-colors',
                  'relative py-2 px-1',
                  'active:bg-slate-100 dark:active:bg-slate-800',
                  isActive(item.path)
                    ? currentIsDark
                      ? 'text-primary-400'
                      : 'text-primary-600'
                    : currentIsDark
                      ? 'text-slate-400 hover:text-slate-300'
                      : 'text-slate-600 hover:text-slate-900'
                )}
              >
                <div className={cn(
                  "mb-1 p-1.5 rounded-full",
                  isActive(item.path) && (currentIsDark ? 'bg-primary-900/30' : 'bg-primary-50')
                )}>
                  {item.icon}
                </div>
                <span>{item.label}</span>
                {isActive(item.path) && (
                  <div
                    className={cn(
                      'absolute bottom-0 h-1 w-12 rounded-t-full transition-all duration-300',
                      currentIsDark ? 'bg-primary-400' : 'bg-primary-600'
                    )}
                  />
                )}
              </Link>
            ))}
          </div>

          {/* زر السلة والمفضلة */}
          <div className="absolute top-0 right-0 transform -translate-y-full flex">
            <Link
              href={`/${language}/cart`}
              className={cn(
                'relative p-3 rounded-tl-lg',
                currentIsDark ? 'bg-slate-900 text-white' : 'bg-white text-slate-900'
              )}
            >
              <ShoppingCart size={20} />
              {cartItems.length > 0 && (
                <span className="absolute top-1 right-1 flex items-center justify-center w-4 h-4 text-xs text-white bg-primary-500 rounded-full">
                  {cartItems.length}
                </span>
              )}
            </Link>
            <Link
              href={`/${language}/shop/wishlist`}
              className={cn(
                'relative p-3 rounded-tr-lg',
                currentIsDark ? 'bg-slate-900 text-white' : 'bg-white text-slate-900'
              )}
            >
              <Heart size={20} />
              {wishlistItems.length > 0 && (
                <span className="absolute top-1 right-1 flex items-center justify-center w-4 h-4 text-xs text-white bg-primary-500 rounded-full">
                  {wishlistItems.length}
                </span>
              )}
            </Link>
          </div>
        </div>
      )}
    </>
  );
}
