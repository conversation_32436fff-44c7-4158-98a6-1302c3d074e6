'use client';

import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';

/**
 * مكون لتحسين تجربة المستخدم على الأجهزة المحمولة
 * يقوم بتطبيق تحسينات مختلفة على الأجهزة المحمولة
 */
export function MobileOptimizer() {
  const [isMobile, setIsMobile] = useState(false);
  const { resolvedTheme } = useTheme();

  // التحقق مما إذا كان الجهاز محمولاً
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // التحقق عند التحميل
    checkMobile();

    // التحقق عند تغيير حجم النافذة
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // تطبيق تحسينات الأجهزة المحمولة
  useEffect(() => {
    if (isMobile) {
      // تحسين الأداء على الأجهزة المحمولة

      // 1. تعطيل بعض الرسوم المتحركة الثقيلة
      document.documentElement.classList.add('mobile-device');

      // 2. تحسين حجم الخط للقراءة على الأجهزة المحمولة
      document.documentElement.style.fontSize = resolvedTheme === 'dark' ? '15px' : '16px';

      // 3. تحسين التمرير السلس
      document.documentElement.style.scrollBehavior = 'smooth';

      // 4. تحسين اللمس (زيادة مساحة النقر)
      const style = document.createElement('style');
      style.id = 'mobile-optimizations';
      style.innerHTML = `
        .mobile-device button,
        .mobile-device a,
        .mobile-device input[type="button"],
        .mobile-device input[type="submit"] {
          min-height: 44px;
          min-width: 44px;
        }

        .mobile-device .tap-target {
          padding: 0.5rem;
        }

        /* تحسين أحجام الخطوط للأجهزة المحمولة */
        .mobile-device h1 {
          font-size: 1.75rem !important;
          line-height: 1.2 !important;
        }

        .mobile-device h2 {
          font-size: 1.5rem !important;
          line-height: 1.25 !important;
        }

        .mobile-device h3 {
          font-size: 1.25rem !important;
          line-height: 1.3 !important;
        }

        /* تحسين المسافات بين العناصر */
        .mobile-device .container-custom {
          padding-left: 1rem !important;
          padding-right: 1rem !important;
        }

        /* تحسين أزرار التفاعل */
        .mobile-device button,
        .mobile-device .button {
          padding: 0.625rem 1rem !important;
          border-radius: 0.5rem !important;
          font-weight: 500 !important;
        }

        /* تحسين تجربة التمرير */
        .mobile-device {
          scroll-padding-top: 80px;
          -webkit-overflow-scrolling: touch;
        }

        @media (max-width: 767px) {
          .container-custom {
            padding-left: 1rem;
            padding-right: 1rem;
          }

          .section {
            padding-top: 2rem;
            padding-bottom: 2rem;
          }

          h1 {
            font-size: 2rem;
            line-height: 1.2;
          }

          h2 {
            font-size: 1.75rem;
            line-height: 1.2;
          }

          .mobile-spacing {
            margin-bottom: 1.5rem;
          }

          .mobile-stack {
            flex-direction: column;
          }

          .mobile-full-width {
            width: 100%;
          }

          .mobile-center {
            text-align: center;
          }

          .mobile-hidden {
            display: none;
          }

          .mobile-visible {
            display: block;
          }

          .mobile-touch-scroll {
            -webkit-overflow-scrolling: touch;
          }

          .mobile-no-scroll {
            overflow: hidden;
          }
        }
      `;

      document.head.appendChild(style);

      // 5. تحسين سرعة التحميل عن طريق تأخير تحميل الصور غير المرئية
      const lazyImages = document.querySelectorAll('img[loading="lazy"]');
      if (lazyImages.length === 0) {
        const images = document.querySelectorAll('img:not([loading])');
        images.forEach(img => {
          if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
          }
        });
      }

      return () => {
        // إزالة التحسينات عند تغيير الجهاز
        document.documentElement.classList.remove('mobile-device');
        document.documentElement.style.fontSize = '';
        document.documentElement.style.scrollBehavior = '';
        const mobileStyle = document.getElementById('mobile-optimizations');
        if (mobileStyle) {
          mobileStyle.remove();
        }
      };
    }
  }, [isMobile, resolvedTheme]);

  // هذا المكون لا يعرض أي شيء في واجهة المستخدم
  return null;
}
