'use client';

import { ReactNode, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { SmoothTransition } from './animations/SmoothTransition';

interface PageTransitionProps {
  children: ReactNode;
  type?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'none';
  duration?: number;
  disabled?: boolean;
}

export function PageTransition({
  children,
  type = 'none', // تغيير القيمة الافتراضية إلى 'none' لتعطيل الانتقالات على مستوى الصفحة
  duration = 0.5,
  disabled = true // تعطيل الانتقالات افتراضيًا
}: PageTransitionProps) {
  const pathname = usePathname();
  const [transitionType, setTransitionType] = useState(disabled ? 'none' : type);
  const [isMobile, setIsMobile] = useState(false);

  // التحقق مما إذا كان الجهاز محمولاً
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // التحقق عند التحميل
    checkMobile();

    // التحقق عند تغيير حجم النافذة
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // تعديل نوع الانتقال للأجهزة المحمولة
  useEffect(() => {
    if (disabled) {
      setTransitionType('none');
    } else if (isMobile) {
      // استخدام انتقالات أبسط على الأجهزة المحمولة لتحسين الأداء
      setTransitionType('fade');
    } else {
      setTransitionType(type);
    }
  }, [isMobile, type, disabled]);

  // إذا كانت الانتقالات معطلة، قم بإرجاع المحتوى مباشرة بدون تأثيرات
  if (disabled) {
    return <>{children}</>;
  }

  return (
    <SmoothTransition
      type={transitionType}
      direction="up"
      duration={duration}
      id={pathname ?? undefined}
    >
      {children}
    </SmoothTransition>
  );
}
