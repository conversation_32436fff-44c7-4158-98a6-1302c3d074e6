import { ReactNode } from 'react';
import { cn } from '../../lib/utils';
import { useTheme } from 'next-themes';
import { SmoothTransition } from './animations/SmoothTransition';

interface SectionTitleProps {
  title: string;
  subtitle?: string;
  align?: 'left' | 'center' | 'right';
  titleSize?: 'sm' | 'md' | 'lg' | 'xl';
  subtitleSize?: 'sm' | 'md' | 'lg';
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  children?: ReactNode;
  animated?: boolean;
  decorative?: boolean;
}

export function SectionTitle({
  title,
  subtitle,
  align = 'left',
  titleSize = 'lg',
  subtitleSize = 'md',
  className,
  titleClassName,
  subtitleClassName,
  children,
  animated = true,
  decorative = true,
}: SectionTitleProps) {
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';

  const alignments = {
    left: 'text-left',
    center: 'text-center mx-auto',
    right: 'text-right ml-auto',
  };

  const titleSizes = {
    sm: 'text-xl md:text-2xl',
    md: 'text-2xl md:text-3xl',
    lg: 'text-3xl md:text-4xl',
    xl: 'text-4xl md:text-5xl lg:text-6xl',
  };

  const subtitleSizes = {
    sm: 'text-sm md:text-base',
    md: 'text-base md:text-lg',
    lg: 'text-lg md:text-xl',
  };

  const content = (
    <div className={cn('mb-8 md:mb-12 relative', alignments[align], className)}>
      {decorative && (
        <div className={cn(
          'absolute -top-4 left-0 w-12 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full',
          align === 'center' && 'left-1/2 transform -translate-x-1/2',
          align === 'right' && 'left-auto right-0'
        )} />
      )}
      
      <h2 className={cn(
        'font-bold tracking-tight mb-3',
        titleSizes[titleSize],
        currentIsDark ? 'text-white' : 'text-slate-900',
        titleClassName
      )}>
        {title}
      </h2>
      
      {subtitle && (
        <p className={cn(
          'max-w-2xl',
          subtitleSizes[subtitleSize],
          currentIsDark ? 'text-slate-300' : 'text-slate-600',
          align === 'center' && 'mx-auto',
          align === 'right' && 'ml-auto',
          subtitleClassName
        )}>
          {subtitle}
        </p>
      )}
      
      {children && (
        <div className={cn(
          'mt-4',
          align === 'center' && 'flex justify-center',
          align === 'right' && 'flex justify-end'
        )}>
          {children}
        </div>
      )}
    </div>
  );

  if (animated) {
    return (
      <SmoothTransition type="slide" direction="up" duration={0.5}>
        {content}
      </SmoothTransition>
    );
  }

  return content;
}
