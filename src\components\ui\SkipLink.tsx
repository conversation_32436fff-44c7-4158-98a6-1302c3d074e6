import { cn } from '../../lib/utils';

interface SkipLinkProps {
  /** المكان المراد الانتقال إليه */
  href: string;
  /** النص المعروض */
  children: React.ReactNode;
  /** فئات CSS إضافية */
  className?: string;
}

/**
 * مكون لتخطي التنقل للمستخدمين الذين يستخدمون لوحة المفاتيح
 * يظهر فقط عند التركيز عليه باستخدام لوحة المفاتيح
 */
export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50',
        'bg-primary-500 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500',
        className
      )}
    >
      {children}
    </a>
  );
}
