import { useState, useEffect } from 'react';
import { <PERSON>, Sun, Monitor } from 'lucide-react';
import { useTheme } from 'next-themes';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button' | 'menu';
}

export function ThemeToggle({
  className = '',
  showLabel = false,
  size = 'md',
  variant = 'icon'
}: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isOpen]);

  if (!mounted) {
    return <div className={`w-10 h-10 ${className}`} />;
  }

  const currentIsDark = resolvedTheme === 'dark';

  const iconSize = {
    sm: 16,
    md: 20,
    lg: 24
  }[size];

  const buttonSize = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  }[size];

  if (variant === 'menu') {
    return (
      <div className={cn("relative", className)}>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className={cn(
            "flex items-center justify-center rounded-full transition-colors",
            currentIsDark
              ? 'bg-slate-800 text-slate-200 hover:bg-slate-700'
              : 'bg-slate-100 text-slate-900 hover:bg-slate-200',
            buttonSize
          )}
          aria-label="Toggle theme menu"
        >
          {currentIsDark ? (
            <Sun size={iconSize} />
          ) : (
            <Moon size={iconSize} />
          )}
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
              className={cn(
                "absolute right-0 mt-2 w-48 rounded-md shadow-lg z-50",
                currentIsDark ? "bg-slate-800 border border-slate-700" : "bg-white border border-slate-200"
              )}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="py-1">
                <button
                  onClick={() => {
                    setTheme('light');
                    setIsOpen(false);
                  }}
                  className={cn(
                    "flex items-center w-full px-4 py-2 text-sm",
                    theme === 'light' && !currentIsDark
                      ? "bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                      : "text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700"
                  )}
                >
                  <Sun size={16} className="mr-2" />
                  وضع النهار
                </button>
                <button
                  onClick={() => {
                    setTheme('dark');
                    setIsOpen(false);
                  }}
                  className={cn(
                    "flex items-center w-full px-4 py-2 text-sm",
                    theme === 'dark' && currentIsDark
                      ? "bg-primary-900 text-primary-300"
                      : "text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700"
                  )}
                >
                  <Moon size={16} className="mr-2" />
                  وضع الليل
                </button>
                <button
                  onClick={() => {
                    setTheme('system');
                    setIsOpen(false);
                  }}
                  className={cn(
                    "flex items-center w-full px-4 py-2 text-sm",
                    theme === 'system'
                      ? "bg-secondary-50 text-secondary-700 dark:bg-secondary-900 dark:text-secondary-300"
                      : "text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700"
                  )}
                >
                  <Monitor size={16} className="mr-2" />
                  تفضيل النظام
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  if (variant === 'button') {
    return (
      <button
        onClick={() => setTheme(currentIsDark ? 'light' : 'dark')}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-md transition-colors",
          currentIsDark
            ? 'bg-slate-800 text-slate-200 hover:bg-slate-700'
            : 'bg-slate-100 text-slate-900 hover:bg-slate-200',
          className
        )}
        aria-label={currentIsDark ? 'التبديل إلى وضع النهار' : 'التبديل إلى وضع الليل'}
      >
        {currentIsDark ? (
          <Sun size={iconSize} />
        ) : (
          <Moon size={iconSize} />
        )}
        {showLabel && (
          <span>{currentIsDark ? 'وضع النهار' : 'وضع الليل'}</span>
        )}
      </button>
    );
  }

  return (
    <button
      onClick={() => setTheme(currentIsDark ? 'light' : 'dark')}
      className={cn(
        "relative rounded-full transition-colors flex items-center justify-center",
        currentIsDark
          ? 'bg-slate-800 text-yellow-300 hover:bg-slate-700'
          : 'bg-slate-100 text-slate-900 hover:bg-slate-200',
        buttonSize,
        className
      )}
      aria-label={currentIsDark ? 'التبديل إلى وضع النهار' : 'التبديل إلى وضع الليل'}
    >
      <div className="relative overflow-hidden">
        <AnimatePresence mode="wait">
          {currentIsDark ? (
            <motion.div
              key="sun"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ duration: 0.3, type: 'spring' }}
            >
              <Sun size={iconSize} />
            </motion.div>
          ) : (
            <motion.div
              key="moon"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ duration: 0.3, type: 'spring' }}
            >
              <Moon size={iconSize} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </button>
  );
}
