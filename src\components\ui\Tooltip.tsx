'use client';

import { useState, useRef, ReactNode, cloneElement, isValidElement, ReactElement } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

interface TooltipProps {
  children: ReactElement;
  content: ReactNode;
  position?: 'top' | 'right' | 'bottom' | 'left';
  delay?: number;
  className?: string;
  contentClassName?: string;
  arrowClassName?: string;
  showArrow?: boolean;
  disabled?: boolean;
}

export function Tooltip({
  children,
  content,
  position = 'top',
  delay = 300,
  className,
  contentClassName,
  arrowClassName,
  showArrow = true,
  disabled = false,
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { isDarkMode } = useThemeStore();

  const showTooltip = () => {
    if (disabled) return;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      setIsMounted(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    setIsVisible(false);
    
    // إزالة العنصر من DOM بعد انتهاء الحركة
    setTimeout(() => {
      setIsMounted(false);
    }, 200);
  };

  // تحديد موضع التلميح
  const getPosition = () => {
    switch (position) {
      case 'top':
        return { top: '-10px', left: '50%', transform: 'translate(-50%, -100%)' };
      case 'right':
        return { top: '50%', left: 'calc(100% + 10px)', transform: 'translateY(-50%)' };
      case 'bottom':
        return { top: 'calc(100% + 10px)', left: '50%', transform: 'translateX(-50%)' };
      case 'left':
        return { top: '50%', right: 'calc(100% + 10px)', transform: 'translateY(-50%)' };
      default:
        return { top: '-10px', left: '50%', transform: 'translate(-50%, -100%)' };
    }
  };

  // تحديد موضع السهم
  const getArrowPosition = () => {
    switch (position) {
      case 'top':
        return { bottom: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };
      case 'right':
        return { left: '-5px', top: '50%', transform: 'translateY(-50%) rotate(45deg)' };
      case 'bottom':
        return { top: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };
      case 'left':
        return { right: '-5px', top: '50%', transform: 'translateY(-50%) rotate(45deg)' };
      default:
        return { bottom: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };
    }
  };

  // تحديد حركة الظهور
  const getAnimationVariants = () => {
    const baseVariants = {
      hidden: { opacity: 0 },
      visible: { opacity: 1 },
    };

    switch (position) {
      case 'top':
        return {
          hidden: { ...baseVariants.hidden, y: 10 },
          visible: { ...baseVariants.visible, y: 0 },
        };
      case 'right':
        return {
          hidden: { ...baseVariants.hidden, x: -10 },
          visible: { ...baseVariants.visible, x: 0 },
        };
      case 'bottom':
        return {
          hidden: { ...baseVariants.hidden, y: -10 },
          visible: { ...baseVariants.visible, y: 0 },
        };
      case 'left':
        return {
          hidden: { ...baseVariants.hidden, x: 10 },
          visible: { ...baseVariants.visible, x: 0 },
        };
      default:
        return baseVariants;
    }
  };

  if (!isValidElement(children)) {
    return null;
  }

  const childProps = {
    onMouseEnter: showTooltip,
    onMouseLeave: hideTooltip,
    onFocus: showTooltip,
    onBlur: hideTooltip,
  };

  return (
    <div className={cn("relative inline-block", className)}>
      {cloneElement(children, childProps)}
      
      <AnimatePresence>
        {isMounted && (
          <motion.div
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            exit="hidden"
            variants={getAnimationVariants()}
            transition={{ duration: 0.2 }}
            className={cn(
              "absolute z-50 whitespace-nowrap px-3 py-1.5 text-xs font-medium",
              "bg-slate-900 text-white dark:bg-slate-700",
              "rounded-md shadow-md",
              contentClassName
            )}
            style={getPosition()}
          >
            {content}
            
            {showArrow && (
              <div
                className={cn(
                  "absolute w-2 h-2 bg-slate-900 dark:bg-slate-700",
                  arrowClassName
                )}
                style={getArrowPosition()}
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
