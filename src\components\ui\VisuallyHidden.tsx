import { HTMLAttributes } from 'react';

// مكون لإخفاء العناصر بصريًا مع الحفاظ على إمكانية الوصول لقارئات الشاشة
export function VisuallyHidden({
  children,
  ...delegated
}: HTMLAttributes<HTMLSpanElement>) {
  return (
    <span
      {...delegated}
      style={{
        border: 0,
        clip: 'rect(0 0 0 0)',
        height: '1px',
        margin: '-1px',
        overflow: 'hidden',
        padding: 0,
        position: 'absolute',
        width: '1px',
        whiteSpace: 'nowrap',
        wordWrap: 'normal',
      }}
    >
      {children}
    </span>
  );
}
