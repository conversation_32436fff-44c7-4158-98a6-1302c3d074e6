import { ReactNode } from 'react';
import { motion, MotionProps } from 'framer-motion';

interface FadeInProps extends MotionProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
}

export function FadeIn({ 
  children, 
  delay = 0, 
  duration = 0.5, 
  className = '',
  direction = 'up',
  distance = 20,
  ...props 
}: FadeInProps) {
  
  // Set initial position based on direction
  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { y: distance };
      case 'down':
        return { y: -distance };
      case 'left':
        return { x: distance };
      case 'right':
        return { x: -distance };
      case 'none':
      default:
        return {};
    }
  };

  return (
    <motion.div
      initial={{ 
        opacity: 0,
        ...getInitialPosition()
      }}
      animate={{ 
        opacity: 1,
        y: direction === 'up' ? 0 : direction === 'down' ? 0 : undefined,
        x: direction === 'left' ? 0 : direction === 'right' ? 0 : undefined
      }}
      transition={{ 
        duration,
        delay,
        ease: [0.25, 0.1, 0.25, 1.0] // Cubic bezier curve for smooth easing
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
}
