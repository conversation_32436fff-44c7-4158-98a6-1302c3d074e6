'use client';

import { ReactNode, useState, useEffect } from 'react';
import { motion, Variants } from 'framer-motion';
import { cn } from '../../../lib/utils';

interface HoverAnimationProps {
  children: ReactNode;
  className?: string;
  animation?: 'scale' | 'lift' | 'glow' | 'rotate' | 'pulse' | 'bounce' | 'tilt' | 'shine' | 'border' | 'shadow' | 'fade';
  scale?: number;
  duration?: number;
  disabled?: boolean;
  as?: React.ElementType;
  onClick?: () => void;
}

export function HoverAnimation({
  children,
  className,
  animation = 'scale',
  scale = 1.05,
  duration = 0.3,
  disabled = false,
  as = 'div',
  onClick,
  ...props
}: HoverAnimationProps & React.HTMLAttributes<HTMLElement>) {
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    setIsMobile(window.innerWidth < 768);

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true
  const isAnimationDisabled = disabled || (isMobile && typeof document !== 'undefined' && document.documentElement.classList.contains('mobile-device'));

  // تحسين الأداء عن طريق استخدام will-change للعناصر التي تحتاج إلى تحسين أداء الرسوم المتحركة
  useEffect(() => {
    if (isMounted && !isAnimationDisabled) {
      const needsHardwareAcceleration = ['lift', 'rotate', 'tilt', 'pulse', 'bounce'].includes(animation);
      if (needsHardwareAcceleration) {
        const element = document.querySelector(`.animation-${animation}`);
        if (element) {
          element.classList.add('will-change-transform');
        }
      }
    }
  }, [isMounted, isAnimationDisabled, animation]);

  // تحديد متغيرات الرسوم المتحركة بناءً على النوع
  const getVariants = (): Variants => {
    const baseTransition = {
      type: 'tween',
      duration,
    };

    switch (animation) {
      case 'scale':
        return {
          initial: {},
          hover: {
            scale,
            transition: baseTransition
          },
          tap: {
            scale: scale * 0.95,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'lift':
        return {
          initial: {},
          hover: {
            y: -8,
            transition: baseTransition
          },
          tap: {
            y: -4,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'glow':
        return {
          initial: {},
          hover: {
            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',
            transition: baseTransition
          },
          tap: {
            boxShadow: '0 0 8px rgba(var(--color-primary-500), 0.3)',
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'rotate':
        return {
          initial: {},
          hover: {
            rotate: 5,
            transition: baseTransition
          },
          tap: {
            rotate: 2,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'pulse':
        return {
          initial: {},
          hover: {
            scale: [1, scale, 1, scale, 1],
            transition: {
              duration: duration * 2,
              repeat: Infinity,
              repeatType: 'loop'
            }
          },
          tap: {
            scale: scale * 0.95,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'bounce':
        return {
          initial: {},
          hover: {
            y: [0, -10, 0],
            transition: {
              duration: duration * 1.5,
              repeat: Infinity,
              repeatType: 'loop'
            }
          },
          tap: {
            y: -5,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'tilt':
        return {
          initial: {},
          hover: {
            rotateX: -10,
            rotateY: 10,
            transition: baseTransition
          },
          tap: {
            rotateX: -5,
            rotateY: 5,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'shine':
        return {
          initial: {},
          hover: {},
          tap: {
            scale: 0.98,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'border':
        return {
          initial: { borderColor: 'rgba(var(--color-primary-500), 0)' },
          hover: {
            borderColor: 'rgba(var(--color-primary-500), 1)',
            borderWidth: '2px',
            transition: baseTransition
          },
          tap: {
            borderColor: 'rgba(var(--color-primary-600), 1)',
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'shadow':
        return {
          initial: { boxShadow: '0 0 0 rgba(0, 0, 0, 0)' },
          hover: {
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
            y: -2,
            transition: baseTransition
          },
          tap: {
            boxShadow: '0 5px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
            y: -1,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      case 'fade':
        return {
          initial: { opacity: 1 },
          hover: {
            opacity: 0.8,
            transition: baseTransition
          },
          tap: {
            opacity: 0.9,
            transition: { ...baseTransition, duration: duration / 2 }
          }
        };
      default:
        return {
          initial: {},
          hover: {},
          tap: {}
        };
    }
  };

  const variants = getVariants();
  const Component = motion[as as keyof typeof motion] || motion.div;

  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة
  if (isAnimationDisabled || !isMounted) {
    const ElementType = as;
    return (
      <ElementType className={className} onClick={onClick} {...props}>
        {children}
      </ElementType>
    );
  }

  return (
    <Component
      className={cn(
        className,
        `animation-${animation}`,
        animation === 'shine' && 'group overflow-hidden relative',
        animation === 'border' && 'border border-transparent',
        animation === 'shadow' && 'transition-shadow'
      )}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      variants={variants}
      onClick={onClick}
      {...props}
    >
      {children}
      {animation === 'shine' && (
        <motion.div
          className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full"
          transition={{ duration: duration * 2, ease: 'linear' }}
        />
      )}
      {animation === 'glow' && (
        <motion.div
          className="absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100"
          initial={{ opacity: 0, boxShadow: '0 0 0 rgba(var(--color-primary-500), 0)' }}
          whileHover={{
            opacity: 1,
            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',
            transition: { duration }
          }}
        />
      )}
    </Component>
  );
}
