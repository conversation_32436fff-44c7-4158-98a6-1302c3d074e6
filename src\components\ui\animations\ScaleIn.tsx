import { ReactNode } from 'react';
import { motion, MotionProps } from 'framer-motion';

interface ScaleInProps extends MotionProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
  initialScale?: number;
}

export function ScaleIn({
  children,
  delay = 0,
  duration = 0.5,
  className = '',
  initialScale = 0.95,
  ...props
}: ScaleInProps) {
  return (
    <motion.div
      initial={{ 
        opacity: 0,
        scale: initialScale
      }}
      animate={{ 
        opacity: 1,
        scale: 1
      }}
      transition={{ 
        duration,
        delay,
        ease: [0.25, 0.1, 0.25, 1.0]
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
}
