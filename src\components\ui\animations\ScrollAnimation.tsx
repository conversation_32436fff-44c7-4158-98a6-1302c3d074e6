'use client';

import { useRef, useEffect, useState, ReactNode } from 'react';
import { motion, useAnimation, Variants } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { cn } from '../../../lib/utils';

interface ScrollAnimationProps {
  children: ReactNode;
  className?: string;
  animation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'bounce' | 'reveal';
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  threshold?: number;
  once?: boolean;
  distance?: number;
  staggerChildren?: boolean;
  staggerDelay?: number;
  rootMargin?: string;
  disabled?: boolean;
}

export function ScrollAnimation({
  children,
  className,
  animation = 'fade',
  direction = 'up',
  delay = 0,
  duration = 0.5,
  threshold = 0.1,
  once = true,
  distance = 50,
  staggerChildren = false,
  staggerDelay = 0.1,
  rootMargin = '0px',
  disabled = false,
}: ScrollAnimationProps) {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold,
    rootMargin,
  });
  const [isMounted, setIsMounted] = useState(false);

  // تحقق مما إذا كان الجهاز محمولاً
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
    setIsMobile(window.innerWidth < 768);
    
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true
  const isAnimationDisabled = disabled || (isMobile && document.documentElement.classList.contains('mobile-device'));

  // تحديد متغيرات الرسوم المتحركة بناءً على النوع والاتجاه
  const getVariants = (): Variants => {
    const baseVariants: Variants = {
      hidden: {},
      visible: {
        transition: {
          duration,
          delay,
          ease: 'easeOut',
          staggerChildren: staggerChildren ? staggerDelay : 0,
        },
      },
    };

    switch (animation) {
      case 'fade':
        return {
          ...baseVariants,
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { ...baseVariants.visible.transition }
          },
        };
      case 'slide':
        return {
          ...baseVariants,
          hidden: { 
            opacity: 0,
            x: direction === 'left' ? distance : direction === 'right' ? -distance : 0,
            y: direction === 'up' ? distance : direction === 'down' ? -distance : 0,
          },
          visible: { 
            opacity: 1,
            x: 0,
            y: 0,
            transition: { ...baseVariants.visible.transition }
          },
        };
      case 'scale':
        return {
          ...baseVariants,
          hidden: { opacity: 0, scale: 0.8 },
          visible: { 
            opacity: 1,
            scale: 1,
            transition: { ...baseVariants.visible.transition }
          },
        };
      case 'rotate':
        return {
          ...baseVariants,
          hidden: { 
            opacity: 0,
            rotate: direction === 'left' ? -90 : 90,
            scale: 0.8,
          },
          visible: { 
            opacity: 1,
            rotate: 0,
            scale: 1,
            transition: { ...baseVariants.visible.transition }
          },
        };
      case 'flip':
        return {
          ...baseVariants,
          hidden: { 
            opacity: 0,
            rotateX: direction === 'up' || direction === 'down' ? 90 : 0,
            rotateY: direction === 'left' || direction === 'right' ? 90 : 0,
          },
          visible: { 
            opacity: 1,
            rotateX: 0,
            rotateY: 0,
            transition: { ...baseVariants.visible.transition }
          },
        };
      case 'bounce':
        return {
          ...baseVariants,
          hidden: { 
            opacity: 0,
            y: direction === 'up' ? 50 : -50,
          },
          visible: { 
            opacity: 1,
            y: 0,
            transition: { 
              ...baseVariants.visible.transition,
              type: 'spring',
              stiffness: 300,
              damping: 15,
            }
          },
        };
      case 'reveal':
        return {
          ...baseVariants,
          hidden: { 
            clipPath: direction === 'up' ? 'inset(100% 0 0 0)' : 
                      direction === 'down' ? 'inset(0 0 100% 0)' : 
                      direction === 'left' ? 'inset(0 0 0 100%)' : 
                      'inset(0 100% 0 0)',
            opacity: 0,
          },
          visible: { 
            clipPath: 'inset(0 0 0 0)',
            opacity: 1,
            transition: { 
              ...baseVariants.visible.transition,
              duration: duration * 1.2,
            }
          },
        };
      default:
        return baseVariants;
    }
  };

  const variants = getVariants();

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    } else if (!once) {
      controls.start('hidden');
    }
  }, [controls, inView, once]);

  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة
  if (isAnimationDisabled || !isMounted) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
}
