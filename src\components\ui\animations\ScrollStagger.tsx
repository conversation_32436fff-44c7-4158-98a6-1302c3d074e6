'use client';

import { useRef, useEffect, useState, ReactNode, Children, cloneElement, isValidElement } from 'react';
import { motion, useAnimation, Variants } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { cn } from '../../../lib/utils';

interface ScrollStaggerProps {
  children: ReactNode;
  className?: string;
  animation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'bounce';
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  staggerDelay?: number;
  threshold?: number;
  once?: boolean;
  distance?: number;
  rootMargin?: string;
  disabled?: boolean;
  childClassName?: string;
}

export function ScrollStagger({
  children,
  className,
  animation = 'fade',
  direction = 'up',
  delay = 0,
  duration = 0.5,
  staggerDelay = 0.1,
  threshold = 0.1,
  once = true,
  distance = 50,
  rootMargin = '0px',
  disabled = false,
  childClassName,
}: ScrollStaggerProps) {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold,
    rootMargin,
  });
  const [isMounted, setIsMounted] = useState(false);

  // تحقق مما إذا كان الجهاز محمولاً
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
    setIsMobile(window.innerWidth < 768);
    
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true
  const isAnimationDisabled = disabled || (isMobile && document.documentElement.classList.contains('mobile-device'));

  // تحديد متغيرات الرسوم المتحركة للحاوية
  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: delay,
      },
    },
  };

  // تحديد متغيرات الرسوم المتحركة للعناصر الفرعية
  const getItemVariants = (): Variants => {
    switch (animation) {
      case 'fade':
        return {
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { duration }
          },
        };
      case 'slide':
        return {
          hidden: { 
            opacity: 0,
            x: direction === 'left' ? distance : direction === 'right' ? -distance : 0,
            y: direction === 'up' ? distance : direction === 'down' ? -distance : 0,
          },
          visible: { 
            opacity: 1,
            x: 0,
            y: 0,
            transition: { duration }
          },
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { 
            opacity: 1,
            scale: 1,
            transition: { duration }
          },
        };
      case 'rotate':
        return {
          hidden: { 
            opacity: 0,
            rotate: direction === 'left' ? -90 : 90,
            scale: 0.8,
          },
          visible: { 
            opacity: 1,
            rotate: 0,
            scale: 1,
            transition: { duration }
          },
        };
      case 'flip':
        return {
          hidden: { 
            opacity: 0,
            rotateX: direction === 'up' || direction === 'down' ? 90 : 0,
            rotateY: direction === 'left' || direction === 'right' ? 90 : 0,
          },
          visible: { 
            opacity: 1,
            rotateX: 0,
            rotateY: 0,
            transition: { duration }
          },
        };
      case 'bounce':
        return {
          hidden: { 
            opacity: 0,
            y: direction === 'up' ? 50 : -50,
          },
          visible: { 
            opacity: 1,
            y: 0,
            transition: { 
              duration,
              type: 'spring',
              stiffness: 300,
              damping: 15,
            }
          },
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration } },
        };
    }
  };

  const itemVariants = getItemVariants();

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    } else if (!once) {
      controls.start('hidden');
    }
  }, [controls, inView, once]);

  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة
  if (isAnimationDisabled || !isMounted) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={controls}
      variants={containerVariants}
    >
      {Children.map(children, (child, index) => {
        if (isValidElement(child)) {
          return (
            <motion.div
              key={index}
              variants={itemVariants}
              className={childClassName}
            >
              {child}
            </motion.div>
          );
        }
        return child;
      })}
    </motion.div>
  );
}
