import { ReactNode } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';

interface SmoothTransitionProps {
  children: ReactNode;
  type?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'none';
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
  className?: string;
  id?: string;
  once?: boolean;
  staggerChildren?: number;
  staggerDirection?: 1 | -1;
  custom?: any;
}

export function SmoothTransition({
  children,
  type = 'fade',
  direction = 'up',
  duration = 0.5,
  delay = 0,
  className = '',
  id,
  once = false,
  staggerChildren,
  staggerDirection = 1,
  custom,
}: SmoothTransitionProps) {
  // تحديد الرسوم المتحركة بناءً على النوع والاتجاه
  const getVariants = (): Variants => {
    switch (type) {
      case 'fade':
        return {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              duration,
              delay,
              staggerChildren,
              staggerDirection,
              ease: [0.25, 0.1, 0.25, 1.0] // منحنى بيزيه للحركة الطبيعية
            }
          },
          exit: {
            opacity: 0,
            transition: {
              duration: duration * 0.75,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          }
        };
      case 'slide':
        const offset = 50;
        const slideDirection = {
          up: { y: offset },
          down: { y: -offset },
          left: { x: offset },
          right: { x: -offset }
        };
        return {
          hidden: {
            opacity: 0,
            ...slideDirection[direction]
          },
          visible: {
            opacity: 1,
            x: 0,
            y: 0,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 400,
              damping: 25,
              staggerChildren,
              staggerDirection,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          },
          exit: {
            opacity: 0,
            ...slideDirection[direction],
            transition: {
              duration: duration * 0.75,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          }
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.95 },
          visible: {
            opacity: 1,
            scale: 1,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 400,
              damping: 25,
              staggerChildren,
              staggerDirection,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          },
          exit: {
            opacity: 0,
            scale: 0.95,
            transition: {
              duration: duration * 0.75,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          }
        };
      case 'rotate':
        return {
          hidden: {
            opacity: 0,
            rotate: -5,
            scale: 0.95
          },
          visible: {
            opacity: 1,
            rotate: 0,
            scale: 1,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 400,
              damping: 25,
              staggerChildren,
              staggerDirection,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          },
          exit: {
            opacity: 0,
            rotate: 5,
            scale: 0.95,
            transition: {
              duration: duration * 0.75,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          }
        };
      case 'flip':
        return {
          hidden: {
            opacity: 0,
            rotateX: direction === 'up' || direction === 'down' ? 15 : 0,
            rotateY: direction === 'left' || direction === 'right' ? 15 : 0,
            transformPerspective: 1000,
            scale: 0.95
          },
          visible: {
            opacity: 1,
            rotateX: 0,
            rotateY: 0,
            transformPerspective: 1000,
            scale: 1,
            transition: {
              duration,
              delay,
              type: 'spring',
              stiffness: 400,
              damping: 25,
              staggerChildren,
              staggerDirection,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          },
          exit: {
            opacity: 0,
            rotateX: direction === 'up' || direction === 'down' ? -15 : 0,
            rotateY: direction === 'left' || direction === 'right' ? -15 : 0,
            transformPerspective: 1000,
            scale: 0.95,
            transition: {
              duration: duration * 0.75,
              ease: [0.25, 0.1, 0.25, 1.0]
            }
          }
        };
      case 'none':
      default:
        return {
          hidden: {},
          visible: {},
          exit: {}
        };
    }
  };

  const variants = getVariants();

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={id}
        className={className}
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={variants}
        custom={custom}
        whileInView={once ? "visible" : undefined}
        viewport={once ? { once: true, margin: "-100px" } : undefined}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}
