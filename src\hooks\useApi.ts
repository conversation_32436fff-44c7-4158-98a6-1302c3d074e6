import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import api from '../lib/api';
import { AxiosError, AxiosResponse } from 'axios';
import { defaultCache } from '../lib/localCache';

// Hook لاستخدام الاستعلامات مع React Query
export function useApiQuery<TData = unknown, TError = AxiosError>(
  queryKey: string[],
  url: string,
  options?: UseQueryOptions<TData, TError>
) {
  return useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      // محاولة استرداد البيانات من التخزين المؤقت المحلي أولاً
      const cacheKey = queryKey.join('-');
      const cachedData = defaultCache.get<TData>(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      // إذا لم تكن البيانات متاحة في التخزين المؤقت، استعلم من الخادم
      const response = await api.get<TData>(url);

      // تخزين البيانات في التخزين المؤقت المحلي (1 ساعة افتراضيًا)
      defaultCache.set(cacheKey, response.data);

      return response.data;
    },
    ...options,
  });
}

// Hook لاستخدام التعديلات مع React Query
export function useApiMutation<TData = unknown, TVariables = unknown, TError = AxiosError>(
  url: string,
  method: 'post' | 'put' | 'patch' | 'delete' = 'post',
  options?: UseMutationOptions<AxiosResponse<TData>, TError, TVariables>
) {
  const queryClient = useQueryClient();

  return useMutation<AxiosResponse<TData>, TError, TVariables>({
    mutationFn: async (variables) => {
      switch (method) {
        case 'post':
          return api.post<TData>(url, variables);
        case 'put':
          return api.put<TData>(url, variables);
        case 'patch':
          return api.patch<TData>(url, variables);
        case 'delete':
          return api.delete<TData>(`${url}${variables ? `/${variables}` : ''}`);
        default:
          return api.post<TData>(url, variables);
      }
    },
    // إعادة تعيين ذاكرة التخزين المؤقت عند نجاح التعديل
    onSuccess: (data, variables, context) => {
      // إذا تم تحديد وظيفة onSuccess في الخيارات، استدعها أولاً
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }

      // إعادة تعيين الاستعلامات ذات الصلة
      queryClient.invalidateQueries({ queryKey: [url.split('/')[1]] });
    },
    ...options,
  });
}

// Hook لاستخدام الاستعلامات مع التخزين المؤقت المحلي
export function useLocalStorageQuery<TData = unknown>(
  queryKey: string[],
  url: string,
  options?: UseQueryOptions<TData, AxiosError>
) {
  return useQuery<TData, AxiosError>({
    queryKey,
    queryFn: async () => {
      // استخدام مكتبة التخزين المؤقت المحلي
      const cacheKey = queryKey.join('-');
      const cachedData = defaultCache.get<TData>(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      // إذا لم تكن البيانات متاحة في التخزين المؤقت، استعلم من الخادم
      const response = await api.get<TData>(url);

      // تخزين البيانات في التخزين المؤقت المحلي (1 ساعة افتراضيًا)
      defaultCache.set(cacheKey, response.data, 3600);

      return response.data;
    },
    ...options,
  });
}
