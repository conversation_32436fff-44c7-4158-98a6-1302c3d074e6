import { useAuthStore } from '../stores/authStore';
import { useCallback } from 'react';

/**
 * A custom hook that wraps an action with an authentication check.
 * If the user is not authenticated, it calls the onUnauthenticated callback.
 * Otherwise, it executes the provided action.
 *
 * @param action The function to execute if the user is authenticated.
 * @param onUnauthenticated The callback function to execute if the user is not authenticated.
 * @returns A memoized function that wraps the original action with authentication logic.
 */
export function useAuthenticatedAction<TArgs extends any[], TReturn = void>(
  action: (...args: TArgs) => TReturn,
  onUnauthenticated: () => void
): (...args: TArgs) => TReturn | void {
  const { user } = useAuthStore();

  const wrappedAction = useCallback((...args: TArgs): TReturn | void => {
    if (!user) {
      onUnauthenticated();
      return; // Return void if unauthenticated
    }
    return action(...args);
  }, [user, action, onUnauthenticated]);

  return wrappedAction;
}
