import axios, { AxiosError, AxiosResponse } from 'axios';
import { defaultRateLimiter } from './rateLimiter';

// إنشاء نسخة من Axios مع الإعدادات الافتراضية
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 ثوانٍ
});

// إضافة معترض للطلبات لتنفيذ CSRF Protection و Rate Limiting
api.interceptors.request.use(
  async (config) => {
    // تنفيذ Rate Limiting
    if (!defaultRateLimiter.isAllowed()) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    // تسجيل الطلب الجديد
    defaultRateLimiter.logRequest();

    // إضافة CSRF token إذا كان متاحًا
    if (typeof document !== 'undefined') {
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      if (csrfToken && config.headers) {
        config.headers['X-CSRF-Token'] = csrfToken;
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة معترض للاستجابات للتعامل مع الأخطاء
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // التعامل مع أخطاء الاستجابة
    if (error.response) {
      // خطأ من الخادم
      const status = error.response.status;

      if (status === 401) {
        // غير مصرح - تسجيل الخروج
        console.error('Unauthorized access. Please login again.');
        // يمكن استدعاء وظيفة تسجيل الخروج هنا
      } else if (status === 403) {
        // ممنوع
        console.error('Access forbidden.');
      } else if (status === 429) {
        // تجاوز حد الطلبات
        console.error('Rate limit exceeded. Please try again later.');
      } else if (status >= 500) {
        // خطأ في الخادم
        console.error('Server error. Please try again later.');
      }
    } else if (error.request) {
      // لم يتم استلام استجابة
      console.error('No response received from server.');
    } else {
      // خطأ في إعداد الطلب
      console.error('Error setting up the request:', error.message);
    }

    return Promise.reject(error);
  }
);

export default api;
