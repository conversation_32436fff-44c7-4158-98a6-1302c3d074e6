const exchangeRates: Record<string, number> = {
  USD: 1,
  EUR: 0.92,
  GBP: 0.79,
  JPY: 148.50,
  AUD: 1.52,
  CAD: 1.35
};

export function convertCurrency(amount: number, from: string, to: string): number {
  if (from === to) return amount;
  
  const usdAmount = from === 'USD' ? amount : amount / exchangeRates[from];
  return usdAmount * exchangeRates[to];
}

export function formatCurrency(amount: number, currency: string): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return formatter.format(amount);
}