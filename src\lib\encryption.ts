// تنفيذ تشفير البيانات المحلية
// هذا يساعد في حماية البيانات المخزنة في localStorage

/**
 * وظيفة لتشفير البيانات باستخدام خوارزمية بسيطة
 * ملاحظة: هذا ليس تشفيرًا قويًا وينبغي استخدام مكتبات أكثر أمانًا في الإنتاج
 * مثل CryptoJS أو Web Crypto API
 */
export function encrypt(data: any, secretKey: string = 'default-secret-key'): string {
  try {
    // تحويل البيانات إلى سلسلة نصية
    const jsonString = JSON.stringify(data);

    // تشفير بسيط باستخدام XOR
    let encrypted = '';
    for (let i = 0; i < jsonString.length; i++) {
      const charCode = jsonString.charCodeAt(i) ^ secretKey.charCodeAt(i % secretKey.length);
      encrypted += String.fromCharCode(charCode);
    }

    // تحويل إلى Base64 للتخزين
    return btoa(encrypted);
  } catch (error) {
    console.error('Error encrypting data:', error);
    return '';
  }
}

/**
 * وظيفة لفك تشفير البيانات
 */
export function decrypt<T>(encryptedData: string, secretKey: string = 'default-secret-key'): T | null {
  try {
    // فك تشفير Base64
    const base64Decoded = atob(encryptedData);

    // فك التشفير باستخدام XOR
    let decrypted = '';
    for (let i = 0; i < base64Decoded.length; i++) {
      const charCode = base64Decoded.charCodeAt(i) ^ secretKey.charCodeAt(i % secretKey.length);
      decrypted += String.fromCharCode(charCode);
    }

    // تحويل البيانات المفككة إلى كائن
    return JSON.parse(decrypted) as T;
  } catch (error) {
    console.error('Error decrypting data:', error);
    return null;
  }
}

/**
 * وظيفة للحصول على مفتاح سري فريد للمستخدم
 * يمكن استخدام معرف المستخدم أو معلومات أخرى لإنشاء مفتاح فريد
 */
export function getUserSecretKey(userId?: string): string {
  if (typeof window === 'undefined') {
    return `server-key-${userId || 'anonymous'}`;
  }

  if (userId) {
    return `user-key-${userId}-${window.location.hostname}`;
  }

  // إذا لم يكن المستخدم مسجل الدخول، استخدم معرف الجهاز
  const deviceId = getOrCreateDeviceId();
  return `device-key-${deviceId}-${window.location.hostname}`;
}

/**
 * وظيفة للحصول على أو إنشاء معرف فريد للجهاز
 */
function getOrCreateDeviceId(): string {
  if (typeof window === 'undefined') {
    return `server-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  const storageKey = 'device-id';
  let deviceId;

  try {
    deviceId = localStorage.getItem(storageKey);

    if (!deviceId) {
      // إنشاء معرف فريد للجهاز
      deviceId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem(storageKey, deviceId);
    }
  } catch (error) {
    // في حالة عدم توفر localStorage
    deviceId = `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  return deviceId;
}

/**
 * وظيفة لتشفير وتخزين البيانات في localStorage
 */
export const secureLocalStorage = {
  setItem(key: string, value: any, userId?: string): void {
    if (typeof window === 'undefined') return;

    try {
      const secretKey = getUserSecretKey(userId);
      const encryptedData = encrypt(value, secretKey);
      localStorage.setItem(key, encryptedData);
    } catch (error) {
      console.error('Error in secureLocalStorage.setItem:', error);
    }
  },

  getItem<T>(key: string, userId?: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const encryptedData = localStorage.getItem(key);
      if (!encryptedData) return null;

      const secretKey = getUserSecretKey(userId);
      return decrypt<T>(encryptedData, secretKey);
    } catch (error) {
      console.error('Error in secureLocalStorage.getItem:', error);
      return null;
    }
  },

  removeItem(key: string): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error in secureLocalStorage.removeItem:', error);
    }
  },

  clear(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error in secureLocalStorage.clear:', error);
    }
  }
};
