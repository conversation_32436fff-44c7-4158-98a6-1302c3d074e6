import { useParams } from 'next/navigation';

export type Locale = 'en' | 'ar';

export const defaultLocale: Locale = 'ar';

export const locales: Locale[] = ['ar', 'en'];

export function useLocale() {
  const params = useParams();
  const locale = params?.locale as Locale || defaultLocale;
  return locale;
}

export const translations = {
  en: {
    // Common
    'app.name': 'CommercePro',
    'app.tagline': 'Your Complete Business Solution',

    // Navigation
    'nav.home': 'Home',
    'nav.shop': 'Shop',
    'nav.wholesale': 'Wholesale',
    'nav.services': 'Services',
    'nav.about': 'About',
    'nav.contact': 'Contact',

    // Home Page
    'home.hero.title': 'Your One-Stop Business Solution',
    'home.hero.subtitle': 'From retail to wholesale, production to services',
    'home.hero.cta': 'Explore Products',
    'home.hero.secondary': 'Our Services',

    // Shop
    'shop.title': 'Shop',
    'shop.filter': 'Filter',
    'shop.sort': 'Sort',
    'shop.search': 'Search products...',
    'shop.categories': 'Categories',
    'shop.price': 'Price',
    'shop.inStock': 'In Stock',

    // Product
    'product.addToCart': 'Add to Cart',
    'product.addToWishlist': 'Add to Wishlist',
    'product.inStock': 'In Stock',
    'product.outOfStock': 'Out of Stock',
    'product.description': 'Description',
    'product.specifications': 'Specifications',
    'product.reviews': 'Reviews',

    // Cart
    'cart.title': 'Shopping Cart',
    'cart.empty': 'Your cart is empty',
    'cart.continueShopping': 'Continue Shopping',
    'cart.checkout': 'Checkout',
    'cart.total': 'Total',
    'cart.subtotal': 'Subtotal',
    'cart.shipping': 'Shipping',
    'cart.tax': 'Tax',

    // Wishlist
    'wishlist.title': 'Wishlist',
    'wishlist.empty': 'Your wishlist is empty',
    'wishlist.addAll': 'Add All to Cart',

    // Account
    'account.title': 'My Account',
    'account.profile': 'Profile',
    'account.orders': 'Orders',
    'account.addresses': 'Addresses',
    'account.paymentMethods': 'Payment Methods',
    'account.settings': 'Settings',
    'account.logout': 'Logout',

    // Auth
    'auth.signIn': 'Sign In',
    'auth.signUp': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.noAccount': 'Don\'t have an account?',
    'auth.haveAccount': 'Already have an account?',

    // Footer
    'footer.copyright': '© 2024 CommercePro. All rights reserved.',
    'footer.terms': 'Terms of Service',
    'footer.privacy': 'Privacy Policy',
  },
  ar: {
    // Common
    'app.name': 'كوميرس برو',
    'app.tagline': 'حلول الأعمال المتكاملة',

    // Navigation
    'nav.home': 'الرئيسية',
    'nav.shop': 'المتجر',
    'nav.wholesale': 'الجملة',
    'nav.services': 'الخدمات',
    'nav.about': 'من نحن',
    'nav.contact': 'اتصل بنا',

    // Home Page
    'home.hero.title': 'حلول الأعمال الشاملة',
    'home.hero.subtitle': 'من التجزئة إلى الجملة، من الإنتاج إلى الخدمات',
    'home.hero.cta': 'استكشف المنتجات',
    'home.hero.secondary': 'خدماتنا',

    // Shop
    'shop.title': 'المتجر',
    'shop.filter': 'تصفية',
    'shop.sort': 'ترتيب',
    'shop.search': 'البحث عن منتجات...',
    'shop.categories': 'الفئات',
    'shop.price': 'السعر',
    'shop.inStock': 'متوفر',

    // Product
    'product.addToCart': 'أضف إلى السلة',
    'product.addToWishlist': 'أضف إلى المفضلة',
    'product.inStock': 'متوفر',
    'product.outOfStock': 'غير متوفر',
    'product.description': 'الوصف',
    'product.specifications': 'المواصفات',
    'product.reviews': 'التقييمات',

    // Cart
    'cart.title': 'سلة التسوق',
    'cart.empty': 'سلة التسوق فارغة',
    'cart.continueShopping': 'مواصلة التسوق',
    'cart.checkout': 'إتمام الشراء',
    'cart.total': 'الإجمالي',
    'cart.subtotal': 'المجموع الفرعي',
    'cart.shipping': 'الشحن',
    'cart.tax': 'الضريبة',

    // Wishlist
    'wishlist.title': 'المفضلة',
    'wishlist.empty': 'قائمة المفضلة فارغة',
    'wishlist.addAll': 'أضف الكل إلى السلة',

    // Account
    'account.title': 'حسابي',
    'account.profile': 'الملف الشخصي',
    'account.orders': 'الطلبات',
    'account.addresses': 'العناوين',
    'account.paymentMethods': 'طرق الدفع',
    'account.settings': 'الإعدادات',
    'account.logout': 'تسجيل الخروج',

    // Auth
    'auth.signIn': 'تسجيل الدخول',
    'auth.signUp': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.forgotPassword': 'نسيت كلمة المرور؟',
    'auth.noAccount': 'ليس لديك حساب؟',
    'auth.haveAccount': 'لديك حساب بالفعل؟',

    // Footer
    'footer.copyright': '© 2024 كوميرس برو. جميع الحقوق محفوظة.',
    'footer.terms': 'شروط الخدمة',
    'footer.privacy': 'سياسة الخصوصية',
  },
};

export function useTranslation() {
  const locale = useLocale();

  function t(key: keyof typeof translations.en) {
    return translations[locale][key] || translations.en[key] || key;
  }

  return { t, locale };
}
