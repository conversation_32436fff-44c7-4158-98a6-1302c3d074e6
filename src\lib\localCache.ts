// تنفيذ التخزين المؤقت المحلي للبيانات
import { encrypt, decrypt, getUserSecretKey } from './encryption';

interface CacheItem<T> {
  data: T;
  expiry: number;
}

export class LocalCache {
  private prefix: string;

  constructor(prefix = 'app-cache') {
    this.prefix = prefix;
  }

  // إنشاء مفتاح التخزين
  private createKey(key: string): string {
    return `${this.prefix}:${key}`;
  }

  // تخزين البيانات في التخزين المحلي مع التشفير
  set<T>(key: string, data: T, ttlSeconds = 3600, userId?: string): void {
    try {
      const cacheKey = this.createKey(key);
      const item: CacheItem<T> = {
        data,
        expiry: Date.now() + (ttlSeconds * 1000)
      };

      // استخدام التشفير لتخزين البيانات
      const secretKey = getUserSecretKey(userId);
      const encryptedData = encrypt(item, secretKey);
      localStorage.setItem(cacheKey, encryptedData);
    } catch (error) {
      console.error('Error setting cache item:', error);
    }
  }

  // استرداد البيانات من التخزين المحلي مع فك التشفير
  get<T>(key: string, userId?: string): T | null {
    try {
      const cacheKey = this.createKey(key);
      const encryptedItem = localStorage.getItem(cacheKey);

      if (!encryptedItem) {
        return null;
      }

      // فك تشفير البيانات
      const secretKey = getUserSecretKey(userId);
      const parsedItem = decrypt<CacheItem<T>>(encryptedItem, secretKey);

      if (!parsedItem) {
        this.remove(key);
        return null;
      }

      // التحقق من صلاحية البيانات
      if (parsedItem.expiry < Date.now()) {
        this.remove(key);
        return null;
      }

      return parsedItem.data;
    } catch (error) {
      console.error('Error getting cache item:', error);
      return null;
    }
  }

  // إزالة عنصر من التخزين المؤقت
  remove(key: string): void {
    try {
      const cacheKey = this.createKey(key);
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.error('Error removing cache item:', error);
    }
  }

  // مسح جميع العناصر المخزنة مؤقتًا
  clear(): void {
    try {
      const keys = Object.keys(localStorage);

      for (const key of keys) {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // مسح العناصر منتهية الصلاحية
  clearExpired(): void {
    try {
      const keys = Object.keys(localStorage);
      const now = Date.now();

      for (const key of keys) {
        if (key.startsWith(this.prefix)) {
          const item = localStorage.getItem(key);

          if (item) {
            try {
              const parsedItem = JSON.parse(item);

              if (parsedItem.expiry && parsedItem.expiry < now) {
                localStorage.removeItem(key);
              }
            } catch (e) {
              // تجاهل الأخطاء في تحليل العناصر غير الصالحة
            }
          }
        }
      }
    } catch (error) {
      console.error('Error clearing expired cache items:', error);
    }
  }
}

// إنشاء نسخة افتراضية من التخزين المؤقت المحلي
export const defaultCache = new LocalCache();
