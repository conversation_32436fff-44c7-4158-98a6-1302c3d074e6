/**
 * Performance Monitoring and Analytics System
 * Tracks application performance, errors, and user interactions
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface ErrorLog {
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  metadata?: Record<string, any>;
}

interface UserInteraction {
  event: string;
  element: string;
  timestamp: number;
  userId?: string;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private errors: ErrorLog[] = [];
  private interactions: UserInteraction[] = [];
  private isEnabled: boolean = true;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeWebVitals();
      this.initializeErrorTracking();
      this.initializeUserTracking();
    }
  }

  // Initialize Web Vitals tracking
  private initializeWebVitals() {
    // Track Core Web Vitals
    this.observePerformance('navigation', (entries) => {
      entries.forEach((entry: any) => {
        this.recordMetric('page-load-time', entry.loadEventEnd - entry.loadEventStart);
        this.recordMetric('dom-content-loaded', entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart);
        this.recordMetric('first-contentful-paint', entry.loadEventEnd - entry.fetchStart);
      });
    });

    // Track Largest Contentful Paint (LCP)
    this.observePerformance('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1];
      this.recordMetric('largest-contentful-paint', lastEntry.startTime);
    });

    // Track First Input Delay (FID)
    this.observePerformance('first-input', (entries) => {
      entries.forEach((entry: any) => {
        this.recordMetric('first-input-delay', entry.processingStart - entry.startTime);
      });
    });

    // Track Cumulative Layout Shift (CLS)
    this.observePerformance('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.recordMetric('cumulative-layout-shift', clsValue);
    });
  }

  // Initialize error tracking
  private initializeErrorTracking() {
    window.addEventListener('error', (event) => {
      this.recordError({
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        metadata: {
          type: 'unhandledrejection',
          reason: event.reason,
        },
      });
    });
  }

  // Initialize user interaction tracking
  private initializeUserTracking() {
    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (target.matches('button, a, [role="button"]')) {
        this.recordInteraction('click', this.getElementSelector(target), {
          text: target.textContent?.trim(),
          href: (target as HTMLAnchorElement).href,
        });
      }
    });

    // Track form submissions
    document.addEventListener('submit', (event) => {
      const target = event.target as HTMLFormElement;
      this.recordInteraction('form-submit', this.getElementSelector(target), {
        action: target.action,
        method: target.method,
      });
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.recordInteraction('visibility-change', 'document', {
        hidden: document.hidden,
      });
    });
  }

  // Helper method to observe performance entries
  private observePerformance(type: string, callback: (entries: any[]) => void) {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          callback(list.getEntries());
        });
        observer.observe({ entryTypes: [type] });
      } catch (error) {
        console.warn(`Failed to observe ${type}:`, error);
      }
    }
  }

  // Get CSS selector for an element
  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`;
    if (element.className) return `.${element.className.split(' ')[0]}`;
    return element.tagName.toLowerCase();
  }

  // Record performance metric
  recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);
    this.sendMetricToAnalytics(metric);
  }

  // Record error
  recordError(error: Omit<ErrorLog, 'timestamp'> & { timestamp?: number }) {
    if (!this.isEnabled) return;

    const errorLog: ErrorLog = {
      ...error,
      timestamp: error.timestamp || Date.now(),
    };

    this.errors.push(errorLog);
    this.sendErrorToAnalytics(errorLog);
  }

  // Record user interaction
  recordInteraction(event: string, element: string, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const interaction: UserInteraction = {
      event,
      element,
      timestamp: Date.now(),
      metadata,
    };

    this.interactions.push(interaction);
    this.sendInteractionToAnalytics(interaction);
  }

  // Send metric to analytics service
  private sendMetricToAnalytics(metric: PerformanceMetric) {
    if (process.env.NODE_ENV === 'production') {
      // In production, send to analytics service
      // Example: Google Analytics, Mixpanel, etc.
      console.log('Metric:', metric);
    }
  }

  // Send error to analytics service
  private sendErrorToAnalytics(error: ErrorLog) {
    if (process.env.NODE_ENV === 'production') {
      // In production, send to error tracking service
      // Example: Sentry, Bugsnag, etc.
      console.error('Error logged:', error);
    }
  }

  // Send interaction to analytics service
  private sendInteractionToAnalytics(interaction: UserInteraction) {
    if (process.env.NODE_ENV === 'production') {
      // In production, send to analytics service
      console.log('Interaction:', interaction);
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    return {
      metrics: this.metrics.slice(-100), // Last 100 metrics
      errors: this.errors.slice(-50), // Last 50 errors
      interactions: this.interactions.slice(-200), // Last 200 interactions
    };
  }

  // Enable/disable monitoring
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  // Clear all data
  clear() {
    this.metrics = [];
    this.errors = [];
    this.interactions = [];
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for manual tracking
export const trackPageLoad = (pageName: string) => {
  performanceMonitor.recordMetric('page-load', performance.now(), { page: pageName });
};

export const trackApiCall = async <T>(
  apiName: string,
  apiCall: () => Promise<T>
): Promise<T> => {
  const startTime = performance.now();
  try {
    const result = await apiCall();
    const duration = performance.now() - startTime;
    performanceMonitor.recordMetric('api-call-success', duration, { api: apiName });
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    performanceMonitor.recordMetric('api-call-error', duration, { api: apiName });
    performanceMonitor.recordError({
      message: `API call failed: ${apiName}`,
      stack: error instanceof Error ? error.stack : undefined,
      url: window.location.href,
      userAgent: navigator.userAgent,
      metadata: { api: apiName, error: String(error) },
    });
    throw error;
  }
};

export const trackUserAction = (action: string, metadata?: Record<string, any>) => {
  performanceMonitor.recordInteraction('user-action', action, metadata);
};

// React hook for component performance tracking
export const usePerformanceTracking = (componentName: string) => {
  const trackRender = () => {
    performanceMonitor.recordMetric('component-render', performance.now(), {
      component: componentName,
    });
  };

  return { trackRender };
};
