import { QueryClient } from '@tanstack/react-query';

// تكوين React Query مع استراتيجيات التخزين المؤقت المحسنة
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // تكوين افتراضي للاستعلامات
      refetchOnWindowFocus: false, // عدم إعادة الاستعلام عند التركيز على النافذة
      refetchOnMount: true, // إعادة الاستعلام عند تركيب المكون
      refetchOnReconnect: true, // إعادة الاستعلام عند إعادة الاتصال
      retry: 3, // عدد محاولات إعادة المحاولة عند فشل الاستعلام
      staleTime: 5 * 60 * 1000, // 5 دقائق قبل اعتبار البيانات قديمة
      cacheTime: 10 * 60 * 1000, // 10 دقائق للاحتفاظ بالبيانات في ذاكرة التخزين المؤقت
    },
    mutations: {
      // تكوين افتراضي للتعديلات
      retry: 2, // عدد محاولات إعادة المحاولة عند فشل التعديل
    },
  },
});
