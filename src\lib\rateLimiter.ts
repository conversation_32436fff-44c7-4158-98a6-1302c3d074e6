// تنفيذ Rate Limiting على جانب العميل
// هذا مفيد لمنع المستخدمين من إرسال طلبات متعددة في فترة زمنية قصيرة

interface RateLimiterOptions {
  maxRequests: number; // الحد الأقصى لعدد الطلبات المسموح بها في النافذة الزمنية
  windowMs: number; // النافذة الزمنية بالمللي ثانية
  storageKey?: string; // مفتاح التخزين في localStorage
}

interface RateLimiterStorage {
  timestamps: number[]; // طوابع زمنية للطلبات السابقة
  lastReset: number; // آخر وقت تم فيه إعادة تعيين العداد
}

export class RateLimiter {
  private options: RateLimiterOptions;
  private storage: RateLimiterStorage;
  private storageKey: string;

  constructor(options: RateLimiterOptions) {
    this.options = {
      maxRequests: options.maxRequests || 60,
      windowMs: options.windowMs || 60000, // 1 دقيقة افتراضيًا
      storageKey: options.storageKey || 'rate-limiter'
    };
    this.storageKey = this.options.storageKey!;
    this.storage = this.loadStorage();
  }

  // تحميل بيانات التخزين من localStorage
  private loadStorage(): RateLimiterStorage {
    if (typeof window !== 'undefined') {
      try {
        const data = localStorage.getItem(this.storageKey);
        if (data) {
          return JSON.parse(data);
        }
      } catch (error) {
        console.error('Error loading rate limiter data:', error);
      }
    }

    return {
      timestamps: [],
      lastReset: Date.now()
    };
  }

  // حفظ بيانات التخزين في localStorage
  private saveStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(this.storage));
      } catch (error) {
        console.error('Error saving rate limiter data:', error);
      }
    }
  }

  // تنظيف الطوابع الزمنية القديمة
  private cleanup(): void {
    const now = Date.now();
    const windowStart = now - this.options.windowMs;

    // إزالة الطوابع الزمنية القديمة خارج النافذة الزمنية
    this.storage.timestamps = this.storage.timestamps.filter(
      timestamp => timestamp >= windowStart
    );

    this.saveStorage();
  }

  // التحقق مما إذا كان الطلب مسموحًا به
  public isAllowed(): boolean {
    this.cleanup();

    return this.storage.timestamps.length < this.options.maxRequests;
  }

  // تسجيل طلب جديد
  public logRequest(): boolean {
    this.cleanup();

    if (this.storage.timestamps.length >= this.options.maxRequests) {
      return false;
    }

    this.storage.timestamps.push(Date.now());
    this.saveStorage();

    return true;
  }

  // الحصول على عدد الطلبات المتبقية
  public getRemainingRequests(): number {
    this.cleanup();

    return Math.max(0, this.options.maxRequests - this.storage.timestamps.length);
  }

  // الحصول على الوقت المتبقي حتى إعادة تعيين العداد
  public getResetTime(): number {
    const now = Date.now();

    if (this.storage.timestamps.length === 0) {
      return 0;
    }

    const oldestTimestamp = Math.min(...this.storage.timestamps);
    return Math.max(0, oldestTimestamp + this.options.windowMs - now);
  }
}

// إنشاء نسخة افتراضية من Rate Limiter
export const defaultRateLimiter = new RateLimiter({
  maxRequests: 50,
  windowMs: 60000, // 1 دقيقة
  storageKey: 'app-rate-limiter'
});
