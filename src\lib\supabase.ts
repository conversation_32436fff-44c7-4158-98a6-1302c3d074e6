import { createClient } from '@supabase/supabase-js';

// استخدام متغيرات البيئة للحصول على معلومات الاتصال بـ Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://example.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

// تخزين المستخدمين المحليين في localStorage
const LOCAL_USERS_KEY = 'local-users';
const LOCAL_PROFILES_KEY = 'local-profiles';

// الحصول على المستخدمين المحليين من localStorage
export const getLocalUsers = () => {
  if (typeof window !== 'undefined') {
    try {
      const usersJson = localStorage.getItem(LOCAL_USERS_KEY);
      return usersJson ? JSON.parse(usersJson) : [];
    } catch (error) {
      console.error('Error getting local users:', error);
      return [];
    }
  }
  return [];
};

// الحصول على ملفات المستخدمين المحليين من localStorage
export const getLocalProfiles = () => {
  if (typeof window !== 'undefined') {
    try {
      const profilesJson = localStorage.getItem(LOCAL_PROFILES_KEY);
      return profilesJson ? JSON.parse(profilesJson) : [];
    } catch (error) {
      console.error('Error getting local profiles:', error);
      return [];
    }
  }
  return [];
};

// حفظ المستخدمين المحليين في localStorage
export const saveLocalUsers = (users: any[]) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(LOCAL_USERS_KEY, JSON.stringify(users));
    } catch (error) {
      console.error('Error saving local users:', error);
    }
  }
};

// حفظ ملفات المستخدمين المحليين في localStorage
export const saveLocalProfiles = (profiles: any[]) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(LOCAL_PROFILES_KEY, JSON.stringify(profiles));
    } catch (error) {
      console.error('Error saving local profiles:', error);
    }
  }
};

// إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
const initializeLocalUsers = () => {
  const users = getLocalUsers();
  const profiles = getLocalProfiles();

  if (users.length === 0) {
    // إنشاء مستخدم افتراضي
    const defaultUserId = 'default-user-id';
    const defaultUser = {
      id: defaultUserId,
      email: '<EMAIL>',
      password: 'password', // في الإنتاج، يجب تشفير كلمات المرور
    };

    // إنشاء ملف المستخدم الافتراضي
    const defaultProfile = {
      id: defaultUserId,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'user',
      createdAt: new Date().toISOString()
    };

    saveLocalUsers([defaultUser]);
    saveLocalProfiles([defaultProfile]);

    console.log('Created default user:', defaultUser.email);
  }
};

// تهيئة المستخدمين المحليين
if (typeof window !== 'undefined') {
  initializeLocalUsers();
}

// Mock Supabase client for local development without database connection
const mockSupabase = {
  auth: {
    signOut: async () => {
      console.log('User signed out');
      return { error: null };
    }
  },
  from: (table: string) => ({
    select: (columns = '*') => ({
      eq: (column: string, value: any) => ({
        single: async () => {
          console.log(`Querying table: ${table}, column: ${column}, value: ${value}`);

          if (table === 'users') {
            // الحصول على ملفات المستخدمين
            const profiles = getLocalProfiles();
            console.log('Looking for user profile with id:', value);
            console.log('Available profiles:', profiles);

            // البحث عن ملف المستخدم بالمعرف
            const profile = profiles.find((p: any) => p.id === value);

            if (profile) {
              console.log('User profile found:', profile.email, 'with role:', profile.role);

              // تأكد من أن الملف يحتوي على دور
              if (!profile.role) {
                profile.role = 'admin'; // تعيين الدور الافتراضي إلى 'admin'
                console.log('Setting default role to admin for user:', profile.email);

                // حفظ التغييرات
                const updatedProfiles = profiles.map((p: any) =>
                  p.id === value ? profile : p
                );
                saveLocalProfiles(updatedProfiles);
              }

              return {
                data: profile,
                error: null
              };
            } else {
              // إذا لم يتم العثور على الملف الشخصي، قم بإنشاء ملف افتراضي
              console.log('User profile not found, creating default profile');

              // الحصول على المستخدم من قائمة المستخدمين
              const users = getLocalUsers();
              const user = users.find((u: any) => u.id === value);

              if (user) {
                // إنشاء ملف شخصي افتراضي
                const defaultProfile = {
                  id: value,
                  email: user.email,
                  firstName: 'User',
                  lastName: user.email.split('@')[0],
                  role: user.email.includes('admin') ? 'admin' : 'user', // تعيين الدور بناءً على البريد الإلكتروني
                  createdAt: new Date().toISOString()
                };

                console.log('Created default profile with role:', defaultProfile.role);

                // حفظ الملف الشخصي
                const updatedProfiles = [...profiles, defaultProfile];
                saveLocalProfiles(updatedProfiles);

                console.log('Created default profile for user:', user.email);

                return {
                  data: defaultProfile,
                  error: null
                };
              }
            }

            console.log('User profile not found and could not create default');
          }

          // محاكاة عدم وجود بيانات
          return { data: null, error: null };
        },
        limit: (limit: number) => ({
          data: Array(limit).fill(0).map((_, i) => ({
            id: i + 1,
            name: `Mock Item ${i + 1}`,
            createdAt: new Date().toISOString()
          })),
          error: null
        })
      })
    }),
    insert: async (data: any) => {
      console.log(`Inserting data into table: ${table}`, data);

      if (table === 'users') {
        // إذا كانت البيانات مصفوفة، تعامل مع العنصر الأول
        const userData = Array.isArray(data) ? data[0] : data;

        // إضافة معرف إذا لم يكن موجودًا
        if (!userData.id) {
          userData.id = 'mock-id-' + Date.now();
        }

        // التحقق من وجود الملف الشخصي
        const profiles = getLocalProfiles();
        const existingProfile = profiles.find((p: any) => p.id === userData.id);

        if (existingProfile) {
          // تحديث الملف الشخصي الموجود
          const updatedProfiles = profiles.map((profile: any) =>
            profile.id === userData.id ? { ...profile, ...userData } : profile
          );
          saveLocalProfiles(updatedProfiles);
          console.log('User profile updated:', userData);
        } else {
          // إنشاء ملف شخصي جديد
          const updatedProfiles = [...profiles, userData];
          saveLocalProfiles(updatedProfiles);
          console.log('User profile created:', userData);
        }

        return { data: userData, error: null };
      }

      return { data, error: null };
    },
    update: async (data: any) => {
      console.log(`Updating data in table: ${table}`, data);

      if (table === 'users') {
        // إذا كانت البيانات مصفوفة، تعامل مع العنصر الأول
        const userData = Array.isArray(data) ? data[0] : data;

        // تحديث ملف المستخدم
        const profiles = getLocalProfiles();
        const updatedProfiles = profiles.map((profile: any) =>
          profile.id === userData.id ? { ...profile, ...userData } : profile
        );

        saveLocalProfiles(updatedProfiles);

        console.log('User profile updated:', userData);

        return { data: userData, error: null };
      }

      return { data, error: null };
    },
    delete: async () => {
      console.log(`Deleting data from table: ${table}`);
      return { data: null, error: null };
    }
  }),
  rpc: (functionName: string) => {
    console.log(`Calling RPC function: ${functionName}`);
    return { data: null, error: null };
  }
};

// Use mock Supabase client instead of real one for local development
export const supabase = mockSupabase as any;

// Uncomment to use real Supabase client
// export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// وظيفة للتحقق من اتصال Supabase
export async function checkSupabaseConnection() {
  try {
    // في بيئة المحاكاة، دائمًا نعتبر الاتصال ناجحًا
    return true;
  } catch (error) {
    console.error('Supabase connection error:', error);
    return false;
  }
}