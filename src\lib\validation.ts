import { z } from 'zod';

// مخططات التحقق من المدخلات للنماذج المختلفة

// مخطط تسجيل الدخول
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'البريد الإلكتروني مطلوب' })
    .email({ message: 'البريد الإلكتروني غير صالح' }),
  password: z
    .string()
    .min(8, { message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' }),
});

// مخطط التسجيل
export const registerSchema = z
  .object({
    firstName: z.string().min(1, { message: 'الاسم الأول مطلوب' }),
    lastName: z.string().min(1, { message: 'الاسم الأخير مطلوب' }),
    email: z
      .string()
      .min(1, { message: 'ال<PERSON><PERSON>يد الإلكتروني مطلوب' })
      .email({ message: 'البريد الإلكتروني غير صالح' }),
    password: z
      .string()
      .min(8, { message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' })
      .regex(/[A-Z]/, { message: 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل' })
      .regex(/[a-z]/, { message: 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل' })
      .regex(/[0-9]/, { message: 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل' }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'كلمات المرور غير متطابقة',
    path: ['confirmPassword'],
  });

// مخطط نموذج الاتصال
export const contactFormSchema = z.object({
  name: z.string().min(1, { message: 'الاسم مطلوب' }),
  email: z
    .string()
    .min(1, { message: 'البريد الإلكتروني مطلوب' })
    .email({ message: 'البريد الإلكتروني غير صالح' }),
  phone: z.string().optional(),
  company: z.string().optional(),
  subject: z.string().min(1, { message: 'الموضوع مطلوب' }),
  message: z.string().min(10, { message: 'الرسالة يجب أن تكون 10 أحرف على الأقل' }),
  department: z.string(),
});

// مخطط نموذج طلب الخدمة
export const serviceRequestSchema = z.object({
  fullName: z.string().min(1, { message: 'الاسم الكامل مطلوب' }),
  email: z
    .string()
    .min(1, { message: 'البريد الإلكتروني مطلوب' })
    .email({ message: 'البريد الإلكتروني غير صالح' }),
  phone: z.string().min(1, { message: 'رقم الهاتف مطلوب' }),
  companyName: z.string().optional(),
  serviceDate: z.string().optional(),
  message: z.string().min(10, { message: 'الرسالة يجب أن تكون 10 أحرف على الأقل' }),
});

// مخطط نموذج طلب عرض السعر
export const quoteRequestSchema = z.object({
  companyName: z.string().min(1, { message: 'اسم الشركة مطلوب' }),
  contactName: z.string().min(1, { message: 'اسم جهة الاتصال مطلوب' }),
  email: z
    .string()
    .min(1, { message: 'البريد الإلكتروني مطلوب' })
    .email({ message: 'البريد الإلكتروني غير صالح' }),
  phone: z.string().min(1, { message: 'رقم الهاتف مطلوب' }),
  productType: z.string().min(1, { message: 'نوع المنتج مطلوب' }),
  specifications: z.string().optional(),
  targetQuantity: z.string().min(1, { message: 'الكمية المستهدفة مطلوبة' }),
  targetPrice: z.string().optional(),
  timeline: z.string().optional(),
  additionalNotes: z.string().optional(),
});
