'use client';

import { useState } from 'react';
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  MessageSquare,
  Send,
  Building2,
  Users,
  Globe,
  CheckCircle,
  Facebook,
  Twitter,
  Linkedin,
  Instagram
} from 'lucide-react';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Form, FormField } from '../components/ui/Form';
import { FormInput, validators } from '../components/ui/FormInput';
import { Input } from '../components/ui/Input';
import { useThemeStore } from '../stores/themeStore';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';
import { cn } from '../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';

// تعريف نوع البيانات للنموذج
type ContactFormData = {
  name: string;
  email: string;
  phone: string;
  company: string;
  subject: string;
  message: string;
  department: string;
};

export default function ContactPage() {
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // معالجة إرسال النموذج
  const handleFormSubmit = async (values: Record<string, any>) => {
    setIsLoading(true);
    try {
      // في بيئة الإنتاج، سيتم استدعاء نقطة نهاية حقيقية
      // هنا نقوم بمحاكاة الاستجابة للتطوير المحلي
      console.log('Form data submitted:', values);

      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSubmitted(true);
      setTimeout(() => setSubmitted(false), 5000);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <MessageSquare size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'اتصل بنا' : 'Contact Us'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'تواصل مع فريقنا لأي استفسارات أو دعم أو فرص عمل'
                  : 'Get in touch with our team for any inquiries, support, or business opportunities'}
              </p>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Contact Information Cards */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.2}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
          >
            {[
              {
                icon: <MapPin className="h-8 w-8 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? "زورنا" : "Visit Us",
                details: [
                  "123 Business Street",
                  "Suite 100",
                  "New York, NY 10001",
                  "United States"
                ]
              },
              {
                icon: <Phone className="h-8 w-8 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? "اتصل بنا" : "Call Us",
                details: [
                  "+****************",
                  "+****************",
                  "Mon-Fri 9:00-18:00"
                ]
              },
              {
                icon: <Mail className="h-8 w-8 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? "راسلنا" : "Email Us",
                details: [
                  "<EMAIL>",
                  "<EMAIL>",
                  "<EMAIL>"
                ]
              },
              {
                icon: <Clock className="h-8 w-8 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? "ساعات العمل" : "Business Hours",
                details: [
                  "Monday - Friday",
                  "9:00 AM - 6:00 PM",
                  "Eastern Time (ET)"
                ]
              }
            ].map((item, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <div className="flex items-center mb-4">
                    <div className={cn(
                      "p-3 rounded-full mr-3",
                      isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                    )}>
                      {item.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">{item.title}</h3>
                  </div>
                  <ul className="space-y-2">
                    {item.details.map((detail, i) => (
                      <li key={i} className="text-slate-600 dark:text-slate-300">{detail}</li>
                    ))}
                  </ul>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <ScrollAnimation animation="fade" delay={0.3}>
              <Card className="p-8 shadow-lg">
                <h2 className="text-2xl font-bold mb-6 text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'أرسل لنا رسالة' : 'Send Us a Message'}
                </h2>

                {submitted && (
                  <div className="mb-6 p-4 bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700 rounded-md">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                      <p className="text-green-800 dark:text-green-200">
                        {currentLanguage === 'ar' ? 'تم إرسال رسالتك بنجاح!' : 'Your message has been sent successfully!'}
                      </p>
                    </div>
                  </div>
                )}

                <Form
                  initialValues={{
                    name: '',
                    email: '',
                    phone: '',
                    company: '',
                    subject: '',
                    message: '',
                    department: 'general'
                  }}
                  onSubmit={handleFormSubmit}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormInput
                      name="name"
                      label={currentLanguage === 'ar' ? 'الاسم' : 'Name'}
                      placeholder={currentLanguage === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'}
                      required
                      validators={[validators.minLength(2)]}
                    />
                    <FormInput
                      name="email"
                      label={currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                      type="email"
                      placeholder={currentLanguage === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
                      required
                      validators={[validators.email]}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormInput
                      name="phone"
                      label={currentLanguage === 'ar' ? 'الهاتف' : 'Phone'}
                      type="tel"
                      placeholder={currentLanguage === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'}
                    />
                    <FormInput
                      name="company"
                      label={currentLanguage === 'ar' ? 'الشركة' : 'Company'}
                      placeholder={currentLanguage === 'ar' ? 'أدخل اسم شركتك' : 'Enter your company name'}
                    />
                  </div>

                  <FormInput
                    name="subject"
                    label={currentLanguage === 'ar' ? 'الموضوع' : 'Subject'}
                    placeholder={currentLanguage === 'ar' ? 'أدخل موضوع رسالتك' : 'Enter your message subject'}
                    required
                    validators={[validators.minLength(3)]}
                  />

                  <div>
                    <label className="block text-sm font-medium mb-2 text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? 'الرسالة' : 'Message'} *
                    </label>
                    <FormField
                      name="message"
                      required
                      validators={[validators.minLength(10)]}
                    >
                      {({ value, error, onChange, onBlur }) => (
                        <div>
                          <textarea
                            id="message"
                            name="message"
                            value={value || ''}
                            onChange={(e) => onChange(e.target.value)}
                            onBlur={onBlur}
                            placeholder={currentLanguage === 'ar' ? 'اكتب رسالتك هنا...' : 'Write your message here...'}
                            className={cn(
                              "w-full min-h-[150px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 resize-vertical",
                              isDarkMode ? "bg-slate-800 border-slate-700 text-white placeholder-slate-400" : "bg-white border-slate-300 text-slate-900 placeholder-slate-500",
                              error && "border-red-500 focus:ring-red-500"
                            )}
                            rows={6}
                          />
                          {error && (
                            <p className="mt-1 text-sm text-red-500">{error}</p>
                          )}
                        </div>
                      )}
                    </FormField>
                  </div>

                  <HoverAnimation animation="scale">
                    <Button
                      type="submit"
                      className="w-full flex items-center justify-center"
                      disabled={isLoading}
                    >
                      <Send className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                      {isLoading
                        ? (currentLanguage === 'ar' ? 'جاري الإرسال...' : 'Sending...')
                        : (currentLanguage === 'ar' ? 'إرسال الرسالة' : 'Send Message')
                      }
                    </Button>
                  </HoverAnimation>
                </Form>
              </Card>
            </ScrollAnimation>

            {/* Additional Information */}
            <ScrollAnimation animation="fade" delay={0.4}>
              <div className="space-y-8">
                <HoverAnimation animation="lift">
                  <Card className="p-6">
                    <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? 'تواصل معنا' : 'Connect With Us'}
                    </h3>
                    <div className={`flex ${currentLanguage === 'ar' ? 'space-x-reverse' : 'space-x-4'}`}>
                      <HoverAnimation animation="scale">
                        <a href="#" className="text-slate-600 dark:text-slate-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors p-2 rounded-full hover:bg-primary-50 dark:hover:bg-primary-900/20">
                          <Facebook size={24} />
                        </a>
                      </HoverAnimation>
                      <HoverAnimation animation="scale">
                        <a href="#" className="text-slate-600 dark:text-slate-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors p-2 rounded-full hover:bg-primary-50 dark:hover:bg-primary-900/20">
                          <Twitter size={24} />
                        </a>
                      </HoverAnimation>
                      <HoverAnimation animation="scale">
                        <a href="#" className="text-slate-600 dark:text-slate-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors p-2 rounded-full hover:bg-primary-50 dark:hover:bg-primary-900/20">
                          <Linkedin size={24} />
                        </a>
                      </HoverAnimation>
                      <HoverAnimation animation="scale">
                        <a href="#" className="text-slate-600 dark:text-slate-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors p-2 rounded-full hover:bg-primary-50 dark:hover:bg-primary-900/20">
                          <Instagram size={24} />
                        </a>
                      </HoverAnimation>
                    </div>
                  </Card>
                </HoverAnimation>

                <HoverAnimation animation="lift">
                  <Card className="p-6">
                    <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? 'المكاتب العالمية' : 'Global Offices'}
                    </h3>
                    <ScrollStagger
                      animation="slide"
                      direction="right"
                      staggerDelay={0.1}
                      className="space-y-4"
                    >
                      {[
                        {
                          city: currentLanguage === 'ar' ? "نيويورك" : "New York",
                          address: currentLanguage === 'ar' ? "123 شارع الأعمال، نيويورك 10001" : "123 Business Street, NY 10001",
                          phone: "+****************"
                        },
                        {
                          city: currentLanguage === 'ar' ? "لندن" : "London",
                          address: currentLanguage === 'ar' ? "456 طريق التجارة، EC1A 1BB" : "456 Commerce Road, EC1A 1BB",
                          phone: "+44 20 7123 4567"
                        },
                        {
                          city: currentLanguage === 'ar' ? "سنغافورة" : "Singapore",
                          address: currentLanguage === 'ar' ? "789 مركز التجارة، 018956" : "789 Trade Center, 018956",
                          phone: "+65 6789 0123"
                        }
                      ].map((office, index) => (
                        <div key={index} className={`flex items-start ${currentLanguage === 'ar' ? 'flex-row-reverse text-right' : ''}`}>
                          <MapPin className={`h-5 w-5 text-primary-500 dark:text-primary-400 mt-1 ${currentLanguage === 'ar' ? 'mr-0 ml-3' : 'mr-3'}`} />
                          <div>
                            <h4 className="font-medium text-slate-900 dark:text-white">{office.city}</h4>
                            <p className="text-sm text-slate-600 dark:text-slate-400">{office.address}</p>
                            <p className="text-sm text-slate-600 dark:text-slate-400">{office.phone}</p>
                          </div>
                        </div>
                      ))}
                    </ScrollStagger>
                  </Card>
                </HoverAnimation>

                <HoverAnimation animation="lift">
                  <Card className={cn("p-6", isDarkMode ? "bg-primary-900/20" : "bg-primary-50")}>
                    <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? 'هل تحتاج إلى مساعدة فورية؟' : 'Need Immediate Assistance?'}
                    </h3>
                    <p className="text-slate-600 dark:text-slate-300 mb-6">
                      {currentLanguage === 'ar'
                        ? 'فريق خدمة العملاء لدينا متاح على مدار الساعة طوال أيام الأسبوع لمساعدتك في الاستفسارات العاجلة.'
                        : 'Our customer service team is available 24/7 to help you with urgent inquiries.'}
                    </p>
                    <div className={`flex ${currentLanguage === 'ar' ? 'flex-row-reverse space-x-reverse' : ''} items-center space-x-4`}>
                      <HoverAnimation animation="scale">
                        <Button variant="primary" className="flex items-center">
                          <Phone className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                          {currentLanguage === 'ar' ? 'اتصل الآن' : 'Call Now'}
                        </Button>
                      </HoverAnimation>
                      <HoverAnimation animation="scale">
                        <Button variant="outline" className="flex items-center">
                          <MessageSquare className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                          {currentLanguage === 'ar' ? 'محادثة مباشرة' : 'Live Chat'}
                        </Button>
                      </HoverAnimation>
                    </div>
                  </Card>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'ابحث عن إجابات سريعة للأسئلة الشائعة'
                  : 'Find quick answers to common questions'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.4}
            className="max-w-4xl mx-auto space-y-6"
          >
            {[
              {
                question: currentLanguage === 'ar' ? "ما هي ساعات العمل لديكم؟" : "What are your business hours?",
                answer: currentLanguage === 'ar'
                  ? "مكتبنا الرئيسي مفتوح من الاثنين إلى الجمعة، من الساعة 9:00 صباحًا حتى 6:00 مساءً بالتوقيت الشرقي. ومع ذلك، فإن فريق الدعم لدينا متاح على مدار الساعة طوال أيام الأسبوع للمسائل العاجلة."
                  : "Our main office is open Monday through Friday, 9:00 AM to 6:00 PM Eastern Time. However, our support team is available 24/7 for urgent matters."
              },
              {
                question: currentLanguage === 'ar' ? "ما هي سرعة الرد المتوقعة؟" : "How quickly can I expect a response?",
                answer: currentLanguage === 'ar'
                  ? "نهدف إلى الرد على جميع الاستفسارات في غضون 24 ساعة. بالنسبة للأمور العاجلة، وقت الاستجابة لدينا عادة أقل من ساعتين."
                  : "We aim to respond to all inquiries within 24 hours. For urgent matters, our response time is typically under 2 hours."
              },
              {
                question: currentLanguage === 'ar' ? "هل توفرون الشحن الدولي؟" : "Do you offer international shipping?",
                answer: currentLanguage === 'ar'
                  ? "نعم، نقدم خدمة الشحن الدولي إلى معظم البلدان. تختلف أسعار الشحن وأوقات التسليم حسب الموقع."
                  : "Yes, we offer international shipping to most countries. Shipping rates and delivery times vary by location."
              },
              {
                question: currentLanguage === 'ar' ? "كيف يمكنني تتبع طلبي؟" : "How can I track my order?",
                answer: currentLanguage === 'ar'
                  ? "بمجرد شحن طلبك، ستتلقى رقم تتبع عبر البريد الإلكتروني. يمكنك استخدام هذا الرقم لتتبع شحنتك على موقعنا."
                  : "Once your order is shipped, you'll receive a tracking number via email. You can use this number to track your shipment on our website."
              }
            ].map((faq, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 border-l-4 border-primary-500 dark:border-primary-600">
                  <h3 className="text-lg font-semibold mb-3 text-slate-900 dark:text-white">{faq.question}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>
    </div>
  );
}