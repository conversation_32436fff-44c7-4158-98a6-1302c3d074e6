'use client';

import { HelpCircle } from 'lucide-react';
import { Card } from '../components/ui/Card';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';
import { useThemeStore } from '../stores/themeStore';
import { cn } from '../lib/utils';

export default function FAQPage() {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  return (
    <div className="container-custom py-12">
      <div className="max-w-4xl mx-auto">
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="flex items-center justify-center mb-8">
            <div className="inline-flex justify-center items-center w-24 h-24 rounded-full bg-primary-50 dark:bg-primary-900/20">
              <HelpCircle className="h-12 w-12 text-primary-500" />
            </div>
          </div>

          <h1 className="text-4xl font-bold text-center mb-8 text-slate-900 dark:text-white">
            {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
          </h1>
        </ScrollAnimation>

        <ScrollStagger
          animation="fade"
          staggerDelay={0.1}
          className="space-y-6"
        >
          {[
            {
              category: currentLanguage === 'ar' ? "الطلبات والشحن" : "Orders & Shipping",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "كم من الوقت يستغرق الشحن؟" : "How long does shipping take?",
                  a: currentLanguage === 'ar'
                    ? "يستغرق الشحن القياسي 5-7 أيام عمل داخل الولايات المتحدة القارية. الشحن السريع متاح للتسليم خلال 2-3 أيام عمل. تختلف أوقات الشحن الدولي حسب الموقع."
                    : "Standard shipping takes 5-7 business days within the continental US. Express shipping is available for 2-3 business days delivery. International shipping times vary by location."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تشحنون دولياً؟" : "Do you ship internationally?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نشحن إلى أكثر من 100 دولة حول العالم. تختلف أسعار الشحن الدولي وأوقات التسليم حسب الموقع."
                    : "Yes, we ship to over 100 countries worldwide. International shipping rates and delivery times vary by location."
                },
                {
                  q: currentLanguage === 'ar' ? "كيف يمكنني تتبع طلبي؟" : "How can I track my order?",
                  a: currentLanguage === 'ar'
                    ? "بمجرد شحن طلبك، ستتلقى رقم تتبع عبر البريد الإلكتروني. يمكنك أيضًا تتبع طلبك عن طريق تسجيل الدخول إلى حسابك."
                    : "Once your order ships, you'll receive a tracking number via email. You can also track your order by logging into your account."
                },
                {
                  q: currentLanguage === 'ar' ? "ما هي طرق الدفع التي تقبلونها؟" : "What payment methods do you accept?",
                  a: currentLanguage === 'ar'
                    ? "نقبل جميع بطاقات الائتمان الرئيسية، وباي بال، والتحويلات المصرفية للحسابات التجارية."
                    : "We accept all major credit cards, PayPal, and bank transfers for business accounts."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "الإرجاع واسترداد الأموال" : "Returns & Refunds",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "ما هي سياسة الإرجاع لديكم؟" : "What is your return policy?",
                  a: currentLanguage === 'ar'
                    ? "نقبل الإرجاع خلال 30 يومًا من التسليم لمعظم العناصر في حالتها الأصلية. قد تكون للطلبات المخصصة والمشتريات بالجملة شروط مختلفة."
                    : "We accept returns within 30 days of delivery for most items in their original condition. Custom orders and bulk purchases may have different terms."
                },
                {
                  q: currentLanguage === 'ar' ? "كيف أبدأ عملية الإرجاع؟" : "How do I initiate a return?",
                  a: currentLanguage === 'ar'
                    ? "اتصل بفريق خدمة العملاء للحصول على تصريح الإرجاع. بمجرد الموافقة، سنقدم تعليمات الشحن."
                    : "Contact our customer service team to receive a return authorization. Once approved, we'll provide shipping instructions."
                },
                {
                  q: currentLanguage === 'ar' ? "متى سأستلم المبلغ المسترد؟" : "When will I receive my refund?",
                  a: currentLanguage === 'ar'
                    ? "تتم معالجة المبالغ المستردة في غضون 5-7 أيام عمل من استلام المرتجع. سيتم إصدار المبلغ المسترد إلى طريقة الدفع الأصلية."
                    : "Refunds are processed within 5-7 business days of receiving your return. The refund will be issued to the original payment method."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون إرجاع مجاني؟" : "Do you offer free returns?",
                  a: currentLanguage === 'ar'
                    ? "شحن الإرجاع مجاني للعناصر المعيبة أو الأخطاء من جانبنا. قد تتطلب عمليات الإرجاع القياسية دفع رسوم شحن الإرجاع."
                    : "Return shipping is free for defective items or errors on our part. Standard returns may require return shipping payment."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "المنتجات والخدمات" : "Products & Services",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون أسعاراً خاصة للطلبات بالجملة؟" : "Do you offer bulk pricing?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نقدم أسعاراً خاصة للطلبات بالجملة. اتصل بفريق المبيعات للحصول على عرض سعر مخصص."
                    : "Yes, we offer special pricing for bulk orders. Contact our sales team for a custom quote."
                },
                {
                  q: currentLanguage === 'ar' ? "هل يمكنني تخصيص المنتجات؟" : "Can I customize products?",
                  a: currentLanguage === 'ar'
                    ? "نعم، يمكن تخصيص العديد من منتجاتنا. قد تنطبق الحد الأدنى لكميات الطلب."
                    : "Yes, many of our products can be customized. Minimum order quantities may apply."
                },
                {
                  q: currentLanguage === 'ar' ? "ما هي معايير الجودة التي تتبعونها؟" : "What quality standards do you follow?",
                  a: currentLanguage === 'ar'
                    ? "تلبي جميع منتجاتنا معايير الجودة الدولية وتخضع لاختبارات صارمة."
                    : "All our products meet international quality standards and undergo rigorous testing."
                },
                {
                  q: currentLanguage === 'ar' ? "هل تقدمون الدعم الفني؟" : "Do you provide technical support?",
                  a: currentLanguage === 'ar'
                    ? "نعم، فريق الدعم الفني لدينا متاح خلال ساعات العمل للمساعدة في الأسئلة المتعلقة بالمنتج."
                    : "Yes, our technical support team is available during business hours to assist with product-related questions."
                }
              ]
            },
            {
              category: currentLanguage === 'ar' ? "الحساب والأمان" : "Account & Security",
              questions: [
                {
                  q: currentLanguage === 'ar' ? "كيف أنشئ حساباً؟" : "How do I create an account?",
                  a: currentLanguage === 'ar'
                    ? "انقر على زر 'التسجيل' في الزاوية العلوية اليمنى واتبع عملية التسجيل."
                    : "Click the 'Sign Up' button in the top right corner and follow the registration process."
                },
                {
                  q: currentLanguage === 'ar' ? "هل معلومات الدفع الخاصة بي آمنة؟" : "Is my payment information secure?",
                  a: currentLanguage === 'ar'
                    ? "نعم، نستخدم تشفيراً بمعايير الصناعة لحماية معلومات الدفع الخاصة بك."
                    : "Yes, we use industry-standard encryption to protect your payment information."
                },
                {
                  q: currentLanguage === 'ar' ? "هل يمكنني تغيير تفاصيل حسابي؟" : "Can I change my account details?",
                  a: currentLanguage === 'ar'
                    ? "نعم، يمكنك تحديث معلومات حسابك في أي وقت عن طريق تسجيل الدخول وزيارة إعدادات حسابك."
                    : "Yes, you can update your account information anytime by logging in and visiting your account settings."
                },
                {
                  q: currentLanguage === 'ar' ? "ماذا يحدث إذا نسيت كلمة المرور الخاصة بي؟" : "What happens if I forget my password?",
                  a: currentLanguage === 'ar'
                    ? "استخدم رابط 'نسيت كلمة المرور' في صفحة تسجيل الدخول لإعادة تعيين كلمة المرور الخاصة بك عبر البريد الإلكتروني."
                    : "Use the 'Forgot Password' link on the login page to reset your password via email."
                }
              ]
            }
          ].map((section, index) => (
            <div key={index} className="mb-12">
              <h2 className="text-2xl font-semibold mb-6 text-slate-900 dark:text-white">{section.category}</h2>
              <ScrollStagger
                animation="slide"
                direction="up"
                staggerDelay={0.05}
                className="space-y-4"
              >
                {section.questions.map((item, i) => (
                  <HoverAnimation key={i} animation="lift">
                    <Card className="p-6">
                      <h3 className="text-lg font-semibold mb-3 text-slate-900 dark:text-white">{item.q}</h3>
                      <p className="text-slate-600 dark:text-slate-300">{item.a}</p>
                    </Card>
                  </HoverAnimation>
                ))}
              </ScrollStagger>
            </div>
          ))}
        </ScrollStagger>

        <ScrollAnimation animation="fade" delay={0.4} className="mt-12">
          <div className={cn("p-8 rounded-lg", isDarkMode ? "bg-slate-800" : "bg-primary-50")}>
            <h2 className="text-2xl font-semibold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'هل لا تزال لديك أسئلة؟' : 'Still Have Questions?'}
            </h2>
            <p className="text-slate-600 dark:text-slate-300 mb-4">
              {currentLanguage === 'ar'
                ? 'فريق خدمة العملاء لدينا هنا للمساعدة. اتصل بنا:'
                : 'Our customer service team is here to help. Contact us:'}
            </p>
            <ul className="list-none space-y-3 text-slate-700 dark:text-slate-300">
              <li className="flex items-center">
                <span className="font-medium">{currentLanguage === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}</span>
                <span className="ml-2"><EMAIL></span>
              </li>
              <li className="flex items-center">
                <span className="font-medium">{currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'}</span>
                <span className="ml-2">+****************</span>
              </li>
              <li className="flex items-center">
                <span className="font-medium">{currentLanguage === 'ar' ? 'ساعات العمل:' : 'Hours:'}</span>
                <span className="ml-2">{currentLanguage === 'ar' ? 'الاثنين - الجمعة، 9:00 صباحاً - 6:00 مساءً بالتوقيت الشرقي' : 'Monday - Friday, 9:00 AM - 6:00 PM ET'}</span>
              </li>
            </ul>
          </div>
        </ScrollAnimation>
      </div>
    </div>
  );
}