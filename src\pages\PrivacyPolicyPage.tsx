'use client';

import Link from 'next/link';
import { Shield } from 'lucide-react';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';

export default function PrivacyPolicyPage() {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div className="container-custom py-12">
      <div className="max-w-4xl mx-auto">
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="flex items-center justify-center mb-8">
            <div className="inline-flex justify-center items-center w-24 h-24 rounded-full bg-primary-50 dark:bg-primary-900/20">
              <Shield className="h-12 w-12 text-primary-500" />
            </div>
          </div>

          <h1 className="text-4xl font-bold text-center mb-8 text-slate-900 dark:text-white">
            {currentLanguage === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
          </h1>
        </ScrollAnimation>

        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="prose prose-lg max-w-none dark:prose-invert">
            <p className="text-slate-600 dark:text-slate-400">
              {currentLanguage === 'ar' ? 'آخر تحديث: 15 مارس 2025' : 'Last updated: March 15, 2025'}
            </p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '1. مقدمة' : '1. Introduction'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'كوميرس برو ("نحن" أو "لنا") تحترم خصوصيتك وملتزمة بحماية بياناتك الشخصية. توضح سياسة الخصوصية هذه كيفية جمعنا واستخدامنا وحماية معلوماتك عند استخدام خدماتنا.'
                : 'CommercePro ("we," "our," or "us") respects your privacy and is committed to protecting your personal data. This privacy policy explains how we collect, use, and safeguard your information when you use our services.'}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '2. المعلومات التي نجمعها' : '2. Information We Collect'}
            </h2>
            <h3 className="text-xl font-semibold mb-3">
              {currentLanguage === 'ar' ? '2.1 المعلومات الشخصية' : '2.1 Personal Information'}
            </h3>
            <p>{currentLanguage === 'ar' ? 'نجمع المعلومات التي تقدمها لنا مباشرة، بما في ذلك:' : 'We collect information that you provide directly to us, including:'}</p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'الاسم ومعلومات الاتصال' : 'Name and contact information'}</li>
              <li>{currentLanguage === 'ar' ? 'تفاصيل الشركة' : 'Company details'}</li>
              <li>{currentLanguage === 'ar' ? 'معلومات الدفع' : 'Payment information'}</li>
              <li>{currentLanguage === 'ar' ? 'بيانات اعتماد الحساب' : 'Account credentials'}</li>
              <li>{currentLanguage === 'ar' ? 'تفضيلات الاتصال' : 'Communication preferences'}</li>
            </ul>

            <h3 className="text-xl font-semibold mb-3">
              {currentLanguage === 'ar' ? '2.2 المعلومات المجمعة تلقائيًا' : '2.2 Automatically Collected Information'}
            </h3>
            <p>{currentLanguage === 'ar' ? 'عندما تستخدم خدماتنا، نقوم تلقائيًا بجمع:' : 'When you use our services, we automatically collect:'}</p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'معلومات الجهاز' : 'Device information'}</li>
              <li>{currentLanguage === 'ar' ? 'بيانات السجل' : 'Log data'}</li>
              <li>{currentLanguage === 'ar' ? 'معلومات الاستخدام' : 'Usage information'}</li>
              <li>{currentLanguage === 'ar' ? 'بيانات الموقع' : 'Location data'}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '3. كيف نستخدم معلوماتك' : '3. How We Use Your Information'}
            </h2>
            <p>{currentLanguage === 'ar' ? 'نستخدم المعلومات المجمعة لـ:' : 'We use the collected information for:'}</p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'تقديم وتحسين خدماتنا' : 'Providing and improving our services'}</li>
              <li>{currentLanguage === 'ar' ? 'معالجة المعاملات' : 'Processing transactions'}</li>
              <li>{currentLanguage === 'ar' ? 'التواصل مع المستخدمين' : 'Communication with users'}</li>
              <li>{currentLanguage === 'ar' ? 'التحليلات وتحسين الخدمة' : 'Analytics and service optimization'}</li>
              <li>{currentLanguage === 'ar' ? 'الامتثال القانوني' : 'Legal compliance'}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '4. مشاركة المعلومات' : '4. Information Sharing'}
            </h2>
            <p>{currentLanguage === 'ar' ? 'قد نشارك معلوماتك مع:' : 'We may share your information with:'}</p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'مقدمي الخدمات وشركاء الأعمال' : 'Service providers and business partners'}</li>
              <li>{currentLanguage === 'ar' ? 'السلطات القانونية عند الطلب' : 'Legal authorities when required'}</li>
              <li>{currentLanguage === 'ar' ? 'أطراف أخرى بموافقتك' : 'Other parties with your consent'}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '5. أمن البيانات' : '5. Data Security'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'نقوم بتنفيذ إجراءات تقنية وتنظيمية مناسبة لحماية معلوماتك الشخصية ضد الوصول غير المصرح به أو التغيير أو الكشف أو التدمير.'
                : 'We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.'}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '6. حقوقك' : '6. Your Rights'}
            </h2>
            <p>{currentLanguage === 'ar' ? 'لديك الحق في:' : 'You have the right to:'}</p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'الوصول إلى معلوماتك الشخصية' : 'Access your personal information'}</li>
              <li>{currentLanguage === 'ar' ? 'تصحيح البيانات غير الدقيقة' : 'Correct inaccurate data'}</li>
              <li>{currentLanguage === 'ar' ? 'طلب حذف بياناتك' : 'Request deletion of your data'}</li>
              <li>{currentLanguage === 'ar' ? 'الاعتراض على معالجة البيانات' : 'Object to data processing'}</li>
              <li>{currentLanguage === 'ar' ? 'نقل البيانات' : 'Data portability'}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? '7. اتصل بنا' : '7. Contact Us'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى الاتصال بنا على:'
                : 'If you have any questions about this Privacy Policy, please contact us at:'}
            </p>
            <ul className="list-none pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'البريد الإلكتروني:' : 'Email:'} <EMAIL></li>
              <li>{currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'} +****************</li>
              <li>{currentLanguage === 'ar' ? 'العنوان:' : 'Address:'} 123 Business Street, Suite 100, New York, NY 10001</li>
            </ul>
          </section>
          </div>
        </ScrollAnimation>
      </div>
    </div>
  );
}