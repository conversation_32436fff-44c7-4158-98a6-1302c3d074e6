'use client';

import { RefreshCw, Package, CheckCircle, AlertCircle } from 'lucide-react';
import { Card } from '../components/ui/Card';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';

export default function ReturnsPage() {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div className="container-custom py-12">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-8">
          <RefreshCw className="h-12 w-12 text-primary-500" />
        </div>

        <h1 className="text-4xl font-bold text-center mb-8">
          {currentLanguage === 'ar' ? 'الإرجاع والاستبدال' : 'Returns & Exchanges'}
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {[
            {
              icon: <Package className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "إرجاع سهل" : "Easy Returns",
              description: currentLanguage === 'ar'
                ? "قم بإرجاع العناصر المؤهلة في غضون 30 يومًا من التسليم للحصول على استرداد كامل."
                : "Return eligible items within 30 days of delivery for a full refund."
            },
            {
              icon: <RefreshCw className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "استبدال مجاني" : "Free Exchanges",
              description: currentLanguage === 'ar'
                ? "استبدل العناصر بحجم أو لون مختلف دون أي تكلفة إضافية."
                : "Exchange items for a different size or color at no additional cost."
            },
            {
              icon: <CheckCircle className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "ضمان الجودة" : "Quality Guarantee",
              description: currentLanguage === 'ar'
                ? "جميع المنتجات مدعومة بضمان الرضا الخاص بنا."
                : "All products are backed by our satisfaction guarantee."
            },
            {
              icon: <AlertCircle className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "حماية من التلف" : "Damage Protection",
              description: currentLanguage === 'ar'
                ? "العناصر التي تتعرض للتلف أثناء الشحن مؤهلة للاستبدال الفوري."
                : "Items damaged during shipping are eligible for immediate replacement."
            }
          ].map((item, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center mb-4">
                {item.icon}
                <h3 className="text-xl font-semibold ml-3">{item.title}</h3>
              </div>
              <p className="text-slate-600">{item.description}</p>
            </Card>
          ))}
        </div>

        <div className="prose prose-lg max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'سياسة الإرجاع' : 'Return Policy'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'نقبل الإرجاع في غضون 30 يومًا من التسليم لمعظم العناصر في حالتها الأصلية. تنطبق بعض القيود على الطلبات المخصصة والمشتريات بالجملة.'
                : 'We accept returns within 30 days of delivery for most items in their original condition. Some restrictions apply to custom orders and bulk purchases.'}
            </p>

            <h3 className="text-xl font-semibold mt-6 mb-3">
              {currentLanguage === 'ar' ? 'العناصر المؤهلة' : 'Eligible Items'}
            </h3>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'غير مستخدمة وفي العبوة الأصلية' : 'Unused and in original packaging'}</li>
              <li>{currentLanguage === 'ar' ? 'جميع العلامات الأصلية مرفقة' : 'All original tags attached'}</li>
              <li>{currentLanguage === 'ar' ? 'لا توجد علامات تآكل أو تلف' : 'No signs of wear or damage'}</li>
              <li>{currentLanguage === 'ar' ? 'مصحوبة بالإيصال الأصلي' : 'Accompanied by original receipt'}</li>
            </ul>

            <h3 className="text-xl font-semibold mt-6 mb-3">
              {currentLanguage === 'ar' ? 'العناصر غير القابلة للإرجاع' : 'Non-Returnable Items'}
            </h3>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'الطلبات المخصصة أو الشخصية' : 'Custom or personalized orders'}</li>
              <li>{currentLanguage === 'ar' ? 'عناصر التصفية' : 'Clearance items'}</li>
              <li>{currentLanguage === 'ar' ? 'المواد الخطرة' : 'Hazardous materials'}</li>
              <li>{currentLanguage === 'ar' ? 'منتجات العناية الشخصية' : 'Personal care items'}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'عملية الاستبدال' : 'Exchange Process'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'لاستبدال عنصر:' : 'To exchange an item:'}
            </p>
            <ol className="list-decimal pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'اتصل بفريق خدمة العملاء لدينا' : 'Contact our customer service team'}</li>
              <li>{currentLanguage === 'ar' ? 'احصل على تصريح الإرجاع' : 'Receive a return authorization'}</li>
              <li>{currentLanguage === 'ar' ? 'أعد شحن العنصر إلينا' : 'Ship the item back to us'}</li>
              <li>{currentLanguage === 'ar' ? 'اختر العنصر البديل الخاص بك' : 'Select your replacement item'}</li>
              <li>{currentLanguage === 'ar' ? 'استلم البديل الخاص بك' : 'Receive your exchange'}</li>
            </ol>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'معلومات استرداد الأموال' : 'Refund Information'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'تتم معالجة المبالغ المستردة في غضون 5-7 أيام عمل من استلام المرتجعات. سيتم إصدار المبلغ المسترد إلى طريقة الدفع الأصلية. تكاليف الشحن غير قابلة للاسترداد ما لم يكن الإرجاع بسبب خطأ من جانبنا.'
                : 'Refunds are processed within 5-7 business days of receiving your return. The refund will be issued to the original payment method. Shipping costs are non-refundable unless the return is due to our error.'}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'العناصر التالفة' : 'Damaged Items'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'إذا استلمت عنصرًا تالفًا:' : 'If you receive a damaged item:'}
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>{currentLanguage === 'ar' ? 'وثق التلف بالصور' : 'Document the damage with photos'}</li>
              <li>{currentLanguage === 'ar' ? 'اتصل بنا في غضون 48 ساعة من التسليم' : 'Contact us within 48 hours of delivery'}</li>
              <li>{currentLanguage === 'ar' ? 'احتفظ بجميع العبوات الأصلية' : 'Keep all original packaging'}</li>
              <li>{currentLanguage === 'ar' ? 'سنقوم بترتيب شحن الإرجاع' : 'We\'ll arrange for return shipping'}</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'الاتصال بقسم الإرجاع' : 'Contact Returns Department'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'للأسئلة حول الإرجاع أو الاستبدال:' : 'For questions about returns or exchanges:'}
            </p>
            <ul className="list-none pl-0">
              <li>{currentLanguage === 'ar' ? 'البريد الإلكتروني:' : 'Email:'} <EMAIL></li>
              <li>{currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'} +****************</li>
              <li>{currentLanguage === 'ar' ? 'ساعات العمل: الاثنين - الجمعة، 9:00 صباحًا - 6:00 مساءً بالتوقيت الشرقي' : 'Hours: Monday - Friday, 9:00 AM - 6:00 PM ET'}</li>
            </ul>
          </section>
        </div>
      </div>
    </div>
  );
}