import { useState, useEffect } from 'react';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { FormInput } from '../components/ui/FormInput';
import { encrypt, decrypt, getUserSecretKey, secureLocalStorage } from '../lib/encryption';
import { defaultRateLimiter } from '../lib/rateLimiter';
import { defaultCache } from '../lib/localCache';
import { useAuthStore } from '../stores/authStore';

// نوع البيانات للاختبار
interface TestData {
  id: number;
  name: string;
  value: string;
  timestamp: number;
}

export default function SecurityTestPage() {
  const [testData, setTestData] = useState<TestData>({
    id: 1,
    name: 'اختبار',
    value: 'بيانات سرية',
    timestamp: Date.now()
  });
  
  const [encryptedData, setEncryptedData] = useState('');
  const [decryptedData, setDecryptedData] = useState<TestData | null>(null);
  const [secretKey, setSecretKey] = useState('');
  const [secureStorageKey, setSecureStorageKey] = useState('test-secure-storage');
  const [secureStorageValue, setSecureStorageValue] = useState('');
  const [retrievedValue, setRetrievedValue] = useState('');
  const [rateLimitInfo, setRateLimitInfo] = useState({ remaining: 0, allowed: false });
  
  const { user } = useAuthStore();
  
  // تحديث مفتاح التشفير عند تغيير المستخدم
  useEffect(() => {
    const key = getUserSecretKey(user?.id);
    setSecretKey(key);
  }, [user]);
  
  // اختبار التشفير
  const handleEncrypt = () => {
    const encrypted = encrypt(testData, secretKey);
    setEncryptedData(encrypted);
  };
  
  // اختبار فك التشفير
  const handleDecrypt = () => {
    if (!encryptedData) return;
    
    const decrypted = decrypt<TestData>(encryptedData, secretKey);
    setDecryptedData(decrypted);
  };
  
  // اختبار التخزين المحلي الآمن
  const handleSecureStore = () => {
    if (!secureStorageKey || !secureStorageValue) return;
    
    secureLocalStorage.setItem(secureStorageKey, secureStorageValue, user?.id);
    setSecureStorageValue('');
  };
  
  // اختبار استرداد البيانات من التخزين المحلي الآمن
  const handleSecureRetrieve = () => {
    if (!secureStorageKey) return;
    
    const value = secureLocalStorage.getItem<string>(secureStorageKey, user?.id);
    setRetrievedValue(value || 'لا توجد بيانات');
  };
  
  // اختبار مسح التخزين المحلي الآمن
  const handleSecureClear = () => {
    if (!secureStorageKey) return;
    
    secureLocalStorage.removeItem(secureStorageKey);
    setRetrievedValue('تم المسح');
  };
  
  // اختبار Rate Limiting
  const handleRateLimitTest = () => {
    const allowed = defaultRateLimiter.isAllowed();
    
    if (allowed) {
      defaultRateLimiter.logRequest();
    }
    
    setRateLimitInfo({
      remaining: defaultRateLimiter.getRemainingRequests(),
      allowed
    });
  };
  
  // اختبار التخزين المؤقت
  const handleCacheTest = () => {
    // تخزين البيانات في التخزين المؤقت
    defaultCache.set('cache-test', {
      data: testData,
      timestamp: Date.now()
    });
    
    // استرداد البيانات من التخزين المؤقت
    const cachedData = defaultCache.get('cache-test');
    
    setRetrievedValue(JSON.stringify(cachedData, null, 2));
  };
  
  return (
    <div className="container-custom py-12">
      <h1 className="text-3xl font-bold mb-8">اختبار الأمان</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار التشفير</h2>
          
          <div className="mb-4">
            <p className="mb-2"><strong>البيانات الأصلية:</strong></p>
            <pre className="bg-slate-100 dark:bg-slate-800 p-2 rounded-md text-xs overflow-auto">
              {JSON.stringify(testData, null, 2)}
            </pre>
          </div>
          
          <div className="mb-4">
            <p className="mb-2"><strong>مفتاح التشفير:</strong></p>
            <pre className="bg-slate-100 dark:bg-slate-800 p-2 rounded-md text-xs overflow-auto">
              {secretKey}
            </pre>
          </div>
          
          <div className="flex gap-4 mb-4">
            <Button onClick={handleEncrypt}>تشفير</Button>
            <Button onClick={handleDecrypt} disabled={!encryptedData}>فك التشفير</Button>
          </div>
          
          {encryptedData && (
            <div className="mb-4">
              <p className="mb-2"><strong>البيانات المشفرة:</strong></p>
              <pre className="bg-slate-100 dark:bg-slate-800 p-2 rounded-md text-xs overflow-auto">
                {encryptedData}
              </pre>
            </div>
          )}
          
          {decryptedData && (
            <div>
              <p className="mb-2"><strong>البيانات بعد فك التشفير:</strong></p>
              <pre className="bg-slate-100 dark:bg-slate-800 p-2 rounded-md text-xs overflow-auto">
                {JSON.stringify(decryptedData, null, 2)}
              </pre>
            </div>
          )}
        </Card>
        
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار التخزين المحلي الآمن</h2>
          
          <div className="space-y-4 mb-4">
            <FormInput
              label="مفتاح التخزين"
              value={secureStorageKey}
              onChange={(e) => setSecureStorageKey(e.target.value)}
            />
            
            <FormInput
              label="القيمة"
              value={secureStorageValue}
              onChange={(e) => setSecureStorageValue(e.target.value)}
            />
          </div>
          
          <div className="flex gap-4 mb-4">
            <Button onClick={handleSecureStore} disabled={!secureStorageKey || !secureStorageValue}>
              تخزين
            </Button>
            <Button onClick={handleSecureRetrieve} disabled={!secureStorageKey}>
              استرداد
            </Button>
            <Button onClick={handleSecureClear} disabled={!secureStorageKey} variant="outline">
              مسح
            </Button>
          </div>
          
          {retrievedValue && (
            <div>
              <p className="mb-2"><strong>القيمة المستردة:</strong></p>
              <pre className="bg-slate-100 dark:bg-slate-800 p-2 rounded-md text-xs overflow-auto">
                {retrievedValue}
              </pre>
            </div>
          )}
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار Rate Limiting</h2>
          
          <Button onClick={handleRateLimitTest} className="mb-4">
            اختبار Rate Limiting
          </Button>
          
          <div>
            <p><strong>الطلبات المتبقية:</strong> {rateLimitInfo.remaining}</p>
            <p><strong>مسموح بالطلب:</strong> {rateLimitInfo.allowed ? 'نعم' : 'لا'}</p>
          </div>
        </Card>
        
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار التخزين المؤقت</h2>
          
          <Button onClick={handleCacheTest} className="mb-4">
            اختبار التخزين المؤقت
          </Button>
        </Card>
      </div>
    </div>
  );
}
