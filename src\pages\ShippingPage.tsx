'use client';

import { Truck, Package, Globe, Clock } from 'lucide-react';
import { Card } from '../components/ui/Card';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';

export default function ShippingPage() {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div className="container-custom py-12">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center mb-8">
          <Truck className="h-12 w-12 text-primary-500" />
        </div>

        <h1 className="text-4xl font-bold text-center mb-8">
          {currentLanguage === 'ar' ? 'معلومات الشحن' : 'Shipping Information'}
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {[
            {
              icon: <Globe className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "الشحن الدولي" : "International Shipping",
              description: currentLanguage === 'ar'
                ? "نشحن إلى أكثر من 100 دولة حول العالم مع ناقلين موثوقين وأسعار تنافسية."
                : "We ship to over 100 countries worldwide with reliable carriers and competitive rates."
            },
            {
              icon: <Clock className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "وقت المعالجة" : "Processing Time",
              description: currentLanguage === 'ar'
                ? "تتم معالجة الطلبات خلال 1-2 يوم عمل قبل الشحن."
                : "Orders are processed within 1-2 business days before shipping."
            },
            {
              icon: <Package className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "التتبع" : "Tracking",
              description: currentLanguage === 'ar'
                ? "تتضمن جميع الطلبات معلومات التتبع المرسلة عبر البريد الإلكتروني."
                : "All orders include tracking information sent via email."
            },
            {
              icon: <Truck className="h-8 w-8 text-primary-500" />,
              title: currentLanguage === 'ar' ? "خيارات التوصيل" : "Delivery Options",
              description: currentLanguage === 'ar'
                ? "اختر من بين طرق الشحن القياسية أو السريعة أو ذات الأولوية."
                : "Choose from standard, express, or priority shipping methods."
            }
          ].map((item, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center mb-4">
                {item.icon}
                <h3 className="text-xl font-semibold ml-3">{item.title}</h3>
              </div>
              <p className="text-slate-600">{item.description}</p>
            </Card>
          ))}
        </div>

        <div className="prose prose-lg max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">Shipping Methods</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr>
                    <th>Method</th>
                    <th>Delivery Time</th>
                    <th>Cost</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Standard</td>
                    <td>5-7 business days</td>
                    <td>From $9.99</td>
                  </tr>
                  <tr>
                    <td>Express</td>
                    <td>2-3 business days</td>
                    <td>From $19.99</td>
                  </tr>
                  <tr>
                    <td>Priority</td>
                    <td>1-2 business days</td>
                    <td>From $29.99</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'الشحن المجاني' : 'Free Shipping'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'الطلبات التي تزيد عن 500 دولار تؤهلك للحصول على شحن قياسي مجاني داخل الولايات المتحدة القارية. الطلبات الدولية التي تزيد عن 2000 دولار تحصل على شحن سريع مجاني.'
                : 'Orders over $500 qualify for free standard shipping within the continental United States. International orders over $2000 receive free express shipping.'}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'الشحن الدولي' : 'International Shipping'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'تختلف أسعار الشحن الدولي وأوقات التسليم حسب الموقع. لا تشمل رسوم الاستيراد والضرائب والرسوم في سعر المنتج أو تكلفة الشحن. هذه الرسوم هي مسؤولية المشتري.'
                : 'International shipping rates and delivery times vary by location. Import duties, taxes, and charges are not included in the item price or shipping cost. These charges are the buyer\'s responsibility.'}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'تتبع طلبك' : 'Tracking Your Order'}
            </h2>
            <p>
              {currentLanguage === 'ar'
                ? 'بمجرد شحن طلبك، ستتلقى رسالة بريد إلكتروني للتأكيد مع معلومات التتبع. يمكنك أيضًا تتبع طلبك عن طريق تسجيل الدخول إلى حسابك على موقعنا.'
                : 'Once your order ships, you\'ll receive a confirmation email with tracking information. You can also track your order by logging into your account on our website.'}
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">
              {currentLanguage === 'ar' ? 'الاتصال بدعم الشحن' : 'Contact Shipping Support'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'للأسئلة حول الشحن، اتصل بفريق الدعم لدينا:' : 'For questions about shipping, contact our support team:'}
            </p>
            <ul className="list-none pl-0">
              <li>{currentLanguage === 'ar' ? 'البريد الإلكتروني:' : 'Email:'} <EMAIL></li>
              <li>{currentLanguage === 'ar' ? 'الهاتف:' : 'Phone:'} +****************</li>
              <li>{currentLanguage === 'ar' ? 'ساعات العمل: الاثنين - الجمعة، 9:00 صباحًا - 6:00 مساءً بالتوقيت الشرقي' : 'Hours: Monday - Friday, 9:00 AM - 6:00 PM ET'}</li>
            </ul>
          </section>
        </div>
      </div>
    </div>
  );
}