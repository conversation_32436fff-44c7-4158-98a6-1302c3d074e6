import Link from 'next/link';
import { Card } from '../components/ui/Card';

export default function TestLinksPage() {
  // قائمة بصفحات الاختبار
  const testPages = [
    {
      title: 'اختبار React Query',
      description: 'اختبار استخدام React Query للاستعلامات والتعديلات والتخزين المؤقت',
      path: '/test-query',
      icon: '🔄'
    },
    {
      title: 'اختبار الأمان',
      description: 'اختبار التشفير والتخزين المحلي الآمن وRate Limiting',
      path: '/test-security',
      icon: '🔒'
    }
  ];

  return (
    <div className="container-custom py-12">
      <h1 className="text-3xl font-bold mb-8">صفحات الاختبار</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {testPages.map((page, index) => (
          <Link key={index} href={page.path} className="block">
            <Card className="p-6 h-full hover:shadow-md transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <span className="text-4xl mr-4">{page.icon}</span>
                <h2 className="text-xl font-bold">{page.title}</h2>
              </div>
              <p className="text-slate-600 dark:text-slate-300 mb-4">{page.description}</p>
              <div className="text-primary-500 font-medium">فتح الصفحة &rarr;</div>
            </Card>
          </Link>
        ))}
      </div>

      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-4">معلومات عن التحسينات</h2>

        <Card className="p-6 mb-8">
          <h3 className="text-xl font-bold mb-4">React Query</h3>
          <p className="mb-4">تم تنفيذ React Query لتحسين إدارة حالة البيانات وتنفيذ استراتيجيات التخزين المؤقت المتقدمة. المميزات المنفذة:</p>
          <ul className="list-disc list-inside space-y-2 mb-4">
            <li>إدارة حالة الاستعلامات والتعديلات</li>
            <li>التخزين المؤقت التلقائي للبيانات</li>
            <li>إعادة تعيين ذاكرة التخزين المؤقت عند التعديل</li>
            <li>تحميل البيانات المسبق</li>
          </ul>
        </Card>

        <Card className="p-6 mb-8">
          <h3 className="text-xl font-bold mb-4">تحسين التخزين المؤقت</h3>
          <p className="mb-4">تم تنفيذ آليات متقدمة للتخزين المؤقت لتحسين الأداء:</p>
          <ul className="list-disc list-inside space-y-2 mb-4">
            <li>التخزين المؤقت المحلي مع التشفير</li>
            <li>التحقق من صلاحية البيانات المخزنة</li>
            <li>تحميل البيانات المسبق للصفحات المتوقع زيارتها</li>
          </ul>
        </Card>

        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">تحسين الأمان</h3>
          <p className="mb-4">تم تنفيذ تقنيات أمان متقدمة لحماية البيانات والمستخدمين:</p>
          <ul className="list-disc list-inside space-y-2 mb-4">
            <li>CSRF Protection لمنع هجمات طلب التزوير المتعدية للمواقع</li>
            <li>التحقق من المدخلات باستخدام Zod</li>
            <li>Rate Limiting لمنع هجمات حجب الخدمة</li>
            <li>تشفير البيانات المحلية</li>
            <li>تحسين آلية المصادقة</li>
          </ul>
        </Card>
      </div>
    </div>
  );
}
