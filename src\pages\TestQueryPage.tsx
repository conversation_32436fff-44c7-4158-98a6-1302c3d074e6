import { useState } from 'react';
import { useApiQuery, useApiMutation, useLocalStorageQuery } from '../hooks/useApi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { defaultCache } from '../lib/localCache';
import { defaultRateLimiter } from '../lib/rateLimiter';

// نوع البيانات للاختبار
interface TestData {
  id: number;
  title: string;
  completed: boolean;
}

export default function TestQueryPage() {
  const [queryId, setQueryId] = useState(1);
  const [showCacheInfo, setShowCacheInfo] = useState(false);
  const [cacheStats, setCacheStats] = useState({ hits: 0, misses: 0 });

  // استخدام React Query للحصول على البيانات
  const { data, isLoading, isError, refetch } = useApiQuery<TestData>(
    ['test', queryId.toString()],
    `https://jsonplaceholder.typicode.com/todos/${queryId}`,
    {
      onSuccess: () => {
        // زيادة عدد مرات نجاح التخزين المؤقت
        setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
      },
      onError: () => {
        // زيادة عدد مرات فشل التخزين المؤقت
        setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
      }
    }
  );

  // استخدام React Query للتعديل
  const mutation = useApiMutation<TestData, Partial<TestData>>(
    'https://jsonplaceholder.typicode.com/todos',
    'post'
  );

  // استخدام التخزين المؤقت المحلي
  const localStorageQuery = useLocalStorageQuery<TestData>(
    ['local-test', queryId.toString()],
    `https://jsonplaceholder.typicode.com/todos/${queryId}`
  );

  // تغيير معرف الاستعلام
  const handleChangeId = (increment: number) => {
    setQueryId(prev => Math.max(1, prev + increment));
  };

  // إرسال بيانات جديدة
  const handleSubmit = () => {
    mutation.mutate({
      title: `اختبار جديد ${Date.now()}`,
      completed: false,
      userId: 1
    });
  };

  // مسح التخزين المؤقت
  const handleClearCache = () => {
    defaultCache.clear();
    setCacheStats({ hits: 0, misses: 0 });
  };

  // الحصول على معلومات Rate Limiter
  const getRateLimiterInfo = () => {
    const remaining = defaultRateLimiter.getRemainingRequests();
    const resetTime = defaultRateLimiter.getResetTime();
    return {
      remaining,
      resetTime: Math.ceil(resetTime / 1000)
    };
  };

  return (
    <div className="container-custom py-12">
      <h1 className="text-3xl font-bold mb-8">صفحة اختبار React Query</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار الاستعلام الأساسي</h2>
          <div className="flex items-center gap-4 mb-4">
            <Button onClick={() => handleChangeId(-1)} disabled={queryId <= 1}>السابق</Button>
            <span className="font-bold">معرف: {queryId}</span>
            <Button onClick={() => handleChangeId(1)}>التالي</Button>
            <Button onClick={() => refetch()}>إعادة التحميل</Button>
          </div>

          {isLoading ? (
            <div className="animate-pulse bg-slate-200 dark:bg-slate-700 h-20 rounded-md"></div>
          ) : isError ? (
            <div className="bg-error-100 dark:bg-error-900 text-error-800 dark:text-error-200 p-4 rounded-md">
              حدث خطأ أثناء تحميل البيانات
            </div>
          ) : data ? (
            <div className="bg-slate-100 dark:bg-slate-800 p-4 rounded-md">
              <p><strong>العنوان:</strong> {data.title}</p>
              <p><strong>الحالة:</strong> {data.completed ? 'مكتمل' : 'غير مكتمل'}</p>
            </div>
          ) : null}
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار التعديل</h2>
          <Button onClick={handleSubmit} disabled={mutation.isPending}>
            {mutation.isPending ? 'جاري الإرسال...' : 'إرسال بيانات جديدة'}
          </Button>

          {mutation.isSuccess && (
            <div className="mt-4 bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200 p-4 rounded-md">
              <p><strong>تم الإرسال بنجاح!</strong></p>
              <pre className="mt-2 text-xs overflow-auto">
                {JSON.stringify(mutation.data?.data, null, 2)}
              </pre>
            </div>
          )}

          {mutation.isError && (
            <div className="mt-4 bg-error-100 dark:bg-error-900 text-error-800 dark:text-error-200 p-4 rounded-md">
              حدث خطأ أثناء الإرسال
            </div>
          )}
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار التخزين المؤقت</h2>
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <Button onClick={() => setShowCacheInfo(!showCacheInfo)}>
                {showCacheInfo ? 'إخفاء معلومات التخزين المؤقت' : 'عرض معلومات التخزين المؤقت'}
              </Button>
              <Button onClick={handleClearCache} variant="outline">مسح التخزين المؤقت</Button>
            </div>

            {showCacheInfo && (
              <div className="bg-slate-100 dark:bg-slate-800 p-4 rounded-md">
                <p><strong>عدد مرات النجاح:</strong> {cacheStats.hits}</p>
                <p><strong>عدد مرات الفشل:</strong> {cacheStats.misses}</p>
                <p><strong>نسبة النجاح:</strong> {cacheStats.hits + cacheStats.misses > 0 
                  ? Math.round((cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100) 
                  : 0}%</p>
              </div>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">اختبار Rate Limiting</h2>
          <div className="bg-slate-100 dark:bg-slate-800 p-4 rounded-md">
            <p><strong>الطلبات المتبقية:</strong> {getRateLimiterInfo().remaining}</p>
            <p><strong>وقت إعادة التعيين:</strong> {getRateLimiterInfo().resetTime} ثانية</p>
          </div>
          <div className="mt-4">
            <Button 
              onClick={() => {
                // إرسال عدة طلبات لاختبار Rate Limiting
                for (let i = 0; i < 5; i++) {
                  setTimeout(() => refetch(), i * 200);
                }
              }}
            >
              إرسال عدة طلبات
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
