'use client';

import { useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { User, Package, MapPin, CreditCard, Heart, LogOut, Settings, ChevronRight, Award } from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { useAuthStore } from '../../stores/authStore';
import { useAuthModalStore } from '../../stores/authModalStore';
import { useTranslation } from '../../translations';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useLanguageStore } from '../../stores/languageStore';

interface AccountPageProps {
  children?: ReactNode;
}

export default function AccountPage({ children }: AccountPageProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated, signOut } = useAuthStore();
  const { t } = useTranslation();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const { openModal } = useAuthModalStore();
  const { language } = useLanguageStore();
  const [activeTab, setActiveTab] = useState('profile');

  // التحقق من حالة المصادقة
  useEffect(() => {
    if (!isAuthenticated) {
      openModal('sign-in');
    }
  }, [isAuthenticated, openModal]);

  // تحديد التبويب النشط بناءً على المسار
  useEffect(() => {
    const path = pathname;
    if (path) {
      if (path.includes('/orders')) {
        setActiveTab('orders');
      } else if (path.includes('/addresses')) {
        setActiveTab('addresses');
      } else if (path.includes('/payment-methods')) {
        setActiveTab('payment');
      } else if (path.includes('/wishlist')) {
        setActiveTab('wishlist');
      } else if (path.includes('/loyalty')) {
        setActiveTab('loyalty');
      } else if (path.includes('/settings')) {
        setActiveTab('settings');
      } else {
        setActiveTab('profile');
      }
    }
  }, [pathname]);

  // تسجيل الخروج
  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  // قائمة التبويبات
  const tabs = [
    { id: 'profile', label: t('account.profile'), icon: <User className="h-5 w-5" />, path: '/account' },
    { id: 'orders', label: t('account.orders'), icon: <Package className="h-5 w-5" />, path: '/account/orders' },
    { id: 'addresses', label: t('account.addresses'), icon: <MapPin className="h-5 w-5" />, path: '/account/addresses' },
    { id: 'payment', label: t('account.paymentMethods'), icon: <CreditCard className="h-5 w-5" />, path: '/account/payment-methods' },
    { id: 'wishlist', label: t('account.wishlist'), icon: <Heart className="h-5 w-5" />, path: '/shop/wishlist' },
    { id: 'loyalty', label: t('account.loyalty') || (language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program'), icon: <Award className="h-5 w-5" />, path: '/account/loyalty' },
    { id: 'settings', label: t('account.settings'), icon: <Settings className="h-5 w-5" />, path: '/account/settings' },
  ];

  // إذا لم يكن المستخدم مسجل الدخول، عرض صفحة تسجيل الدخول
  if (!isAuthenticated) {
    return (
      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="text-center py-16">
            <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-slate-100 dark:bg-slate-800 mb-6">
              <User className="h-10 w-10 text-slate-400 dark:text-slate-500" />
            </div>
            <h2 className="text-2xl font-semibold mb-3 text-slate-900 dark:text-white">
              {t('account.notLoggedIn')}
            </h2>
            <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6">
              {t('account.loginRequired')}
            </p>
            <HoverAnimation animation="scale">
              <Button
                onClick={() => openModal('sign-in')}
                size="lg"
                className="px-6"
              >
                {t('auth.signIn')}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      <ScrollAnimation animation="fade" delay={0.1}>
        <h1 className="text-3xl font-bold mb-8 text-slate-900 dark:text-white">
          {t('account.myAccount')}
        </h1>
      </ScrollAnimation>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <ScrollAnimation animation="fade" delay={0.2}>
            <Card className={cn(
              "p-4",
              currentIsDark ? "bg-slate-800" : "bg-white"
            )}>
              {/* User Info */}
              <div className="p-4 mb-4 text-center border-b border-slate-200 dark:border-slate-700">
                <HoverAnimation animation="scale">
                  <div className={cn(
                    "w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-4",
                    currentIsDark ? "bg-slate-700" : "bg-slate-100"
                  )}>
                    <User className={cn(
                      "h-12 w-12",
                      currentIsDark ? "text-slate-300" : "text-slate-600"
                    )} />
                  </div>
                </HoverAnimation>
                <h3 className="font-semibold text-lg text-slate-900 dark:text-white mb-1">
                  {user?.firstName} {user?.lastName}
                </h3>
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  {user?.email}
                </p>
              </div>

              {/* Navigation */}
              <ScrollStagger
                animation="slide"
                direction="right"
                staggerDelay={0.05}
                className="space-y-1"
              >
                {tabs.map((tab) => (
                  <HoverAnimation key={tab.id} animation="lift">
                    <Link
                      href={tab.path}
                      className={cn(
                        "flex items-center px-4 py-3 rounded-md transition-colors",
                        activeTab === tab.id
                          ? currentIsDark
                            ? "bg-primary-900/30 text-primary-400"
                            : "bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "text-slate-300 hover:bg-slate-700"
                            : "text-slate-700 hover:bg-slate-50"
                      )}
                    >
                      <span className="mr-3">{tab.icon}</span>
                      <span>{tab.label}</span>
                      <ChevronRight className="ml-auto h-4 w-4" />
                    </Link>
                  </HoverAnimation>
                ))}

                <HoverAnimation animation="lift">
                  <button
                    onClick={handleSignOut}
                    className={cn(
                      "flex items-center w-full px-4 py-3 rounded-md transition-colors",
                      currentIsDark
                        ? "text-red-400 hover:bg-red-900/20"
                        : "text-red-600 hover:bg-red-50"
                    )}
                  >
                    <LogOut className="mr-3 h-5 w-5" />
                    <span>{t('account.signOut')}</span>
                  </button>
                </HoverAnimation>
              </ScrollStagger>
            </Card>
          </ScrollAnimation>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <ScrollAnimation animation="fade" delay={0.3}>
            <Card className={cn(
              "p-6",
              currentIsDark ? "bg-slate-800" : "bg-white"
            )}>
              {children}
            </Card>
          </ScrollAnimation>
        </div>
      </div>
    </div>
  );
}
