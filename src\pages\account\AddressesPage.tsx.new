import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MapPin, Plus, Edit, Trash2, Check } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';

// Dynamically import animations to avoid Jest issues
let ScrollAnimation: React.FC<{
  children: React.ReactNode;
  animation?: string;
  delay?: number;
  className?: string;
}>;
let ScrollStagger: React.FC<{
  children: React.ReactNode;
  animation?: string;
  direction?: string;
  staggerDelay?: number;
  delay?: number;
  className?: string;
}>;
let HoverAnimation: React.FC<{
  children: React.ReactNode;
  animation?: string;
}>;

// Simple component wrappers for test environment
const TestScrollAnimation: React.FC<{
  children: React.ReactNode;
  animation?: string;
  delay?: number;
  className?: string;
}> = ({ children, className }) => <div data-testid="scroll-animation" className={className}>{children}</div>;

const TestScrollStagger: React.FC<{
  children: React.ReactNode;
  animation?: string;
  direction?: string;
  staggerDelay?: number;
  delay?: number;
  className?: string;
}> = ({ children, className }) => <div data-testid="scroll-stagger" className={className}>{children}</div>;

const TestHoverAnimation: React.FC<{
  children: React.ReactNode;
  animation?: string;
}> = ({ children }) => <div data-testid="hover-animation">{children}</div>;

// Assign appropriate components based on environment
if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
  ScrollAnimation = TestScrollAnimation;
  ScrollStagger = TestScrollStagger;
  HoverAnimation = TestHoverAnimation;
} else {
  try {
    const animations = require('../../components/ui/animations');
    ScrollAnimation = animations.ScrollAnimation;
    ScrollStagger = animations.ScrollStagger;
    HoverAnimation = animations.HoverAnimation;
  } catch (e) {
    // Fallback if imports fail
    ScrollAnimation = TestScrollAnimation;
    ScrollStagger = TestScrollStagger;
    HoverAnimation = TestHoverAnimation;
  }
}

// مخطط التحقق من صحة نموذج العنوان
const addressSchema = z.object({
  name: z.string().min(2, 'الاسم قصير جدًا'),
  street: z.string().min(5, 'العنوان قصير جدًا'),
  city: z.string().min(2, 'المدينة قصيرة جدًا'),
  state: z.string().min(2, 'الولاية/المنطقة قصيرة جدًا'),
  postalCode: z.string().min(3, 'الرمز البريدي قصير جدًا'),
  country: z.string().min(2, 'البلد قصير جدًا'),
  isDefault: z.boolean().default(false),
});

type AddressFormData = z.infer<typeof addressSchema>;

// Address interface matching our database structure
interface Address {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  userId?: string; // Foreign key to user
}

// Mock functions for SQLite operations (to be implemented later)
const addressService = {
  getAddresses: async (userId: string): Promise<Address[]> => {
    // This will be replaced with actual SQLite calls in the future
    try {
      const storedAddresses = localStorage.getItem(`user_addresses_${userId}`);
      if (storedAddresses) {
        return JSON.parse(storedAddresses);
      }
    } catch (error) {
      console.error('Error getting addresses:', error);
    }
    return [];
  },

  saveAddresses: (userId: string, addresses: Address[]): void => {
    // This will be replaced with actual SQLite calls in the future
    try {
      localStorage.setItem(`user_addresses_${userId}`, JSON.stringify(addresses));
    } catch (error) {
      console.error('Error saving addresses:', error);
    }
  },

  createAddress: async (address: Address): Promise<boolean> => {
    // This will be replaced with actual SQLite calls in the future
    return true;
  },

  updateAddress: async (id: string, address: Partial<AddressFormData>): Promise<boolean> => {
    // This will be replaced with actual SQLite calls in the future
    return true;
  },

  deleteAddress: async (id: string): Promise<boolean> => {
    // This will be replaced with actual SQLite calls in the future
    return true;
  }
};

export default function AddressesPage() {
  const { user } = useAuthStore();
  const { t } = useTranslation();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      name: '',
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      isDefault: false,
    },
  });

  // Load addresses from localStorage (future: SQLite database)
  useEffect(() => {
    async function loadAddresses() {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!user?.id) {
          setAddresses([]);
          return;
        }
        
        const loadedAddresses = await addressService.getAddresses(user.id);
        setAddresses(loadedAddresses);
      } catch (err) {
        console.error('Error loading addresses:', err);
        setError('Failed to load addresses');
      } finally {
        setIsLoading(false);
      }
    }
    
    loadAddresses();
  }, [user?.id]);
  
  // Save addresses to localStorage (future: SQLite database)
  const saveAddressesToStorage = (newAddresses: Address[]) => {
    if (user?.id) {
      addressService.saveAddresses(user.id, newAddresses);
    }
  };

  // إضافة عنوان جديد
  const handleAddAddress = () => {
    setIsAddingAddress(true);
    setEditingAddressId(null);
    reset({
      name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      isDefault: addresses.length === 0,
    });
  };

  // تحرير عنوان موجود
  const handleEditAddress = (address: Address) => {
    setIsAddingAddress(true);
    setEditingAddressId(address.id);
    reset({
      name: address.name,
      street: address.street,
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      isDefault: address.isDefault,
    });
  };

  // حذف عنوان
  const handleDeleteAddress = (id: string) => {
    const newAddresses = addresses.filter(address => address.id !== id);
    setAddresses(newAddresses);
    saveAddressesToStorage(newAddresses);
    
    // In the future, will call addressService.deleteAddress(id)
  };

  // تعيين عنوان افتراضي
  const handleSetDefaultAddress = (id: string) => {
    const updatedAddresses = addresses.map(address => ({
      ...address,
      isDefault: address.id === id,
    }));
    setAddresses(updatedAddresses);
    saveAddressesToStorage(updatedAddresses);
    
    // In the future, will call addressService.updateAddress(id, { isDefault: true })
  };

  // إلغاء إضافة/تحرير العنوان
  const handleCancelAddEdit = () => {
    setIsAddingAddress(false);
    setEditingAddressId(null);
    reset();
  };

  // حفظ العنوان
  const onSubmit = (data: AddressFormData) => {
    if (editingAddressId) {
      // تحديث عنوان موجود
      const updatedAddresses = addresses.map(address => {
        if (address.id === editingAddressId) {
          return {
            ...address,
            ...data,
          };
        }

        // إذا كان العنوان الجديد محدّدًا كافتراضي، فقم بإلغاء تحديد الآخرين
        if (data.isDefault && address.isDefault) {
          return {
            ...address,
            isDefault: false,
          };
        }

        return address;
      });
      
      setAddresses(updatedAddresses);
      saveAddressesToStorage(updatedAddresses);
      
      // In the future: addressService.updateAddress(editingAddressId, data)
    } else {
      // إضافة عنوان جديد
      const newAddress: Address = {
        id: `address-${Date.now().toString()}`,
        ...data,
        // Ensure isDefault is a boolean, not undefined
        isDefault: data.isDefault === undefined ? false : data.isDefault,
        userId: user?.id // Associate with current user
      };

      let newAddresses: Address[];
      // إذا كان العنوان الجديد محدّدًا كافتراضي، فقم بإلغاء تحديد الآخرين
      if (newAddress.isDefault) {
        newAddresses = [
          ...addresses.map(address => ({ ...address, isDefault: false })),
          newAddress,
        ];
      } else {
        newAddresses = [...addresses, newAddress];
      }
      
      setAddresses(newAddresses);
      saveAddressesToStorage(newAddresses);
      
      // In the future: addressService.createAddress(newAddress)
    }

    setIsAddingAddress(false);
    setEditingAddressId(null);
    reset();
  };

  return (
    <div className="container py-8">
      <ScrollAnimation animation="fade" delay={0.1} className="mb-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-semibold text-slate-900 dark:text-white">
              {t('addresses.title')}
            </h1>

            {addresses.length > 0 && !isAddingAddress && (
              <HoverAnimation animation="scale">
                <Button onClick={handleAddAddress} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {t('addresses.addNew')}
                </Button>
              </HoverAnimation>
            )}
          </div>
          
          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">{t('common.loading')}</p>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}
        </div>
      </ScrollAnimation>

      {isAddingAddress ? (
        <ScrollAnimation animation="fade" delay={0.2} className="max-w-4xl mx-auto">
          <Card className={cn(
            "p-6",
            currentIsDark ? "bg-slate-800" : "bg-white"
          )}>
            <h3 className="text-lg font-medium mb-4 text-slate-900 dark:text-white">
              {editingAddressId ? t('addresses.editAddress') : t('addresses.addNewAddress')}
            </h3>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <ScrollStagger
                animation="fade"
                staggerDelay={0.05}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div>
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.fullName')}
                  </label>
                  <Input
                    {...register('name')}
                    error={errors.name?.message}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.streetAddress')}
                  </label>
                  <Input
                    {...register('street')}
                    error={errors.street?.message}
                  />
                </div>

                <div>
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.city')}
                  </label>
                  <Input
                    {...register('city')}
                    error={errors.city?.message}
                  />
                </div>

                <div>
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.stateProvince')}
                  </label>
                  <Input
                    {...register('state')}
                    error={errors.state?.message}
                  />
                </div>

                <div>
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.postalCode')}
                  </label>
                  <Input
                    {...register('postalCode')}
                    error={errors.postalCode?.message}
                  />
                </div>

                <div>
                  <label className={cn(
                    "block text-sm font-medium mb-1",
                    currentIsDark ? "text-slate-300" : "text-slate-700"
                  )}>
                    {t('addresses.country')}
                  </label>
                  <Input
                    {...register('country')}
                    error={errors.country?.message}
                  />
                </div>
              </ScrollStagger>

              <div className="flex items-center mt-4">
                <input
                  type="checkbox"
                  id="isDefault"
                  {...register('isDefault')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded"
                />
                <label htmlFor="isDefault" className="ml-2 block text-sm text-slate-900 dark:text-white">
                  {t('addresses.setAsDefault')}
                </label>
              </div>

              <div className="flex justify-end gap-3 pt-4 mt-4">
                <HoverAnimation animation="scale">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelAddEdit}
                  >
                    {t('common.cancel')}
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button type="submit">
                    {editingAddressId ? t('common.save') : t('addresses.addAddress')}
                  </Button>
                </HoverAnimation>
              </div>
            </form>
          </Card>
        </ScrollAnimation>
      ) : addresses.length === 0 ? (
        <ScrollAnimation animation="fade" delay={0.2} className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-slate-100 dark:bg-slate-800 mb-4">
              <MapPin className="h-10 w-10 text-slate-400 dark:text-slate-500" />
            </div>
            <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
              {t('addresses.noAddresses')}
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6">
              {t('addresses.addAddressPrompt')}
            </p>
            <HoverAnimation animation="scale">
              <Button
                onClick={handleAddAddress}
                size="lg"
              >
                {t('addresses.addFirst')}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      ) : (
        <ScrollStagger
          animation="slide"
          direction="up"
          staggerDelay={0.1}
          delay={0.3}
          className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto"
        >
          {addresses.map((address) => (
            <HoverAnimation key={address.id} animation="lift">
              <Card className={cn(
                "p-5 relative",
                address.isDefault && (currentIsDark ? "border-primary-500" : "border-primary-500"),
                currentIsDark ? "bg-slate-800" : "bg-white"
              )}>
                {address.isDefault && (
                  <div className={cn(
                    "absolute top-3 right-3 px-2 py-1 text-xs rounded-full",
                    currentIsDark ? "bg-primary-900/30 text-primary-400" : "bg-primary-50 text-primary-700"
                  )}>
                    {t('addresses.default')}
                  </div>
                )}

                <div className="mb-5">
                  <h3 className="font-medium text-slate-900 dark:text-white mb-2">{address.name}</h3>
                  <p className="text-sm text-slate-700 dark:text-slate-300">{address.street}</p>
                  <p className="text-sm text-slate-700 dark:text-slate-300">
                    {address.city}, {address.state} {address.postalCode}
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-300">{address.country}</p>
                </div>

                <div className="flex flex-wrap gap-2">
                  <HoverAnimation animation="scale">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditAddress(address)}
                      className="flex items-center gap-1"
                    >
                      <Edit className="h-3 w-3" />
                      {t('common.edit')}
                    </Button>
                  </HoverAnimation>

                  <HoverAnimation animation="scale">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteAddress(address.id)}
                      className={cn(
                        "flex items-center gap-1",
                        currentIsDark ? "text-red-400 hover:text-red-300" : "text-red-500 hover:text-red-700"
                      )}
                      disabled={address.isDefault}
                    >
                      <Trash2 className="h-3 w-3" />
                      {t('common.delete')}
                    </Button>
                  </HoverAnimation>

                  {!address.isDefault && (
                    <HoverAnimation animation="scale">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefaultAddress(address.id)}
                        className="flex items-center gap-1"
                      >
                        <Check className="h-3 w-3" />
                        {t('addresses.setDefault')}
                      </Button>
                    </HoverAnimation>
                  )}
                </div>
              </Card>
            </HoverAnimation>
          ))}
        </ScrollStagger>
      )}
    </div>
  );
}
