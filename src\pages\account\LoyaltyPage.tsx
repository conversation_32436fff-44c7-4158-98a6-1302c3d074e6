'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Award } from 'lucide-react';
import { LoyaltyProgram } from '../../components/marketing/LoyaltyProgram';
import { useAuthStore } from '../../stores/authStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { SEO } from '../../components/seo/SEO';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function LoyaltyPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { language } = useLanguageStore();
  const { t } = useTranslation();

  // التحقق من حالة المصادقة
  useEffect(() => {
    if (!isAuthenticated) {
      router.push(`/${language}/account`);
    }
  }, [isAuthenticated, router, language]);

  return (
    <>
      <SEO
        title={language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program'}
        description={language === 'ar'
          ? 'اكسب النقاط واستمتع بالمكافآت الحصرية مع برنامج الولاء من كوميرس برو.'
          : 'Earn points and enjoy exclusive rewards with CommercePro\'s loyalty program.'}
      />

      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.1}>
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2 text-slate-900 dark:text-white">
              {language === 'ar' ? 'برنامج الولاء' : 'Loyalty Program'}
            </h1>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl">
              {language === 'ar'
                ? 'اكسب النقاط واستمتع بالمكافآت الحصرية مع كل عملية شراء.'
                : 'Earn points and enjoy exclusive rewards with every purchase.'}
            </p>
          </div>
        </ScrollAnimation>

        <ScrollAnimation animation="fade" delay={0.2}>
          <LoyaltyProgram />
        </ScrollAnimation>

        <ScrollAnimation animation="fade" delay={0.3}>
          <div className="mt-12 bg-slate-50 dark:bg-slate-800 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-6">
              <Award className="h-6 w-6 text-primary-500" />
              <h2 className="text-xl font-bold text-slate-900 dark:text-white">
                {language === 'ar' ? 'كيف يعمل برنامج الولاء؟' : 'How Does the Loyalty Program Work?'}
              </h2>
            </div>

            <ScrollStagger
              animation="slide"
              direction="up"
              staggerDelay={0.1}
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              <HoverAnimation animation="lift">
                <div className="bg-white dark:bg-slate-700 p-5 rounded-lg shadow-sm">
                  <h3 className="font-medium mb-3 text-slate-900 dark:text-white text-lg">
                    {language === 'ar' ? '1. اكسب النقاط' : '1. Earn Points'}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    {language === 'ar'
                      ? 'اكسب نقاط مع كل عملية شراء، وإكمال ملفك الشخصي، وكتابة مراجعات للمنتجات، وإحالة الأصدقاء.'
                      : 'Earn points with every purchase, completing your profile, writing product reviews, and referring friends.'}
                  </p>
                </div>
              </HoverAnimation>

              <HoverAnimation animation="lift">
                <div className="bg-white dark:bg-slate-700 p-5 rounded-lg shadow-sm">
                  <h3 className="font-medium mb-3 text-slate-900 dark:text-white text-lg">
                    {language === 'ar' ? '2. ارتقِ في المستويات' : '2. Level Up'}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    {language === 'ar'
                      ? 'كلما كسبت المزيد من النقاط، ارتقيت في المستويات من برونزي إلى فضي إلى ذهبي إلى بلاتيني.'
                      : 'As you earn more points, level up from Bronze to Silver to Gold to Platinum.'}
                  </p>
                </div>
              </HoverAnimation>

              <HoverAnimation animation="lift">
                <div className="bg-white dark:bg-slate-700 p-5 rounded-lg shadow-sm">
                  <h3 className="font-medium mb-3 text-slate-900 dark:text-white text-lg">
                    {language === 'ar' ? '3. استمتع بالمكافآت' : '3. Enjoy Rewards'}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    {language === 'ar'
                      ? 'استبدل نقاطك بخصومات، وشحن مجاني، وهدايا حصرية، ووصول مبكر إلى المنتجات الجديدة.'
                      : 'Redeem your points for discounts, free shipping, exclusive gifts, and early access to new products.'}
                  </p>
                </div>
              </HoverAnimation>
            </ScrollStagger>
          </div>
        </ScrollAnimation>
      </div>
    </>
  );
}
