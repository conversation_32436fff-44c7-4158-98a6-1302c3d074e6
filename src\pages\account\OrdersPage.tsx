import { useState, useEffect } from 'react';
import { Package, ChevronDown, ChevronUp, ExternalLink, Search } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { formatCurrency } from '../../lib/utils';
import { useCurrencyStore } from '../../stores/currencyStore';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

// بيانات وهمية للطلبات
const mockOrders = [
  {
    id: 'ORD-123456',
    date: '2023-10-15',
    status: 'delivered',
    total: 1250.99,
    items: [
      { id: 1, name: 'Industrial Pump XL-5000', quantity: 1, price: 999.99 },
      { id: 2, name: 'Pressure Gauge Pro', quantity: 2, price: 125.50 },
    ],
    shippingAddress: {
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    trackingNumber: 'TRK-987654321',
  },
  {
    id: 'ORD-789012',
    date: '2023-09-28',
    status: 'processing',
    total: 3450.00,
    items: [
      { id: 3, name: 'Industrial Control Panel', quantity: 1, price: 2999.00 },
      { id: 4, name: 'Safety Valve System', quantity: 3, price: 150.00 },
    ],
    shippingAddress: {
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    trackingNumber: '',
  },
  {
    id: 'ORD-345678',
    date: '2023-08-15',
    status: 'cancelled',
    total: 750.50,
    items: [
      { id: 5, name: 'Maintenance Kit Pro', quantity: 1, price: 750.50 },
    ],
    shippingAddress: {
      name: 'John Doe',
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    trackingNumber: '',
  },
];

export default function OrdersPage() {
  const { user } = useAuthStore();
  const { t } = useTranslation();
  const { currency } = useCurrencyStore();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const [orders, setOrders] = useState(mockOrders);
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // تصفية الطلبات
  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.items.some(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = filterStatus === 'all' || order.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // توسيع/طي تفاصيل الطلب
  const toggleOrderDetails = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  // ترجمة حالة الطلب
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'processing':
        return t('orders.statusProcessing');
      case 'shipped':
        return t('orders.statusShipped');
      case 'delivered':
        return t('orders.statusDelivered');
      case 'cancelled':
        return t('orders.statusCancelled');
      default:
        return status;
    }
  };

  // لون حالة الطلب
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return currentIsDark ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-50 text-blue-600';
      case 'shipped':
        return currentIsDark ? 'bg-amber-900/20 text-amber-300' : 'bg-amber-50 text-amber-600';
      case 'delivered':
        return currentIsDark ? 'bg-green-900/20 text-green-300' : 'bg-green-50 text-green-600';
      case 'cancelled':
        return currentIsDark ? 'bg-red-900/20 text-red-300' : 'bg-red-50 text-red-600';
      default:
        return currentIsDark ? 'bg-slate-700 text-slate-300' : 'bg-slate-100 text-slate-600';
    }
  };

  return (
    <div>
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
            {t('account.myOrders')}
          </h2>
        </div>
      </ScrollAnimation>

      {/* فلاتر البحث */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
              <Input
                type="text"
                placeholder={t('orders.searchOrders')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="min-w-[180px]">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className={cn(
                "w-full p-2 border rounded-md",
                currentIsDark
                  ? "bg-slate-700 border-slate-600 text-white"
                  : "bg-white border-slate-300 text-slate-900",
                "focus:outline-none focus:ring-2 focus:ring-primary-500"
              )}
            >
              <option value="all">{t('orders.allOrders')}</option>
              <option value="processing">{t('orders.statusProcessing')}</option>
              <option value="shipped">{t('orders.statusShipped')}</option>
              <option value="delivered">{t('orders.statusDelivered')}</option>
              <option value="cancelled">{t('orders.statusCancelled')}</option>
            </select>
          </div>
        </div>
      </ScrollAnimation>

      {filteredOrders.length === 0 ? (
        <ScrollAnimation animation="fade" delay={0.3}>
          <div className="text-center py-12">
            <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-slate-100 dark:bg-slate-800 mb-4">
              <Package className="h-10 w-10 text-slate-400 dark:text-slate-500" />
            </div>
            <h3 className="text-lg font-medium mb-2 text-slate-900 dark:text-white">
              {t('orders.noOrders')}
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400 max-w-md mx-auto">
              {searchQuery || filterStatus !== 'all'
                ? t('orders.noOrdersMatchingFilters')
                : t('orders.noOrdersYet')}
            </p>
          </div>
        </ScrollAnimation>
      ) : (
        <ScrollStagger
          animation="slide"
          direction="up"
          staggerDelay={0.1}
          delay={0.3}
          className="space-y-4"
        >
          {filteredOrders.map((order) => (
            <HoverAnimation key={order.id} animation="lift">
              <Card className={cn("overflow-hidden", currentIsDark ? "bg-slate-800" : "bg-white")}>
                <div className="p-5">
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-slate-900 dark:text-white">{order.id}</h3>
                        <span className={cn(
                          "px-2 py-1 text-xs rounded-full",
                          getStatusColor(order.status)
                        )}>
                          {getStatusLabel(order.status)}
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        {new Date(order.date).toLocaleDateString()} • {order.items.length} {t('orders.items')}
                      </p>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="font-semibold text-slate-900 dark:text-white">
                        {formatCurrency(order.total, currency)}
                      </p>
                      <HoverAnimation animation="scale">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleOrderDetails(order.id)}
                          className="p-1"
                        >
                          {expandedOrder === order.id ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </Button>
                      </HoverAnimation>
                    </div>
                  </div>

                  {expandedOrder === order.id && (
                    <ScrollAnimation animation="fade" delay={0.1}>
                      <div className="mt-5 pt-5 border-t border-slate-200 dark:border-slate-700">
                        <h4 className="font-medium mb-3 text-slate-900 dark:text-white">{t('orders.orderItems')}</h4>
                        <div className="space-y-3">
                          {order.items.map((item) => (
                            <div key={item.id} className="flex justify-between p-3 rounded-md bg-slate-50 dark:bg-slate-700/30">
                              <div>
                                <p className="font-medium text-slate-900 dark:text-white">{item.name}</p>
                                <p className="text-sm text-slate-600 dark:text-slate-400">
                                  {t('orders.quantity')}: {item.quantity}
                                </p>
                              </div>
                              <p className="font-medium text-slate-900 dark:text-white">
                                {formatCurrency(item.price * item.quantity, currency)}
                              </p>
                            </div>
                          ))}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-5">
                          <div className="p-4 rounded-md bg-slate-50 dark:bg-slate-700/30">
                            <h4 className="font-medium mb-3 text-slate-900 dark:text-white">{t('orders.shippingAddress')}</h4>
                            <p className="text-sm text-slate-700 dark:text-slate-300">{order.shippingAddress.name}</p>
                            <p className="text-sm text-slate-700 dark:text-slate-300">{order.shippingAddress.street}</p>
                            <p className="text-sm text-slate-700 dark:text-slate-300">
                              {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}
                            </p>
                            <p className="text-sm text-slate-700 dark:text-slate-300">{order.shippingAddress.country}</p>
                          </div>

                          {order.trackingNumber && (
                            <div className="p-4 rounded-md bg-slate-50 dark:bg-slate-700/30">
                              <h4 className="font-medium mb-3 text-slate-900 dark:text-white">{t('orders.tracking')}</h4>
                              <p className="text-sm text-slate-700 dark:text-slate-300 mb-2">{order.trackingNumber}</p>
                              <HoverAnimation animation="scale">
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="p-0 h-auto text-primary-600 dark:text-primary-400"
                                >
                                  <ExternalLink className="h-4 w-4 mr-1" />
                                  {t('orders.trackPackage')}
                                </Button>
                              </HoverAnimation>
                            </div>
                          )}
                        </div>
                      </div>
                    </ScrollAnimation>
                  )}
                </div>
              </Card>
            </HoverAnimation>
          ))}
        </ScrollStagger>
      )}
    </div>
  );
}
