import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CreditCard, Plus, Edit, Trash2, Check, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

// مخطط التحقق من صحة نموذج بطاقة الائتمان
const creditCardSchema = z.object({
  cardNumber: z.string()
    .min(13, 'رقم البطاقة قصير جدًا')
    .max(19, 'رقم البطاقة طويل جدًا')
    .regex(/^\d+$/, 'رقم البطاقة يجب أن يحتوي على أرقام فقط'),
  cardholderName: z.string().min(2, 'اسم حامل البطاقة قصير جدًا'),
  expiryMonth: z.string()
    .min(1, 'الشهر مطلوب')
    .max(2, 'الشهر طويل جدًا')
    .regex(/^(0?[1-9]|1[0-2])$/, 'الشهر غير صالح'),
  expiryYear: z.string()
    .min(2, 'السنة مطلوبة')
    .max(4, 'السنة طويلة جدًا')
    .regex(/^\d+$/, 'السنة يجب أن تحتوي على أرقام فقط'),
  cvv: z.string()
    .min(3, 'رمز الأمان قصير جدًا')
    .max(4, 'رمز الأمان طويل جدًا')
    .regex(/^\d+$/, 'رمز الأمان يجب أن يحتوي على أرقام فقط'),
  isDefault: z.boolean().optional(),
});

type CreditCardFormData = z.infer<typeof creditCardSchema>;

// بيانات وهمية لبطاقات الائتمان
const mockCreditCards = [
  {
    id: '1',
    cardNumber: '****************',
    maskedNumber: '•••• •••• •••• 1111',
    cardholderName: 'John Doe',
    expiryMonth: '12',
    expiryYear: '2025',
    cvv: '123',
    isDefault: true,
    cardType: 'visa',
  },
  {
    id: '2',
    cardNumber: '****************',
    maskedNumber: '•••• •••• •••• 4444',
    cardholderName: 'John Doe',
    expiryMonth: '06',
    expiryYear: '2024',
    cvv: '456',
    isDefault: false,
    cardType: 'mastercard',
  },
];

// وظيفة لتحديد نوع البطاقة
const getCardType = (cardNumber: string) => {
  const firstDigit = cardNumber.charAt(0);
  const firstTwoDigits = cardNumber.substring(0, 2);

  if (cardNumber.startsWith('4')) {
    return 'visa';
  } else if (['51', '52', '53', '54', '55'].includes(firstTwoDigits)) {
    return 'mastercard';
  } else if (['34', '37'].includes(firstTwoDigits)) {
    return 'amex';
  } else if (['60', '65'].includes(firstTwoDigits)) {
    return 'discover';
  } else {
    return 'unknown';
  }
};

// وظيفة لتنسيق رقم البطاقة
const formatCardNumber = (cardNumber: string) => {
  return cardNumber.replace(/\s/g, '').replace(/(\d{4})/g, '$1 ').trim();
};

// وظيفة لإخفاء رقم البطاقة
const maskCardNumber = (cardNumber: string) => {
  const last4 = cardNumber.slice(-4);
  return `•••• •••• •••• ${last4}`;
};

export default function PaymentMethodsPage() {
  const { user } = useAuthStore();
  const { t } = useTranslation();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const [paymentMethods, setPaymentMethods] = useState(mockCreditCards);
  const [isAddingCard, setIsAddingCard] = useState(false);
  const [editingCardId, setEditingCardId] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CreditCardFormData>({
    resolver: zodResolver(creditCardSchema),
    defaultValues: {
      cardNumber: '',
      cardholderName: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      isDefault: false,
    },
  });

  const watchCardNumber = watch('cardNumber');
  const cardType = getCardType(watchCardNumber.replace(/\s/g, ''));

  // إضافة بطاقة جديدة
  const handleAddCard = () => {
    setIsAddingCard(true);
    setEditingCardId(null);
    reset({
      cardNumber: '',
      cardholderName: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      isDefault: paymentMethods.length === 0,
    });
  };

  // تحرير بطاقة موجودة
  const handleEditCard = (card: any) => {
    setIsAddingCard(true);
    setEditingCardId(card.id);
    reset({
      cardNumber: card.cardNumber,
      cardholderName: card.cardholderName,
      expiryMonth: card.expiryMonth,
      expiryYear: card.expiryYear,
      cvv: card.cvv,
      isDefault: card.isDefault,
    });
  };

  // حذف بطاقة
  const handleDeleteCard = (id: string) => {
    setPaymentMethods(paymentMethods.filter(card => card.id !== id));
  };

  // تعيين بطاقة افتراضية
  const handleSetDefaultCard = (id: string) => {
    setPaymentMethods(paymentMethods.map(card => ({
      ...card,
      isDefault: card.id === id,
    })));
  };

  // إلغاء إضافة/تحرير البطاقة
  const handleCancelAddEdit = () => {
    setIsAddingCard(false);
    setEditingCardId(null);
    reset();
  };

  // حفظ البطاقة
  const onSubmit = (data: CreditCardFormData) => {
    const cardType = getCardType(data.cardNumber);
    const maskedNumber = maskCardNumber(data.cardNumber);
    const currentIsDefault = data.isDefault ?? false; // Ensure boolean value

    if (editingCardId) {
      // تحديث بطاقة موجودة
      setPaymentMethods(
        paymentMethods.map((card) =>
          card.id === editingCardId
            ? { ...card, ...data, cardType, maskedNumber, isDefault: currentIsDefault } // Use currentIsDefault
            : card.isDefault && currentIsDefault // If this card is becoming default, unset other defaults
            ? { ...card, isDefault: false }
            : card
        )
      );
    } else {
      // إضافة بطاقة جديدة
      const newCard = {
        id: String(Date.now()),
        ...data,
        cardType,
        maskedNumber,
        isDefault: currentIsDefault, // Use currentIsDefault
      };
      if (currentIsDefault) {
        setPaymentMethods(
          paymentMethods.map((card) => ({ ...card, isDefault: false })).concat(newCard)
        );
      } else {
        setPaymentMethods([...paymentMethods, newCard]);
      }
    }
    handleCancelAddEdit();
  };

  // الحصول على أيقونة البطاقة
  const getCardIcon = (type: string) => {
    switch (type) {
      case 'visa':
        return '💳 Visa';
      case 'mastercard':
        return '💳 Mastercard';
      case 'amex':
        return '💳 Amex';
      case 'discover':
        return '💳 Discover';
      default:
        return '💳';
    }
  };

  return (
    <div>
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex flex-col sm:flex-row items-center justify-between mb-6 gap-4">
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
            {t('account.paymentMethods')}
          </h2>
          {!isAddingCard && (
            <HoverAnimation animation="scale">
              <Button
                onClick={handleAddCard}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {t('payment.addNew')}
              </Button>
            </HoverAnimation>
          )}
        </div>
      </ScrollAnimation>

      <ScrollAnimation animation="fade" delay={0.2}>
        <div className={cn(
          "p-4 rounded-md mb-6 flex items-center",
          currentIsDark ? "bg-amber-900/20 text-amber-300" : "bg-amber-50 text-amber-600"
        )}>
          <AlertCircle className="mr-2 h-5 w-5 flex-shrink-0" />
          <p className="text-sm">
            {t('payment.securityNote')}
          </p>
        </div>
      </ScrollAnimation>

      {isAddingCard ? (
        <ScrollAnimation animation="fade" delay={0.3}>
          <Card className={cn(
            "p-6",
            currentIsDark ? "bg-slate-700" : "bg-slate-50"
          )}>
            <h3 className="text-lg font-medium mb-4 text-slate-900 dark:text-white">
              {editingCardId ? t('payment.editCard') : t('payment.addNewCard')}
            </h3>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className={cn(
                "block text-sm font-medium mb-1",
                currentIsDark ? "text-slate-300" : "text-slate-700"
              )}>
                {t('payment.cardNumber')}
              </label>
              <div className="relative">
                <Input
                  {...register('cardNumber')}
                  onChange={(e) => {
                    const formatted = formatCardNumber(e.target.value.replace(/\s/g, ''));
                    setValue('cardNumber', formatted);
                  }}
                  placeholder="0000 0000 0000 0000"
                  maxLength={19}
                  error={errors.cardNumber?.message}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm">
                  {cardType !== 'unknown' && getCardIcon(cardType)}
                </div>
              </div>
            </div>

            <div>
              <label className={cn(
                "block text-sm font-medium mb-1",
                currentIsDark ? "text-slate-300" : "text-slate-700"
              )}>
                {t('payment.cardholderName')}
              </label>
              <Input
                {...register('cardholderName')}
                error={errors.cardholderName?.message}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className={cn(
                  "block text-sm font-medium mb-1",
                  currentIsDark ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('payment.expiryDate')}
                </label>
                <div className="flex gap-2">
                  <Input
                    {...register('expiryMonth')}
                    placeholder="MM"
                    maxLength={2}
                    error={errors.expiryMonth?.message}
                    className="w-16"
                  />
                  <span className="self-center">/</span>
                  <Input
                    {...register('expiryYear')}
                    placeholder="YY"
                    maxLength={4}
                    error={errors.expiryYear?.message}
                    className="w-20"
                  />
                </div>
              </div>

              <div>
                <label className={cn(
                  "block text-sm font-medium mb-1",
                  currentIsDark ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('payment.cvv')}
                </label>
                <Input
                  {...register('cvv')}
                  type="password"
                  maxLength={4}
                  error={errors.cvv?.message}
                  className="w-20"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isDefault"
                {...register('isDefault')}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded"
              />
              <label htmlFor="isDefault" className="ml-2 block text-sm dark:text-white">
                {t('payment.setAsDefault')}
              </label>
            </div>

            <div className="flex justify-end gap-3 pt-2">
              <HoverAnimation animation="scale">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelAddEdit}
                >
                  {t('common.cancel')}
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button type="submit">
                  {editingCardId ? t('common.save') : t('payment.addCard')}
                </Button>
              </HoverAnimation>
            </div>
          </form>
        </Card>
        </ScrollAnimation>
      ) : paymentMethods.length === 0 ? (
        <ScrollAnimation animation="fade" delay={0.3}>
          <div className="text-center py-12">
            <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-slate-100 dark:bg-slate-800 mb-4">
              <CreditCard className="h-10 w-10 text-slate-400 dark:text-slate-500" />
            </div>
            <h3 className="text-lg font-medium mb-2 text-slate-900 dark:text-white">
              {t('payment.noCards')}
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6">
              {t('payment.addCardPrompt')}
            </p>
            <HoverAnimation animation="scale">
              <Button
                onClick={handleAddCard}
                size="lg"
              >
                {t('payment.addFirst')}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      ) : (
        <ScrollStagger
          animation="slide"
          direction="up"
          staggerDelay={0.1}
          delay={0.3}
          className="grid grid-cols-1 md:grid-cols-2 gap-4"
        >
          {paymentMethods.map((card) => (
            <HoverAnimation key={card.id} animation="lift">
              <Card className={cn(
                "p-5 relative",
                card.isDefault && (currentIsDark ? "border-primary-500" : "border-primary-500"),
                currentIsDark ? "bg-slate-800" : "bg-white"
              )}>
                {card.isDefault && (
                  <div className={cn(
                    "absolute top-3 right-3 px-2 py-1 text-xs rounded-full",
                    currentIsDark ? "bg-primary-900/30 text-primary-400" : "bg-primary-50 text-primary-700"
                  )}>
                    {t('payment.default')}
                  </div>
                )}

                <div className="mb-5">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{getCardIcon(card.cardType)}</span>
                    <h3 className="font-medium text-slate-900 dark:text-white">{card.maskedNumber}</h3>
                  </div>
                  <p className="text-sm text-slate-700 dark:text-slate-300">{card.cardholderName}</p>
                  <p className="text-sm text-slate-700 dark:text-slate-300">
                    {t('payment.expires')}: {card.expiryMonth}/{card.expiryYear}
                  </p>
                </div>

                <div className="flex flex-wrap gap-2">
                  <HoverAnimation animation="scale">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditCard(card)}
                      className="flex items-center gap-1"
                    >
                      <Edit className="h-3 w-3" />
                      {t('common.edit')}
                    </Button>
                  </HoverAnimation>

                  <HoverAnimation animation="scale">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCard(card.id)}
                      className={cn(
                        "flex items-center gap-1",
                        currentIsDark ? "text-red-400 hover:text-red-300" : "text-red-500 hover:text-red-700"
                      )}
                      disabled={card.isDefault}
                    >
                      <Trash2 className="h-3 w-3" />
                      {t('common.delete')}
                    </Button>
                  </HoverAnimation>

                  {!card.isDefault && (
                    <HoverAnimation animation="scale">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefaultCard(card.id)}
                        className="flex items-center gap-1"
                      >
                        <Check className="h-3 w-3" />
                        {t('payment.setDefault')}
                      </Button>
                    </HoverAnimation>
                  )}
                </div>
              </Card>
            </HoverAnimation>
          ))}
        </ScrollStagger>
      )}
    </div>
  );
}
