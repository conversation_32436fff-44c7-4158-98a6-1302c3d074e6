import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, User } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

// مخطط التحقق من صحة النموذج
const profileSchema = z.object({
  firstName: z.string().min(2, 'الاسم الأول قصير جدًا'),
  lastName: z.string().min(2, 'الاسم الأخير قصير جدًا'),
  email: z.string().email('البريد الإلكتروني غير صالح'),
  phone: z.string().optional(),
  company: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const { user, setUser } = useAuthStore();
  const { t } = useTranslation();
  const { resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      company: user?.company || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    setIsSubmitting(true);
    setSuccess(false);

    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      // تحديث بيانات المستخدم
      setUser({
        ...user!,
        ...data,
      });

      setSuccess(true);
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
            {t('account.profileInformation')}
          </h2>
        </div>
      </ScrollAnimation>

      <ScrollAnimation animation="fade" delay={0.2}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <ScrollStagger
            animation="fade"
            staggerDelay={0.05}
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
          >
          <div>
            <label className={cn(
              "block text-sm font-medium mb-1",
              currentIsDark ? "text-slate-300" : "text-slate-700"
            )}>
              {t('account.firstName')}
            </label>
            <Input
              {...register('firstName')}
              error={errors.firstName?.message}
            />
          </div>

          <div>
            <label className={cn(
              "block text-sm font-medium mb-1",
              currentIsDark ? "text-slate-300" : "text-slate-700"
            )}>
              {t('account.lastName')}
            </label>
            <Input
              {...register('lastName')}
              error={errors.lastName?.message}
            />
          </div>

          <div>
            <label className={cn(
              "block text-sm font-medium mb-1",
              currentIsDark ? "text-slate-300" : "text-slate-700"
            )}>
              {t('account.email')}
            </label>
            <Input
              type="email"
              {...register('email')}
              error={errors.email?.message}
              disabled
            />
            <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
              {t('account.emailCannotBeChanged')}
            </p>
          </div>

          <div>
            <label className={cn(
              "block text-sm font-medium mb-1",
              currentIsDark ? "text-slate-300" : "text-slate-700"
            )}>
              {t('account.phone')}
            </label>
            <Input
              type="tel"
              {...register('phone')}
              error={errors.phone?.message}
            />
          </div>

          <div className="md:col-span-2">
            <label className={cn(
              "block text-sm font-medium mb-1",
              currentIsDark ? "text-slate-300" : "text-slate-700"
            )}>
              {t('account.company')}
            </label>
            <Input
              {...register('company')}
              error={errors.company?.message}
            />
          </div>
          </ScrollStagger>

        {success && (
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className={cn(
              "p-4 rounded-md",
              currentIsDark ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
            )}>
              {t('account.profileUpdated')}
            </div>
          </ScrollAnimation>
        )}

        <div className="flex justify-end">
          <HoverAnimation animation="scale">
            <Button
              type="submit"
              className="flex items-center gap-2"
              isLoading={isSubmitting}
              disabled={isSubmitting}
              size="lg"
            >
              {!isSubmitting && <Save className="h-5 w-5" />}
              {t('account.saveChanges')}
            </Button>
          </HoverAnimation>
        </div>
      </form>
      </ScrollAnimation>
    </div>
  );
}
