import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, Bell, Globe, Lock, Eye, EyeOff } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { useLanguageStore } from '../../stores/languageStore';
import { useCurrencyStore } from '../../stores/currencyStore';
import { useTheme } from 'next-themes';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

// مخطط التحقق من صحة نموذج تغيير كلمة المرور
const passwordSchema = z.object({
  currentPassword: z.string().min(6, 'كلمة المرور الحالية قصيرة جدًا'),
  newPassword: z.string().min(6, 'كلمة المرور الجديدة قصيرة جدًا'),
  confirmPassword: z.string().min(6, 'تأكيد كلمة المرور قصير جدًا'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'كلمات المرور غير متطابقة',
  path: ['confirmPassword'],
});

type PasswordFormData = z.infer<typeof passwordSchema>;

// مخطط التحقق من صحة نموذج الإشعارات
const notificationsSchema = z.object({
  emailNotifications: z.boolean().optional(),
  orderUpdates: z.boolean().optional(),
  promotions: z.boolean().optional(),
  newsletter: z.boolean().optional(),
});

type NotificationsFormData = z.infer<typeof notificationsSchema>;

type SupportedLanguage = 'en' | 'ar';

export default function SettingsPage() {
  const { user } = useAuthStore();
  const { t, language } = useTranslation();
  const { setLanguage } = useLanguageStore();
  const { currency, setCurrency } = useCurrencyStore();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const currentIsDark = resolvedTheme === 'dark';

  const [activeTab, setActiveTab] = useState('password');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [notificationsSuccess, setNotificationsSuccess] = useState(false);

  // نموذج تغيير كلمة المرور
  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    reset: resetPassword,
    formState: { errors: passwordErrors, isSubmitting: isPasswordSubmitting },
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // نموذج الإشعارات
  const {
    register: registerNotifications,
    handleSubmit: handleSubmitNotifications,
    formState: { isSubmitting: isNotificationsSubmitting },
  } = useForm<NotificationsFormData>({
    defaultValues: {
      emailNotifications: true,
      orderUpdates: true,
      promotions: false,
      newsletter: true,
    },
  });

  // تغيير كلمة المرور
  const onPasswordSubmit = async (data: PasswordFormData) => {
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('Password changed:', data);
      setPasswordSuccess(true);
      resetPassword();

      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setPasswordSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error changing password:', error);
    }
  };

  // تحديث إعدادات الإشعارات
  const onNotificationsSubmit = async (data: NotificationsFormData) => {
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('Notification settings updated:', data);
      setNotificationsSuccess(true);

      // إخفاء رسالة النجاح بعد 3 ثوانٍ
      setTimeout(() => {
        setNotificationsSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };

  // تغيير اللغة
  const handleLanguageChange = (newLanguage: SupportedLanguage) => {
    setLanguage(newLanguage);
    console.log('Language changed to:', newLanguage);
  };

  // تغيير العملة
  const handleCurrencyChange = (newCurrency: string) => {
    setCurrency(newCurrency);
  };

  return (
    <div>
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
            {t('account.settings')}
          </h2>
        </div>
      </ScrollAnimation>

      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="mb-6">
          <div className="flex border-b border-slate-200 dark:border-slate-700">
            <HoverAnimation animation="lift">
              <button
                className={cn(
                  "px-4 py-2 font-medium text-sm",
                  activeTab === 'password'
                    ? currentIsDark
                      ? "border-b-2 border-primary-500 text-primary-400"
                      : "border-b-2 border-primary-500 text-primary-700"
                    : currentIsDark
                      ? "text-slate-400 hover:text-slate-300"
                      : "text-slate-600 hover:text-slate-900"
                )}
                onClick={() => setActiveTab('password')}
              >
                <Lock className="inline-block mr-2 h-4 w-4" />
                {t('settings.password')}
              </button>
            </HoverAnimation>
            <HoverAnimation animation="lift">
              <button
                className={cn(
                  "px-4 py-2 font-medium text-sm",
                  activeTab === 'notifications'
                    ? currentIsDark
                      ? "border-b-2 border-primary-500 text-primary-400"
                      : "border-b-2 border-primary-500 text-primary-700"
                    : currentIsDark
                      ? "text-slate-400 hover:text-slate-300"
                      : "text-slate-600 hover:text-slate-900"
                )}
                onClick={() => setActiveTab('notifications')}
              >
                <Bell className="inline-block mr-2 h-4 w-4" />
                {t('settings.notifications')}
              </button>
            </HoverAnimation>
            <HoverAnimation animation="lift">
              <button
                className={cn(
                  "px-4 py-2 font-medium text-sm",
                  activeTab === 'preferences'
                    ? currentIsDark
                      ? "border-b-2 border-primary-500 text-primary-400"
                      : "border-b-2 border-primary-500 text-primary-700"
                    : currentIsDark
                      ? "text-slate-400 hover:text-slate-300"
                      : "text-slate-600 hover:text-slate-900"
                )}
                onClick={() => setActiveTab('preferences')}
              >
                <Globe className="inline-block mr-2 h-4 w-4" />
                {t('settings.preferences')}
              </button>
            </HoverAnimation>
          </div>
        </div>
      </ScrollAnimation>

      {activeTab === 'password' && (
        <ScrollAnimation animation="fade" delay={0.3}>
          <form onSubmit={handleSubmitPassword(onPasswordSubmit)} className="space-y-4">
            <ScrollStagger
              animation="fade"
              staggerDelay={0.05}
              className="space-y-4"
            >
              <div>
                <label className={cn(
                  "block text-sm font-medium mb-1",
                  currentIsDark ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('settings.currentPassword')}
                </label>
                <div className="relative">
                  <Input
                    type={showCurrentPassword ? 'text' : 'password'}
                    {...registerPassword('currentPassword')}
                    error={passwordErrors.currentPassword?.message}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div>
                <label className={cn(
                  "block text-sm font-medium mb-1",
                  currentIsDark ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('settings.newPassword')}
                </label>
                <div className="relative">
                  <Input
                    type={showNewPassword ? 'text' : 'password'}
                    {...registerPassword('newPassword')}
                    error={passwordErrors.newPassword?.message}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div>
                <label className={cn(
                  "block text-sm font-medium mb-1",
                  currentIsDark ? "text-slate-300" : "text-slate-700"
                )}>
                  {t('settings.confirmPassword')}
                </label>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    {...registerPassword('confirmPassword')}
                    error={passwordErrors.confirmPassword?.message}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

            </ScrollStagger>

            {passwordSuccess && (
              <div className={cn(
                "p-4 rounded-md mt-4",
                currentIsDark ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
              )}>
                {t('settings.passwordChanged')}
              </div>
            )}

            <div className="flex justify-end mt-4">
              <HoverAnimation animation="scale">
                <Button
                  type="submit"
                  className="flex items-center gap-2"
                  isLoading={isPasswordSubmitting}
                  disabled={isPasswordSubmitting}
                  size="lg"
                >
                  {!isPasswordSubmitting && <Save className="h-5 w-5" />}
                  {t('settings.changePassword')}
                </Button>
              </HoverAnimation>
            </div>
          </form>
        </ScrollAnimation>
      )}

      {activeTab === 'notifications' && (
        <ScrollAnimation animation="fade" delay={0.3}>
          <form onSubmit={handleSubmitNotifications(onNotificationsSubmit)} className="space-y-4">
            <ScrollStagger
              animation="fade"
              staggerDelay={0.05}
              className="space-y-3"
            >
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  {...registerNotifications('emailNotifications')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded dark:focus:ring-primary-600 dark:ring-offset-slate-800 dark:bg-slate-700 dark:border-slate-600"
                />
                <label htmlFor="emailNotifications" className="ml-2 block text-sm dark:text-white">
                  {t('settings.emailNotifications')}
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="orderUpdates"
                  {...registerNotifications('orderUpdates')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded dark:focus:ring-primary-600 dark:ring-offset-slate-800 dark:bg-slate-700 dark:border-slate-600"
                />
                <label htmlFor="orderUpdates" className="ml-2 block text-sm dark:text-white">
                  {t('settings.orderUpdates')}
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="promotions"
                  {...registerNotifications('promotions')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded dark:focus:ring-primary-600 dark:ring-offset-slate-800 dark:bg-slate-700 dark:border-slate-600"
                />
                <label htmlFor="promotions" className="ml-2 block text-sm dark:text-white">
                  {t('settings.promotions')}
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="newsletter"
                  {...registerNotifications('newsletter')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded dark:focus:ring-primary-600 dark:ring-offset-slate-800 dark:bg-slate-700 dark:border-slate-600"
                />
                <label htmlFor="newsletter" className="ml-2 block text-sm dark:text-white">
                  {t('settings.newsletter')}
                </label>
              </div>
            </ScrollStagger>

            {notificationsSuccess && (
              <div className={cn(
                "p-4 rounded-md mt-4",
                currentIsDark ? "bg-green-900/20 text-green-300" : "bg-green-50 text-green-600"
              )}>
                {t('settings.notificationsUpdated')}
              </div>
            )}

            <div className="flex justify-end mt-4">
              <HoverAnimation animation="scale">
                <Button
                  type="submit"
                  className="flex items-center gap-2"
                  isLoading={isNotificationsSubmitting}
                  disabled={isNotificationsSubmitting}
                  size="lg"
                >
                  {!isNotificationsSubmitting && <Save className="h-5 w-5" />}
                  {t('settings.savePreferences')}
                </Button>
              </HoverAnimation>
            </div>
          </form>
        </ScrollAnimation>
      )}

      {activeTab === 'preferences' && (
        <ScrollAnimation animation="fade" delay={0.3}>
          <div className="space-y-6">
            <ScrollStagger
              animation="fade"
              staggerDelay={0.1}
              className="space-y-6"
            >
              <div>
                <h3 className={cn(
                  "text-lg font-medium mb-3",
                  currentIsDark ? "text-white" : "text-slate-900"
                )}>
                  {t('settings.language')}
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => handleLanguageChange('en')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        language === 'en'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      <span className="mr-2">🇺🇸</span>
                      English
                    </button>
                  </HoverAnimation>
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => handleLanguageChange('ar')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        language === 'ar'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      <span className="mr-2">🇸🇦</span>
                      العربية
                    </button>
                  </HoverAnimation>
                </div>
              </div>

              <div>
                <h3 className={cn(
                  "text-lg font-medium mb-3",
                  currentIsDark ? "text-white" : "text-slate-900"
                )}>
                  {t('settings.currency')}
                </h3>
                <div className="grid grid-cols-3 gap-3">
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => handleCurrencyChange('USD')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        currency === 'USD'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      USD ($)
                    </button>
                  </HoverAnimation>
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => handleCurrencyChange('SAR')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        currency === 'SAR'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      SAR (﷼)
                    </button>
                  </HoverAnimation>
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => handleCurrencyChange('CNY')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        currency === 'CNY'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      CNY (¥)
                    </button>
                  </HoverAnimation>
                </div>
              </div>

              <div>
                <h3 className={cn(
                  "text-lg font-medium mb-3",
                  currentIsDark ? "text-white" : "text-slate-900"
                )}>
                  {t('settings.theme')}
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => setTheme('light')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        theme === 'light'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-700 hover:border-slate-600"
                            : "border-slate-200 hover:border-slate-300"
                      )}
                    >
                      {t('settings.lightMode')}
                    </button>
                  </HoverAnimation>
                  <HoverAnimation animation="scale">
                    <button
                      type="button"
                      onClick={() => setTheme('dark')}
                      className={cn(
                        "p-3 border rounded-md flex items-center justify-center",
                        theme === 'dark'
                          ? currentIsDark
                            ? "border-primary-500 bg-primary-900/20 text-primary-400"
                            : "border-primary-500 bg-primary-50 text-primary-700"
                          : currentIsDark
                            ? "border-slate-600 bg-slate-800 text-white hover:border-slate-500"
                            : "border-slate-700 hover:border-slate-600 bg-slate-800 text-white"
                      )}
                    >
                      {t('settings.darkMode')}
                    </button>
                  </HoverAnimation>
                </div>
              </div>
            </ScrollStagger>
          </div>
        </ScrollAnimation>
      )}
    </div>
  );
}
