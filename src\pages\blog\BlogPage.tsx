'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowRight, Calendar, Tag, Search, Filter } from 'lucide-react';
import { blogPosts, blogCategories } from '../../data/blogPosts';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function BlogPage() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    const matchesSearch = searchQuery === '' ||
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="container-custom py-12">
      {/* Hero Section */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            {currentLanguage === 'ar' ? 'رؤى صناعية ومركز معرفي' : 'Industry Insights & Knowledge Hub'}
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-300">
            {currentLanguage === 'ar'
              ? 'مقالات خبراء حول التجارة الدولية والخدمات اللوجستية واتجاهات الصناعة لمساعدة عملك على النجاح عالمياً.'
              : 'Expert articles on international trade, logistics, and industry trends to help your business succeed globally.'}
          </p>
        </div>
      </ScrollAnimation>

      {/* Search and Filters */}
      <ScrollAnimation animation="fade" delay={0.3} className="mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <Input
              type="search"
              placeholder={currentLanguage === 'ar' ? 'البحث عن مقالات...' : 'Search articles...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10"
            />
          </div>
          <HoverAnimation animation="scale">
            <div className="flex gap-2">
              <select
                className="px-4 py-2 border rounded-md bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">{currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}</option>
                {blogCategories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </HoverAnimation>
        </div>
      </ScrollAnimation>

      {/* Featured Article */}
      {selectedCategory === 'all' && searchQuery === '' && (
        <ScrollAnimation animation="fade" delay={0.4} className="mb-12">
          <HoverAnimation animation="lift">
            <Card className="relative overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="aspect-video md:aspect-auto md:h-full overflow-hidden">
                  <EnhancedImage
                    src={blogPosts[0].coverImage}
                    alt={blogPosts[0].title}
                    fill={true}
                    objectFit="cover"
                    effect="zoom"
                    progressive={true}
                    placeholder="shimmer"
                    className="w-full h-full"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
                <div className="p-8">
                  <span className="inline-block bg-primary-100 dark:bg-primary-900/50 text-primary-800 dark:text-primary-300 px-3 py-1 rounded-full text-sm font-medium mb-4">
                    {currentLanguage === 'ar' ? 'مقال مميز' : 'Featured Article'}
                  </span>
                  <h2 className="text-2xl font-bold mb-4 text-slate-900 dark:text-white">
                    {blogPosts[0].title}
                  </h2>
                  <p className="text-slate-600 dark:text-slate-300 mb-6">
                    {blogPosts[0].excerpt}
                  </p>
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-10 h-10 rounded-full overflow-hidden">
                      <EnhancedImage
                        src={blogPosts[0].authorImage}
                        alt={blogPosts[0].author}
                        fill={true}
                        objectFit="cover"
                        progressive={true}
                        placeholder="blur"
                        className="w-full h-full"
                        containerClassName="w-full h-full"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-slate-900 dark:text-white">{blogPosts[0].author}</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">{blogPosts[0].authorTitle}</p>
                    </div>
                  </div>
                  <HoverAnimation animation="scale">
                    <Link
                      href={`/${currentLanguage}/blog/${blogPosts[0].slug}`}
                      className="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300"
                    >
                      {currentLanguage === 'ar' ? 'قراءة المقال' : 'Read Article'}
                      <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4`} />
                    </Link>
                  </HoverAnimation>
                </div>
              </div>
            </Card>
          </HoverAnimation>
        </ScrollAnimation>
      )}

      {/* Articles Grid */}
      <ScrollStagger
        animation="slide"
        direction="up"
        staggerDelay={0.1}
        delay={0.5}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {filteredPosts.map((post) => (
          <HoverAnimation key={post.id} animation="lift">
            <Card className="group overflow-hidden">
              <Link href={`/${currentLanguage}/blog/${post.slug}`} className="block">
                <div className="aspect-video overflow-hidden">
                  <EnhancedImage
                    src={post.coverImage}
                    alt={post.title}
                    fill={true}
                    objectFit="cover"
                    effect="zoom"
                    progressive={true}
                    placeholder="shimmer"
                    className="relative w-full h-full"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400 mb-3">
                    <span className="flex items-center gap-1">
                      <Calendar size={16} />
                      {new Date(post.publishedAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Tag size={16} />
                      {post.category}
                    </span>
                  </div>
                  <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {post.title}
                  </h2>
                  <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full overflow-hidden">
                        <EnhancedImage
                          src={post.authorImage}
                          alt={post.author}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="blur"
                          className="w-full h-full"
                          containerClassName="w-full h-full"
                        />
                      </div>
                      <span className="text-sm font-medium text-slate-900 dark:text-white">{post.author}</span>
                    </div>
                    <span className="text-sm text-slate-500 dark:text-slate-400">{post.readTime}</span>
                  </div>
                </div>
              </Link>
            </Card>
          </HoverAnimation>
        ))}
      </ScrollStagger>

      {filteredPosts.length === 0 && (
        <ScrollAnimation animation="fade" delay={0.3}>
          <div className="text-center py-12">
            <div className="inline-flex justify-center items-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-800 text-slate-400 dark:text-slate-500 mb-4">
              <Search size={24} />
            </div>
            <h2 className="text-xl font-medium text-slate-900 dark:text-white mb-2">
              {currentLanguage === 'ar' ? 'لم يتم العثور على مقالات' : 'No articles found'}
            </h2>
            <p className="text-slate-600 dark:text-slate-300 mb-6">
              {currentLanguage === 'ar'
                ? 'حاول تعديل البحث أو الفلتر للعثور على ما تبحث عنه.'
                : 'Try adjusting your search or filter to find what you\'re looking for.'}
            </p>
            <HoverAnimation animation="scale">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory('all');
                  setSearchQuery('');
                }}
              >
                {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      )}
    </div>
  );
}