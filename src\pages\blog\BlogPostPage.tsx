'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Calendar, Tag, Linkedin, ArrowRight, Facebook, Twitter } from 'lucide-react';
import { blogPosts } from '../../data/blogPosts';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { useEffect } from 'react';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function BlogPostPage({ slug }: { slug: string }) {
  const router = useRouter();
  const post = blogPosts.find((p) => p.slug === slug);

  const currentIndex = post ? blogPosts.findIndex(p => p.id === post.id) : -1;
  const previousPost = currentIndex > 0 ? blogPosts[currentIndex - 1] : null;
  const nextPost = currentIndex < blogPosts.length - 1 ? blogPosts[currentIndex + 1] : null;

  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  useEffect(() => {
    if (post) {
      document.title = `${post.title} | ${currentLanguage === 'ar' ? 'مدونة كوميرس برو' : 'CommercePro Blog'}`;
    }
  }, [post, currentLanguage]);

  if (!post) {
    return (
      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.2}>
          <Card className="p-8 text-center">
            <h1 className="text-2xl font-semibold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'المقال غير موجود' : 'Article Not Found'}
            </h1>
            <p className="text-slate-600 dark:text-slate-300 mb-6">
              {currentLanguage === 'ar' ? 'المقال الذي تبحث عنه غير موجود.' : 'The article you\'re looking for doesn\'t exist.'}
            </p>
            <HoverAnimation animation="scale">
              <Link
                href={`/${currentLanguage}/blog`}
                className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
              >
                <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                {currentLanguage === 'ar' ? 'العودة إلى المدونة' : 'Back to Blog'}
              </Link>
            </HoverAnimation>
          </Card>
        </ScrollAnimation>
      </div>
    );
  }

  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';
  const shareText = `Check out this article: ${post.title}`;

  const shareOnFacebook = () => {
    if (typeof window !== 'undefined') {
      window.open(
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
        '_blank'
      );
    }
  };

  const shareOnTwitter = () => {
    if (typeof window !== 'undefined') {
      window.open(
        `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`,
        '_blank'
      );
    }
  };

  const shareOnLinkedIn = () => {
    if (typeof window !== 'undefined') {
      window.open(
        `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(post.title)}&summary=${encodeURIComponent(post.excerpt)}`,
        '_blank'
      );
    }
  };

  return (
    <div className="container-custom py-12">
      <article className="max-w-4xl mx-auto">
        <ScrollAnimation animation="fade" delay={0.1} className="mb-8">
          <HoverAnimation animation="scale">
            <Link
              href={`/${currentLanguage}/blog`}
              className="inline-flex items-center text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400"
            >
              <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
              {currentLanguage === 'ar' ? 'العودة إلى المدونة' : 'Back to Blog'}
            </Link>
          </HoverAnimation>
        </ScrollAnimation>

        {/* Article Header */}
        <ScrollAnimation animation="fade" delay={0.2} className="mb-8">
          <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400 mb-4">
            <span className="flex items-center gap-1">
              <Calendar size={16} />
              {new Date(post.publishedAt).toLocaleDateString()}
            </span>
            <span className="flex items-center gap-1">
              <Tag size={16} />
              {post.category}
            </span>
            <span>{post.readTime}</span>
          </div>

          <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-6">{post.title}</h1>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full overflow-hidden">
                <EnhancedImage
                  src={post.authorImage}
                  alt={post.author}
                  fill={true}
                  objectFit="cover"
                  progressive={true}
                  placeholder="blur"
                  className="w-full h-full"
                  containerClassName="w-full h-full"
                />
              </div>
              <div>
                <p className="font-medium text-slate-900 dark:text-white">{post.author}</p>
                <p className="text-sm text-slate-500 dark:text-slate-400">{post.authorTitle}</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-500 dark:text-slate-400">{currentLanguage === 'ar' ? 'مشاركة:' : 'Share:'}</span>
              <HoverAnimation animation="scale">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={shareOnFacebook}
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <Facebook size={18} />
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={shareOnTwitter}
                  className="text-sky-500 hover:text-sky-600 dark:text-sky-400 dark:hover:text-sky-300"
                >
                  <Twitter size={18} />
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={shareOnLinkedIn}
                  className="text-blue-700 hover:text-blue-800 dark:text-blue-500 dark:hover:text-blue-400"
                >
                  <Linkedin size={18} />
                </Button>
              </HoverAnimation>
            </div>
          </div>
        </ScrollAnimation>

        {/* Featured Image */}
        <ScrollAnimation animation="fade" delay={0.3} className="mb-8">
          <div className="aspect-video rounded-xl overflow-hidden">
            <EnhancedImage
              src={post.coverImage}
              alt={post.title}
              fill={true}
              objectFit="cover"
              effect="fade"
              progressive={true}
              placeholder="shimmer"
              className="w-full h-full"
              containerClassName="w-full h-full"
              sizes="(max-width: 1024px) 100vw, 1024px"
            />
          </div>
        </ScrollAnimation>

        {/* Article Content */}
        <ScrollAnimation animation="fade" delay={0.4}>
          <div className="prose prose-lg dark:prose-invert max-w-none">
            {post.content.split('\n').map((paragraph, index) => {
              if (paragraph.startsWith('#')) {
                const level = paragraph.match(/^#+/)[0].length;
                const text = paragraph.replace(/^#+\s/, '');
                const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
                return <HeadingTag key={index} className="mt-8 mb-4">{text}</HeadingTag>;
              }
              return paragraph.trim() && <p key={index} className="mb-4">{paragraph}</p>;
            })}
          </div>
        </ScrollAnimation>

        {/* Tags */}
        <ScrollAnimation animation="fade" delay={0.5} className="mt-8">
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag, index) => (
              <HoverAnimation key={tag} animation="scale" delay={index * 0.05}>
                <span
                  className="bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              </HoverAnimation>
            ))}
          </div>
        </ScrollAnimation>

        {/* Navigation */}
        <ScrollAnimation animation="fade" delay={0.6} className="mt-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {previousPost && (
              <HoverAnimation animation="lift">
                <Link
                  href={`/${currentLanguage}/blog/${previousPost.slug}`}
                  className="group block p-4 rounded-lg border border-slate-200 dark:border-slate-700 hover:border-primary-500 dark:hover:border-primary-500 transition-colors"
                >
                  <span className="text-sm text-slate-500 dark:text-slate-400 flex items-center">
                    <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'} h-4 w-4`} />
                    {currentLanguage === 'ar' ? 'المقال السابق' : 'Previous Article'}
                  </span>
                  <span className="font-medium text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {previousPost.title}
                  </span>
                </Link>
              </HoverAnimation>
            )}
            {nextPost && (
              <HoverAnimation animation="lift">
                <Link
                  href={`/${currentLanguage}/blog/${nextPost.slug}`}
                  className="group block p-4 rounded-lg border border-slate-200 dark:border-slate-700 hover:border-primary-500 dark:hover:border-primary-500 transition-colors text-right"
                >
                  <span className="text-sm text-slate-500 dark:text-slate-400 flex items-center justify-end">
                    {currentLanguage === 'ar' ? 'المقال التالي' : 'Next Article'}
                    <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-1' : 'ml-1'} h-4 w-4`} />
                  </span>
                  <span className="font-medium text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {nextPost.title}
                  </span>
                </Link>
              </HoverAnimation>
            )}
          </div>
        </ScrollAnimation>

        {/* Related Articles */}
        <ScrollAnimation animation="fade" delay={0.7} className="mt-16">
          <h2 className="text-2xl font-bold mb-8 text-slate-900 dark:text-white">
            {currentLanguage === 'ar' ? 'مقالات ذات صلة' : 'Related Articles'}
          </h2>
          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {blogPosts
              .filter(p => p.category === post.category && p.id !== post.id)
              .slice(0, 3)
              .map(relatedPost => (
                <HoverAnimation key={relatedPost.id} animation="lift">
                  <Card className="group overflow-hidden">
                    <Link href={`/${currentLanguage}/blog/${relatedPost.slug}`}>
                      <div className="aspect-video overflow-hidden">
                        <EnhancedImage
                          src={relatedPost.coverImage}
                          alt={relatedPost.title}
                          fill={true}
                          objectFit="cover"
                          effect="zoom"
                          progressive={true}
                          placeholder="shimmer"
                          className="relative w-full h-full"
                          containerClassName="w-full h-full"
                          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                          {relatedPost.title}
                        </h3>
                        <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                          {relatedPost.readTime}
                        </p>
                      </div>
                    </Link>
                  </Card>
                </HoverAnimation>
              ))}
          </ScrollStagger>
        </ScrollAnimation>
      </article>
    </div>
  );
}