'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Heart, Package, MapPin, AlertTriangle, Send, Search, X, Filter, SlidersHorizontal } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { clearanceItems } from '../../data/clearanceItems';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { formatCurrency, cn } from '../../lib/utils';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { Product, ClearanceItem } from '../../types/index';

export default function ClearancePage() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [priceRange, setPriceRange] = useState<{ min: number; max: number }>({ min: 0, max: 20000 });
  const [showMobileFilters, setShowMobileFilters] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<ClearanceItem | null>(null);
  const [showQuoteForm, setShowQuoteForm] = useState<boolean>(false);

  const { user } = useAuthStore();
  const wishlist = useWishlistStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // استخراج جميع الفئات المتاحة
  const categories = ['all', ...new Set(clearanceItems.map(item => item.category))];

  // استخراج أعلى سعر للمنتجات
  const maxPrice = Math.max(...clearanceItems.map(item => item.originalPrice));

  // تصفية المنتجات حسب الفئة والبحث ونطاق السعر
  const filteredItems = clearanceItems
    .filter(item => selectedCategory === 'all' || item.category === selectedCategory)
    .filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(item => item.clearancePrice >= priceRange.min && item.clearancePrice <= priceRange.max);

  // تحويل عنصر التصفية إلى منتج متوافق مع نموذج طلب عرض السعر
  const clearanceItemToProduct = (item: ClearanceItem): Product => ({
    id: item.id,
    name: item.name,
    slug: `/clearance/${item.id}`,
    description: item.description,
    price: item.clearancePrice,
    compareAtPrice: item.originalPrice,
    images: item.image ? [item.image] : [],
    category: item.category,
    tags: [item.category],
    stock: item.availableQuantity,
    featured: false,
    specifications: {
      condition: item.condition,
      minOrder: `${item.minOrder} units`,
      location: item.location,
      availableQuantity: `${item.availableQuantity} units`
    },
    createdAt: new Date().toISOString(),
    reviews: [],
    rating: 0,
    reviewCount: 0,
    inStock: item.availableQuantity > 0
  });

  // طلب عرض سعر للمنتج
  const handleRequestQuote = (item: ClearanceItem) => {
    setSelectedItem(item);
    setShowQuoteForm(true);
  };

  // إضافة المنتج إلى المفضلة
  const toggleWishlist = (item: ClearanceItem) => {
    if (!user) {
      // يمكن إضافة نافذة تسجيل الدخول هنا
      console.log('يجب تسجيل الدخول أولاً');
      return;
    }

    const productItem = clearanceItemToProduct(item);
    const wishlistStore = useWishlistStore.getState();

    if (wishlistStore.isInWishlist(item.id)) {
      wishlistStore.removeItem(item.id);
    } else {
      wishlistStore.addItem(productItem);
    }
  };

  return (
    <div className="container-custom py-12">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
          {currentLanguage === 'ar' ? 'تصفية المخزون بأسعار زهيدة' : 'Clearance Stock at Low Prices'}
        </h1>
        <p className="text-lg text-slate-600 dark:text-slate-300">
          {currentLanguage === 'ar'
            ? 'فرصة ذهبية للحصول على منتجات بكميات كبيرة وبأسعار مخفضة. مثالية للشركات التي تبحث عن توفير التكاليف.'
            : 'Golden opportunity to get products in bulk quantities at discounted prices. Perfect for businesses looking to save costs.'}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Filters */}
        <div className="lg:col-span-1">
          <div className="sticky top-24">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedCategory('all');
                    setSearchQuery('');
                    setPriceRange({ min: 0, max: maxPrice });
                  }}
                  className="text-sm text-primary-600 dark:text-primary-400"
                >
                  {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset'}
                </Button>
              </div>

              {/* Search */}
              <div className="mb-6">
                <label htmlFor="search" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  {currentLanguage === 'ar' ? 'البحث' : 'Search'}
                </label>
                <div className="relative">
                  <Input
                    id="search"
                    type="text"
                    placeholder={currentLanguage === 'ar' ? 'ابحث عن منتج...' : 'Search products...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 border-slate-300 dark:border-slate-600"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400 dark:text-slate-500" />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setSearchQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      <X size={18} />
                    </Button>
                  )}
                </div>
              </div>

              {/* Categories */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                  {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      id="category-all"
                      type="radio"
                      name="category"
                      checked={selectedCategory === 'all'}
                      onChange={() => setSelectedCategory('all')}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                    />
                    <label htmlFor="category-all" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}
                    </label>
                  </div>
                  {categories.filter(cat => cat !== 'all').map(category => (
                    <div key={category} className="flex items-center">
                      <input
                        id={`category-${category}`}
                        type="radio"
                        name="category"
                        checked={selectedCategory === category}
                        onChange={() => setSelectedCategory(category)}
                        className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                      />
                      <label htmlFor={`category-${category}`} className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                  {currentLanguage === 'ar' ? 'نطاق السعر' : 'Price Range'}
                </h3>
                <div className="mb-2 flex justify-between text-sm text-slate-600 dark:text-slate-400">
                  <span>{formatCurrency(priceRange.min)}</span>
                  <span>{formatCurrency(priceRange.max)}</span>
                </div>
                <input
                  type="range"
                  id="priceRangeMin"
                  min="0"
                  max={maxPrice}
                  value={priceRange.min}
                  onChange={(e) => setPriceRange({ ...priceRange, min: parseInt(e.target.value) })}
                  className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400 mb-2"
                />
                <input
                  type="range"
                  id="priceRangeMax"
                  min="0"
                  max={maxPrice}
                  value={priceRange.max}
                  onChange={(e) => setPriceRange({ ...priceRange, max: parseInt(e.target.value) })}
                  className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="lg:col-span-3">
          {/* Mobile Filters Toggle */}
          <div className="lg:hidden mb-6">
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-2"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
            >
              <SlidersHorizontal size={16} />
              {currentLanguage === 'ar' ? 'عرض الفلاتر' : 'Show Filters'}
            </Button>

            {showMobileFilters && (
              <div className="mt-4 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6">
                {/* Mobile Search */}
                <div className="mb-6">
                  <label htmlFor="mobile-search" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    {currentLanguage === 'ar' ? 'البحث' : 'Search'}
                  </label>
                  <div className="relative">
                    <Input
                      id="mobile-search"
                      type="text"
                      placeholder={currentLanguage === 'ar' ? 'ابحث عن منتج...' : 'Search products...'}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 border-slate-300 dark:border-slate-600"
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400 dark:text-slate-500" />
                  </div>
                </div>

                {/* Mobile Categories */}
                <div className="mb-6">
                  <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                    {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('all')}
                    >
                      {currentLanguage === 'ar' ? 'الكل' : 'All'}
                    </Button>
                    {categories.filter(cat => cat !== 'all').map(category => (
                      <Button
                        key={category}
                        variant={selectedCategory === category ? 'primary' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Results Count */}
          <div className="mb-6 flex justify-between items-center">
            <p className="text-slate-600 dark:text-slate-300">
              {currentLanguage === 'ar'
                ? `تم العثور على ${filteredItems.length} منتج`
                : `Found ${filteredItems.length} products`}
            </p>
          </div>

          {/* Items Grid */}
          <ScrollStagger className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map((item) => (
              <Link
                key={item.id}
                href={`/${currentLanguage}/clearance/${item.id}`}
                className="block group"
              >
                <Card className="relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full">
                  {/* شارة الخصم */}
                  <div className="absolute top-4 left-4 z-10 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    {currentLanguage === 'ar'
                      ? `خصم ${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%`
                      : `${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}% OFF`}
                  </div>

                  {/* شارة تصفية المخزون */}
                  <div className="absolute top-4 right-4 z-10 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    {currentLanguage === 'ar' ? 'تصفية' : 'Clearance'}
                  </div>

                  {/* صورة المنتج */}
                  <div className="relative aspect-video overflow-hidden">
                    <EnhancedImage
                      src={item.image}
                      alt={item.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      containerClassName="w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" />

                    {/* أزرار الإجراءات السريعة */}
                    <div className="absolute top-16 right-4 flex flex-col gap-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      {/* زر المفضلة */}
                      <Button
                        variant="icon"
                        size="sm"
                        className={`p-2 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110 ${
                          user && useWishlistStore.getState().isInWishlist(item.id)
                            ? "bg-primary-500 text-white"
                            : "bg-white text-slate-700 dark:bg-slate-700 dark:text-white"
                        }`}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          toggleWishlist(item);
                        }}
                        aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}
                      >
                        <Heart
                          className={`h-5 w-5 ${user && useWishlistStore.getState().isInWishlist(item.id) && "fill-current"}`}
                        />
                      </Button>
                    </div>
                  </div>

                  <div className="p-6 flex-grow flex flex-col">
                    {/* تصنيف المنتج وحالته */}
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300">
                        {item.category}
                      </span>
                      <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
                        item.condition === 'new' ? 'bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300' :
                        item.condition === 'like-new' ? 'bg-info-50 dark:bg-info-900/30 text-info-700 dark:text-info-300' :
                        'bg-warning-50 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300'
                      }`}>
                        {currentLanguage === 'ar'
                          ? (item.condition === 'new' ? 'جديد' : item.condition === 'like-new' ? 'كالجديد' : 'مستعمل')
                          : item.condition}
                      </span>
                    </div>

                    {/* اسم المنتج */}
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {item.name}
                    </h3>

                    {/* وصف المنتج */}
                    <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                      {item.description}
                    </p>

                    {/* معلومات إضافية */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                        <Package size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                        {currentLanguage === 'ar'
                          ? `الحد الأدنى للطلب: ${item.minOrder} وحدة`
                          : `Min. Order: ${item.minOrder} units`}
                      </div>
                      <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                        <MapPin size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                        {item.location}
                      </div>
                      {item.expiryDate && (
                        <div className="flex items-center text-sm text-error-600 dark:text-error-400">
                          <AlertTriangle size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                          {currentLanguage === 'ar'
                            ? `ينتهي في: ${new Date(item.expiryDate).toLocaleDateString('ar-EG')}`
                            : `Expires: ${new Date(item.expiryDate).toLocaleDateString()}`}
                        </div>
                      )}
                    </div>

                    {/* السعر والكمية المتوفرة */}
                    <div className="flex items-end justify-between mb-4 mt-auto">
                      <div>
                        <div className="text-2xl font-bold text-slate-900 dark:text-white">
                          {formatCurrency(item.clearancePrice)}
                        </div>
                        <div className="text-sm text-slate-500 dark:text-slate-400 line-through">
                          {formatCurrency(item.originalPrice)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-slate-600 dark:text-slate-400">
                          {currentLanguage === 'ar' ? 'الكمية المتوفرة:' : 'Available:'}
                        </div>
                        <div className="font-medium text-slate-900 dark:text-white">
                          {currentLanguage === 'ar'
                            ? `${item.availableQuantity} وحدة`
                            : `${item.availableQuantity} units`}
                        </div>
                      </div>
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="flex gap-2">
                      <Button
                        variant="primary"
                        className="flex-1 flex items-center justify-center gap-2"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleRequestQuote(item);
                        }}
                      >
                        <Send size={16} />
                        {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
                      </Button>

                      <Button
                        variant="outline"
                        className="flex items-center justify-center"
                        onClick={(e) => {
                          e.preventDefault();
                          router.push(`/${currentLanguage}/clearance/${item.id}`);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'التفاصيل' : 'Details'}
                      </Button>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </ScrollStagger>

          {filteredItems.length === 0 && (
            <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow">
              <Package className="mx-auto h-16 w-16 text-slate-400 dark:text-slate-500" />
              <h2 className="mt-4 text-xl font-semibold text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'لم يتم العثور على منتجات' : 'No items found'}
              </h2>
              <p className="mt-2 text-slate-600 dark:text-slate-400">
                {currentLanguage === 'ar'
                  ? 'لا توجد منتجات تصفية متاحة في هذه الفئة في الوقت الحالي.'
                  : 'No clearance items available in this category at the moment.'}
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSelectedCategory('all');
                  setSearchQuery('');
                  setPriceRange({ min: 0, max: maxPrice });
                }}
              >
                {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* نموذج طلب عرض سعر */}
      {showQuoteForm && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <Card className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar'
                    ? `طلب عرض سعر لمنتج التصفية: ${selectedItem.name}`
                    : `Request Quote for Clearance Item: ${selectedItem.name}`}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setShowQuoteForm(false);
                    setSelectedItem(null);
                  }}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X size={20} />
                </Button>
              </div>

              <WholesaleQuoteForm
                onClose={() => {
                  setShowQuoteForm(false);
                  setSelectedItem(null);
                }}
                isCustomProduct={false}
                product={clearanceItemToProduct(selectedItem)}
              />
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}