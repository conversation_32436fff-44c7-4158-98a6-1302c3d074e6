'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Package, MapPin, AlertTriangle, Send, Heart, Share2, Truck, ShieldCheck, Clock, ChevronRight, X } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { clearanceItems } from '../../data/clearanceItems';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { formatCurrency, cn } from '../../lib/utils';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { Product, ClearanceItem } from '../../types/index';

export default function ClearanceItemPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [item, setItem] = useState<ClearanceItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [quantity, setQuantity] = useState<number>(0);
  const [showQuoteForm, setShowQuoteForm] = useState<boolean>(false);
  const [relatedItems, setRelatedItems] = useState<ClearanceItem[]>([]);

  const { user } = useAuthStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // تحميل بيانات المنتج
  useEffect(() => {
    if (id) {
      setLoading(true);
      // البحث عن المنتج في البيانات
      const foundItem = clearanceItems.find(item => item.id === id);

      if (foundItem) {
        setItem(foundItem);
        // تعيين الكمية الافتراضية إلى الحد الأدنى للطلب
        setQuantity(foundItem.minOrder);

        // البحث عن منتجات مشابهة (من نفس الفئة)
        const similar = clearanceItems
          .filter(i => i.id !== id && i.category === foundItem.category)
          .slice(0, 3);
        setRelatedItems(similar);
      }

      setLoading(false);
    }
  }, [id]);

  // تحويل عنصر التصفية إلى منتج متوافق مع نموذج طلب عرض السعر
  const clearanceItemToProduct = (item: ClearanceItem): Product => ({
    id: item.id,
    name: item.name,
    slug: `/clearance/${item.id}`,
    description: item.description,
    price: item.clearancePrice,
    compareAtPrice: item.originalPrice,
    images: item.image ? [item.image] : [],
    category: item.category,
    tags: [item.category],
    stock: item.availableQuantity,
    featured: false,
    specifications: {
      condition: item.condition,
      minOrder: `${item.minOrder} units`,
      location: item.location,
      availableQuantity: `${item.availableQuantity} units`
    },
    createdAt: new Date().toISOString(),
    reviews: [],
    rating: 0,
    reviewCount: 0,
    inStock: item.availableQuantity > 0
  });

  // إضافة المنتج إلى المفضلة
  const toggleWishlist = () => {
    if (!user || !item) {
      // يمكن إضافة نافذة تسجيل الدخول هنا
      console.log('يجب تسجيل الدخول أولاً');
      return;
    }

    const productItem = clearanceItemToProduct(item);
    const wishlistStore = useWishlistStore.getState();

    if (wishlistStore.isInWishlist(item.id)) {
      wishlistStore.removeItem(item.id);
    } else {
      wishlistStore.addItem(productItem);
    }
  };

  // طلب عرض سعر
  const handleRequestQuote = () => {
    if (!item) return;
    setShowQuoteForm(true);
  };

  // زيادة الكمية
  const increaseQuantity = () => {
    if (!item) return;
    setQuantity(prev => prev + item.minOrder);
  };

  // تقليل الكمية
  const decreaseQuantity = () => {
    if (!item) return;
    if (quantity > item.minOrder) {
      setQuantity(prev => prev - item.minOrder);
    }
  };

  // التحقق من صحة الكمية
  const validateQuantity = (value: number) => {
    if (!item) return false;
    return value >= item.minOrder && value <= item.availableQuantity;
  };

  if (loading) {
    return (
      <div className="container-custom py-12">
        <div className="flex justify-center items-center h-64">
          <div className="animate-pulse">
            <div className="h-8 w-64 bg-slate-200 dark:bg-slate-700 rounded mb-4"></div>
            <div className="h-4 w-96 bg-slate-200 dark:bg-slate-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!item) {
    return (
      <div className="container-custom py-12">
        <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow">
          <Package className="mx-auto h-16 w-16 text-slate-400 dark:text-slate-500" />
          <h2 className="mt-4 text-xl font-semibold text-slate-900 dark:text-white">
            {currentLanguage === 'ar' ? 'المنتج غير موجود' : 'Item Not Found'}
          </h2>
          <p className="mt-2 text-slate-600 dark:text-slate-400">
            {currentLanguage === 'ar'
              ? 'عذراً، لم نتمكن من العثور على منتج التصفية المطلوب.'
              : 'Sorry, we couldn\'t find the requested clearance item.'}
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push(`/${currentLanguage}/clearance`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {currentLanguage === 'ar' ? 'العودة إلى صفحة التصفية' : 'Back to Clearance Page'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      {/* Breadcrumbs */}
      <div className="flex items-center text-sm text-slate-500 dark:text-slate-400 mb-6">
        <Link href="/" className="hover:text-primary-600 dark:hover:text-primary-400">
          {currentLanguage === 'ar' ? 'الرئيسية' : 'Home'}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link href={`/${currentLanguage}/clearance`} className="hover:text-primary-600 dark:hover:text-primary-400">
          {currentLanguage === 'ar' ? 'تصفية المخزون' : 'Clearance'}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-slate-900 dark:text-white font-medium truncate max-w-[200px]">
          {item.name}
        </span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* صورة المنتج */}
        <div className="relative">
          <div className="relative aspect-square overflow-hidden rounded-lg">
            <EnhancedImage
              src={item.image}
              alt={item.name}
              fill={true}
              objectFit="cover"
              progressive={true}
              placeholder="shimmer"
              className="w-full h-full"
              containerClassName="w-full h-full"
              sizes="(max-width: 768px) 100vw, 50vw"
            />
          </div>

          {/* شارة الخصم */}
          <div className="absolute top-4 left-4 z-10 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-bold">
            {currentLanguage === 'ar'
              ? `خصم ${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%`
              : `${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}% OFF`}
          </div>

          {/* شارة تصفية المخزون */}
          <div className="absolute top-4 right-4 z-10 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold">
            {currentLanguage === 'ar' ? 'تصفية' : 'Clearance'}
          </div>
        </div>

        {/* معلومات المنتج */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium px-2.5 py-0.5 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300">
              {item.category}
            </span>
            <span className={`text-sm font-medium px-2.5 py-0.5 rounded-full ${
              item.condition === 'new' ? 'bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300' :
              item.condition === 'like-new' ? 'bg-info-50 dark:bg-info-900/30 text-info-700 dark:text-info-300' :
              'bg-warning-50 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300'
            }`}>
              {currentLanguage === 'ar'
                ? (item.condition === 'new' ? 'جديد' : item.condition === 'like-new' ? 'كالجديد' : 'مستعمل')
                : item.condition}
            </span>
          </div>

          <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
            {item.name}
          </h1>

          <div className="flex items-end gap-4 mb-6">
            <div className="text-3xl font-bold text-slate-900 dark:text-white">
              {formatCurrency(item.clearancePrice)}
            </div>
            <div className="text-xl text-slate-500 dark:text-slate-400 line-through">
              {formatCurrency(item.originalPrice)}
            </div>
          </div>

          <p className="text-slate-600 dark:text-slate-300 mb-6">
            {item.description}
          </p>

          <div className="space-y-4 mb-6">
            <div className="flex items-center text-slate-600 dark:text-slate-400">
              <Package className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 flex-shrink-0`} />
              <span>
                {currentLanguage === 'ar'
                  ? `الحد الأدنى للطلب: ${item.minOrder} وحدة`
                  : `Minimum Order: ${item.minOrder} units`}
              </span>
            </div>

            <div className="flex items-center text-slate-600 dark:text-slate-400">
              <MapPin className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 flex-shrink-0`} />
              <span>{item.location}</span>
            </div>

            <div className="flex items-center text-success-600 dark:text-success-400">
              <Truck className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 flex-shrink-0`} />
              <span>
                {currentLanguage === 'ar'
                  ? `متوفر: ${item.availableQuantity} وحدة`
                  : `Available: ${item.availableQuantity} units`}
              </span>
            </div>

            {item.expiryDate && (
              <div className="flex items-center text-error-600 dark:text-error-400">
                <AlertTriangle className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 flex-shrink-0`} />
                <span>
                  {currentLanguage === 'ar'
                    ? `ينتهي في: ${new Date(item.expiryDate).toLocaleDateString('ar-EG')}`
                    : `Expires: ${new Date(item.expiryDate).toLocaleDateString()}`}
                </span>
              </div>
            )}
          </div>

          <div className="border-t border-b border-slate-200 dark:border-slate-700 py-6 mb-6">
            <div className="mb-4">
              <label htmlFor="quantity" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                {currentLanguage === 'ar' ? 'الكمية المطلوبة' : 'Requested Quantity'}
              </label>
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={decreaseQuantity}
                  disabled={quantity <= item.minOrder}
                  className="rounded-r-none"
                >
                  <span className="text-xl">-</span>
                </Button>
                <Input
                  id="quantity"
                  type="number"
                  value={quantity}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && validateQuantity(value)) {
                      setQuantity(value);
                    }
                  }}
                  min={item.minOrder}
                  max={item.availableQuantity}
                  step={item.minOrder}
                  className="rounded-none text-center w-24 border-x-0"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={increaseQuantity}
                  disabled={quantity >= item.availableQuantity}
                  className="rounded-l-none"
                >
                  <span className="text-xl">+</span>
                </Button>
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                {currentLanguage === 'ar'
                  ? `يجب أن تكون الكمية مضاعفات ${item.minOrder}`
                  : `Quantity must be in multiples of ${item.minOrder}`}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                variant="primary"
                size="lg"
                className="flex-1 flex items-center justify-center gap-2"
                onClick={handleRequestQuote}
              >
                <Send className="h-5 w-5" />
                {currentLanguage === 'ar' ? 'طلب عرض سعر للكمية' : 'Request Quote for Quantity'}
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="flex items-center justify-center gap-2"
                onClick={toggleWishlist}
              >
                <Heart className={cn("h-5 w-5", user && useWishlistStore.getState().isInWishlist(item.id) && "fill-current text-red-500")} />
                {currentLanguage === 'ar' ? 'إضافة للمفضلة' : 'Add to Wishlist'}
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="hidden sm:flex items-center justify-center"
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: item.name,
                      text: item.description,
                      url: window.location.href,
                    });
                  }
                }}
              >
                <Share2 className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <ShieldCheck className="h-6 w-6 text-primary-500 dark:text-primary-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {currentLanguage === 'ar' ? 'منتجات مضمونة الجودة' : 'Guaranteed quality products'}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <Truck className="h-6 w-6 text-primary-500 dark:text-primary-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'شحن سريع' : 'Fast Shipping'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {currentLanguage === 'ar' ? 'للطلبات بالجملة' : 'For bulk orders'}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <Clock className="h-6 w-6 text-primary-500 dark:text-primary-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'عروض محدودة' : 'Limited Time Offers'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {currentLanguage === 'ar' ? 'أسعار خاصة لفترة محدودة' : 'Special prices for limited time'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* المنتجات ذات الصلة */}
      {relatedItems.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
            {currentLanguage === 'ar' ? 'منتجات تصفية مشابهة' : 'Similar Clearance Items'}
          </h2>

          <ScrollStagger className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedItems.map((relatedItem) => (
              <Link
                key={relatedItem.id}
                href={`/${currentLanguage}/clearance/${relatedItem.id}`}
                className="group block"
              >
                <Card className="h-full overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="relative aspect-video overflow-hidden">
                    <EnhancedImage
                      src={relatedItem.image}
                      alt={relatedItem.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      containerClassName="w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />

                    {/* شارة الخصم */}
                    <div className="absolute top-2 left-2 z-10 bg-error-500 text-white px-2 py-0.5 rounded-full text-xs font-bold">
                      {currentLanguage === 'ar'
                        ? `خصم ${Math.round((1 - relatedItem.clearancePrice / relatedItem.originalPrice) * 100)}%`
                        : `${Math.round((1 - relatedItem.clearancePrice / relatedItem.originalPrice) * 100)}% OFF`}
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors line-clamp-1">
                      {relatedItem.name}
                    </h3>

                    <div className="flex items-baseline gap-2 mb-2">
                      <span className="text-lg font-bold text-slate-900 dark:text-white">
                        {formatCurrency(relatedItem.clearancePrice)}
                      </span>
                      <span className="text-sm text-slate-500 dark:text-slate-400 line-through">
                        {formatCurrency(relatedItem.originalPrice)}
                      </span>
                    </div>

                    <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                      <Package className="h-4 w-4 mr-1 flex-shrink-0" />
                      <span>
                        {currentLanguage === 'ar'
                          ? `الحد الأدنى: ${relatedItem.minOrder}`
                          : `Min: ${relatedItem.minOrder}`}
                      </span>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </ScrollStagger>
        </div>
      )}

      {/* نموذج طلب عرض سعر */}
      {showQuoteForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <Card className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar'
                    ? `طلب عرض سعر لمنتج التصفية: ${item.name}`
                    : `Request Quote for Clearance Item: ${item.name}`}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowQuoteForm(false)}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X size={20} />
                </Button>
              </div>

              <WholesaleQuoteForm
                onClose={() => setShowQuoteForm(false)}
                isCustomProduct={false}
                product={clearanceItemToProduct(item)}
                initialQuantity={quantity}
              />
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
