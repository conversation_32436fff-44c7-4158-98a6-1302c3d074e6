'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON>t, Gauge, Set<PERSON>s, Check, Phone } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { productionLines } from '../../data/productionLines';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { EnhancedImage } from '../../components/ui/EnhancedImage';

export default function ProductionLinePage({ slug }: { slug: string }) {
  const router = useRouter();
  const line = productionLines.find(l => l.slug === slug);
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  if (!line) {
    return (
      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.2}>
          <Card className="p-8 text-center">
            <h1 className="text-2xl font-semibold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'خط الإنتاج غير موجود' : 'Production Line Not Found'}
            </h1>
            <p className="text-slate-600 dark:text-slate-300 mb-6">
              {currentLanguage === 'ar' ? 'خط الإنتاج الذي تبحث عنه غير موجود.' : 'The production line you\'re looking for doesn\'t exist.'}
            </p>
            <HoverAnimation animation="scale">
              <Link
                href={`/${currentLanguage}/production-lines`}
                className="inline-flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
              >
                <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                {currentLanguage === 'ar' ? 'العودة إلى خطوط الإنتاج' : 'Back to Production Lines'}
              </Link>
            </HoverAnimation>
          </Card>
        </ScrollAnimation>
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      <ScrollAnimation animation="fade" delay={0.1} className="mb-8">
        <HoverAnimation animation="scale">
          <Link
            href={`/${currentLanguage}/production-lines`}
            className="inline-flex items-center text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400"
          >
            <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {currentLanguage === 'ar' ? 'العودة إلى خطوط الإنتاج' : 'Back to Production Lines'}
          </Link>
        </HoverAnimation>
      </ScrollAnimation>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <EnhancedImage
              src={line.images[0]}
              alt={line.name}
              fill={true}
              objectFit="cover"
              effect="zoom"
              progressive={true}
              placeholder="shimmer"
              className="w-full h-96"
              containerClassName="w-full h-96 relative"
              sizes="(max-width: 1024px) 100vw, 50vw"
            />
          </div>
        </ScrollAnimation>
        <ScrollAnimation animation="fade" delay={0.3}>
          <div>
            <span className={cn(
              "inline-block px-3 py-1 rounded-full text-sm font-medium mb-4",
              isDarkMode ? "bg-primary-900/20 text-primary-400" : "bg-primary-100 text-primary-800"
            )}>
              {line.category}
            </span>
            <h1 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? line.name_ar || line.name : line.name}
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-300 mb-6">
              {currentLanguage === 'ar' ? line.description_ar || line.description : line.description}
            </p>
            <div className="flex items-center text-lg font-medium mb-6 text-slate-800 dark:text-slate-200">
              <Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 text-primary-500`} />
              {currentLanguage === 'ar' ? `سعة الإنتاج: ${line.capacity}` : `Production Capacity: ${line.capacity}`}
            </div>
            <HoverAnimation animation="scale">
              <Button size="lg" className="w-full sm:w-auto">
                <Phone className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      </div>

      <ScrollStagger
        animation="slide"
        direction="up"
        staggerDelay={0.1}
        className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
      >
        <HoverAnimation animation="lift">
          <Card className="p-6 h-full">
            <h2 className="text-xl font-semibold mb-4 flex items-center text-slate-900 dark:text-white">
              <div className={cn(
                "p-2 rounded-full mr-3",
                isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
              )}>
                <Settings className="h-5 w-5 text-primary-500" />
              </div>
              {currentLanguage === 'ar' ? 'المواصفات الفنية' : 'Technical Specifications'}
            </h2>
            <div className="space-y-3">
              {Object.entries(line.specifications).map(([key, value]) => (
                <div key={key} className={cn(
                  "flex justify-between py-2 border-b",
                  isDarkMode ? "border-slate-700" : "border-slate-100"
                )}>
                  <span className="text-slate-600 dark:text-slate-300">{currentLanguage === 'ar' ? key : key}</span>
                  <span className="font-medium text-slate-900 dark:text-white">{currentLanguage === 'ar' ? value : value}</span>
                </div>
              ))}
            </div>
          </Card>
        </HoverAnimation>

        <HoverAnimation animation="lift">
          <Card className="p-6 h-full">
            <h2 className="text-xl font-semibold mb-4 flex items-center text-slate-900 dark:text-white">
              <div className={cn(
                "p-2 rounded-full mr-3",
                isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
              )}>
                <Check className="h-5 w-5 text-primary-500" />
              </div>
              {currentLanguage === 'ar' ? 'الميزات الرئيسية' : 'Key Features'}
            </h2>
            <ul className="space-y-3">
              {line.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 text-primary-500 flex-shrink-0 mt-1`} />
                  <span className="text-slate-700 dark:text-slate-300">{currentLanguage === 'ar' && line.features_ar ? line.features_ar[index] || feature : feature}</span>
                </li>
              ))}
            </ul>
          </Card>
        </HoverAnimation>
      </ScrollStagger>

      <ScrollAnimation animation="fade" delay={0.4}>
        <Card className={cn(
          "p-8",
          isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
        )}>
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'هل أنت مستعد لتحويل إنتاجك؟' : 'Ready to Transform Your Production?'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 mb-6 max-w-2xl mx-auto">
              {currentLanguage === 'ar'
                ? 'خبراؤنا هنا لمساعدتك في تنفيذ خط الإنتاج هذا في منشأتك.'
                : 'Our experts are here to help you implement this production line in your facility.'}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <HoverAnimation animation="scale">
                <Button size="lg" variant="primary">
                  {currentLanguage === 'ar' ? 'جدولة استشارة' : 'Schedule Consultation'}
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button size="lg" variant="outline">
                  {currentLanguage === 'ar' ? 'تحميل المواصفات' : 'Download Specifications'}
                </Button>
              </HoverAnimation>
            </div>
          </div>
        </Card>
      </ScrollAnimation>
    </div>
  );
}