'use client';

import { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ArrowRight,
  Factory,
  Settings,
  Gauge,
  PenTool as Tool,
  Shield,
  Search,
  Filter,
  SlidersHorizontal,
  Grid3X3,
  List,
  Star,
  TrendingUp,
  Zap,
  Award,
  Clock,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  Wrench,
  Play,
  Download,
  Eye,
  BarChart3,
  Calendar,
  MapPin,
  Users
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { productionLines } from '../../data/productionLines';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { ProductionLineFilters, ProductionLineSortOption } from '../../types/index';

const categoriesData = {
  en: [
    { id: 'all', name: 'All Lines', icon: Factory, count: 0 },
    { id: 'Manufacturing', name: 'Manufacturing', icon: Settings, count: 0 },
    { id: 'Food & Beverage', name: 'Food & Beverage', icon: Gauge, count: 0 },
    { id: 'Packaging', name: 'Packaging', icon: Tool, count: 0 },
    { id: 'Pharmaceutical', name: 'Pharmaceutical', icon: Shield, count: 0 },
  ],
  ar: [
    { id: 'all', name: 'جميع الخطوط', icon: Factory, count: 0 },
    { id: 'Manufacturing', name: 'التصنيع', icon: Settings, count: 0 },
    { id: 'Food & Beverage', name: 'الأغذية والمشروبات', icon: Gauge, count: 0 },
    { id: 'Packaging', name: 'التعبئة والتغليف', icon: Tool, count: 0 },
    { id: 'Pharmaceutical', name: 'الصناعات الدوائية', icon: Shield, count: 0 },
  ]
};

const statusData = {
  en: [
    { id: 'all', name: 'All Status', icon: Activity, color: 'text-slate-500' },
    { id: 'active', name: 'Active', icon: CheckCircle, color: 'text-green-500' },
    { id: 'maintenance', name: 'Maintenance', icon: Wrench, color: 'text-yellow-500' },
    { id: 'inactive', name: 'Inactive', icon: AlertTriangle, color: 'text-red-500' },
  ],
  ar: [
    { id: 'all', name: 'جميع الحالات', icon: Activity, color: 'text-slate-500' },
    { id: 'active', name: 'نشط', icon: CheckCircle, color: 'text-green-500' },
    { id: 'maintenance', name: 'صيانة', icon: Wrench, color: 'text-yellow-500' },
    { id: 'inactive', name: 'غير نشط', icon: AlertTriangle, color: 'text-red-500' },
  ]
};

const sortOptions = {
  en: [
    { id: 'name', name: 'Name A-Z', icon: ArrowRight },
    { id: 'efficiency', name: 'Efficiency', icon: TrendingUp },
    { id: 'capacity', name: 'Capacity', icon: Gauge },
    { id: 'priority', name: 'Priority', icon: Star },
    { id: 'createdAt', name: 'Date Added', icon: Calendar },
  ],
  ar: [
    { id: 'name', name: 'الاسم أ-ي', icon: ArrowRight },
    { id: 'efficiency', name: 'الكفاءة', icon: TrendingUp },
    { id: 'capacity', name: 'السعة', icon: Gauge },
    { id: 'priority', name: 'الأولوية', icon: Star },
    { id: 'createdAt', name: 'تاريخ الإضافة', icon: Calendar },
  ]
};

export default function ProductionLinesPage() {
  const router = useRouter();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // State management for enhanced filtering and UI
  const [filters, setFilters] = useState<ProductionLineFilters>({
    category: 'all',
    status: 'all',
    efficiency: { min: 0, max: 100 },
    capacity: { min: 0, max: 100000 },
    manufacturer: 'all',
    searchQuery: '',
    tags: [],
    featured: false
  });

  const [sortBy, setSortBy] = useState<ProductionLineSortOption>('priority');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // استخدام البيانات المناسبة حسب اللغة
  const categories = categoriesData[currentLanguage];
  const statusOptions = statusData[currentLanguage];
  const sortingOptions = sortOptions[currentLanguage];

  // Calculate category counts
  const categoriesWithCounts = useMemo(() => {
    return categories.map(category => ({
      ...category,
      count: category.id === 'all'
        ? productionLines.length
        : productionLines.filter(line => line.category === category.id).length
    }));
  }, [categories]);

  // Advanced filtering logic
  const filteredLines = useMemo(() => {
    let filtered = productionLines;

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(line => line.category === filters.category);
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(line => line.status === filters.status);
    }

    // Search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(line =>
        line.name.toLowerCase().includes(query) ||
        line.description.toLowerCase().includes(query) ||
        line.manufacturer?.toLowerCase().includes(query) ||
        line.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Efficiency filter
    filtered = filtered.filter(line =>
      line.efficiency >= filters.efficiency.min &&
      line.efficiency <= filters.efficiency.max
    );

    // Featured filter
    if (filters.featured) {
      filtered = filtered.filter(line => line.featured);
    }

    // Tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter(line =>
        filters.tags.some(tag => line.tags?.includes(tag))
      );
    }

    return filtered;
  }, [filters]);

  // Sorting logic
  const sortedLines = useMemo(() => {
    const sorted = [...filteredLines].sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      // Handle special cases
      if (sortBy === 'capacity') {
        aValue = parseInt(a.capacity.replace(/[^\d]/g, ''));
        bValue = parseInt(b.capacity.replace(/[^\d]/g, ''));
      } else if (sortBy === 'createdAt') {
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [filteredLines, sortBy, sortDirection]);

  // Statistics calculation
  const stats = useMemo(() => {
    const activeLines = productionLines.filter(line => line.status === 'active').length;
    const maintenanceLines = productionLines.filter(line => line.status === 'maintenance').length;
    const averageEfficiency = productionLines.reduce((sum, line) => sum + line.efficiency, 0) / productionLines.length;

    return {
      totalLines: productionLines.length,
      activeLines,
      maintenanceLines,
      averageEfficiency: Math.round(averageEfficiency * 10) / 10,
      filteredCount: sortedLines.length
    };
  }, [sortedLines]);

  // Handle filter changes
  const handleFilterChange = (key: keyof ProductionLineFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle sort changes
  const handleSortChange = (field: ProductionLineSortOption) => {
    if (field === sortBy) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      category: 'all',
      status: 'all',
      efficiency: { min: 0, max: 100 },
      capacity: { min: 0, max: 100000 },
      manufacturer: 'all',
      searchQuery: '',
      tags: [],
      featured: false
    });
  };

  return (
    <div>
      {/* Compact Professional Hero Section */}
      <section className={cn(
        "relative py-16 overflow-hidden transition-colors duration-500",
        isDarkMode
          ? "bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
          : "bg-gradient-to-br from-slate-50 via-white to-slate-100"
      )}>
        {/* Subtle Background Pattern */}
        <div className="absolute inset-0">
          <div className={cn(
            "absolute inset-0 opacity-5 transition-opacity duration-500",
            isDarkMode ? "opacity-10" : "opacity-5"
          )}>
            <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
          </div>
        </div>

        <div className="container-custom relative z-10">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-4xl mx-auto text-center">
              {/* Main Heading */}
              <h1 className={cn(
                "text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6",
                isDarkMode ? "text-white" : "text-slate-900"
              )}>
                <span className="block">
                  {currentLanguage === 'ar' ? 'خطوط الإنتاج' : 'Production'}
                </span>
                <span className={cn(
                  "block bg-gradient-to-r bg-clip-text text-transparent",
                  isDarkMode
                    ? "from-primary-400 to-blue-400"
                    : "from-primary-600 to-blue-600"
                )}>
                  {currentLanguage === 'ar' ? 'الصناعية' : 'Lines'}
                </span>
              </h1>

              {/* Description */}
              <p className={cn(
                "text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8",
                isDarkMode ? "text-slate-300" : "text-slate-600"
              )}>
                {currentLanguage === 'ar'
                  ? 'تقنيات إنتاج متقدمة مصممة للكفاءة والموثوقية مع أنظمة ذكية للتحكم والمراقبة.'
                  : 'Advanced production technologies engineered for efficiency and reliability with intelligent control and monitoring systems.'}
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    size="lg"
                    variant="primary"
                    className="px-8 py-3 text-lg font-semibold group"
                    onClick={() => router.push(`/${currentLanguage}/contact`)}
                  >
                    <Users className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 group-hover:scale-110 transition-transform`} />
                    {currentLanguage === 'ar' ? 'استشارة متخصصة' : 'Expert Consultation'}
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-8 py-3 text-lg font-semibold group"
                    onClick={() => document.getElementById('production-lines')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 group-hover:translate-x-1 transition-transform`} />
                    {currentLanguage === 'ar' ? 'استكشف المنتجات' : 'Explore Products'}
                  </Button>
                </HoverAnimation>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Enhanced Search and Filtering Section */}
      <section className={cn("py-16", isDarkMode ? "bg-slate-900" : "bg-slate-50")} id="production-lines">
        <div className="container-custom">
          {/* Search Bar */}
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-4xl mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder={currentLanguage === 'ar' ? 'البحث في خطوط الإنتاج...' : 'Search production lines...'}
                  value={filters.searchQuery}
                  onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                  className="pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-slate-200 dark:border-slate-700 focus:border-primary-500 transition-colors"
                />
                {filters.searchQuery && (
                  <button
                    onClick={() => handleFilterChange('searchQuery', '')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          </ScrollAnimation>

          {/* Filter Controls */}
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="flex flex-col lg:flex-row gap-6 mb-8">
              {/* Categories */}
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3">
                  {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {categoriesWithCounts.map(category => {
                    const Icon = category.icon;
                    return (
                      <HoverAnimation key={category.id} animation="scale">
                        <Button
                          variant={filters.category === category.id ? 'primary' : 'outline'}
                          onClick={() => handleFilterChange('category', category.id)}
                          className="flex items-center gap-2 text-sm"
                          size="sm"
                        >
                          <Icon size={16} />
                          {category.name}
                          <span className={cn(
                            "px-2 py-0.5 rounded-full text-xs",
                            filters.category === category.id
                              ? "bg-white/20 text-white"
                              : "bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300"
                          )}>
                            {category.count}
                          </span>
                        </Button>
                      </HoverAnimation>
                    );
                  })}
                </div>
              </div>

              {/* Status Filter */}
              <div className="lg:w-64">
                <h3 className="text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {statusOptions.map(status => {
                    const Icon = status.icon;
                    return (
                      <HoverAnimation key={status.id} animation="scale">
                        <Button
                          variant={filters.status === status.id ? 'primary' : 'outline'}
                          onClick={() => handleFilterChange('status', status.id)}
                          className="flex items-center gap-2 text-sm"
                          size="sm"
                        >
                          <Icon size={16} className={status.color} />
                          {status.name}
                        </Button>
                      </HoverAnimation>
                    );
                  })}
                </div>
              </div>
            </div>
          </ScrollAnimation>

          {/* Advanced Filters and Controls */}
          <ScrollAnimation animation="fade" delay={0.4}>
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2"
                >
                  <SlidersHorizontal size={16} />
                  {currentLanguage === 'ar' ? 'مرشحات متقدمة' : 'Advanced Filters'}
                </Button>

                <div className="flex items-center gap-2">
                  <label className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
                    <input
                      type="checkbox"
                      checked={filters.featured}
                      onChange={(e) => handleFilterChange('featured', e.target.checked)}
                      className="rounded border-slate-300 text-primary-600 focus:ring-primary-500"
                    />
                    <Star size={16} className="text-yellow-500" />
                    {currentLanguage === 'ar' ? 'مميز فقط' : 'Featured Only'}
                  </label>
                </div>

                {(filters.searchQuery || filters.category !== 'all' || filters.status !== 'all' || filters.featured) && (
                  <Button
                    variant="ghost"
                    onClick={resetFilters}
                    className="text-sm text-slate-500 hover:text-slate-700"
                  >
                    {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset Filters'}
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-4">
                {/* Results Count */}
                <div className="text-sm text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar'
                    ? `${stats.filteredCount} من ${stats.totalLines} خط إنتاج`
                    : `${stats.filteredCount} of ${stats.totalLines} production lines`}
                </div>

                {/* Sort Options */}
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value as ProductionLineSortOption)}
                  className={cn(
                    "px-3 py-2 rounded-lg border text-sm",
                    isDarkMode
                      ? "bg-slate-800 border-slate-700 text-white"
                      : "bg-white border-slate-300 text-slate-900"
                  )}
                >
                  {sortingOptions.map(option => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>

                {/* View Mode Toggle */}
                <div className="flex rounded-lg border border-slate-300 dark:border-slate-700 overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={cn(
                      "p-2 transition-colors",
                      viewMode === 'grid'
                        ? "bg-primary-500 text-white"
                        : "bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700"
                    )}
                  >
                    <Grid3X3 size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={cn(
                      "p-2 transition-colors",
                      viewMode === 'list'
                        ? "bg-primary-500 text-white"
                        : "bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700"
                    )}
                  >
                    <List size={16} />
                  </button>
                </div>
              </div>
            </div>
          </ScrollAnimation>

          {/* Advanced Filters Panel */}
          {showFilters && (
            <ScrollAnimation animation="slide" direction="down" delay={0.1}>
              <Card className="p-6 mb-8 border-2 border-primary-200 dark:border-primary-800">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Efficiency Range */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2">
                      {currentLanguage === 'ar' ? 'نطاق الكفاءة (%)' : 'Efficiency Range (%)'}
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={filters.efficiency.min}
                        onChange={(e) => handleFilterChange('efficiency', { ...filters.efficiency, min: Number(e.target.value) })}
                        className="w-20"
                        placeholder="Min"
                      />
                      <span className="text-slate-400">-</span>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={filters.efficiency.max}
                        onChange={(e) => handleFilterChange('efficiency', { ...filters.efficiency, max: Number(e.target.value) })}
                        className="w-20"
                        placeholder="Max"
                      />
                    </div>
                  </div>

                  {/* Manufacturer Filter */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2">
                      {currentLanguage === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'}
                    </label>
                    <select
                      value={filters.manufacturer}
                      onChange={(e) => handleFilterChange('manufacturer', e.target.value)}
                      className={cn(
                        "w-full px-3 py-2 rounded-lg border",
                        isDarkMode
                          ? "bg-slate-800 border-slate-700 text-white"
                          : "bg-white border-slate-300 text-slate-900"
                      )}
                    >
                      <option value="all">{currentLanguage === 'ar' ? 'جميع الشركات' : 'All Manufacturers'}</option>
                      <option value="AFTAL Industries">AFTAL Industries</option>
                      <option value="AFTAL Food Systems">AFTAL Food Systems</option>
                      <option value="AFTAL Packaging Solutions">AFTAL Packaging Solutions</option>
                      <option value="AFTAL Pharma Systems">AFTAL Pharma Systems</option>
                    </select>
                  </div>

                  {/* Tags Filter */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2">
                      {currentLanguage === 'ar' ? 'العلامات' : 'Tags'}
                    </label>
                    <div className="flex flex-wrap gap-1">
                      {['automation', 'robotics', 'ai', 'quality-control', 'haccp', 'gmp'].map(tag => (
                        <button
                          key={tag}
                          onClick={() => {
                            const newTags = filters.tags.includes(tag)
                              ? filters.tags.filter(t => t !== tag)
                              : [...filters.tags, tag];
                            handleFilterChange('tags', newTags);
                          }}
                          className={cn(
                            "px-2 py-1 rounded text-xs transition-colors",
                            filters.tags.includes(tag)
                              ? "bg-primary-500 text-white"
                              : "bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600"
                          )}
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </ScrollAnimation>
          )}
        </div>
      </section>

      {/* Enhanced Production Lines Display */}
      <section className={cn("py-12", isDarkMode ? "bg-slate-800" : "bg-white")}>
        <div className="container-custom">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
            </div>
          ) : sortedLines.length === 0 ? (
            <ScrollAnimation animation="fade" delay={0.3}>
              <Card className="p-12 text-center">
                <Factory className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'لا توجد خطوط إنتاج' : 'No Production Lines Found'}
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-6">
                  {currentLanguage === 'ar'
                    ? 'لم يتم العثور على خطوط إنتاج تطابق معايير البحث الخاصة بك.'
                    : 'No production lines match your search criteria.'}
                </p>
                <Button onClick={resetFilters} variant="outline">
                  {currentLanguage === 'ar' ? 'إعادة تعيين المرشحات' : 'Reset Filters'}
                </Button>
              </Card>
            </ScrollAnimation>
          ) : viewMode === 'grid' ? (
            <ScrollStagger
              animation="slide"
              direction="up"
              staggerDelay={0.1}
              delay={0.3}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {sortedLines.map(line => (
                <HoverAnimation key={line.id} animation="lift">
                  <Card className="flex flex-col overflow-hidden group h-full relative">
                    {/* Status Badge */}
                    <div className="absolute top-4 right-4 z-20">
                      <span className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        line.status === 'active' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                        line.status === 'maintenance' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                        line.status === 'inactive' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                      )}>
                        {line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active')}
                        {line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance')}
                        {line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')}
                      </span>
                    </div>

                    {/* Featured Badge */}
                    {line.featured && (
                      <div className="absolute top-4 left-4 z-20">
                        <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                          <Star size={12} />
                          {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                        </span>
                      </div>
                    )}

                    <div className="relative">
                      <EnhancedImage
                        src={line.images[0]}
                        alt={line.name}
                        fill={true}
                        objectFit="cover"
                        effect="zoom"
                        progressive={true}
                        placeholder="shimmer"
                        className="w-full h-56"
                        containerClassName="w-full h-56"
                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      {/* Quick Actions */}
                      <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="flex gap-2">
                          {line.videoUrl && (
                            <Button size="sm" variant="secondary" className="flex-1">
                              <Play size={14} className="mr-1" />
                              {currentLanguage === 'ar' ? 'فيديو' : 'Video'}
                            </Button>
                          )}
                          <Button size="sm" variant="secondary" className="flex-1">
                            <Download size={14} className="mr-1" />
                            {currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 flex-grow">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <span className={cn(
                            "inline-block px-2 py-1 rounded text-xs font-medium mb-2",
                            isDarkMode ? "bg-primary-900/20 text-primary-400" : "bg-primary-100 text-primary-800"
                          )}>
                            {currentLanguage === 'ar' ? line.category_ar || line.category : line.category}
                          </span>
                          <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                            {currentLanguage === 'ar' ? line.name_ar || line.name : line.name}
                          </h3>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-slate-600 dark:text-slate-300">
                            {currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'}
                          </div>
                          <div className={cn(
                            "text-lg font-bold",
                            line.efficiency >= 95 && "text-green-600",
                            line.efficiency >= 90 && line.efficiency < 95 && "text-yellow-600",
                            line.efficiency < 90 && "text-red-600"
                          )}>
                            {line.efficiency}%
                          </div>
                        </div>
                      </div>

                      <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2">
                        {currentLanguage === 'ar' ? line.description_ar || line.description : line.description}
                      </p>

                      <div className="space-y-3 mb-4">
                        <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                          <Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                          {currentLanguage === 'ar' ? `السعة: ${line.capacity_ar || line.capacity}` : `Capacity: ${line.capacity}`}
                        </div>
                        <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                          <Zap className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                          {currentLanguage === 'ar' ? `استهلاك الطاقة: ${line.energyConsumption}` : `Energy: ${line.energyConsumption}`}
                        </div>
                        <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                          <DollarSign className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                          {currentLanguage === 'ar' ? `التكلفة التشغيلية: $${line.operatingCost}/يوم` : `Operating Cost: $${line.operatingCost}/day`}
                        </div>
                      </div>

                      {/* Tags */}
                      {line.tags && line.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {line.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                          {line.tags.length > 3 && (
                            <span className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs">
                              +{line.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="p-6 pt-0">
                      <div className="flex gap-2">
                        <HoverAnimation animation="scale" className="flex-1">
                          <Link href={`/${currentLanguage}/production-lines/${line.slug}`}>
                            <Button className="w-full">
                              <Eye className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                              {currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                            </Button>
                          </Link>
                        </HoverAnimation>
                        <HoverAnimation animation="scale">
                          <Button variant="outline" size="sm" className="px-3">
                            <BarChart3 size={16} />
                          </Button>
                        </HoverAnimation>
                      </div>
                    </div>
                  </Card>
                </HoverAnimation>
              ))}
            </ScrollStagger>
          ) : (
            /* List View */
            <ScrollStagger
              animation="slide"
              direction="up"
              staggerDelay={0.05}
              delay={0.3}
              className="space-y-4"
            >
              {sortedLines.map(line => (
                <HoverAnimation key={line.id} animation="lift">
                  <Card className="p-6 group">
                    <div className="flex flex-col lg:flex-row gap-6">
                      {/* Image */}
                      <div className="lg:w-64 flex-shrink-0">
                        <div className="relative">
                          <EnhancedImage
                            src={line.images[0]}
                            alt={line.name}
                            fill={true}
                            objectFit="cover"
                            effect="zoom"
                            progressive={true}
                            placeholder="shimmer"
                            className="w-full h-40 lg:h-32 rounded-lg"
                            containerClassName="w-full h-40 lg:h-32 rounded-lg overflow-hidden"
                            sizes="(max-width: 1024px) 100vw, 256px"
                          />
                          {line.featured && (
                            <span className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                              <Star size={10} />
                              {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-3">
                          <div>
                            <div className="flex items-center gap-3 mb-2">
                              <span className={cn(
                                "px-2 py-1 rounded text-xs font-medium",
                                isDarkMode ? "bg-primary-900/20 text-primary-400" : "bg-primary-100 text-primary-800"
                              )}>
                                {currentLanguage === 'ar' ? line.category_ar || line.category : line.category}
                              </span>
                              <span className={cn(
                                "px-2 py-1 rounded text-xs font-medium",
                                line.status === 'active' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                                line.status === 'maintenance' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                                line.status === 'inactive' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                              )}>
                                {line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active')}
                                {line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance')}
                                {line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')}
                              </span>
                            </div>
                            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                              {currentLanguage === 'ar' ? line.name_ar || line.name : line.name}
                            </h3>
                            <p className="text-slate-600 dark:text-slate-300 mb-3 line-clamp-2">
                              {currentLanguage === 'ar' ? line.description_ar || line.description : line.description}
                            </p>
                          </div>

                          <div className="text-right lg:ml-6">
                            <div className="text-sm font-medium text-slate-600 dark:text-slate-300 mb-1">
                              {currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'}
                            </div>
                            <div className={cn(
                              "text-2xl font-bold",
                              line.efficiency >= 95 && "text-green-600",
                              line.efficiency >= 90 && line.efficiency < 95 && "text-yellow-600",
                              line.efficiency < 90 && "text-red-600"
                            )}>
                              {line.efficiency}%
                            </div>
                          </div>
                        </div>

                        {/* Specifications Grid */}
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                            <Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                            <div>
                              <div className="font-medium">{currentLanguage === 'ar' ? 'السعة' : 'Capacity'}</div>
                              <div>{currentLanguage === 'ar' ? line.capacity_ar || line.capacity : line.capacity}</div>
                            </div>
                          </div>
                          <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                            <Zap className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                            <div>
                              <div className="font-medium">{currentLanguage === 'ar' ? 'الطاقة' : 'Energy'}</div>
                              <div>{line.energyConsumption}</div>
                            </div>
                          </div>
                          <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                            <DollarSign className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                            <div>
                              <div className="font-medium">{currentLanguage === 'ar' ? 'التكلفة' : 'Cost'}</div>
                              <div>${line.operatingCost}/day</div>
                            </div>
                          </div>
                          <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                            <Calendar className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                            <div>
                              <div className="font-medium">{currentLanguage === 'ar' ? 'الصيانة' : 'Maintenance'}</div>
                              <div>{new Date(line.maintenanceSchedule || '').toLocaleDateString()}</div>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap gap-2">
                          <HoverAnimation animation="scale">
                            <Link href={`/${currentLanguage}/production-lines/${line.slug}`}>
                              <Button size="sm">
                                <Eye className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                                {currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                              </Button>
                            </Link>
                          </HoverAnimation>
                          {line.videoUrl && (
                            <HoverAnimation animation="scale">
                              <Button variant="outline" size="sm">
                                <Play className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                                {currentLanguage === 'ar' ? 'فيديو' : 'Video'}
                              </Button>
                            </HoverAnimation>
                          )}
                          <HoverAnimation animation="scale">
                            <Button variant="outline" size="sm">
                              <Download className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                              {currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'}
                            </Button>
                          </HoverAnimation>
                          <HoverAnimation animation="scale">
                            <Button variant="outline" size="sm">
                              <BarChart3 className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                              {currentLanguage === 'ar' ? 'إحصائيات' : 'Analytics'}
                            </Button>
                          </HoverAnimation>
                        </div>
                      </div>
                    </div>
                  </Card>
                </HoverAnimation>
              ))}
            </ScrollStagger>
          )}
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className={cn("py-20", isDarkMode ? "bg-slate-900" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-4xl mx-auto text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'لماذا تختار خطوط إنتاجنا؟' : 'Why Choose Our Production Lines?'}
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed">
                {currentLanguage === 'ar'
                  ? 'حلول رائدة في الصناعة مدعومة بالابتكار والخبرة مع أحدث التقنيات والمعايير العالمية'
                  : 'Industry-leading solutions backed by innovation and expertise with cutting-edge technology and global standards'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.4}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          >
            {[
              {
                icon: <Settings className="h-12 w-12 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'أتمتة متقدمة' : 'Advanced Automation',
                description: currentLanguage === 'ar'
                  ? 'أنظمة روبوتية وتحكم متطورة مع الذكاء الاصطناعي لتحقيق أقصى قدر من الكفاءة والدقة'
                  : 'State-of-the-art robotics and AI-powered control systems for maximum efficiency and precision',
                features: currentLanguage === 'ar'
                  ? ['تحكم PLC متقدم', 'أنظمة SCADA', 'صيانة تنبؤية']
                  : ['Advanced PLC Control', 'SCADA Systems', 'Predictive Maintenance']
              },
              {
                icon: <Shield className="h-12 w-12 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Assurance',
                description: currentLanguage === 'ar'
                  ? 'أنظمة متكاملة لمراقبة الجودة مع مراقبة في الوقت الفعلي ومعايير دولية'
                  : 'Integrated quality control systems with real-time monitoring and international standards',
                features: currentLanguage === 'ar'
                  ? ['مراقبة في الوقت الفعلي', 'معايير ISO', 'تتبع كامل']
                  : ['Real-time Monitoring', 'ISO Standards', 'Full Traceability']
              },
              {
                icon: <Award className="h-12 w-12 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'شهادات عالمية' : 'Global Certifications',
                description: currentLanguage === 'ar'
                  ? 'معتمدة من أهم المنظمات العالمية مع ضمان الامتثال للمعايير الدولية'
                  : 'Certified by leading global organizations with guaranteed compliance to international standards',
                features: currentLanguage === 'ar'
                  ? ['شهادات ISO', 'معايير FDA', 'امتثال GMP']
                  : ['ISO Certified', 'FDA Standards', 'GMP Compliance']
              },
            ].map((feature, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-8 text-center h-full group hover:shadow-xl transition-shadow duration-300">
                  <div className={cn(
                    "w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300",
                    isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                  )}>
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{feature.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">{feature.description}</p>
                  <div className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <div key={idx} className="flex items-center justify-center text-sm text-slate-500 dark:text-slate-400">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        {item}
                      </div>
                    ))}
                  </div>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>

          {/* Performance Metrics */}
          <ScrollAnimation animation="fade" delay={0.6}>
            <Card className={cn(
              "p-8 text-center",
              isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
            )}>
              <h3 className="text-2xl font-bold mb-8 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'أداء مثبت' : 'Proven Performance'}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">99.5%</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'معدل التشغيل' : 'Uptime Rate'}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">30%</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'توفير في التكاليف' : 'Cost Savings'}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">50+</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'دولة' : 'Countries'}
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">15+</div>
                  <div className="text-sm text-slate-600 dark:text-slate-300">
                    {currentLanguage === 'ar' ? 'سنة خبرة' : 'Years Experience'}
                  </div>
                </div>
              </div>
            </Card>
          </ScrollAnimation>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-600 via-primary-500 to-primary-700 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/images/industrial-pattern.svg')] bg-repeat"></div>
        </div>

        <div className="container-custom relative z-10">
          <ScrollAnimation animation="fade" delay={0.5}>
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد لتحويل إنتاجك؟' : 'Ready to Transform Your Production?'}
              </h2>
              <p className="text-xl md:text-2xl mb-8 text-primary-50 max-w-3xl mx-auto leading-relaxed">
                {currentLanguage === 'ar'
                  ? 'انضم إلى أكثر من 500 شركة حول العالم تثق في حلولنا الصناعية المتطورة. احصل على استشارة مجانية من خبرائنا اليوم.'
                  : 'Join over 500 companies worldwide who trust our advanced industrial solutions. Get a free consultation from our experts today.'}
              </p>

              {/* Contact Options */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold mb-2">
                    {currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'}
                  </h3>
                  <p className="text-sm text-primary-100">
                    {currentLanguage === 'ar' ? 'تحدث مع خبرائنا' : 'Talk to our experts'}
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold mb-2">
                    {currentLanguage === 'ar' ? 'زيارة موقعية' : 'Site Visit'}
                  </h3>
                  <p className="text-sm text-primary-100">
                    {currentLanguage === 'ar' ? 'تقييم احتياجاتك' : 'Assess your needs'}
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Download className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold mb-2">
                    {currentLanguage === 'ar' ? 'كتالوج مجاني' : 'Free Catalog'}
                  </h3>
                  <p className="text-sm text-primary-100">
                    {currentLanguage === 'ar' ? 'احصل على المواصفات' : 'Get specifications'}
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
                <HoverAnimation animation="scale">
                  <Button
                    variant="accent"
                    size="lg"
                    className="px-8 py-4 text-lg font-semibold"
                    onClick={() => router.push(`/${currentLanguage}/contact`)}
                  >
                    <Users className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                    {currentLanguage === 'ar' ? 'احجز استشارة مجانية' : 'Book Free Consultation'}
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-8 py-4 text-lg font-semibold border-white/30 text-white hover:bg-white/10"
                    onClick={() => router.push(`/${currentLanguage}/services`)}
                  >
                    <Download className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                    {currentLanguage === 'ar' ? 'تحميل الكتالوج' : 'Download Catalog'}
                  </Button>
                </HoverAnimation>
              </div>

              {/* Contact Information */}
              <div className="flex flex-col sm:flex-row justify-center items-center gap-6 text-primary-100">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">
                    {currentLanguage === 'ar' ? 'متاح الآن للاستشارة' : 'Available now for consultation'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">
                    {currentLanguage === 'ar' ? 'استجابة خلال 24 ساعة' : 'Response within 24 hours'}
                  </span>
                </div>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>
    </div>
  );
}