'use client';

import { useState } from 'react';
import { ArrowRight, FileCheck, Shield, CheckCircle, Globe, Award, FileText, Beaker, Factory, Truck } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';

export default function CertificationPage() {
  const [showBookingForm, setShowBookingForm] = useState(false);
  const { language } = useLanguageStore();
  const { locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-flex justify-center items-center w-16 h-16 rounded-full bg-primary-500/20 text-primary-300 mb-6">
              <FileCheck size={32} />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {currentLanguage === 'ar' ? 'خدمات شهادات المنتجات' : 'Product Certification Services'}
            </h1>
            <p className="text-xl mb-8 text-slate-300">
              {currentLanguage === 'ar'
                ? 'حلول شهادات شاملة تشمل تدقيق المصانع واختبار المنتجات ووثائق الامتثال الدولية.'
                : 'Comprehensive certification solutions including factory audits, product testing, and international compliance documentation.'}
            </p>
            <Button
              size="lg"
              variant="accent"
              className="px-8"
              onClick={() => setShowBookingForm(true)}
            >
              {currentLanguage === 'ar' ? 'ابدأ الآن' : 'Get Started'}
              <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              {currentLanguage === 'ar' ? 'حلول الشهادات' : 'Certification Solutions'}
            </h2>
            <p className="text-lg text-slate-600">
              {currentLanguage === 'ar'
                ? 'خدمات شهادات متكاملة للوصول إلى الأسواق العالمية'
                : 'End-to-end certification services for global market access'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <Factory className="h-8 w-8 text-primary-500" />,
                title: currentLanguage === 'ar' ? "خدمات تدقيق المصانع" : "Factory Audit Services",
                description: currentLanguage === 'ar'
                  ? "فحص شامل للمصنع وتقييم نظام الجودة"
                  : "Comprehensive factory inspection and quality system evaluation",
                features: currentLanguage === 'ar'
                  ? [
                      "تقييم المصنع الأولي",
                      "تدقيق نظام إدارة الجودة",
                      "تقييم قدرات الإنتاج",
                      "التحقق من الامتثال الاجتماعي",
                      "فحص المعايير البيئية",
                      "تقييمات المتابعة"
                    ]
                  : [
                      "Initial factory assessment",
                      "Quality management system audit",
                      "Production capability evaluation",
                      "Social compliance verification",
                      "Environmental standards check",
                      "Follow-up assessments"
                    ]
              },
              {
                icon: <Beaker className="h-8 w-8 text-primary-500" />,
                title: currentLanguage === 'ar' ? "اختبار المنتجات" : "Product Testing",
                description: currentLanguage === 'ar'
                  ? "خدمات اختبار وتحقق صارمة"
                  : "Rigorous testing and verification services",
                features: currentLanguage === 'ar'
                  ? [
                      "بروتوكولات اختبار السلامة",
                      "تقييم الأداء",
                      "تحليل المواد",
                      "اختبار التوافق الكهرومغناطيسي",
                      "الاختبارات البيئية",
                      "تقييم الموثوقية"
                    ]
                  : [
                      "Safety testing protocols",
                      "Performance evaluation",
                      "Material analysis",
                      "EMC testing",
                      "Environmental testing",
                      "Reliability assessment"
                    ]
              },
              {
                icon: <FileText className="h-8 w-8 text-primary-500" />,
                title: currentLanguage === 'ar' ? "شهادة سابر" : "SABER Certification",
                description: currentLanguage === 'ar'
                  ? "إدارة شاملة لشهادة سابر"
                  : "Complete SABER certification management",
                features: currentLanguage === 'ar'
                  ? [
                      "إعداد الوثائق",
                      "تجميع الملف الفني",
                      "تقييم المطابقة",
                      "طلب الشهادة",
                      "التحقق من الشحنة",
                      "مراقبة الامتثال"
                    ]
                  : [
                      "Documentation preparation",
                      "Technical file compilation",
                      "Conformity assessment",
                      "Certificate application",
                      "Shipment verification",
                      "Compliance monitoring"
                    ]
              }
            ].map((service, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-center mb-4">
                  {service.icon}
                  <h3 className="text-xl font-semibold ml-3">{service.title}</h3>
                </div>
                <p className="text-slate-600 mb-4">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm text-slate-600">
                      <CheckCircle className={`h-4 w-4 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {feature}
                    </li>
                  ))}
                </ul>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Certification Types */}
      <section className="py-16 md:py-24 bg-slate-50">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              {currentLanguage === 'ar' ? 'أنواع الشهادات' : 'Certification Types'}
            </h2>
            <p className="text-lg text-slate-600">
              {currentLanguage === 'ar'
                ? 'دعم شامل للشهادات لمختلف المعايير والأسواق'
                : 'Comprehensive certification support for various standards and markets'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: currentLanguage === 'ar' ? "سلامة المنتج" : "Product Safety",
                description: currentLanguage === 'ar'
                  ? "شهادات السلامة لمختلف فئات المنتجات"
                  : "Safety certifications for various product categories",
                standards: currentLanguage === 'ar'
                  ? ["علامة CE", "شهادة UL", "موافقة CSA", "الامتثال لتوجيه RoHS"]
                  : ["CE Marking", "UL Certification", "CSA Approval", "RoHS Compliance"]
              },
              {
                title: currentLanguage === 'ar' ? "إدارة الجودة" : "Quality Management",
                description: currentLanguage === 'ar'
                  ? "شهادات ومعايير نظام الجودة"
                  : "Quality system certifications and standards",
                standards: ["ISO 9001:2015", "ISO 13485", "AS9100", "IATF 16949"]
              },
              {
                title: currentLanguage === 'ar' ? "البيئة" : "Environmental",
                description: currentLanguage === 'ar'
                  ? "شهادات الإدارة البيئية والاستدامة"
                  : "Environmental management and sustainability certifications",
                standards: currentLanguage === 'ar'
                  ? ["ISO 14001", "نجمة الطاقة", "الشهادة الخضراء", "البصمة الكربونية"]
                  : ["ISO 14001", "Energy Star", "Green Certification", "Carbon Footprint"]
              },
              {
                title: currentLanguage === 'ar' ? "خاص بالصناعة" : "Industry Specific",
                description: currentLanguage === 'ar'
                  ? "شهادات متخصصة لصناعات محددة"
                  : "Specialized certifications for specific industries",
                standards: currentLanguage === 'ar'
                  ? ["الأجهزة الطبية", "السيارات", "الطيران والفضاء", "الاتصالات"]
                  : ["Medical Device", "Automotive", "Aerospace", "Telecommunications"]
              },
              {
                title: currentLanguage === 'ar' ? "الامتثال الإقليمي" : "Regional Compliance",
                description: currentLanguage === 'ar'
                  ? "متطلبات الشهادات الخاصة بالسوق"
                  : "Market-specific certification requirements",
                standards: currentLanguage === 'ar'
                  ? ["الامتثال الأوروبي", "أمريكا الشمالية", "آسيا والمحيط الهادئ", "الشرق الأوسط"]
                  : ["EU Compliance", "North America", "Asia Pacific", "Middle East"]
              },
              {
                title: currentLanguage === 'ar' ? "معايير الاختبار" : "Testing Standards",
                description: currentLanguage === 'ar'
                  ? "معايير اختبار المنتجات والتحقق"
                  : "Product testing and verification standards",
                standards: currentLanguage === 'ar'
                  ? ["معايير IEC", "طرق ASTM", "معايير GB", "معايير EN"]
                  : ["IEC Standards", "ASTM Methods", "GB Standards", "EN Standards"]
              }
            ].map((cert, index) => (
              <Card key={index} className="p-6">
                <h3 className="text-xl font-semibold mb-2">{cert.title}</h3>
                <p className="text-slate-600 mb-4">{cert.description}</p>
                <ul className="space-y-2">
                  {cert.standards.map((standard, i) => (
                    <li key={i} className="flex items-center text-sm text-slate-600">
                      <Shield className={`h-4 w-4 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      {standard}
                    </li>
                  ))}
                </ul>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              {currentLanguage === 'ar' ? 'عملية الشهادة لدينا' : 'Our Certification Process'}
            </h2>
            <p className="text-lg text-slate-600">
              {currentLanguage === 'ar'
                ? 'نهج مبسط لتحقيق نجاح الشهادة'
                : 'Streamlined approach to achieving certification success'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                number: "01",
                title: "Assessment",
                description: "Initial evaluation and gap analysis",
                icon: <FileCheck className="h-8 w-8 text-primary-500" />
              },
              {
                number: "02",
                title: "Testing",
                description: "Product testing and documentation",
                icon: <Beaker className="h-8 w-8 text-primary-500" />
              },
              {
                number: "03",
                title: "Certification",
                description: "Application and approval process",
                icon: <Award className="h-8 w-8 text-primary-500" />
              },
              {
                number: "04",
                title: "Monitoring",
                description: "Ongoing compliance maintenance",
                icon: <Shield className="h-8 w-8 text-primary-500" />
              }
            ].map((step, index) => (
              <div key={index} className="relative text-center">
                <div className="bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto">
                  {step.number}
                </div>
                <Card className="p-6">
                  <div className="flex justify-center mb-4">
                    {step.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                  <p className="text-slate-600">{step.description}</p>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Shipment Certification */}
      <section className="py-16 md:py-24 bg-slate-50">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Shipment Certification Services
            </h2>
            <p className="text-lg text-slate-600">
              Comprehensive documentation and verification for international shipments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <FileText className="h-12 w-12 text-primary-500" />,
                title: "Documentation",
                features: [
                  "Certificate of Origin",
                  "Packing Lists",
                  "Commercial Invoice",
                  "Bill of Lading",
                  "Export Declarations"
                ]
              },
              {
                icon: <Shield className="h-12 w-12 text-primary-500" />,
                title: "Compliance",
                features: [
                  "Import Requirements",
                  "Export Controls",
                  "Customs Regulations",
                  "Safety Standards",
                  "Product Labeling"
                ]
              },
              {
                icon: <Truck className="h-12 w-12 text-primary-500" />,
                title: "Verification",
                features: [
                  "Pre-shipment Inspection",
                  "Loading Supervision",
                  "Quantity Verification",
                  "Quality Check",
                  "Packaging Inspection"
                ]
              }
            ].map((service, index) => (
              <Card key={index} className="p-6">
                <div className="flex justify-center mb-4">
                  {service.icon}
                </div>
                <h3 className="text-xl font-semibold mb-4 text-center">{service.title}</h3>
                <ul className="space-y-2">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm text-slate-600">
                      <CheckCircle className="h-4 w-4 text-primary-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Get Certified?
            </h2>
            <p className="text-xl mb-8 text-primary-50">
              Contact our certification experts to discuss your requirements and start the process today.
            </p>
            <Button
              size="lg"
              variant="accent"
              className="px-8"
              onClick={() => setShowBookingForm(true)}
            >
              Request Consultation
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <ServiceBookingForm
              serviceName="Product Certification"
              onClose={() => setShowBookingForm(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}