'use client';

import { useState } from 'react';
import { ArrowRight, Users, Bar<PERSON>hart, TrendingUp, Target, FileCheck, Briefcase } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

export default function ConsultingPage() {
  const [showBookingForm, setShowBookingForm] = useState(false);
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <Users size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'خدمات الاستشارات التجارية' : 'Business Consulting Services'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'حلول استشارية استراتيجية لتحسين عملياتك، ودفع النمو، وتعظيم إمكانات عملك.'
                  : 'Strategic consulting solutions to optimize your operations, drive growth, and maximize business potential.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowBookingForm(true)}
                >
                  {currentLanguage === 'ar' ? 'جدولة استشارة' : 'Schedule Consultation'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Services Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'حلول الاستشارات لدينا' : 'Our Consulting Solutions'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'خدمات استشارية شاملة للأعمال مصممة خصيصًا لاحتياجات صناعتك'
                  : 'Comprehensive business consulting services tailored to your industry needs'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <TrendingUp className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "التخطيط الاستراتيجي" : "Strategic Planning",
                description: currentLanguage === 'ar'
                  ? "تطوير استراتيجيات أعمال قوية وخطط نمو"
                  : "Develop robust business strategies and growth plans",
                features: currentLanguage === 'ar'
                  ? [
                      "تحليل السوق والتموضع",
                      "تقييم المشهد التنافسي",
                      "تطوير استراتيجية النمو",
                      "تخطيط إدارة المخاطر"
                    ]
                  : [
                      "Market analysis and positioning",
                      "Competitive landscape assessment",
                      "Growth strategy development",
                      "Risk management planning"
                    ]
              },
              {
                icon: <BarChart className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "التميز التشغيلي" : "Operational Excellence",
                description: currentLanguage === 'ar'
                  ? "تحسين عمليات الأعمال والكفاءة"
                  : "Optimize business processes and efficiency",
                features: currentLanguage === 'ar'
                  ? [
                      "تحسين العمليات",
                      "استراتيجيات خفض التكاليف",
                      "أنظمة إدارة الجودة",
                      "تطوير مقاييس الأداء"
                    ]
                  : [
                      "Process optimization",
                      "Cost reduction strategies",
                      "Quality management systems",
                      "Performance metrics development"
                    ]
              },
              {
                icon: <Target className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "توسيع السوق" : "Market Expansion",
                description: currentLanguage === 'ar'
                  ? "دخول أسواق جديدة وتوسيع نطاق الأعمال"
                  : "Enter new markets and expand business reach",
                features: currentLanguage === 'ar'
                  ? [
                      "استراتيجية دخول السوق",
                      "تحسين قنوات التوزيع",
                      "تطوير شبكة الشركاء",
                      "تخطيط التوسع الدولي"
                    ]
                  : [
                      "Market entry strategy",
                      "Distribution channel optimization",
                      "Partner network development",
                      "International expansion planning"
                    ]
              }
            ].map((service, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <div className="flex items-center mb-4">
                    <div className={cn(
                      "p-2 rounded-full mr-3",
                      isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                    )}>
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">{service.title}</h3>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center text-sm text-slate-600 dark:text-slate-300">
                        <FileCheck className={`h-4 w-4 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Industries Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'الصناعات التي نخدمها' : 'Industries We Serve'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'خبرة عبر قطاعات صناعية متنوعة'
                  : 'Expertise across diverse industry sectors'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                title: currentLanguage === 'ar' ? "التصنيع" : "Manufacturing",
                description: currentLanguage === 'ar'
                  ? "تحسين عمليات الإنتاج وكفاءة سلسلة التوريد"
                  : "Optimize production processes and supply chain efficiency",
              },
              {
                title: currentLanguage === 'ar' ? "التكنولوجيا" : "Technology",
                description: currentLanguage === 'ar'
                  ? "دفع التحول الرقمي واستراتيجيات الابتكار"
                  : "Drive digital transformation and innovation strategies",
              },
              {
                title: currentLanguage === 'ar' ? "التجزئة والتجارة الإلكترونية" : "Retail & E-commerce",
                description: currentLanguage === 'ar'
                  ? "تعزيز تجربة العملاء والتواجد عبر القنوات المتعددة"
                  : "Enhance customer experience and omnichannel presence",
              },
              {
                title: currentLanguage === 'ar' ? "الرعاية الصحية" : "Healthcare",
                description: currentLanguage === 'ar'
                  ? "تحسين الكفاءة التشغيلية وتقديم الرعاية للمرضى"
                  : "Improve operational efficiency and patient care delivery",
              },
              {
                title: currentLanguage === 'ar' ? "الخدمات المالية" : "Financial Services",
                description: currentLanguage === 'ar'
                  ? "تعزيز إدارة المخاطر والامتثال التنظيمي"
                  : "Strengthen risk management and regulatory compliance",
              },
              {
                title: currentLanguage === 'ar' ? "الطاقة والمرافق" : "Energy & Utilities",
                description: currentLanguage === 'ar'
                  ? "تطوير العمليات المستدامة وتحسين الموارد"
                  : "Develop sustainable operations and resource optimization",
              },
              {
                title: currentLanguage === 'ar' ? "النقل" : "Transportation",
                description: currentLanguage === 'ar'
                  ? "تبسيط الخدمات اللوجستية وحلول إدارة الأسطول"
                  : "Streamline logistics and fleet management solutions",
              },
              {
                title: currentLanguage === 'ar' ? "الخدمات المهنية" : "Professional Services",
                description: currentLanguage === 'ar'
                  ? "تعزيز تقديم الخدمات وإدارة علاقات العملاء"
                  : "Enhance service delivery and client relationship management",
              }
            ].map((industry, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{industry.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{industry.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Methodology Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'منهجية الاستشارات لدينا' : 'Our Consulting Methodology'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'نهج منهجي لتقديم نتائج استثنائية'
                  : 'A systematic approach to delivering exceptional results'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="right"
            staggerDelay={0.15}
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
          >
            {[
              {
                number: "01",
                title: currentLanguage === 'ar' ? "الاكتشاف" : "Discovery",
                description: currentLanguage === 'ar'
                  ? "تحليل شامل لتحديات وأهداف عملك"
                  : "Comprehensive analysis of your business challenges and objectives",
              },
              {
                number: "02",
                title: currentLanguage === 'ar' ? "تطوير الاستراتيجية" : "Strategy Development",
                description: currentLanguage === 'ar'
                  ? "تصميم حلول مخصصة بناءً على أفضل ممارسات الصناعة"
                  : "Custom solution design based on industry best practices",
              },
              {
                number: "03",
                title: currentLanguage === 'ar' ? "التنفيذ" : "Implementation",
                description: currentLanguage === 'ar'
                  ? "تنفيذ منهجي للاستراتيجيات الموصى بها"
                  : "Systematic execution of recommended strategies",
              },
              {
                number: "04",
                title: currentLanguage === 'ar' ? "التحسين" : "Optimization",
                description: currentLanguage === 'ar'
                  ? "مراقبة مستمرة وتحسين النتائج"
                  : "Continuous monitoring and refinement of results",
              }
            ].map((step, index) => (
              <HoverAnimation key={index} animation="lift" className="relative text-center">
                <div className={cn(
                  "text-white rounded-full h-14 w-14 flex items-center justify-center font-bold text-lg mb-4 mx-auto shadow-md",
                  isDarkMode ? "bg-primary-600" : "bg-primary-500"
                )}>
                  {step.number}
                </div>
                <Card className="p-6 h-full">
                  <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Benefits Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'لماذا تختار خدمات الاستشارات لدينا؟' : 'Why Choose Our Consulting Services?'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'تقديم نتائج قابلة للقياس ونمو مستدام'
                  : 'Delivering measurable results and sustainable growth'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <Briefcase className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "خبرة في الصناعة" : "Industry Expertise",
                description: currentLanguage === 'ar'
                  ? "معرفة عميقة بالمجال عبر قطاعات متعددة مع منهجيات مثبتة"
                  : "Deep domain knowledge across multiple sectors with proven methodologies"
              },
              {
                icon: <Target className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "مدفوعة بالنتائج" : "Results-Driven",
                description: currentLanguage === 'ar'
                  ? "التركيز على تقديم نتائج قابلة للقياس وتحسينات مستدامة"
                  : "Focus on delivering measurable outcomes and sustainable improvements"
              },
              {
                icon: <Users className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "دعم مخصص" : "Dedicated Support",
                description: currentLanguage === 'ar'
                  ? "مستشارون ذوو خبرة يقدمون توجيهات شخصية طوال فترة المشاركة"
                  : "Experienced consultants providing personalized guidance throughout the engagement"
              }
            ].map((benefit, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 text-center h-full">
                  <div className={cn(
                    "w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4",
                    isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                  )}>
                    {benefit.icon}
                  </div>
                  <h3 className="text-lg font-semibold mb-3 text-slate-900 dark:text-white">{benefit.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{benefit.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.4}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد لتحويل عملك؟' : 'Ready to Transform Your Business?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'جدولة استشارة مع خبرائنا لمناقشة تحديات وفرص عملك.'
                  : 'Schedule a consultation with our experts to discuss your business challenges and opportunities.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowBookingForm(true)}
                >
                  {currentLanguage === 'ar' ? 'حجز استشارة' : 'Book Consultation'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceBookingForm
                serviceName={currentLanguage === 'ar' ? 'خدمات الاستشارات التجارية' : 'Business Consulting'}
                onClose={() => setShowBookingForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}