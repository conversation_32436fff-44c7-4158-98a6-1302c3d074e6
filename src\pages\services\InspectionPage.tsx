'use client';

import { useState } from 'react';
import { ArrowRight, Search, CheckCircle, FileCheck, Shield } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Breadcrumb, generateServiceBreadcrumbs } from '../../components/ui/Breadcrumb';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

export default function InspectionPage() {
  const [showBookingForm, setShowBookingForm] = useState(false);
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // Generate breadcrumb items
  const breadcrumbItems = generateServiceBreadcrumbs(
    currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services',
    'inspection',
    currentLanguage
  );

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <section className={cn("py-4 border-b", isDarkMode ? "bg-slate-900 border-slate-700" : "bg-slate-50 border-slate-200")}>
        <div className="container-custom">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </section>

      {/* Hero Section */}
      <section className={cn(
        "py-20 transition-colors duration-500",
        isDarkMode
          ? "bg-gradient-to-b from-slate-800 to-slate-900 text-white"
          : "bg-gradient-to-b from-slate-100 to-white text-slate-900"
      )}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center animate-fade-in">
            <div className={cn(
              "inline-flex justify-center items-center w-20 h-20 rounded-full mb-6",
              isDarkMode ? "bg-primary-500/20 text-primary-300" : "bg-primary-100 text-primary-600"
            )}>
              <Search size={36} />
            </div>
            <h1 className={cn(
              "text-4xl md:text-5xl font-bold mb-6",
              isDarkMode ? "text-white" : "text-slate-900"
            )}>
              {currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services'}
            </h1>
            <p className={cn(
              "text-xl mb-8 max-w-2xl mx-auto",
              isDarkMode ? "text-slate-300" : "text-slate-600"
            )}>
              {currentLanguage === 'ar'
                ? 'خدمات شاملة لمراقبة الجودة وفحص المنتجات في كل مرحلة من مراحل سلسلة التوريد الخاصة بك.'
                : 'Comprehensive quality control and product inspection services at every stage of your supply chain.'}
            </p>
            <Button
              size="lg"
              variant="primary"
              className="px-8 transition-all duration-300 hover:scale-105"
              onClick={() => setShowBookingForm(true)}
            >
              {currentLanguage === 'ar' ? 'حجز فحص' : 'Book Inspection'}
              <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16 animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {currentLanguage === 'ar' ? 'حلول فحص شاملة' : 'Comprehensive Inspection Solutions'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300">
              {currentLanguage === 'ar'
                ? 'خدمات الفحص لدينا تضمن الجودة والامتثال في كل خطوة'
                : 'Our inspection services ensure quality and compliance at every step'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Search className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "فحص ما قبل الإنتاج" : "Pre-Production Inspection",
                description: currentLanguage === 'ar'
                  ? "التحقق من المواد الخام والمكونات قبل بدء الإنتاج"
                  : "Verify raw materials and components before production begins",
              },
              {
                icon: <CheckCircle className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "فحص أثناء الإنتاج" : "During Production Check",
                description: currentLanguage === 'ar'
                  ? "مراقبة عمليات الإنتاج واكتشاف المشكلات مبكرًا"
                  : "Monitor production processes and catch issues early",
              },
              {
                icon: <FileCheck className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "فحص ما قبل الشحن" : "Pre-Shipment Inspection",
                description: currentLanguage === 'ar'
                  ? "فحص الجودة النهائي قبل مغادرة المنتجات للمنشأة"
                  : "Final quality check before products leave the facility",
              },
              {
                icon: <Shield className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "فحص تحميل الحاويات" : "Container Loading Check",
                description: currentLanguage === 'ar'
                  ? "ضمان التحميل والتعبئة المناسبين للنقل الآمن"
                  : "Ensure proper loading and packaging for safe transit",
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="animate-fade-in-stagger transition-all duration-300 hover:scale-105"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <Card className="p-6 h-full hover:shadow-lg transition-shadow duration-300">
                  <div className="flex flex-col items-center mb-4 text-center">
                    <div className="mb-4 p-3 rounded-full bg-primary-50 dark:bg-primary-900/20">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    {feature.description}
                  </p>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16 animate-fade-in">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {currentLanguage === 'ar' ? 'عملية الفحص لدينا' : 'Our Inspection Process'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300">
              {currentLanguage === 'ar'
                ? 'نهج منهجي لضمان مراقبة الجودة الشاملة'
                : 'A systematic approach to ensure thorough quality control'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                number: "01",
                title: currentLanguage === 'ar' ? "جدولة" : "Schedule",
                description: currentLanguage === 'ar'
                  ? "حجز خدمة الفحص وتقديم المتطلبات"
                  : "Book inspection service and provide requirements",
              },
              {
                number: "02",
                title: currentLanguage === 'ar' ? "تحضير" : "Prepare",
                description: currentLanguage === 'ar'
                  ? "يقوم فريقنا بمراجعة المواصفات والمعايير"
                  : "Our team reviews specifications and criteria",
              },
              {
                number: "03",
                title: currentLanguage === 'ar' ? "فحص" : "Inspect",
                description: currentLanguage === 'ar'
                  ? "فحص شامل وفقًا للمعايير المتفق عليها"
                  : "Thorough inspection following agreed standards",
              },
              {
                number: "04",
                title: currentLanguage === 'ar' ? "تقرير" : "Report",
                description: currentLanguage === 'ar'
                  ? "تقرير فحص مفصل مع النتائج"
                  : "Detailed inspection report with findings",
              },
            ].map((step, index) => (
              <div
                key={index}
                className="relative text-center animate-fade-in-stagger transition-all duration-300 hover:scale-105"
                style={{ animationDelay: `${index * 0.15}s` }}
              >
                <div className="bg-primary-500 text-white rounded-full h-14 w-14 flex items-center justify-center font-bold text-lg mb-4 mx-auto shadow-md">
                  {step.number}
                </div>
                <Card className="p-6 h-full hover:shadow-lg transition-shadow duration-300">
                  <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in">
          <div className="max-w-2xl w-full animate-scale-in">
            <ServiceBookingForm
              serviceName={currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services'}
              onClose={() => setShowBookingForm(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}