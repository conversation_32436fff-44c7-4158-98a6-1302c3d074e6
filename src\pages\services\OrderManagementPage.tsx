'use client';

import { useState } from 'react';
import { ArrowRight, BuildingIcon, FileText, CheckCircle, User, Briefcase, Phone } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

export default function OrderManagementPage() {
  const [showQuoteForm, setShowQuoteForm] = useState(false);
  const { language } = useLanguageStore();
  const { locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-900 to-slate-800 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <FileText size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'حلول إدارة الطلبات' : 'Order Management Solutions'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'معالجة مبسطة لطلبات الجملة والطلبات الكبيرة مع دعم مخصص وأسعار تنافسية.'
                  : 'Streamlined wholesale and bulk order processing with dedicated support and competitive pricing.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowQuoteForm(true)}
                >
                  {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Benefits Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'لماذا تختار خدمة إدارة الطلبات لدينا؟' : 'Why Choose Our Order Management Service?'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'استمتع بمزايا حصرية مصممة خصيصًا لعملاء الأعمال لدينا.'
                  : 'Enjoy exclusive benefits designed specifically for our B2B customers.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <BuildingIcon size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "خصومات الكميات الكبيرة" : "Volume Discounts",
                description: currentLanguage === 'ar'
                  ? "استمتع بأسعار تنافسية تتناسب مع حجم طلبك، مما يزيد من هوامش الربح لديك."
                  : "Enjoy competitive pricing that scales with your order volume, maximizing your profit margins.",
              },
              {
                icon: <User size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "مدير حساب مخصص" : "Dedicated Account Manager",
                description: currentLanguage === 'ar'
                  ? "احصل على خدمة شخصية مع ممثل مخصص يفهم احتياجات عملك."
                  : "Get personalized service with a dedicated representative who understands your business needs.",
              },
              {
                icon: <FileText size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "كتالوجات مخصصة" : "Customized Catalogs",
                description: currentLanguage === 'ar'
                  ? "احصل على كتالوجات منتجات مصممة خصيصًا لصناعتك ومتطلباتك الخاصة."
                  : "Receive product catalogs tailored to your industry and specific requirements.",
              },
              {
                icon: <CheckCircle size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "ضمان الجودة" : "Quality Assurance",
                description: currentLanguage === 'ar'
                  ? "تخضع جميع المنتجات لرقابة جودة صارمة لضمان الاتساق والموثوقية."
                  : "All products undergo rigorous quality control to ensure consistency and reliability.",
              },
              {
                icon: <Briefcase size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "شروط دفع مرنة" : "Flexible Payment Terms",
                description: currentLanguage === 'ar'
                  ? "الوصول إلى خيارات دفع مناسبة للأعمال وشروط ائتمانية للحسابات المؤهلة."
                  : "Access to business-friendly payment options and credit terms for qualified accounts.",
              },
              {
                icon: <Phone size={32} className="text-primary-500" />,
                title: currentLanguage === 'ar' ? "دعم ذو أولوية" : "Priority Support",
                description: currentLanguage === 'ar'
                  ? "احصل على ردود سريعة على الاستفسارات وحلول سريعة لأي مشكلات."
                  : "Get quick responses to inquiries and expedited resolution for any issues.",
              },
            ].map((benefit, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="text-center h-full">
                  <div className="p-6">
                    <div className={cn(
                      "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                      isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                    )}>
                      {benefit.icon}
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">
                      {benefit.title}
                    </h3>
                    <p className="text-slate-600 dark:text-slate-300">
                      {benefit.description}
                    </p>
                  </div>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Process Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'عملية إدارة الطلبات لدينا' : 'Our Order Management Process'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'نهج مبسط مصمم للكفاءة والرضا.'
                  : 'A streamlined approach designed for efficiency and satisfaction.'}
              </p>
            </div>
          </ScrollAnimation>

          <div className="relative">
            <div className={cn(
              "hidden md:block absolute top-1/2 left-0 right-0 h-1 transform -translate-y-1/2",
              isDarkMode ? "bg-primary-700/30" : "bg-primary-200"
            )}></div>
            <ScrollStagger
              animation="slide"
              direction="up"
              staggerDelay={0.15}
              className="grid grid-cols-1 md:grid-cols-4 gap-8"
            >
              {[
                {
                  number: "01",
                  title: currentLanguage === 'ar' ? "تقديم طلب عرض سعر" : "Submit RFQ",
                  description: currentLanguage === 'ar'
                    ? "املأ نموذج طلب عرض السعر البسيط الخاص بنا مع احتياجات المنتج والكميات."
                    : "Fill out our simple Request for Quote form with your product needs and quantities.",
                },
                {
                  number: "02",
                  title: currentLanguage === 'ar' ? "استلام عرض السعر" : "Receive Quote",
                  description: currentLanguage === 'ar'
                    ? "سيقوم فريقنا بمراجعة طلبك وتقديم عرض سعر مخصص خلال 24-48 ساعة."
                    : "Our team will review your request and provide a customized quote within 24-48 hours.",
                },
                {
                  number: "03",
                  title: currentLanguage === 'ar' ? "تأكيد الطلب" : "Confirm Order",
                  description: currentLanguage === 'ar'
                    ? "مراجعة واعتماد عرض السعر، ثم إنهاء تفاصيل الدفع والتسليم."
                    : "Review and approve the quote, then finalize payment and delivery details.",
                },
                {
                  number: "04",
                  title: currentLanguage === 'ar' ? "التسليم" : "Delivery",
                  description: currentLanguage === 'ar'
                    ? "استلام منتجاتك وفقًا للجدول الزمني المتفق عليه وطريقة التسليم."
                    : "Receive your products according to the agreed timeline and delivery method.",
                },
              ].map((step, index) => (
                <HoverAnimation key={index} animation="lift" className="relative">
                  <div className={cn(
                    "md:absolute md:top-0 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 text-white rounded-full h-14 w-14 flex items-center justify-center font-bold text-lg mb-4 md:mb-0 mx-auto z-10",
                    isDarkMode ? "bg-primary-600" : "bg-primary-500"
                  )}>
                    {step.number}
                  </div>
                  <Card className={cn(
                    "p-6 text-center mt-8 h-full",
                    isDarkMode ? "bg-slate-700" : "bg-white"
                  )}>
                    <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{step.title}</h3>
                    <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                  </Card>
                </HoverAnimation>
              ))}
            </ScrollStagger>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.4}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد للبدء؟' : 'Ready to Get Started?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'قدم طلب عرض السعر اليوم واكتشف كيف يمكننا دعم نمو أعمالك.'
                  : 'Submit your quote request today and discover how we can support your business growth.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowQuoteForm(true)}
                >
                  {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request a Quote'}
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.5}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'أسئلة شائعة حول برنامج البيع بالجملة وعملية إدارة الطلبات.'
                  : 'Common questions about our wholesale program and order management process.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="max-w-4xl mx-auto space-y-6"
          >
            {[
              {
                question: currentLanguage === 'ar' ? "ما هي الكمية الدنيا للطلب بالجملة؟" : "What is the minimum order quantity for wholesale?",
                answer: currentLanguage === 'ar'
                  ? "تختلف الكميات الدنيا للطلب حسب فئة المنتج. بشكل عام، نطلب حدًا أدنى للشراء بقيمة 1,000 دولار لطلبات الجملة الأولى. قد يكون للطلبات اللاحقة حد أدنى أقل بناءً على حالة حسابك."
                  : "Minimum order quantities vary by product category. Generally, we require a minimum purchase of $1,000 for first-time wholesale orders. Subsequent orders may have lower minimums based on your account status.",
              },
              {
                question: currentLanguage === 'ar' ? "كم من الوقت يستغرق معالجة طلب الجملة؟" : "How long does it take to process a wholesale order?",
                answer: currentLanguage === 'ar'
                  ? "بعد تأكيد طلبك، تستغرق المعالجة عادة 3-5 أيام عمل قبل الشحن. بالنسبة للطلبات الكبيرة أو الطلبات المخصصة، قد تمتد فترة المعالجة إلى 7-10 أيام عمل."
                  : "After confirming your order, processing typically takes 3-5 business days before shipment. For larger orders or custom requests, processing time may extend to 7-10 business days.",
              },
              {
                question: currentLanguage === 'ar' ? "هل تقدمون شحن الجملة الدولي؟" : "Do you offer international wholesale shipping?",
                answer: currentLanguage === 'ar'
                  ? "نعم، نقوم بشحن طلبات الجملة دوليًا. تكاليف الشحن الدولي ورسوم الجمارك وضرائب الاستيراد هي مسؤولية المشتري. نقدم المساعدة في توثيق التخليص الجمركي."
                  : "Yes, we ship wholesale orders internationally. International shipping costs, customs fees, and import taxes are the responsibility of the buyer. We provide assistance with documentation for customs clearance.",
              },
              {
                question: currentLanguage === 'ar' ? "ما هي طرق الدفع التي تقبلونها لطلبات الجملة؟" : "What payment methods do you accept for wholesale orders?",
                answer: currentLanguage === 'ar'
                  ? "نقبل التحويلات المصرفية ومدفوعات ACH وبطاقات الائتمان الرئيسية لطلبات الجملة. قد تكون الحسابات المؤهلة مؤهلة لشروط دفع صافية لمدة 30 يومًا بعد إنشاء سجل دفع."
                  : "We accept wire transfers, ACH payments, and major credit cards for wholesale orders. Qualified accounts may be eligible for net-30 payment terms after establishing a payment history.",
              },
              {
                question: currentLanguage === 'ar' ? "هل يمكنني الحصول على عينات قبل تقديم طلب بكميات كبيرة؟" : "Can I get samples before placing a bulk order?",
                answer: currentLanguage === 'ar'
                  ? "نعم، تتوفر طلبات العينات بأسعار مخفضة. يمكن إضافة تكاليف العينات إلى مشترياتك الجملة الأولى إذا تجاوزت مبلغًا محددًا."
                  : "Yes, sample orders are available at discounted rates. Sample costs may be credited toward your first wholesale purchase if it exceeds a specified amount.",
              },
              {
                question: currentLanguage === 'ar' ? "كيف أصبح موزعًا معتمدًا لمنتجاتكم؟" : "How do I become an authorized reseller of your products?",
                answer: currentLanguage === 'ar'
                  ? "لتصبح موزعًا معتمدًا، يرجى إكمال عملية طلب البيع بالجملة لدينا. يتضمن ذلك تقديم وثائق العمل ومعلومات الهوية الضريبية والموافقة على شروط وأحكام الموزع لدينا."
                  : "To become an authorized reseller, please complete our wholesale application process. This includes submitting business documentation, tax ID information, and agreeing to our reseller terms and conditions.",
              },
            ].map((faq, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className={cn(
                  "p-6",
                  isDarkMode ? "bg-slate-800" : "bg-slate-50"
                )}>
                  <h3 className="text-xl font-medium text-slate-900 dark:text-white mb-3">{faq.question}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Quote Form Modal */}
      {showQuoteForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <WholesaleQuoteForm
                serviceName={currentLanguage === 'ar' ? 'إدارة الطلبات' : 'Order Management'}
                onClose={() => setShowQuoteForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}