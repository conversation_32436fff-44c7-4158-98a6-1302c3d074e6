'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  Phone,
  Mail,
  ArrowRight,
  Search,
  Package,
  Truck,
  FileCheck,
  Users,
  ClipboardList,
  X,
  Star,
  Clock,
  CheckCircle,
  MapPin,
  Filter
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { services } from '../../data/services';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';

// Icon mapping for services
const icons = {
  Search,
  Package,
  Truck,
  FileCheck,
  Users,
  ClipboardList,
};

// Service filter interface
interface ServiceFilters {
  searchQuery: string;
  category: string;
  sortBy: 'name' | 'popularity' | 'recent';
}

export default function ServicesPageEnhanced() {
  const router = useRouter();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();

  // State management
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [selectedService, setSelectedService] = useState<string | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<ServiceFilters>({
    searchQuery: '',
    category: 'all',
    sortBy: 'name'
  });

  // Service categories for filtering
  const categories = [
    { id: 'all', name: language === 'ar' ? 'جميع الخدمات' : 'All Services' },
    { id: 'inspection', name: language === 'ar' ? 'خدمات الفحص' : 'Inspection Services' },
    { id: 'storage', name: language === 'ar' ? 'حلول التخزين' : 'Storage Solutions' },
    { id: 'shipping', name: language === 'ar' ? 'الشحن والخدمات اللوجستية' : 'Shipping & Logistics' },
    { id: 'order-management', name: language === 'ar' ? 'إدارة الطلبات' : 'Order Management' },
    { id: 'certification', name: language === 'ar' ? 'شهادات المنتجات' : 'Product Certification' },
    { id: 'consulting', name: language === 'ar' ? 'الاستشارات التجارية' : 'Business Consulting' },
  ];

  // Filter and sort services
  const filteredServices = useMemo(() => {
    let filtered = services.filter(service => {
      const matchesSearch = filters.searchQuery === '' ||
        service.name.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        (service.name_ar && service.name_ar.includes(filters.searchQuery)) ||
        service.description.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        (service.description_ar && service.description_ar.includes(filters.searchQuery));

      const matchesCategory = filters.category === 'all' || service.id === filters.category;

      return matchesSearch && matchesCategory;
    });

    // Sort services
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          const nameA = language === 'ar' ? (a.name_ar || a.name) : a.name;
          const nameB = language === 'ar' ? (b.name_ar || b.name) : b.name;
          return nameA.localeCompare(nameB);
        case 'popularity':
          return b.id.localeCompare(a.id); // Mock popularity sort
        case 'recent':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [services, filters, language]);

  // Event handlers
  const handleBookService = (serviceName: string) => {
    setSelectedService(serviceName);
    setShowBookingForm(true);
  };

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      category: 'all',
      sortBy: 'name'
    });
  };

  return (
    <div>
      {/* Enhanced Professional Hero Section */}
      <section className={cn(
        "relative py-16 overflow-hidden transition-colors duration-500",
        isDarkMode
          ? "bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
          : "bg-gradient-to-br from-slate-50 via-white to-slate-100"
      )}>
        {/* Subtle Background Pattern */}
        <div className="absolute inset-0">
          <div className={cn(
            "absolute inset-0 opacity-5 transition-opacity duration-500",
            isDarkMode ? "opacity-10" : "opacity-5"
          )}>
            <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
          </div>
        </div>

        <div className="container-custom relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Main Heading */}
            <h1 className={cn(
              "text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6 animate-fade-in",
              isDarkMode ? "text-white" : "text-slate-900"
            )}>
              <span className="block">
                {language === 'ar' ? 'خدمات' : 'Business'}
              </span>
              <span className={cn(
                "block bg-gradient-to-r bg-clip-text text-transparent",
                isDarkMode
                  ? "from-primary-400 to-blue-400"
                  : "from-primary-600 to-blue-600"
              )}>
                {language === 'ar' ? 'الأعمال' : 'Services'}
              </span>
            </h1>

            {/* Description */}
            <p className={cn(
              "text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8 animate-fade-in-delay-1",
              isDarkMode ? "text-slate-300" : "text-slate-600"
            )}>
              {language === 'ar'
                ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.'
                : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'}
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 animate-fade-in-delay-2">
              {[
                {
                  icon: <CheckCircle className="h-5 w-5" />,
                  value: '6+',
                  label: language === 'ar' ? 'خدمة متخصصة' : 'Expert Services'
                },
                {
                  icon: <Star className="h-5 w-5" />,
                  value: '500+',
                  label: language === 'ar' ? 'عميل راضي' : 'Satisfied Clients'
                },
                {
                  icon: <Clock className="h-5 w-5" />,
                  value: '24/48h',
                  label: language === 'ar' ? 'وقت الاستجابة' : 'Response Time'
                },
                {
                  icon: <MapPin className="h-5 w-5" />,
                  value: '50+',
                  label: language === 'ar' ? 'دولة' : 'Countries'
                }
              ].map((stat, index) => (
                <div key={index} className={cn(
                  "p-4 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg",
                  isDarkMode
                    ? "bg-slate-800/50 hover:bg-slate-800/70"
                    : "bg-white/50 hover:bg-white/70"
                )}>
                  <div className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2",
                    isDarkMode ? "bg-primary-500/20 text-primary-400" : "bg-primary-100 text-primary-600"
                  )}>
                    {stat.icon}
                  </div>
                  <div className={cn(
                    "text-lg font-bold mb-1",
                    isDarkMode ? "text-white" : "text-slate-900"
                  )}>
                    {stat.value}
                  </div>
                  <div className={cn(
                    "text-xs font-medium",
                    isDarkMode ? "text-slate-300" : "text-slate-600"
                  )}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-4 animate-fade-in-delay-3">
              <Button
                size="lg"
                variant="primary"
                className="px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Phone className={`${language === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 group-hover:scale-110 transition-transform`} />
                {language === 'ar' ? 'استشارة مجانية' : 'Free Consultation'}
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105"
                onClick={() => document.getElementById('services-grid')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <ArrowRight className={`${language === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 group-hover:translate-x-1 transition-transform`} />
                {language === 'ar' ? 'استكشف الخدمات' : 'Explore Services'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Search and Filter Section */}
      <section className={cn("py-12", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {/* Search Bar */}
            <div className="relative mb-6">
              <Search className={cn(
                "absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400",
                language === 'ar' ? "right-4" : "left-4"
              )} />
              <Input
                type="text"
                placeholder={language === 'ar' ? 'ابحث عن الخدمات...' : 'Search services...'}
                value={filters.searchQuery}
                onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
                className={cn(
                  "w-full h-12 text-lg transition-all duration-300 focus:ring-2 focus:ring-primary-500",
                  language === 'ar' ? "pr-12 pl-4" : "pl-12 pr-4",
                  isDarkMode ? "bg-slate-700 border-slate-600 text-white" : "bg-white border-slate-300"
                )}
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              {/* Category Filter */}
              <div className="flex-1">
                <label className={cn(
                  "block text-sm font-medium mb-2",
                  isDarkMode ? "text-slate-300" : "text-slate-700"
                )}>
                  {language === 'ar' ? 'الفئة' : 'Category'}
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className={cn(
                    "w-full h-10 px-3 rounded-md border transition-all duration-300 focus:ring-2 focus:ring-primary-500",
                    isDarkMode ? "bg-slate-700 border-slate-600 text-white" : "bg-white border-slate-300"
                  )}
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Filter */}
              <div className="flex-1">
                <label className={cn(
                  "block text-sm font-medium mb-2",
                  isDarkMode ? "text-slate-300" : "text-slate-700"
                )}>
                  {language === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as 'name' | 'popularity' | 'recent' }))}
                  className={cn(
                    "w-full h-10 px-3 rounded-md border transition-all duration-300 focus:ring-2 focus:ring-primary-500",
                    isDarkMode ? "bg-slate-700 border-slate-600 text-white" : "bg-white border-slate-300"
                  )}
                >
                  <option value="name">{language === 'ar' ? 'الاسم' : 'Name'}</option>
                  <option value="popularity">{language === 'ar' ? 'الشعبية' : 'Popularity'}</option>
                  <option value="recent">{language === 'ar' ? 'الأحدث' : 'Most Recent'}</option>
                </select>
              </div>

              {/* Clear Filters Button */}
              <div className="flex items-end">
                <Button
                  onClick={clearFilters}
                  variant="outline"
                  className="h-10 px-4 transition-all duration-300 hover:scale-105"
                >
                  <X className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'مسح' : 'Clear'}
                </Button>
              </div>
            </div>

            {/* Results Summary */}
            <div className={cn(
              "text-sm mb-6 p-3 rounded-lg",
              isDarkMode ? "bg-slate-700 text-slate-300" : "bg-white text-slate-600"
            )}>
              {language === 'ar'
                ? `عرض ${filteredServices.length} من ${services.length} خدمة`
                : `Showing ${filteredServices.length} of ${services.length} services`}
              {filters.searchQuery && (
                <span className="ml-2">
                  {language === 'ar'
                    ? `للبحث "${filters.searchQuery}"`
                    : `for "${filters.searchQuery}"`}
                </span>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section id="services-grid" className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {language === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300">
              {language === 'ar'
                ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.'
                : 'Discover the full range of business services designed to support your operations at every stage.'}
            </p>
          </div>

          {filteredServices.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredServices.map((service, index) => {
                const Icon = icons[service.icon as keyof typeof icons];
                return (
                  <div
                    key={service.id}
                    className="group animate-fade-in-stagger"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <Card className="group hover:shadow-xl transition-all duration-300 h-full hover:scale-105">
                      <CardHeader className="text-center pb-4">
                        <div className="flex justify-center mb-4">
                          <div className={cn(
                            "p-4 rounded-full transition-all duration-300 group-hover:scale-110",
                            isDarkMode ? "bg-primary-500/20" : "bg-primary-50"
                          )}>
                            <Icon size={32} className="text-primary-500 dark:text-primary-400" />
                          </div>
                        </div>
                        <CardTitle className="text-xl mb-2 text-slate-900 dark:text-white">
                          {language === 'ar' ? service.name_ar || service.name : service.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex flex-col h-full">
                        <p className="text-slate-600 dark:text-slate-300 mb-6 flex-grow">
                          {language === 'ar' ? service.description_ar || service.description : service.description}
                        </p>

                        {/* Service Features Preview */}
                        <div className="mb-6">
                          <div className="flex flex-wrap gap-2">
                            {(language === 'ar' ? service.features_ar || service.features : service.features)
                              .slice(0, 3)
                              .map((feature, index) => (
                                <span
                                  key={index}
                                  className={cn(
                                    "text-xs px-2 py-1 rounded-full",
                                    isDarkMode
                                      ? "bg-slate-700 text-slate-300"
                                      : "bg-slate-100 text-slate-600"
                                  )}
                                >
                                  {feature}
                                </span>
                              ))}
                            {service.features.length > 3 && (
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full",
                                isDarkMode
                                  ? "bg-primary-500/20 text-primary-400"
                                  : "bg-primary-50 text-primary-600"
                              )}>
                                +{service.features.length - 3} {language === 'ar' ? 'المزيد' : 'more'}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex gap-2 mt-auto">
                          <Button
                            onClick={() => handleBookService(language === 'ar' ? service.name_ar || service.name : service.name)}
                            className="flex-1 group transition-all duration-300 hover:scale-105"
                            variant="primary"
                          >
                            <Phone className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                            {language === 'ar' ? 'احجز الخدمة' : 'Book Service'}
                          </Button>
                          <Button
                            onClick={() => router.push(`/${language}/services/${service.slug}`)}
                            variant="outline"
                            className="flex-1 group transition-all duration-300 hover:scale-105"
                          >
                            {language === 'ar' ? 'معرفة المزيد' : 'Learn More'}
                            <ArrowRight className={`${language === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4 transition-transform group-hover:translate-x-1`} />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16 animate-fade-in">
              <div className={cn(
                "w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6",
                isDarkMode ? "bg-slate-800" : "bg-slate-100"
              )}>
                <Search className="h-12 w-12 text-slate-400" />
              </div>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                {language === 'ar' ? 'لم يتم العثور على خدمات' : 'No Services Found'}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                {language === 'ar'
                  ? 'جرب تعديل معايير البحث أو الفلاتر للعثور على ما تبحث عنه.'
                  : 'Try adjusting your search criteria or filters to find what you\'re looking for.'}
              </p>
              <Button onClick={clearFilters} variant="outline" className="transition-all duration-300 hover:scale-105">
                {language === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'}
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* How We Work Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {language === 'ar' ? 'كيف نعمل' : 'How We Work'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300">
              {language === 'ar'
                ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.'
                : 'Our consultative approach ensures tailored solutions for your unique business needs.'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                number: "01",
                title: language === 'ar' ? "الاستشارة" : "Consultation",
                description: language === 'ar'
                  ? "نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك."
                  : "We begin with a thorough consultation to understand your business needs and challenges.",
              },
              {
                number: "02",
                title: language === 'ar' ? "التحليل" : "Analysis",
                description: language === 'ar'
                  ? "يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة."
                  : "Our experts analyze your requirements and develop customized service recommendations.",
              },
              {
                number: "03",
                title: language === 'ar' ? "التنفيذ" : "Implementation",
                description: language === 'ar'
                  ? "نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة."
                  : "We implement the agreed services with attention to detail and quality assurance.",
              },
              {
                number: "04",
                title: language === 'ar' ? "الدعم المستمر" : "Ongoing Support",
                description: language === 'ar'
                  ? "المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف."
                  : "Continuous monitoring and support ensure optimal results and adaptability.",
              },
            ].map((step, index) => (
              <div key={index} className="relative text-center group">
                <div className="bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto transition-all duration-300 group-hover:scale-110">
                  {step.number}
                </div>
                <div className={cn(
                  "p-6 rounded-lg shadow-sm h-full transition-all duration-300 group-hover:shadow-lg group-hover:scale-105",
                  isDarkMode ? "bg-slate-900" : "bg-white"
                )}>
                  <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {language === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300">
              {language === 'ar'
                ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.'
                : 'Find answers to common questions about our business services.'}
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {[
              {
                question: language === 'ar' ? "ما هي أنواع الشركات التي تخدمونها؟" : "What types of businesses do you serve?",
                answer: language === 'ar'
                  ? "نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة."
                  : "We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.",
              },
              {
                question: language === 'ar' ? "ما هي سرعة ترتيب خدمات الفحص؟" : "How quickly can you arrange inspection services?",
                answer: language === 'ar'
                  ? "يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر."
                  : "Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability.",
              },
              {
                question: language === 'ar' ? "هل تقدمون خدمات دولية؟" : "Do you provide services internationally?",
                answer: language === 'ar'
                  ? "نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين."
                  : "Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas.",
              },
              {
                question: language === 'ar' ? "ما هي شروط الدفع والطرق المتاحة؟" : "What are your payment terms and methods?",
                answer: language === 'ar'
                  ? "نقبل طرق دفع متنوعة بما في ذلك التحويلات المصرفية وبطاقات الائتمان والمدفوعات الرقمية. للعملاء المنتظمين، نقدم شروط دفع مرنة بما في ذلك حسابات صافي 30 يومًا."
                  : "We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts.",
              },
              {
                question: language === 'ar' ? "كيف تضمنون جودة الخدمة والاتساق؟" : "How do you ensure service quality and consistency?",
                answer: language === 'ar'
                  ? "نحافظ على عمليات مراقبة جودة صارمة، بما في ذلك التدريب المنتظم لموظفينا وإجراءات التشغيل المعيارية والمراقبة المستمرة لتقديم الخدمات."
                  : "We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery.",
              }
            ].map((faq, index) => (
              <div key={index} className="group">
                <details className={cn(
                  "rounded-lg overflow-hidden transition-all duration-300 group-hover:shadow-lg",
                  isDarkMode ? "bg-slate-800" : "bg-slate-50"
                )}>
                  <summary className="flex items-center justify-between p-6 cursor-pointer transition-all duration-300 hover:bg-opacity-80">
                    <h3 className="text-xl font-medium text-slate-900 dark:text-white pr-8">{faq.question}</h3>
                    <span className={cn(
                      "flex-shrink-0 ml-1.5 p-1.5 rounded-full transition-all duration-300",
                      isDarkMode ? "text-slate-300 bg-slate-700" : "text-slate-700 bg-white"
                    )}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 transform group-open:rotate-180 transition-transform duration-200"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </span>
                  </summary>
                  <div className="px-6 pb-6">
                    <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                  </div>
                </details>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              {language === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
              {language === 'ar'
                ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.'
                : 'Contact our team to discuss how our services can address your specific business needs.'}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                variant="primary"
                className="px-8 transition-all duration-300 hover:scale-105"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Phone className={`${language === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                {language === 'ar' ? 'اتصل بنا' : 'Contact Us'}
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="px-8 transition-all duration-300 hover:scale-105"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Mail className={`${language === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                {language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in">
          <div className="max-w-2xl w-full animate-scale-in">
            <ServiceBookingForm
              serviceName={selectedService}
              onClose={() => {
                setShowBookingForm(false);
                setSelectedService(undefined);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
