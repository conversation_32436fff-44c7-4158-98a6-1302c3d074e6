'use client';

import { useState } from 'react';
import { ArrowRight, Truck, Globe, Clock, Shield, Package, FileCheck } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

export default function ShippingPage() {
  const [showBookingForm, setShowBookingForm] = useState(false);
  const { language } = useLanguageStore();
  const { locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <Truck size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'الشحن والخدمات اللوجستية' : 'Shipping & Logistics'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'حلول شحن متكاملة تشمل الشحن الجوي والشحن البحري والتوصيل من الباب إلى الباب.'
                  : 'End-to-end shipping solutions including air freight, sea shipping, and door-to-door delivery.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowBookingForm(true)}
                >
                  {currentLanguage === 'ar' ? 'حجز شحن' : 'Book Shipping'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Services Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'خدمات الشحن لدينا' : 'Our Shipping Services'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'حلول لوجستية شاملة لجميع احتياجات الشحن الخاصة بك'
                  : 'Comprehensive logistics solutions for all your shipping needs'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <Globe className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "الشحن الجوي الدولي" : "International Air Freight",
                description: currentLanguage === 'ar'
                  ? "شحن جوي سريع وموثوق به إلى وجهات عالمية"
                  : "Fast and reliable air shipping to global destinations",
                features: currentLanguage === 'ar'
                  ? [
                      "خيارات التسليم السريع",
                      "شحن بدرجة حرارة متحكم بها",
                      "التخليص الجمركي",
                      "خدمة من الباب إلى الباب"
                    ]
                  : [
                      "Express delivery options",
                      "Temperature-controlled cargo",
                      "Customs clearance",
                      "Door-to-door service"
                    ]
              },
              {
                icon: <Package className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "الشحن البحري" : "Ocean Freight",
                description: currentLanguage === 'ar'
                  ? "شحن بحري فعال من حيث التكلفة للأحجام الكبيرة"
                  : "Cost-effective sea shipping for large volumes",
                features: currentLanguage === 'ar'
                  ? [
                      "شحن حاويات كاملة وجزئية",
                      "خدمة من ميناء إلى ميناء",
                      "تتبع الحاويات",
                      "تأمين البضائع"
                    ]
                  : [
                      "FCL & LCL shipping",
                      "Port-to-port service",
                      "Container tracking",
                      "Cargo insurance"
                    ]
              },
              {
                icon: <Truck className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "النقل البري" : "Ground Transportation",
                description: currentLanguage === 'ar'
                  ? "حلول لوجستية برية موثوقة"
                  : "Reliable inland logistics solutions",
                features: currentLanguage === 'ar'
                  ? [
                      "حمولة شاحنة كاملة",
                      "حمولة أقل من شاحنة",
                      "توصيل سريع",
                      "خدمة الميل الأخير"
                    ]
                  : [
                      "Full truckload",
                      "Less than truckload",
                      "Express delivery",
                      "Last-mile service"
                    ]
              }
            ].map((service, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <div className="flex items-center mb-4">
                    <div className={cn(
                      "p-2 rounded-full mr-3",
                      isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                    )}>
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">{service.title}</h3>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center text-sm text-slate-600 dark:text-slate-300">
                        <FileCheck className={`h-4 w-4 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Features Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'لماذا تختار خدمات الشحن لدينا؟' : 'Why Choose Our Shipping Services?'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'اختبر الفرق مع حلولنا اللوجستية المتميزة'
                  : 'Experience the difference with our premium logistics solutions'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                icon: <Globe className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "شبكة عالمية" : "Global Network",
                description: currentLanguage === 'ar'
                  ? "شبكة واسعة من الشركاء في جميع أنحاء العالم"
                  : "Extensive network of partners worldwide"
              },
              {
                icon: <Clock className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "تتبع في الوقت الفعلي" : "Real-time Tracking",
                description: currentLanguage === 'ar'
                  ? "تتبع شحناتك على مدار الساعة في الوقت الفعلي"
                  : "Track your shipments 24/7 in real-time"
              },
              {
                icon: <Shield className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "تأمين البضائع" : "Cargo Insurance",
                description: currentLanguage === 'ar'
                  ? "تغطية شاملة لبضائعك"
                  : "Comprehensive coverage for your goods"
              },
              {
                icon: <Package className="h-12 w-12 text-primary-500" />,
                title: currentLanguage === 'ar' ? "حلول مخصصة" : "Custom Solutions",
                description: currentLanguage === 'ar'
                  ? "حلول شحن مصممة خصيصًا لاحتياجاتك"
                  : "Tailored shipping solutions for your needs"
              }
            ].map((feature, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 text-center h-full">
                  <div className={cn(
                    "w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4",
                    isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                  )}>
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold mb-3 text-slate-900 dark:text-white">{feature.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{feature.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.4}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'جاهز للشحن؟' : 'Ready to Ship?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'ابدأ مع خدمات الشحن لدينا اليوم واختبر حلول لوجستية سلسة.'
                  : 'Get started with our shipping services today and experience seamless logistics solutions.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowBookingForm(true)}
                >
                  {currentLanguage === 'ar' ? 'احصل على عرض سعر' : 'Get a Quote'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceBookingForm
                serviceName={currentLanguage === 'ar' ? 'خدمات الشحن' : 'Shipping Services'}
                onClose={() => setShowBookingForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}