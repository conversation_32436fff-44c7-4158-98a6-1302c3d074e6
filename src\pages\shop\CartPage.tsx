'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { useCartStore } from '../../stores/cartStore';
import { ShoppingCart, Trash2 } from 'lucide-react';
import { useTranslation } from '../../translations';
import { formatCurrency } from '../../lib/utils';
import { useCurrencyStore } from '../../stores/currencyStore';
import { useLanguageStore } from '../../stores/languageStore';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function CartPage() {
  const router = useRouter();
  const { items, removeItem, updateQuantity, clearCart, getTotalPrice } = useCartStore();
  const { t, locale } = useTranslation();
  const { currency } = useCurrencyStore();
  const { language } = useLanguageStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  const total = getTotalPrice();

  useEffect(() => {
    document.title = currentLanguage === 'ar' ? 'سلة التسوق' : 'Shopping Cart';
  }, [currentLanguage]);

  if (items.length === 0) {
    return (
      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="text-center">
            <ShoppingCart className="mx-auto h-20 w-20 text-slate-400 dark:text-slate-500" />
            <h2 className="mt-6 text-2xl font-semibold text-slate-900 dark:text-white">{t('cart.empty')}</h2>
            <p className="mt-3 text-slate-600 dark:text-slate-400 max-w-md mx-auto">{t('cart.emptyMessage')}</p>
            <HoverAnimation animation="scale" className="mt-6 inline-block">
              <Button
                onClick={() => router.push(`/${currentLanguage}/shop`)}
                size="lg"
                className="px-6"
              >
                {t('cart.continueShopping')}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      <ScrollAnimation animation="fade" delay={0.1}>
        <h1 className="text-3xl font-bold mb-8 text-slate-900 dark:text-white">{t('cart.title')}</h1>
      </ScrollAnimation>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.2}
          >
            {items.map((item) => (
              <HoverAnimation key={item.id} animation="lift">
                <Card className="mb-4 p-4">
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <div className="w-24 h-24 relative rounded overflow-hidden">
                      <EnhancedImage
                        src={item.image}
                        alt={item.name}
                        fill={true}
                        objectFit="cover"
                        progressive={true}
                        placeholder="blur"
                        className="w-full h-full"
                        containerClassName="w-full h-full"
                      />
                    </div>
                    <div className="flex-1 text-center sm:text-left">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {currentLanguage === 'ar' ? item.name_ar || item.name : item.name}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-400">{formatCurrency(item.price, currency)}</p>
                      <div className="flex items-center justify-center sm:justify-start gap-4 mt-2">
                        <select
                          value={item.quantity}
                          onChange={(e) => updateQuantity(item.id, parseInt(e.target.value))}
                          className="rounded border p-1 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-700 text-slate-900 dark:text-white"
                        >
                          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                            <option key={num} value={num}>
                              {num}
                            </option>
                          ))}
                        </select>
                        <HoverAnimation animation="scale">
                          <button
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                            aria-label={t('cart.remove')}
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </HoverAnimation>
                      </div>
                    </div>
                    <div className="text-center sm:text-right mt-2 sm:mt-0">
                      <p className="text-lg font-semibold text-slate-900 dark:text-white">
                        {formatCurrency(item.price * item.quantity, currency)}
                      </p>
                    </div>
                  </div>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>

        <div className="lg:col-span-1">
          <ScrollAnimation animation="fade" delay={0.3}>
            <Card className="p-6 sticky top-24">
              <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('cart.orderSummary')}</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-700 dark:text-slate-300">{t('cart.subtotal')}</span>
                  <span className="font-medium text-slate-900 dark:text-white">{formatCurrency(total, currency)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-700 dark:text-slate-300">{t('cart.shipping')}</span>
                  <span className="text-slate-600 dark:text-slate-400">{t('cart.calculatedAtCheckout')}</span>
                </div>
                <div className="border-t border-slate-200 dark:border-slate-700 pt-3 mt-3">
                  <div className="flex justify-between font-semibold">
                    <span className="text-slate-900 dark:text-white">{t('cart.total')}</span>
                    <span className="text-primary-600 dark:text-primary-400">{formatCurrency(total, currency)}</span>
                  </div>
                </div>
              </div>
              <div className="mt-6 space-y-3">
                <HoverAnimation animation="scale">
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={() => router.push(`/${currentLanguage}/checkout`)}
                  >
                    {t('cart.proceedToCheckout')}
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <button
                    onClick={clearCart}
                    className="w-full py-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                  >
                    {t('cart.clearCart')}
                  </button>
                </HoverAnimation>
              </div>
            </Card>
          </ScrollAnimation>
        </div>
      </div>
    </div>
  );
}