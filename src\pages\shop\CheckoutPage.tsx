'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, CreditCard, ShoppingBag, Check, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import { useCurrencyStore } from '../../stores/currencyStore';
import { PaymentMethodSelector } from '../../components/shop/PaymentMethodSelector';
import { ShippingMethodSelector } from '../../components/shop/ShippingMethodSelector';
import { CurrencySelector } from '../../components/shop/CurrencySelector';
import { formatCurrency } from '../../lib/utils';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

// مخطط التحقق من صحة نموذج العنوان
const addressSchema = z.object({
  firstName: z.string().min(2, 'الاسم الأول قصير جدًا'),
  lastName: z.string().min(2, 'الاسم الأخير قصير جدًا'),
  email: z.string().email('البريد الإلكتروني غير صالح'),
  phone: z.string().min(10, 'رقم الهاتف غير صالح'),
  address: z.string().min(5, 'العنوان قصير جدًا'),
  city: z.string().min(2, 'المدينة قصيرة جدًا'),
  state: z.string().min(2, 'الولاية/المنطقة قصيرة جدًا'),
  postalCode: z.string().min(3, 'الرمز البريدي قصير جدًا'),
  country: z.string().min(2, 'البلد قصير جدًا'),
});

type AddressFormData = z.infer<typeof addressSchema>;

export default function CheckoutPage() {
  const [step, setStep] = useState<'cart' | 'address' | 'shipping' | 'payment' | 'review' | 'confirmation'>('address');
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [shippingMethod, setShippingMethod] = useState('standard');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderNumber, setOrderNumber] = useState<string | null>(null);

  const router = useRouter();
  const { items, getTotalPrice, clearCart } = useCartStore();
  const { user } = useAuthStore();
  const { currency } = useCurrencyStore();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: user?.email || '',
      phone: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US',
    },
  });

  // التحقق من وجود عناصر في السلة
  useEffect(() => {
    if (items.length === 0 && step !== 'confirmation') {
      router.push('/cart');
    }
  }, [items, router, step]);

  // حساب المجموع الفرعي
  const subtotal = getTotalPrice();

  // الحصول على سعر الشحن
  const getShippingPrice = (): number => {
    switch (shippingMethod) {
      case 'express':
        return 25;
      case 'overnight':
        return 50;
      case 'international':
        return 35;
      case 'free':
        return 0;
      default: // standard
        return 10;
    }
  };

  // حساب الضريبة (افتراضيًا 10%)
  const tax = subtotal * 0.1;

  // حساب المجموع الكلي
  const total = subtotal + getShippingPrice() + tax;

  // معالجة تقديم النموذج
  const onSubmit = async (data: AddressFormData) => {
    try {
      setError(null);

      if (step === 'address') {
        setStep('shipping');
      } else if (step === 'shipping') {
        setStep('payment');
      } else if (step === 'payment') {
        setStep('review');
      } else if (step === 'review') {
        // معالجة الطلب
        setIsProcessing(true);

        // محاكاة طلب API
        await new Promise(resolve => setTimeout(resolve, 2000));

        // إنشاء رقم طلب عشوائي
        const randomOrderNumber = `ORD-${Math.floor(Math.random() * 1000000)}`;
        setOrderNumber(randomOrderNumber);

        // مسح السلة
        clearCart();

        // الانتقال إلى صفحة التأكيد
        setStep('confirmation');
        setIsProcessing(false);
      }
    } catch (err) {
      setError('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.');
      setIsProcessing(false);
    }
  };

  // العودة إلى الخطوة السابقة
  const goBack = () => {
    if (step === 'shipping') {
      setStep('address');
    } else if (step === 'payment') {
      setStep('shipping');
    } else if (step === 'review') {
      setStep('payment');
    } else {
      router.push('/cart');
    }
  };

  // عرض تأكيد الطلب
  if (step === 'confirmation') {
    return (
      <div className="container-custom py-12">
        <div className="max-w-3xl mx-auto">
          <ScrollAnimation animation="fade" delay={0.2}>
            <Card className="p-8 text-center">
              <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
              </div>

              <h1 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">{t('checkout.orderConfirmed')}</h1>

              <p className="text-lg mb-6 text-slate-700 dark:text-slate-300">
                {t('checkout.thankYou')}
              </p>

              <div className="mb-8 p-6 bg-slate-50 dark:bg-slate-800/50 rounded-lg inline-block">
                <p className="text-sm text-slate-500 dark:text-slate-400">{t('checkout.orderNumber')}</p>
                <p className="text-xl font-semibold text-slate-900 dark:text-white">{orderNumber}</p>
              </div>

              <p className="mb-8 text-slate-600 dark:text-slate-400">
                {t('checkout.confirmationEmail')}
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    onClick={() => router.push(`/${currentLanguage}/shop`)}
                    variant="primary"
                    size="lg"
                    className="px-6"
                  >
                    {t('checkout.continueShopping')}
                  </Button>
                </HoverAnimation>

                <HoverAnimation animation="scale">
                  <Button
                    onClick={() => router.push(`/${currentLanguage}/account/orders`)}
                    variant="outline"
                    size="lg"
                    className="px-6"
                  >
                    {t('checkout.viewOrder')}
                  </Button>
                </HoverAnimation>
              </div>
            </Card>
          </ScrollAnimation>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex flex-col sm:flex-row justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-4 sm:mb-0">{t('checkout.title')}</h1>
          <CurrencySelector showName={false} />
        </div>

        {error && (
          <div className={cn(
            "p-4 rounded-md mb-6 flex items-center",
            isDarkMode ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
          )}>
            <AlertCircle className="mr-2 h-5 w-5" />
            <p>{error}</p>
          </div>
        )}
      </ScrollAnimation>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* المعلومات الرئيسية */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* خطوات الدفع */}
            <ScrollAnimation animation="fade" delay={0.2} className="mb-8">
              <div className="flex">
                {['address', 'shipping', 'payment', 'review'].map((s, index) => (
                  <div
                    key={s}
                    className={cn(
                      "flex-1 text-center relative",
                      index < ['address', 'shipping', 'payment', 'review'].indexOf(step) + 1
                        ? isDarkMode ? "text-primary-400" : "text-primary-600"
                        : "text-slate-400"
                    )}
                  >
                    <HoverAnimation animation="scale" className="inline-block">
                      <div className={cn(
                        "w-10 h-10 rounded-full mx-auto mb-2 flex items-center justify-center",
                        index < ['address', 'shipping', 'payment', 'review'].indexOf(step) + 1
                          ? isDarkMode ? "bg-primary-500 text-white" : "bg-primary-500 text-white"
                          : isDarkMode ? "bg-slate-700 text-slate-400" : "bg-slate-200 text-slate-500"
                      )}>
                        {index + 1}
                      </div>
                    </HoverAnimation>
                    <div className="text-sm font-medium">{t(`checkout.step.${s}`)}</div>

                    {index < 3 && (
                      <div className={cn(
                        "absolute top-5 left-1/2 w-full h-0.5",
                        index < ['address', 'shipping', 'payment', 'review'].indexOf(step)
                          ? isDarkMode ? "bg-primary-500" : "bg-primary-500"
                          : isDarkMode ? "bg-slate-700" : "bg-slate-200"
                      )} />
                    )}
                  </div>
                ))}
              </div>
            </ScrollAnimation>

            {/* معلومات العنوان */}
            {step === 'address' && (
              <ScrollAnimation animation="fade" delay={0.3}>
                <Card className="p-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('checkout.shippingAddress')}</h2>

                  <ScrollStagger
                    animation="fade"
                    staggerDelay={0.05}
                    className="space-y-4"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label={t('checkout.firstName')}
                        {...register('firstName')}
                        error={errors.firstName?.message}
                      />

                      <Input
                        label={t('checkout.lastName')}
                        {...register('lastName')}
                        error={errors.lastName?.message}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label={t('checkout.email')}
                        type="email"
                        {...register('email')}
                        error={errors.email?.message}
                      />

                      <Input
                        label={t('checkout.phone')}
                        {...register('phone')}
                        error={errors.phone?.message}
                      />
                    </div>

                    <div>
                      <Input
                        label={t('checkout.address')}
                        {...register('address')}
                        error={errors.address?.message}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Input
                        label={t('checkout.city')}
                        {...register('city')}
                        error={errors.city?.message}
                      />

                      <Input
                        label={t('checkout.state')}
                        {...register('state')}
                        error={errors.state?.message}
                      />

                      <Input
                        label={t('checkout.postalCode')}
                        {...register('postalCode')}
                        error={errors.postalCode?.message}
                      />
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-900 dark:text-white">
                        {t('checkout.country')}
                      </label>
                      <select
                        {...register('country')}
                        className={cn(
                          "w-full p-2 border rounded-md",
                          errors.country ? "border-red-500" : "border-slate-300 dark:border-slate-600",
                          "focus:outline-none focus:ring-2 focus:ring-primary-500",
                          isDarkMode ? "bg-slate-800 text-white" : "bg-white text-slate-900"
                        )}
                      >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="UK">United Kingdom</option>
                        <option value="SA">Saudi Arabia</option>
                        <option value="AE">United Arab Emirates</option>
                        <option value="CN">China</option>
                        {/* يمكن إضافة المزيد من البلدان */}
                      </select>
                      {errors.country && (
                        <p className="text-red-500 text-sm mt-1">{errors.country.message}</p>
                      )}
                    </div>
                  </ScrollStagger>
                </Card>
              </ScrollAnimation>
            )}

            {/* طريقة الشحن */}
            {step === 'shipping' && (
              <ScrollAnimation animation="fade" delay={0.3}>
                <Card className="p-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('checkout.shippingMethod')}</h2>
                  <ShippingMethodSelector
                    selectedMethod={shippingMethod}
                    onSelect={setShippingMethod}
                    country={watch('country')} // استخدام البلد المحدد في النموذج
                    orderTotal={subtotal}
                  />
                </Card>
              </ScrollAnimation>
            )}

            {/* طريقة الدفع */}
            {step === 'payment' && (
              <ScrollAnimation animation="fade" delay={0.3}>
                <Card className="p-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('checkout.paymentMethod')}</h2>
                  <PaymentMethodSelector
                    selectedMethod={paymentMethod}
                    onSelect={setPaymentMethod}
                  />
                </Card>
              </ScrollAnimation>
            )}

            {/* مراجعة الطلب */}
            {step === 'review' && (
              <ScrollAnimation animation="fade" delay={0.3}>
                <Card className="p-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('checkout.reviewOrder')}</h2>

                  <ScrollStagger
                    animation="fade"
                    staggerDelay={0.1}
                    className="space-y-6"
                  >
                    <div>
                      <h3 className="font-medium mb-2 text-slate-800 dark:text-white">{t('checkout.shippingAddress')}</h3>
                      <div className={cn(
                        "p-4 rounded-md",
                        isDarkMode ? "bg-slate-700" : "bg-slate-50"
                      )}>
                        <p className="text-slate-900 dark:text-white font-medium">{watch('firstName')} {watch('lastName')}</p>
                        <p className="text-slate-700 dark:text-slate-300">{watch('address')}</p>
                        <p className="text-slate-700 dark:text-slate-300">{watch('city')}, {watch('state')} {watch('postalCode')}</p>
                        <p className="text-slate-700 dark:text-slate-300">{watch('country')}</p>
                        <p className="text-slate-700 dark:text-slate-300">{watch('email')}</p>
                        <p className="text-slate-700 dark:text-slate-300">{watch('phone')}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-2 text-slate-800 dark:text-white">{t('checkout.shippingMethod')}</h3>
                      <div className={cn(
                        "p-4 rounded-md",
                        isDarkMode ? "bg-slate-700" : "bg-slate-50"
                      )}>
                        <p className="text-slate-900 dark:text-white font-medium">
                          {shippingMethod === 'standard' && t('shipping.standard')}
                          {shippingMethod === 'express' && t('shipping.express')}
                          {shippingMethod === 'overnight' && t('shipping.overnight')}
                          {shippingMethod === 'international' && t('shipping.international')}
                          {shippingMethod === 'free' && t('shipping.free')}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          {getShippingPrice() === 0
                            ? t('checkout.free')
                            : formatCurrency(getShippingPrice(), currency)}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-2 text-slate-800 dark:text-white">{t('checkout.paymentMethod')}</h3>
                      <div className={cn(
                        "p-4 rounded-md",
                        isDarkMode ? "bg-slate-700" : "bg-slate-50"
                      )}>
                        <p className="text-slate-900 dark:text-white">
                          {paymentMethod === 'credit_card' && (
                            <span className="flex items-center">
                              <CreditCard className="mr-2 h-5 w-5" />
                              {t('payment.creditCard')}
                            </span>
                          )}
                          {paymentMethod === 'paypal' && t('payment.paypal')}
                          {paymentMethod === 'bank_transfer' && t('payment.bankTransfer')}
                        </p>
                      </div>
                    </div>
                  </ScrollStagger>
                </Card>
              </ScrollAnimation>
            )}

            <ScrollAnimation animation="fade" delay={0.4} className="mt-6">
              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={goBack}
                    leftIcon={<ArrowLeft size={16} />}
                    className="w-full sm:w-auto"
                  >
                    {t('checkout.back')}
                  </Button>
                </HoverAnimation>

                <HoverAnimation animation="scale">
                  <Button
                    type="submit"
                    isLoading={isProcessing}
                    className="w-full sm:w-auto"
                    size="lg"
                  >
                    {step === 'review'
                      ? t('checkout.placeOrder')
                      : t('checkout.continue')}
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </form>
        </div>

        {/* ملخص الطلب */}
        <div className="lg:col-span-1">
          <ScrollAnimation animation="fade" delay={0.3}>
            <Card className="p-6 sticky top-24">
              <h2 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">{t('checkout.orderSummary')}</h2>

              <div className="max-h-80 overflow-y-auto mb-4 pr-1">
                <ScrollStagger
                  animation="fade"
                  staggerDelay={0.05}
                  className="space-y-3"
                >
                  {items.map((item) => (
                    <div key={item.id} className="flex py-3 border-b border-slate-200 dark:border-slate-700">
                      <div className="w-16 h-16 flex-shrink-0 relative rounded overflow-hidden">
                        <EnhancedImage
                          src={item.image}
                          alt={item.name}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="blur"
                          className="w-full h-full"
                          containerClassName="w-full h-full"
                        />
                      </div>

                      <div className={`${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} flex-1`}>
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          {currentLanguage === 'ar' ? item.name_ar || item.name : item.name}
                        </h4>
                        <div className="flex justify-between mt-1">
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {t('checkout.quantity')}: {item.quantity}
                          </span>
                          <span className="text-slate-900 dark:text-white">{formatCurrency(item.price * item.quantity, currency)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </ScrollStagger>
              </div>

              <div className="space-y-3 py-4 border-b border-slate-200 dark:border-slate-700">
                <div className="flex justify-between">
                  <span className="text-slate-700 dark:text-slate-300">{t('checkout.subtotal')}</span>
                  <span className="font-medium text-slate-900 dark:text-white">{formatCurrency(subtotal, currency)}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-700 dark:text-slate-300">{t('checkout.shipping')}</span>
                  <span className="text-slate-900 dark:text-white">
                    {getShippingPrice() === 0
                      ? t('checkout.free')
                      : formatCurrency(getShippingPrice(), currency)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-700 dark:text-slate-300">{t('checkout.tax')}</span>
                  <span className="text-slate-900 dark:text-white">{formatCurrency(tax, currency)}</span>
                </div>
              </div>

              <div className="flex justify-between font-semibold text-lg pt-4">
                <span className="text-slate-900 dark:text-white">{t('checkout.total')}</span>
                <span className="text-primary-600 dark:text-primary-400">{formatCurrency(total, currency)}</span>
              </div>
            </Card>
          </ScrollAnimation>
        </div>
      </div>
    </div>
  );
}
