'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ShoppingCart, Heart, Star, ArrowLeft, Share2, Package, Truck, Shield,
  CheckCircle, AlertCircle, XCircle, Plus, Minus, Eye, Compare,
  Award, Clock, Zap, MessageSquare, ThumbsUp, ChevronLeft, ChevronRight
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { products } from '../../data/products';
import { Product } from '../../types/index';
import { ScrollAnimation, HoverAnimation } from '../../components/ui/animations';
import { useAuthenticatedAction } from '../../hooks/useAuthenticatedAction';
import { AuthModal } from '../../components/auth/AuthModal';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';

interface ProductDetailPageProps {
  productSlug: string;
}

const ProductDetailPage: React.FC<ProductDetailPageProps> = ({ productSlug }) => {
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description');
  const [isLoading, setIsLoading] = useState(true);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { theme, resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // Find the product by slug
  const product = products.find(p => p.slug === productSlug);

  // Get related products
  const relatedProducts = product?.relatedProducts
    ? products.filter(p => product.relatedProducts?.includes(p.id)).slice(0, 4)
    : products.filter(p => p.category === product?.category && p.id !== product?.id).slice(0, 4);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, [productSlug]);

  const handleUnauthenticated = () => {
    setShowAuthModal(true);
  };

  const handleAddToCart = useAuthenticatedAction(() => {
    if (product) {
      cartStore.addItem(product, quantity);
      // Show success message (implement toast)
      console.log(`${quantity}x ${product.name} added to cart`);
    }
  }, handleUnauthenticated);

  const handleWholesaleInquiry = useAuthenticatedAction(() => {
    setShowWholesaleForm(true);
  }, handleUnauthenticated);

  const toggleWishlist = useAuthenticatedAction(() => {
    if (product) {
      if (wishlistStore.isInWishlist(product.id)) {
        wishlistStore.removeItem(product.id);
      } else {
        wishlistStore.addItem(product);
      }
    }
  }, handleUnauthenticated);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.stock || 0)) {
      setQuantity(newQuantity);
    }
  };

  const handleShare = async () => {
    if (navigator.share && product) {
      try {
        await navigator.share({
          title: product.name,
          text: product.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (isLoading) {
    return (
      <div className="container-custom py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container-custom py-8">
        <div className="text-center py-12">
          <div className="inline-flex justify-center items-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-800 text-slate-400 dark:text-slate-500 mb-4">
            <Package size={24} />
          </div>
          <h2 className="text-xl font-medium text-slate-900 dark:text-white mb-2">
            {currentLanguage === 'ar' ? 'المنتج غير موجود' : 'Product not found'}
          </h2>
          <p className="text-slate-600 dark:text-slate-300 mb-6">
            {currentLanguage === 'ar'
              ? 'المنتج الذي تبحث عنه غير متوفر أو تم حذفه.'
              : 'The product you\'re looking for is not available or has been removed.'}
          </p>
          <Button
            variant="primary"
            onClick={() => router.push(`/${currentLanguage}/shop`)}
          >
            <ArrowLeft className={`h-4 w-4 ${isRTL ? 'ml-2 rotate-180' : 'mr-2'}`} />
            {currentLanguage === 'ar' ? 'العودة للمتجر' : 'Back to Shop'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      {/* Breadcrumb Navigation */}
      <ScrollAnimation animation="fade" delay={0.1}>
        <nav className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-300 mb-8">
          <Link
            href={`/${currentLanguage}`}
            className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            {currentLanguage === 'ar' ? 'الرئيسية' : 'Home'}
          </Link>
          <span>/</span>
          <Link
            href={`/${currentLanguage}/shop`}
            className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
          >
            {currentLanguage === 'ar' ? 'المتجر' : 'Shop'}
          </Link>
          <span>/</span>
          <span className="text-slate-900 dark:text-white font-medium">
            {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
          </span>
        </nav>
      </ScrollAnimation>

      {/* Back to Shop Button */}
      <ScrollAnimation animation="slide" delay={0.2}>
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(`/${currentLanguage}/shop`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className={`h-4 w-4 ${isRTL ? 'rotate-180' : ''}`} />
            {currentLanguage === 'ar' ? 'العودة للمتجر' : 'Back to Shop'}
          </Button>
        </div>
      </ScrollAnimation>

      {/* Product Detail Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
        {/* Product Images */}
        <ScrollAnimation animation="slide" delay={0.3}>
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-square overflow-hidden rounded-xl bg-slate-100 dark:bg-slate-800">
              <EnhancedImage
                src={product.images[selectedImageIndex] || '/images/product-placeholder-light.svg'}
                alt={product.name}
                fill={true}
                objectFit="cover"
                progressive={true}
                placeholder="shimmer"
                className="rounded-xl"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority={true}
              />

              {/* Product Badges */}
              <div className="absolute top-4 left-4 flex flex-col gap-2">
                {product.featured && (
                  <span className="bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                    <Award className="h-3 w-3" />
                    {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                  </span>
                )}
                {product.compareAtPrice && product.compareAtPrice > product.price && (
                  <span className="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                    {Math.round((1 - product.price / product.compareAtPrice) * 100)}% {currentLanguage === 'ar' ? 'خصم' : 'OFF'}
                  </span>
                )}
              </div>

              {/* Image Navigation */}
              {product.images.length > 1 && (
                <>
                  <button
                    onClick={() => setSelectedImageIndex(prev => prev > 0 ? prev - 1 : product.images.length - 1)}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-slate-800 transition-colors"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => setSelectedImageIndex(prev => prev < product.images.length - 1 ? prev + 1 : 0)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-white dark:hover:bg-slate-800 transition-colors"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}
            </div>

            {/* Thumbnail Images */}
            {product.images.length > 1 && (
              <div className="flex gap-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={cn(
                      "relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors flex-shrink-0",
                      selectedImageIndex === index
                        ? "border-primary-500"
                        : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"
                    )}
                  >
                    <EnhancedImage
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill={true}
                      objectFit="cover"
                      className="rounded-md"
                      sizes="80px"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </ScrollAnimation>

        {/* Product Information */}
        <ScrollAnimation animation="slide" delay={0.4}>
          <div className="space-y-6">
            {/* Product Title and Rating */}
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
              </h1>

              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-5 w-5",
                        i < Math.floor(product.rating || 0)
                          ? "text-yellow-400 fill-current"
                          : "text-slate-300 dark:text-slate-600"
                      )}
                    />
                  ))}
                  <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                    {product.rating?.toFixed(1) || '4.5'} ({product.reviewCount || 0} {currentLanguage === 'ar' ? 'تقييم' : 'reviews'})
                  </span>
                </div>

                {product.category && (
                  <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                    {product.category}
                  </span>
                )}
              </div>
            </div>

            {/* Price */}
            <div className="border-b border-slate-200 dark:border-slate-700 pb-6">
              <div className="flex items-baseline gap-3 mb-2">
                <span className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                  {formatCurrency(product.price)}
                </span>
                {product.compareAtPrice && product.compareAtPrice > product.price && (
                  <>
                    <span className="text-xl text-slate-500 line-through">
                      {formatCurrency(product.compareAtPrice)}
                    </span>
                    <span className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-2 py-1 rounded-full text-sm font-medium">
                      {Math.round((1 - product.price / product.compareAtPrice) * 100)}% {currentLanguage === 'ar' ? 'خصم' : 'OFF'}
                    </span>
                  </>
                )}
              </div>

              <p className="text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar' ? 'شامل ضريبة القيمة المضافة' : 'VAT included'}
              </p>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-3">
              {product.stock > 0 ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    {product.stock > 10
                      ? (currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock')
                      : `${product.stock} ${currentLanguage === 'ar' ? 'متبقي' : 'left'}`
                    }
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-500" />
                  <span className="text-red-600 dark:text-red-400 font-medium">
                    {currentLanguage === 'ar' ? 'نفد المخزون' : 'Out of Stock'}
                  </span>
                </>
              )}
            </div>

            {/* Product Description */}
            <div>
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                {currentLanguage === 'ar'
                  ? (product.description_ar || product.description)
                  : product.description
                }
              </p>
            </div>

            {/* Quantity Selector and Actions */}
            <div className="space-y-4">
              {/* Quantity */}
              <div className="flex items-center gap-4">
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'الكمية:' : 'Quantity:'}
                </label>
                <div className="flex items-center border border-slate-300 dark:border-slate-600 rounded-lg">
                  <button
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                    className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 min-w-[60px] text-center font-medium">
                    {quantity}
                  </span>
                  <button
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= (product.stock || 0)}
                    className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="primary"
                  size="lg"
                  className="flex-1 flex items-center justify-center gap-2"
                  onClick={handleAddToCart}
                  disabled={product.stock <= 0}
                >
                  <ShoppingCart className="h-5 w-5" />
                  {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                </Button>

                <Button
                  variant={user && wishlistStore.isInWishlist(product.id) ? 'primary' : 'outline'}
                  size="lg"
                  onClick={toggleWishlist}
                  className="flex items-center justify-center gap-2"
                >
                  <Heart className={cn(
                    "h-5 w-5",
                    user && wishlistStore.isInWishlist(product.id) && "fill-current"
                  )} />
                  {user && wishlistStore.isInWishlist(product.id)
                    ? (currentLanguage === 'ar' ? 'في المفضلة' : 'In Wishlist')
                    : (currentLanguage === 'ar' ? 'أضف للمفضلة' : 'Add to Wishlist')
                  }
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleShare}
                  className="flex items-center justify-center gap-2"
                >
                  <Share2 className="h-5 w-5" />
                  {currentLanguage === 'ar' ? 'مشاركة' : 'Share'}
                </Button>
              </div>

              {/* Wholesale Quote Button */}
              <Button
                variant="secondary"
                size="lg"
                className="w-full flex items-center justify-center gap-2"
                onClick={handleWholesaleInquiry}
              >
                <Package className="h-5 w-5" />
                {currentLanguage === 'ar' ? 'طلب عرض سعر للجملة' : 'Request Wholesale Quote'}
              </Button>
            </div>

            {/* Product Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                <Truck className="h-5 w-5 text-green-500" />
                <span>{currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                <Shield className="h-5 w-5 text-blue-500" />
                <span>{currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-slate-600 dark:text-slate-300">
                <Clock className="h-5 w-5 text-orange-500" />
                <span>{currentLanguage === 'ar' ? 'دعم 24/7' : '24/7 Support'}</span>
              </div>
            </div>
          </div>
        </ScrollAnimation>
      </div>

      {/* Product Details Tabs */}
      <ScrollAnimation animation="fade" delay={0.5}>
        <Card className="mb-12">
          <div className="border-b border-slate-200 dark:border-slate-700">
            <nav className="flex space-x-8" aria-label="Tabs">
              {[
                { id: 'description', label: currentLanguage === 'ar' ? 'الوصف' : 'Description' },
                { id: 'specifications', label: currentLanguage === 'ar' ? 'المواصفات' : 'Specifications' },
                { id: 'reviews', label: currentLanguage === 'ar' ? 'التقييمات' : 'Reviews' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                    activeTab === tab.id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300 hover:border-slate-300"
                  )}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'description' && (
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                  {currentLanguage === 'ar'
                    ? (product.description_ar || product.description)
                    : product.description
                  }
                </p>

                {/* Additional product features */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-slate-900 dark:text-white mb-3">
                      {currentLanguage === 'ar' ? 'الميزات الرئيسية' : 'Key Features'}
                    </h4>
                    <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                      {product.tags.map((tag, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          {tag}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-slate-900 dark:text-white mb-3">
                      {currentLanguage === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                    </h4>
                    <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                      <li className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        {currentLanguage === 'ar' ? 'شحن مجاني للطلبات فوق 500 ريال' : 'Free shipping on orders over $500'}
                      </li>
                      <li className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-green-500 flex-shrink-0" />
                        {currentLanguage === 'ar' ? 'ضمان لمدة سنتين' : '2-year warranty'}
                      </li>
                      <li className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-orange-500 flex-shrink-0" />
                        {currentLanguage === 'ar' ? 'دعم فني 24/7' : '24/7 technical support'}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                  {currentLanguage === 'ar' ? 'المواصفات التقنية' : 'Technical Specifications'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-slate-100 dark:border-slate-700">
                      <span className="font-medium text-slate-600 dark:text-slate-400">{key}:</span>
                      <span className="text-slate-900 dark:text-white">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                    {currentLanguage === 'ar' ? 'تقييمات العملاء' : 'Customer Reviews'}
                  </h3>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "h-4 w-4",
                            i < Math.floor(product.rating || 0)
                              ? "text-yellow-400 fill-current"
                              : "text-slate-300 dark:text-slate-600"
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-slate-600 dark:text-slate-400">
                      {product.rating?.toFixed(1) || '4.5'} ({product.reviewCount || 0} {currentLanguage === 'ar' ? 'تقييم' : 'reviews'})
                    </span>
                  </div>
                </div>

                {/* Sample Reviews */}
                <div className="space-y-6">
                  {[
                    {
                      name: currentLanguage === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed',
                      rating: 5,
                      date: '2024-01-15',
                      comment: currentLanguage === 'ar'
                        ? 'منتج ممتاز وجودة عالية. أنصح بشرائه بشدة.'
                        : 'Excellent product with high quality. Highly recommend!'
                    },
                    {
                      name: currentLanguage === 'ar' ? 'سارة أحمد' : 'Sarah Ahmed',
                      rating: 4,
                      date: '2024-01-10',
                      comment: currentLanguage === 'ar'
                        ? 'منتج جيد جداً ولكن التسليم كان متأخراً قليلاً.'
                        : 'Very good product but delivery was slightly delayed.'
                    }
                  ].map((review, index) => (
                    <div key={index} className="border-b border-slate-100 dark:border-slate-700 pb-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                              {review.name.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-slate-900 dark:text-white">{review.name}</p>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={cn(
                                      "h-3 w-3",
                                      i < review.rating
                                        ? "text-yellow-400 fill-current"
                                        : "text-slate-300 dark:text-slate-600"
                                    )}
                                  />
                                ))}
                              </div>
                              <span className="text-xs text-slate-500 dark:text-slate-400">
                                {new Date(review.date).toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <p className="text-slate-600 dark:text-slate-300">{review.comment}</p>
                    </div>
                  ))}
                </div>

                <div className="mt-6 text-center">
                  <Button variant="outline">
                    {currentLanguage === 'ar' ? 'عرض جميع التقييمات' : 'View All Reviews'}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      </ScrollAnimation>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <ScrollAnimation animation="fade" delay={0.6}>
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
              {currentLanguage === 'ar' ? 'منتجات ذات صلة' : 'Related Products'}
            </h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <HoverAnimation key={relatedProduct.id} animation="scale">
                  <Link
                    href={`/${currentLanguage}/shop/${relatedProduct.slug}`}
                    className="group bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden flex flex-col h-full"
                  >
                    {/* Product Image */}
                    <div className="relative aspect-square overflow-hidden">
                      <EnhancedImage
                        src={relatedProduct.images[0] || '/images/product-placeholder-light.svg'}
                        alt={relatedProduct.name}
                        fill={true}
                        objectFit="cover"
                        progressive={true}
                        placeholder="shimmer"
                        className="transition-transform duration-500 group-hover:scale-105"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                      />

                      {/* Product Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-1">
                        {relatedProduct.featured && (
                          <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                          </span>
                        )}
                        {relatedProduct.compareAtPrice && relatedProduct.compareAtPrice > relatedProduct.price && (
                          <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {Math.round((1 - relatedProduct.price / relatedProduct.compareAtPrice) * 100)}% {currentLanguage === 'ar' ? 'خصم' : 'OFF'}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-4 flex flex-col flex-grow">
                      <h3 className="font-semibold text-slate-900 dark:text-white line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors mb-2">
                        {currentLanguage === 'ar' ? (relatedProduct.name_ar || relatedProduct.name) : relatedProduct.name}
                      </h3>

                      <div className="flex items-center justify-between mt-auto">
                        <div className="flex items-baseline gap-2">
                          <span className="font-bold text-primary-600 dark:text-primary-400">
                            {formatCurrency(relatedProduct.price)}
                          </span>
                          {relatedProduct.compareAtPrice && relatedProduct.compareAtPrice > relatedProduct.price && (
                            <span className="text-sm text-slate-500 line-through">
                              {formatCurrency(relatedProduct.compareAtPrice)}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center text-sm text-yellow-500">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="ml-1 text-slate-600 dark:text-slate-300">
                            {relatedProduct.rating?.toFixed(1) || '4.5'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </HoverAnimation>
              ))}
            </div>
          </div>
        </ScrollAnimation>
      )}

      {/* Modals */}
      {showAuthModal && (
        <AuthModal onClose={() => setShowAuthModal(false)} />
      )}

      {showWholesaleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <WholesaleQuoteForm
                product={product}
                onClose={() => setShowWholesaleForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
};

export default ProductDetailPage;