'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ArrowLeft, ShoppingCart, Heart, Star, Truck, Shield, Clock, ChevronRight } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { products } from '../../data/products';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useAuthModalStore } from '../../stores/authModalStore';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { SEO } from '../../components/seo/SEO';
import { ProductSchema } from '../../components/seo/ProductSchema';
import { BreadcrumbSchema } from '../../components/seo/BreadcrumbSchema';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function ProductPage({ slug }: { slug: string }) {
  const product = products.find(p => p.slug === slug);
  const pathname = usePathname();

  const [selectedImage, setSelectedImage] = useState(0);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { openModal } = useAuthModalStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // إنشاء عناصر مسار التنقل للمخطط
  const breadcrumbItems = [
    {
      name: currentLanguage === 'ar' ? 'الرئيسية' : 'Home',
      url: `/${currentLanguage}`
    },
    {
      name: currentLanguage === 'ar' ? 'المتجر' : 'Shop',
      url: `/${currentLanguage}/shop`
    }
  ];

  // إضافة المنتج الحالي إلى مسار التنقل إذا كان موجودًا
  if (product) {
    breadcrumbItems.push({
      name: currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name,
      url: `/${currentLanguage}/shop/product/${product.slug}`
    });
  }

  if (!product) {
    return (
      <div className="container-custom py-12">
        <Card className="p-8 text-center">
          <h1 className="text-2xl font-semibold mb-4 dark:text-white">
            {currentLanguage === 'ar' ? 'المنتج غير موجود' : 'Product Not Found'}
          </h1>
          <p className="text-slate-600 dark:text-slate-300 mb-6">
            {currentLanguage === 'ar' ? 'المنتج الذي تبحث عنه غير موجود.' : 'The product you\'re looking for doesn\'t exist.'}
          </p>
          <Link
            href={`/${currentLanguage}/shop`}
            className="inline-flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {currentLanguage === 'ar' ? 'العودة إلى المتجر' : 'Back to Shop'}
          </Link>
        </Card>
      </div>
    );
  }

  const handleAddToCart = () => {
    // يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول
    const cart = useCartStore.getState();
    cart.addItem(product, 1);
  };

  const toggleWishlist = () => {
    if (!user) {
      openModal('sign-in');
      return;
    }

    const wishlist = useWishlistStore.getState();

    if (wishlist.isInWishlist(product.id)) {
      wishlist.removeItem(product.id);
    } else {
      wishlist.addItem(product);
    }
  };

  // إنشاء بيانات SEO للمنتج
  const productSeoTitle = currentLanguage === 'ar'
    ? `${product.name_ar || product.name} | كوميرس برو`
    : `${product.name} | CommercePro`;

  const productSeoDescription = currentLanguage === 'ar'
    ? `اشترِ ${product.name_ar || product.name} - ${product.description_ar || product.description.substring(0, 150)}...`
    : `Buy ${product.name} - ${product.description.substring(0, 150)}...`;

  const productUrl = typeof window !== 'undefined'
    ? window.location.href
    : `https://commercepro.com/${currentLanguage}/shop/product/${product.slug}`;

  return (
    <>
      {/* SEO Component */}
      <SEO
        title={productSeoTitle}
        description={productSeoDescription}
        keywords={product.tags.join(', ')}
        ogImage={product.images[0]}
        ogType="product"
        canonicalUrl={productUrl}
      />

      {/* Schema.org Product Data */}
      <ProductSchema product={product} url={productUrl} />

      {/* Schema.org Breadcrumb Data */}
      <BreadcrumbSchema items={breadcrumbItems} />

      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.1} className="mb-8">
          <HoverAnimation animation="scale">
            <Link
              href={`/${currentLanguage}/shop`}
              className="inline-flex items-center text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400"
            >
              <ArrowLeft className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
              {currentLanguage === 'ar' ? 'العودة إلى المتجر' : 'Back to Shop'}
            </Link>
          </HoverAnimation>
        </ScrollAnimation>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="space-y-4">
            <div className="aspect-square overflow-hidden rounded-lg bg-slate-100 dark:bg-slate-800">
              <EnhancedImage
                src={product.images[selectedImage]}
                alt={product.name}
                fill={true}
                objectFit="cover"
                effect="fade"
                progressive={true}
                placeholder="shimmer"
                className="w-full h-full"
                containerClassName="w-full h-full"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
            </div>
            <ScrollStagger
              animation="fade"
              staggerDelay={0.1}
              className="grid grid-cols-4 gap-4"
            >
              {product.images.map((image, index) => (
                <HoverAnimation key={index} animation="scale">
                  <button
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-square rounded-lg overflow-hidden border-2 ${
                      selectedImage === index
                        ? 'border-primary-500'
                        : 'border-transparent'
                    }`}
                  >
                    <EnhancedImage
                      src={image}
                      alt={`${product.name} view ${index + 1}`}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="blur"
                      className="w-full h-full"
                      containerClassName="w-full h-full"
                    />
                  </button>
                </HoverAnimation>
              ))}
            </ScrollStagger>
          </div>
        </ScrollAnimation>

        {/* Product Info */}
        <ScrollAnimation animation="fade" delay={0.3}>
          <div>
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full">
                {currentLanguage === 'ar' ? 'وصل حديثاً' : 'New Arrival'}
              </span>
              <HoverAnimation animation="scale">
                <button
                  onClick={toggleWishlist}
                  className={`p-2 rounded-full ${
                    user && useWishlistStore.getState().isInWishlist(product.id)
                      ? 'bg-primary-500 text-white'
                      : isDarkMode
                        ? 'bg-slate-800 text-slate-300'
                        : 'bg-slate-100 text-slate-500'
                  } transition-transform`}
                >
                  <Heart
                    size={20}
                    className={user && useWishlistStore.getState().isInWishlist(product.id) ? 'fill-current' : ''}
                  />
                </button>
              </HoverAnimation>
            </div>

            <h1 className="text-3xl font-bold mb-2 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? product.name_ar || product.name : product.name}
            </h1>

            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    size={20}
                    className={i < 4 ? 'text-yellow-400 fill-current' : 'text-slate-300 dark:text-slate-600'}
                  />
                ))}
              </div>
              <span className="text-slate-600 dark:text-slate-400">
                {currentLanguage === 'ar' ? '12 تقييم' : '12 reviews'}
              </span>
            </div>

            <div className="mb-6">
              <div className="flex items-baseline gap-2 mb-2">
                <span className="text-3xl font-bold text-slate-900 dark:text-white">{formatCurrency(product.price)}</span>
                {product.compareAtPrice && (
                  <span className="text-lg text-slate-500 dark:text-slate-400 line-through">
                    {formatCurrency(product.compareAtPrice)}
                  </span>
                )}
              </div>
              {product.compareAtPrice && (
                <span className="text-success-600 dark:text-success-500 font-medium">
                  {currentLanguage === 'ar'
                    ? `وفر ${Math.round((1 - product.price / product.compareAtPrice) * 100)}%`
                    : `Save ${Math.round((1 - product.price / product.compareAtPrice) * 100)}%`}
                </span>
              )}
            </div>

            <div className="prose prose-slate dark:prose-invert mb-6">
              <p className="text-lg text-slate-700 dark:text-slate-300">
                {currentLanguage === 'ar' ? product.description_ar || product.description : product.description}
              </p>
            </div>

            <ScrollStagger
              animation="fade"
              staggerDelay={0.1}
              className="space-y-4 mb-8"
            >
              <div className={`flex items-center ${currentLanguage === 'ar' ? 'flex-row-reverse' : ''} gap-2 text-slate-600 dark:text-slate-400`}>
                <Truck size={20} className="text-slate-600 dark:text-slate-400" />
                <span>
                  {currentLanguage === 'ar'
                    ? 'شحن مجاني للطلبات التي تزيد عن 5000 دولار'
                    : 'Free shipping on orders over $5000'}
                </span>
              </div>
              <div className={`flex items-center ${currentLanguage === 'ar' ? 'flex-row-reverse' : ''} gap-2 text-slate-600 dark:text-slate-400`}>
                <Shield size={20} className="text-slate-600 dark:text-slate-400" />
                <span>
                  {currentLanguage === 'ar'
                    ? 'ضمان لمدة سنتين مشمول'
                    : '2-year warranty included'}
                </span>
              </div>
              <div className={`flex items-center ${currentLanguage === 'ar' ? 'flex-row-reverse' : ''} gap-2 text-slate-600 dark:text-slate-400`}>
                <Clock size={20} className="text-slate-600 dark:text-slate-400" />
                <span>
                  {currentLanguage === 'ar'
                    ? 'الشحن خلال 5-7 أيام عمل'
                    : 'Ships within 5-7 business days'}
                </span>
              </div>
            </ScrollStagger>

            <div className="space-y-4">
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  className="w-full flex items-center justify-center"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                  {currentLanguage === 'ar' ? 'أضف إلى السلة' : 'Add to Cart'}
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={() => setShowWholesaleForm(true)}
                >
                  {currentLanguage === 'ar' ? 'طلب سعر جملة لهذا المنتج' : 'Request Wholesale Quote for this Product'}
                </Button>
              </HoverAnimation>
            </div>
          </div>
        </ScrollAnimation>
      </div>

      {/* Technical Specifications */}
      <ScrollAnimation animation="fade" delay={0.4} className="mt-16">
        <h2 className="text-2xl font-bold mb-8 text-slate-900 dark:text-white">
          {currentLanguage === 'ar' ? 'المواصفات الفنية' : 'Technical Specifications'}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <HoverAnimation animation="lift">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'المميزات' : 'Features'}
              </h3>
              <ScrollStagger
                animation="fade"
                staggerDelay={0.05}
                className="space-y-3"
              >
                {product.tags.map((tag, index) => (
                  <li key={index} className={`flex items-center ${currentLanguage === 'ar' ? 'flex-row-reverse' : ''} gap-2`}>
                    <ChevronRight size={16} className="text-primary-500 dark:text-primary-400" />
                    <span className="text-slate-700 dark:text-slate-300">{tag}</span>
                  </li>
                ))}
              </ScrollStagger>
            </Card>
          </HoverAnimation>
          <HoverAnimation animation="lift">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'المواصفات' : 'Specifications'}
              </h3>
              <ScrollStagger
                animation="fade"
                staggerDelay={0.05}
                className="space-y-3"
              >
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className={`flex ${currentLanguage === 'ar' ? 'flex-row-reverse' : ''} justify-between py-2 border-b dark:border-slate-700`}>
                    <span className="text-slate-600 dark:text-slate-400">{key}</span>
                    <span className="font-medium text-slate-900 dark:text-white">{value}</span>
                  </div>
                ))}
              </ScrollStagger>
            </Card>
          </HoverAnimation>
        </div>
      </ScrollAnimation>

      {/* Related Products */}
      <ScrollAnimation animation="fade" delay={0.5} className="mt-16">
        <h2 className="text-2xl font-bold mb-8 text-slate-900 dark:text-white">
          {currentLanguage === 'ar' ? 'منتجات ذات صلة' : 'Related Products'}
        </h2>
        <ScrollStagger
          animation="slide"
          direction="up"
          staggerDelay={0.1}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {products
            .filter(p => p.category === product.category && p.id !== product.id)
            .slice(0, 4)
            .map(relatedProduct => (
              <HoverAnimation key={relatedProduct.id} animation="lift">
                <Card className="group">
                  <Link href={`/${currentLanguage}/shop/product/${relatedProduct.slug}`}>
                    <div className="aspect-square overflow-hidden">
                      <EnhancedImage
                        src={relatedProduct.images[0]}
                        alt={relatedProduct.name}
                        fill={true}
                        objectFit="cover"
                        effect="zoom"
                        progressive={true}
                        placeholder="shimmer"
                        className="relative w-full h-full"
                        containerClassName="w-full h-full"
                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium text-slate-900 dark:text-white group-hover:text-primary-500 dark:group-hover:text-primary-400 transition-colors">
                        {currentLanguage === 'ar' ? relatedProduct.name_ar || relatedProduct.name : relatedProduct.name}
                      </h3>
                      <p className="text-lg font-bold mt-2 text-slate-900 dark:text-white">
                        {formatCurrency(relatedProduct.price)}
                      </p>
                    </div>
                  </Link>
                </Card>
              </HoverAnimation>
            ))}
        </ScrollStagger>
      </ScrollAnimation>

      {/* Auth Modal is now handled by AuthModalProvider */}

      {showWholesaleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <WholesaleQuoteForm
              onClose={() => setShowWholesaleForm(false)}
              isCustomProduct={false}
              product={product}
            />
          </div>
        </div>
      )}
    </div>
    </>
  );
}