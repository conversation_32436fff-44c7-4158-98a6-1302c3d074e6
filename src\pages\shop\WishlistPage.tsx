'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Heart, ShoppingCart, Trash2, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import { useAuthModalStore } from '../../stores/authModalStore';
import { useTranslation } from '../../translations';
import { formatCurrency } from '../../lib/utils';
import { useCurrencyStore } from '../../stores/currencyStore';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

export default function WishlistPage() {
  const router = useRouter();
  const { items, removeItem, clearWishlist } = useWishlistStore();
  const { addItem, isProductInCart } = useCartStore();
  const { user, isAuthenticated } = useAuthStore();
  const { t, locale } = useTranslation();
  const { currency } = useCurrencyStore();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { openModal } = useAuthModalStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // التحقق من حالة المصادقة
  useEffect(() => {
    if (!isAuthenticated) {
      openModal('sign-in');
    }
  }, [isAuthenticated, openModal]);

  // إضافة منتج إلى السلة
  const handleAddToCart = (product: any) => {
    if (!user) {
      openModal('sign-in');
      return;
    }
    addItem(product, 1);
  };

  // إزالة منتج من المفضلة
  const handleRemoveFromWishlist = (id: string) => {
    removeItem(id);
  };

  // إضافة جميع المنتجات إلى السلة
  const addAllToCart = () => {
    if (!user) {
      openModal('sign-in');
      return;
    }
    items.forEach(item => {
      if (!isProductInCart(item.id)) {
        addItem(item, 1);
      }
    });
    router.push(`/${currentLanguage}/cart`);
  };

  return (
    <div className="container-custom py-12">
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            {t('wishlist.title')}
          </h1>
          {items.length > 0 && (
            <div className="flex flex-wrap gap-3">
              <HoverAnimation animation="scale">
                <Button
                  variant="outline"
                  onClick={clearWishlist}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  {t('wishlist.clearAll')}
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button
                  onClick={addAllToCart}
                  className="flex items-center gap-2"
                >
                  <ShoppingCart className="h-4 w-4" />
                  {t('wishlist.addAllToCart')}
                </Button>
              </HoverAnimation>
            </div>
          )}
        </div>
      </ScrollAnimation>

      {/* Auth Modal is now handled by AuthModalProvider */}

      {items.length === 0 ? (
        <ScrollAnimation animation="fade" delay={0.2}>
          <div className="text-center py-16">
            <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-slate-100 dark:bg-slate-800 mb-4">
              <Heart className="h-10 w-10 text-slate-400 dark:text-slate-500" />
            </div>
            <h2 className="text-2xl font-semibold mb-3 text-slate-900 dark:text-white">
              {t('wishlist.empty')}
            </h2>
            <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6">
              {t('wishlist.emptyMessage')}
            </p>
            <HoverAnimation animation="scale">
              <Button
                onClick={() => router.push(`/${currentLanguage}/shop`)}
                size="lg"
                className="px-6"
              >
                {t('wishlist.continueShopping')}
              </Button>
            </HoverAnimation>
          </div>
        </ScrollAnimation>
      ) : (
        <ScrollStagger
          animation="slide"
          direction="up"
          staggerDelay={0.1}
          delay={0.3}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {items.map((item) => (
            <HoverAnimation key={item.id} animation="lift">
              <Card className="overflow-hidden h-full flex flex-col">
                <div className="relative">
                  <EnhancedImage
                    src={item.images[0]}
                    alt={item.name}
                    fill={true}
                    objectFit="cover"
                    effect="zoom"
                    progressive={true}
                    placeholder="shimmer"
                    className="w-full h-48"
                    containerClassName="w-full h-48"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                  {item.discount > 0 && (
                    <div className={cn(
                      "absolute top-3 left-3 px-2 py-1 rounded text-xs font-semibold z-10",
                      isDarkMode ? "bg-primary-600 text-white" : "bg-primary-500 text-white"
                    )}>
                      {t('products.discount', { percent: item.discount })}
                    </div>
                  )}
                  {!item.inStock && (
                    <div className={cn(
                      "absolute top-3 right-3 px-2 py-1 rounded text-xs font-semibold z-10",
                      isDarkMode ? "bg-red-600 text-white" : "bg-red-500 text-white"
                    )}>
                      {t('products.outOfStock')}
                    </div>
                  )}
                </div>
                <div className="p-5 flex-1 flex flex-col">
                  <h3 className="text-lg font-semibold mb-2 text-slate-900 dark:text-white">
                    {currentLanguage === 'ar' ? item.name_ar || item.name : item.name}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 text-sm mb-4 line-clamp-2 flex-grow">
                    {currentLanguage === 'ar' ? item.description_ar || item.description : item.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-semibold text-slate-900 dark:text-white">
                        {formatCurrency(item.price, currency)}
                      </p>
                      {item.oldPrice > 0 && (
                        <p className="text-sm text-slate-500 dark:text-slate-400 line-through">
                          {formatCurrency(item.oldPrice, currency)}
                        </p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <HoverAnimation animation="scale">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveFromWishlist(item.id)}
                          className={cn(
                            "p-2",
                            isDarkMode ? "text-red-400 hover:text-red-300" : "text-red-500 hover:text-red-700"
                          )}
                          aria-label={t('wishlist.remove')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </HoverAnimation>
                      <HoverAnimation animation="scale">
                        <Button
                          size="sm"
                          onClick={() => handleAddToCart(item)}
                          disabled={!item.inStock || isProductInCart(item.id)}
                          className="p-2"
                          aria-label={t('wishlist.addToCart')}
                        >
                          <ShoppingCart className="h-4 w-4" />
                        </Button>
                      </HoverAnimation>
                    </div>
                  </div>
                  {isProductInCart(item.id) && (
                    <div className={cn(
                      "mt-2 text-xs flex items-center",
                      isDarkMode ? "text-green-400" : "text-green-600"
                    )}>
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {t('wishlist.alreadyInCart')}
                    </div>
                  )}
                </div>
              </Card>
            </HoverAnimation>
          ))}
        </ScrollStagger>
      )}
    </div>
  );
}
