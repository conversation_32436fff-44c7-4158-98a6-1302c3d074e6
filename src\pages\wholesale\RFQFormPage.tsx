import { useState } from 'react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card } from '../../components/ui/Card';
import { Send } from 'lucide-react';

interface RFQFormData {
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  productDetails: string;
  quantity: string;
  targetPrice: string;
  additionalNotes: string;
}

export default function RFQFormPage() {
  const [formData, setFormData] = useState<RFQFormData>({
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    productDetails: '',
    quantity: '',
    targetPrice: '',
    additionalNotes: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement form submission logic
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">Request for Quotation</h1>
      
      <Card className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Company Name</label>
              <Input
                type="text"
                name="companyName"
                value={formData.companyName}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Contact Name</label>
              <Input
                type="text"
                name="contactName"
                value={formData.contactName}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Phone</label>
              <Input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Product Details</label>
            <textarea
              name="productDetails"
              value={formData.productDetails}
              onChange={handleChange}
              required
              className="w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Please describe the product specifications, requirements, and any special considerations"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Quantity</label>
              <Input
                type="text"
                name="quantity"
                value={formData.quantity}
                onChange={handleChange}
                required
                placeholder="e.g., 1000 units"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Target Price (Optional)</label>
              <Input
                type="text"
                name="targetPrice"
                value={formData.targetPrice}
                onChange={handleChange}
                placeholder="Your desired price point"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Additional Notes</label>
            <textarea
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleChange}
              className="w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Any other information you'd like to share"
            />
          </div>

          <div className="flex justify-end">
            <Button type="submit" className="flex items-center gap-2">
              <Send className="w-4 h-4" />
              Submit RFQ
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}