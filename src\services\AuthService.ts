import { sqlite } from '../lib/sqlite';
import {
  getLocalUsers,
  saveLocalUsers,
  getLocalProfiles,
  saveLocalProfiles,
  createDefaultAdminUser as createAdmin,
  resetLocalUsers,
  findUserByCredentials,
  findProfileById,
  findProfileByEmail,
  saveProfile
} from './SQLiteUserService';

// إنشاء مستخدم مدير افتراضي للتطوير المحلي
export async function createDefaultAdminUser() {
  console.log('Creating default admin user using LocalUserService...');

  // استخدام الدالة من LocalUserService
  const result = createAdmin();

  console.log('Default admin user creation result:', result);
  return result;
}

// إنشاء مستخدم مدير ثانٍ
export async function createSecondAdminUser() {
  if (typeof window === 'undefined') {
    return; // تنفيذ فقط في جانب العميل
  }

  console.log('Creating second admin user...');

  const users = getLocalUsers();
  const profiles = getLocalProfiles();

  // التحقق من وجود مستخدم مدير ثانٍ
  const managerUser = users.find((u: any) => u.email === '<EMAIL>');

  if (!managerUser) {
    console.log('Second admin user not found, creating...');

    // إنشاء معرف فريد للمستخدم المدير الثاني
    const managerUserId = 'manager-user-id';

    // إنشاء مستخدم مدير ثانٍ
    const secondAdminUser = {
      id: managerUserId,
      email: '<EMAIL>',
      password: 'password', // في الإنتاج، يجب تشفير كلمات المرور
    };

    // إنشاء ملف المستخدم المدير الثاني
    const secondAdminProfile = {
      id: managerUserId,
      email: '<EMAIL>',
      firstName: 'خالد',
      lastName: 'المدير',
      name: 'خالد المدير',
      role: 'admin',
      status: 'active',
      createdAt: new Date().toISOString(),
      lastLogin: null,
      phone: '+966500000001',
      address: {
        street: 'شارع العليا',
        city: 'الرياض',
        state: 'الرياض',
        zip: '12345',
        country: 'المملكة العربية السعودية'
      }
    };

    // إضافة المستخدم المدير الثاني إلى المستخدمين المحليين
    saveLocalUsers([...users, secondAdminUser]);
    saveLocalProfiles([...profiles, secondAdminProfile]);

    console.log('Second admin user created:', secondAdminUser.email);
    return true;
  } else {
    console.log('Second admin user already exists:', managerUser.email);
    return false;
  }
}

// تهيئة المستخدمين الافتراضيين
export async function initializeDefaultUsers() {
  if (typeof window === 'undefined') {
    return; // تنفيذ فقط في جانب العميل
  }

  try {
    await createDefaultAdminUser();
    await createSecondAdminUser();
    console.log('Default users initialized');
    return { success: true };
  } catch (error) {
    console.error('Initialize default users exception:', error);
    return { success: false, error: 'حدث خطأ غير متوقع أثناء تهيئة المستخدمين الافتراضيين' };
  }
}
