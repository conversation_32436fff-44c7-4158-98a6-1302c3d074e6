/**
 * خدمة إدارة منشورات المدونة باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { BlogPost } from '../types';
import { blogPosts as initialBlogPosts } from '../data/blogPosts';

// مفتاح التخزين المحلي
const LOCAL_BLOG_POSTS_KEY = 'local-blog-posts';

/**
 * الحصول على جميع منشورات المدونة
 */
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    // محاولة الحصول على منشورات المدونة من SQLite
    const blogPosts = sqliteDB.getBlogPosts();
    
    // إذا لم تكن هناك منشورات، قم بتهيئة المنشورات الافتراضية
    if (blogPosts.length === 0) {
      await initializeDefaultBlogPosts();
      return sqliteDB.getBlogPosts();
    }
    
    return blogPosts;
  } catch (error) {
    console.error('Error getting blog posts:', error);
    return [];
  }
}

/**
 * الحصول على منشور مدونة بواسطة المعرف
 */
export async function getBlogPostById(id: string): Promise<BlogPost | null> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    return blogPosts.find(p => p.id === id) || null;
  } catch (error) {
    console.error(`Error getting blog post by ID ${id}:`, error);
    return null;
  }
}

/**
 * الحصول على منشور مدونة بواسطة الرابط
 */
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    return blogPosts.find(p => p.slug === slug) || null;
  } catch (error) {
    console.error(`Error getting blog post by slug ${slug}:`, error);
    return null;
  }
}

/**
 * إنشاء منشور مدونة جديد
 */
export async function createBlogPost(postData: Partial<BlogPost>): Promise<BlogPost> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    
    // إنشاء معرف فريد للمنشور الجديد
    const id = postData.id || `post-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء المنشور الجديد
    const newPost: BlogPost = {
      id,
      title: postData.title || '',
      slug: postData.slug || `post-${id}`,
      excerpt: postData.excerpt || '',
      content: postData.content || '',
      author: postData.author || '',
      authorTitle: postData.authorTitle || '',
      authorImage: postData.authorImage || '',
      coverImage: postData.coverImage || '',
      category: postData.category || '',
      tags: postData.tags || [],
      publishedAt: postData.publishedAt || now,
      readTime: postData.readTime || '5 min'
    };
    
    // إضافة المنشور الجديد إلى المنشورات
    sqliteDB.saveBlogPosts([...blogPosts, newPost]);
    
    return newPost;
  } catch (error) {
    console.error('Error creating blog post:', error);
    throw new Error('فشل إنشاء منشور المدونة');
  }
}

/**
 * تحديث منشور مدونة
 */
export async function updateBlogPost(id: string, postData: Partial<BlogPost>): Promise<BlogPost | null> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    const index = blogPosts.findIndex(p => p.id === id);
    
    if (index === -1) {
      return null;
    }
    
    // تحديث المنشور
    const updatedPost: BlogPost = {
      ...blogPosts[index],
      ...postData
    };
    
    // حفظ المنشورات المحدثة
    blogPosts[index] = updatedPost;
    sqliteDB.saveBlogPosts(blogPosts);
    
    return updatedPost;
  } catch (error) {
    console.error(`Error updating blog post ${id}:`, error);
    return null;
  }
}

/**
 * حذف منشور مدونة
 */
export async function deleteBlogPost(id: string): Promise<boolean> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    const filteredPosts = blogPosts.filter(p => p.id !== id);
    
    if (filteredPosts.length === blogPosts.length) {
      return false;
    }
    
    sqliteDB.saveBlogPosts(filteredPosts);
    return true;
  } catch (error) {
    console.error(`Error deleting blog post ${id}:`, error);
    return false;
  }
}

/**
 * البحث عن منشورات المدونة
 */
export async function searchBlogPosts(query: string): Promise<BlogPost[]> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    
    if (!query) {
      return blogPosts;
    }
    
    const lowerQuery = query.toLowerCase();
    
    return blogPosts.filter(p => 
      p.title.toLowerCase().includes(lowerQuery) ||
      p.excerpt.toLowerCase().includes(lowerQuery) ||
      p.content.toLowerCase().includes(lowerQuery) ||
      p.category.toLowerCase().includes(lowerQuery) ||
      p.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  } catch (error) {
    console.error(`Error searching blog posts for "${query}":`, error);
    return [];
  }
}

/**
 * تصفية منشورات المدونة حسب الفئة
 */
export async function filterBlogPostsByCategory(category: string): Promise<BlogPost[]> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    
    if (!category) {
      return blogPosts;
    }
    
    return blogPosts.filter(p => p.category === category);
  } catch (error) {
    console.error(`Error filtering blog posts by category "${category}":`, error);
    return [];
  }
}

/**
 * الحصول على أحدث منشورات المدونة
 */
export async function getLatestBlogPosts(limit: number = 5): Promise<BlogPost[]> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    
    // ترتيب المنشورات حسب تاريخ النشر (من الأحدث إلى الأقدم)
    return blogPosts
      .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
      .slice(0, limit);
  } catch (error) {
    console.error(`Error getting latest blog posts:`, error);
    return [];
  }
}

/**
 * تهيئة منشورات المدونة الافتراضية
 */
export async function initializeDefaultBlogPosts(): Promise<void> {
  try {
    const blogPosts = sqliteDB.getBlogPosts();
    
    if (blogPosts.length === 0) {
      console.log('Initializing default blog posts...');
      sqliteDB.saveBlogPosts(initialBlogPosts);
    }
  } catch (error) {
    console.error('Error initializing default blog posts:', error);
  }
}

// تهيئة منشورات المدونة الافتراضية عند تحميل الخدمة
if (typeof window !== 'undefined') {
  initializeDefaultBlogPosts();
}
