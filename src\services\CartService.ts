/**
 * خدمة إدارة سلة التسوق باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { Product } from '../types';

// مفتاح التخزين المحلي
const LOCAL_CART_KEY = 'local-cart';

// نوع عنصر السلة
interface CartItem {
  id: string;
  userId: string;
  productId: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
}

// نوع عنصر السلة مع تفاصيل المنتج
interface CartItemWithProduct extends CartItem {
  product: Product;
}

/**
 * الحصول على عناصر سلة التسوق للمستخدم
 */
export async function getCartItems(userId: string): Promise<CartItemWithProduct[]> {
  try {
    // محاولة الحصول على عناصر السلة من localStorage
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // تصفية العناصر حسب المستخدم
    const userCartItems = cartItems.filter(item => item.userId === userId);
    
    // الحصول على المنتجات
    const products = sqliteDB.getProducts();
    
    // إضافة تفاصيل المنتج إلى كل عنصر
    const cartItemsWithProducts: CartItemWithProduct[] = userCartItems.map(item => {
      const product = products.find(p => p.id.toString() === item.productId);
      
      if (!product) {
        // إذا لم يتم العثور على المنتج، قم بإزالة العنصر من السلة
        removeCartItem(userId, item.productId);
        return null;
      }
      
      return {
        ...item,
        product
      };
    }).filter(Boolean) as CartItemWithProduct[];
    
    return cartItemsWithProducts;
  } catch (error) {
    console.error(`Error getting cart items for user ${userId}:`, error);
    return [];
  }
}

/**
 * إضافة منتج إلى سلة التسوق
 */
export async function addToCart(userId: string, productId: string, quantity: number = 1): Promise<CartItem | null> {
  try {
    // التحقق من وجود المنتج
    const products = sqliteDB.getProducts();
    const product = products.find(p => p.id.toString() === productId);
    
    if (!product) {
      throw new Error('المنتج غير موجود');
    }
    
    // التحقق من توفر المنتج
    if (!product.inStock) {
      throw new Error('المنتج غير متوفر حاليًا');
    }
    
    // الحصول على عناصر السلة
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // البحث عن العنصر في السلة
    const existingItemIndex = cartItems.findIndex(item => 
      item.userId === userId && item.productId === productId
    );
    
    const now = new Date().toISOString();
    
    if (existingItemIndex !== -1) {
      // تحديث الكمية إذا كان العنصر موجودًا بالفعل
      cartItems[existingItemIndex] = {
        ...cartItems[existingItemIndex],
        quantity: cartItems[existingItemIndex].quantity + quantity,
        updatedAt: now
      };
    } else {
      // إضافة عنصر جديد إذا لم يكن موجودًا
      const newItem: CartItem = {
        id: `cart-item-${Date.now()}`,
        userId,
        productId,
        quantity,
        createdAt: now,
        updatedAt: now
      };
      
      cartItems.push(newItem);
    }
    
    // حفظ عناصر السلة
    localStorage.setItem(LOCAL_CART_KEY, JSON.stringify(cartItems));
    
    // إرجاع العنصر المضاف أو المحدث
    return existingItemIndex !== -1 ? cartItems[existingItemIndex] : cartItems[cartItems.length - 1];
  } catch (error) {
    console.error(`Error adding product ${productId} to cart for user ${userId}:`, error);
    throw error;
  }
}

/**
 * تحديث كمية منتج في سلة التسوق
 */
export async function updateCartItemQuantity(userId: string, productId: string, quantity: number): Promise<CartItem | null> {
  try {
    if (quantity <= 0) {
      // إذا كانت الكمية صفر أو أقل، قم بإزالة العنصر من السلة
      return removeCartItem(userId, productId);
    }
    
    // الحصول على عناصر السلة
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // البحث عن العنصر في السلة
    const existingItemIndex = cartItems.findIndex(item => 
      item.userId === userId && item.productId === productId
    );
    
    if (existingItemIndex === -1) {
      throw new Error('العنصر غير موجود في السلة');
    }
    
    const now = new Date().toISOString();
    
    // تحديث الكمية
    cartItems[existingItemIndex] = {
      ...cartItems[existingItemIndex],
      quantity,
      updatedAt: now
    };
    
    // حفظ عناصر السلة
    localStorage.setItem(LOCAL_CART_KEY, JSON.stringify(cartItems));
    
    return cartItems[existingItemIndex];
  } catch (error) {
    console.error(`Error updating quantity for product ${productId} in cart for user ${userId}:`, error);
    throw error;
  }
}

/**
 * إزالة منتج من سلة التسوق
 */
export async function removeCartItem(userId: string, productId: string): Promise<CartItem | null> {
  try {
    // الحصول على عناصر السلة
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // البحث عن العنصر في السلة
    const existingItemIndex = cartItems.findIndex(item => 
      item.userId === userId && item.productId === productId
    );
    
    if (existingItemIndex === -1) {
      return null;
    }
    
    // حفظ العنصر قبل إزالته
    const removedItem = cartItems[existingItemIndex];
    
    // إزالة العنصر من السلة
    cartItems.splice(existingItemIndex, 1);
    
    // حفظ عناصر السلة
    localStorage.setItem(LOCAL_CART_KEY, JSON.stringify(cartItems));
    
    return removedItem;
  } catch (error) {
    console.error(`Error removing product ${productId} from cart for user ${userId}:`, error);
    throw error;
  }
}

/**
 * تفريغ سلة التسوق
 */
export async function clearCart(userId: string): Promise<boolean> {
  try {
    // الحصول على عناصر السلة
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // تصفية العناصر لإزالة عناصر المستخدم
    const updatedCartItems = cartItems.filter(item => item.userId !== userId);
    
    // حفظ عناصر السلة
    localStorage.setItem(LOCAL_CART_KEY, JSON.stringify(updatedCartItems));
    
    return true;
  } catch (error) {
    console.error(`Error clearing cart for user ${userId}:`, error);
    return false;
  }
}

/**
 * حساب إجمالي سلة التسوق
 */
export async function getCartTotal(userId: string): Promise<number> {
  try {
    // الحصول على عناصر السلة مع تفاصيل المنتجات
    const cartItems = await getCartItems(userId);
    
    // حساب الإجمالي
    return cartItems.reduce((total, item) => {
      const price = item.product.salePrice || item.product.price;
      return total + (price * item.quantity);
    }, 0);
  } catch (error) {
    console.error(`Error calculating cart total for user ${userId}:`, error);
    return 0;
  }
}

/**
 * الحصول على عدد العناصر في سلة التسوق
 */
export async function getCartItemCount(userId: string): Promise<number> {
  try {
    // الحصول على عناصر السلة
    const cartItemsJson = localStorage.getItem(LOCAL_CART_KEY);
    const cartItems: CartItem[] = cartItemsJson ? JSON.parse(cartItemsJson) : [];
    
    // تصفية العناصر حسب المستخدم
    const userCartItems = cartItems.filter(item => item.userId === userId);
    
    // حساب إجمالي الكمية
    return userCartItems.reduce((total, item) => total + item.quantity, 0);
  } catch (error) {
    console.error(`Error getting cart item count for user ${userId}:`, error);
    return 0;
  }
}
