// خدمة لإدارة المستخدمين المحليين للتطوير

// مفاتيح التخزين المحلي
const LOCAL_USERS_KEY = 'local-users';
const LOCAL_PROFILES_KEY = 'local-profiles';
const LOCAL_ADMIN_INITIALIZED_KEY = 'admin-initialized';

// الحصول على المستخدمين المحليين من localStorage
export const getLocalUsers = () => {
  if (typeof window !== 'undefined') {
    try {
      const usersJson = localStorage.getItem(LOCAL_USERS_KEY);
      return usersJson ? JSON.parse(usersJson) : [];
    } catch (error) {
      console.error('Error getting local users:', error);
      return [];
    }
  }
  return [];
};

// الحصول على ملفات المستخدمين المحليين من localStorage
export const getLocalProfiles = () => {
  if (typeof window !== 'undefined') {
    try {
      const profilesJson = localStorage.getItem(LOCAL_PROFILES_KEY);
      return profilesJson ? JSON.parse(profilesJson) : [];
    } catch (error) {
      console.error('Error getting local profiles:', error);
      return [];
    }
  }
  return [];
};

// حفظ المستخدمين المحليين في localStorage
export const saveLocalUsers = (users: any[]) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(LOCAL_USERS_KEY, JSON.stringify(users));
    } catch (error) {
      console.error('Error saving local users:', error);
    }
  }
};

// حفظ ملفات المستخدمين المحليين في localStorage
export const saveLocalProfiles = (profiles: any[]) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(LOCAL_PROFILES_KEY, JSON.stringify(profiles));
    } catch (error) {
      console.error('Error saving local profiles:', error);
    }
  }
};

// التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته
export const isAdminInitialized = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(LOCAL_ADMIN_INITIALIZED_KEY) === 'true';
  }
  return false;
};

// تعيين حالة تهيئة المستخدم المسؤول
export const setAdminInitialized = (initialized: boolean) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LOCAL_ADMIN_INITIALIZED_KEY, initialized ? 'true' : 'false');
  }
};

// إعادة تعيين جميع بيانات المستخدمين المحليين
export const resetLocalUsers = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(LOCAL_USERS_KEY);
    localStorage.removeItem(LOCAL_PROFILES_KEY);
    localStorage.removeItem(LOCAL_ADMIN_INITIALIZED_KEY);
  }
};

// إنشاء مستخدم مسؤول افتراضي
export const createDefaultAdminUser = () => {
  if (typeof window === 'undefined') {
    return false; // تنفيذ فقط في جانب العميل
  }

  console.log('Creating default admin user...');
  
  // التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته بالفعل
  if (isAdminInitialized()) {
    console.log('Admin user already initialized');
    return false;
  }
  
  const users = getLocalUsers();
  const profiles = getLocalProfiles();
  
  // مسح جميع المستخدمين والملفات الشخصية الموجودة
  resetLocalUsers();
  
  // إنشاء معرف فريد للمستخدم المدير
  const adminUserId = 'admin-user-id';
  
  // إنشاء مستخدم مدير افتراضي
  const defaultAdminUser = {
    id: adminUserId,
    email: '<EMAIL>',
    password: 'password', // في الإنتاج، يجب تشفير كلمات المرور
  };
  
  // إنشاء ملف المستخدم المدير
  const defaultAdminProfile = {
    id: adminUserId,
    email: '<EMAIL>',
    firstName: 'أحمد',
    lastName: 'المدير',
    name: 'أحمد المدير',
    role: 'admin', // تأكد من أن الدور هو 'admin'
    status: 'active',
    createdAt: new Date().toISOString(),
    lastLogin: null,
    phone: '+966500000000',
    address: {
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      zip: '12345',
      country: 'المملكة العربية السعودية'
    }
  };
  
  // إضافة المستخدم المدير إلى المستخدمين المحليين
  saveLocalUsers([defaultAdminUser]);
  saveLocalProfiles([defaultAdminProfile]);
  
  // تعيين حالة تهيئة المستخدم المسؤول
  setAdminInitialized(true);
  
  console.log('Default admin user created:', defaultAdminUser.email);
  return true;
};

// البحث عن مستخدم بالبريد الإلكتروني وكلمة المرور
export const findUserByCredentials = (email: string, password: string) => {
  const users = getLocalUsers();
  return users.find((u: any) => u.email === email && u.password === password);
};

// البحث عن ملف مستخدم بالمعرف
export const findProfileById = (id: string) => {
  const profiles = getLocalProfiles();
  return profiles.find((p: any) => p.id === id);
};

// إضافة أو تحديث ملف مستخدم
export const saveProfile = (profile: any) => {
  const profiles = getLocalProfiles();
  const existingIndex = profiles.findIndex((p: any) => p.id === profile.id);
  
  if (existingIndex >= 0) {
    // تحديث ملف موجود
    profiles[existingIndex] = { ...profiles[existingIndex], ...profile };
  } else {
    // إضافة ملف جديد
    profiles.push(profile);
  }
  
  saveLocalProfiles(profiles);
  return profile;
};
