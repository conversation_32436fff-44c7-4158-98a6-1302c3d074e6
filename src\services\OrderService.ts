/**
 * خدمة إدارة الطلبات باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { Product } from '../types';

// مفاتيح التخزين المحلي
const LOCAL_ORDERS_KEY = 'local-orders';
const LOCAL_ORDER_ITEMS_KEY = 'local-order-items';

// نوع الطلب
interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  shippingFee: number;
  tax: number;
  discount: number;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed';
  shippingAddress: Address;
  billingAddress: Address;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// نوع عنصر الطلب
interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  total: number;
  createdAt: string;
}

// نوع العنوان
interface Address {
  firstName: string;
  lastName: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
}

// نوع عنصر الطلب مع تفاصيل المنتج
interface OrderItemWithProduct extends OrderItem {
  product: Product;
}

// نوع الطلب مع العناصر
interface OrderWithItems extends Order {
  items: OrderItemWithProduct[];
}

/**
 * الحصول على جميع الطلبات للمستخدم
 */
export async function getUserOrders(userId: string): Promise<OrderWithItems[]> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    
    // تصفية الطلبات حسب المستخدم
    const userOrders = orders.filter(order => order.userId === userId);
    
    // الحصول على عناصر الطلبات
    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const orderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];
    
    // الحصول على المنتجات
    const products = sqliteDB.getProducts();
    
    // إضافة العناصر إلى كل طلب
    const ordersWithItems: OrderWithItems[] = userOrders.map(order => {
      // تصفية عناصر الطلب حسب معرف الطلب
      const items = orderItems.filter(item => item.orderId === order.id);
      
      // إضافة تفاصيل المنتج إلى كل عنصر
      const itemsWithProducts: OrderItemWithProduct[] = items.map(item => {
        const product = products.find(p => p.id.toString() === item.productId);
        
        return {
          ...item,
          product: product || {} as Product
        };
      });
      
      return {
        ...order,
        items: itemsWithProducts
      };
    });
    
    return ordersWithItems;
  } catch (error) {
    console.error(`Error getting orders for user ${userId}:`, error);
    return [];
  }
}

/**
 * الحصول على طلب بواسطة المعرف
 */
export async function getOrderById(orderId: string): Promise<OrderWithItems | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    
    // البحث عن الطلب
    const order = orders.find(order => order.id === orderId);
    
    if (!order) {
      return null;
    }
    
    // الحصول على عناصر الطلب
    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const orderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];
    
    // تصفية عناصر الطلب حسب معرف الطلب
    const items = orderItems.filter(item => item.orderId === orderId);
    
    // الحصول على المنتجات
    const products = sqliteDB.getProducts();
    
    // إضافة تفاصيل المنتج إلى كل عنصر
    const itemsWithProducts: OrderItemWithProduct[] = items.map(item => {
      const product = products.find(p => p.id.toString() === item.productId);
      
      return {
        ...item,
        product: product || {} as Product
      };
    });
    
    return {
      ...order,
      items: itemsWithProducts
    };
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    return null;
  }
}

/**
 * إنشاء طلب جديد
 */
export async function createOrder(userId: string, orderData: Partial<Order>, items: { productId: string; quantity: number }[]): Promise<OrderWithItems | null> {
  try {
    // التحقق من وجود عناصر في الطلب
    if (!items || items.length === 0) {
      throw new Error('لا يمكن إنشاء طلب بدون عناصر');
    }
    
    // الحصول على المنتجات
    const products = sqliteDB.getProducts();
    
    // التحقق من وجود جميع المنتجات وتوفرها
    for (const item of items) {
      const product = products.find(p => p.id.toString() === item.productId);
      
      if (!product) {
        throw new Error(`المنتج غير موجود: ${item.productId}`);
      }
      
      if (!product.inStock) {
        throw new Error(`المنتج غير متوفر حاليًا: ${product.name}`);
      }
    }
    
    // حساب إجمالي الطلب
    let subtotal = 0;
    const orderItems: OrderItem[] = [];
    
    for (const item of items) {
      const product = products.find(p => p.id.toString() === item.productId) as Product;
      const price = product.salePrice || product.price;
      const total = price * item.quantity;
      
      subtotal += total;
      
      orderItems.push({
        id: `order-item-${Date.now()}-${item.productId}`,
        orderId: '', // سيتم تحديثه لاحقًا
        productId: item.productId,
        quantity: item.quantity,
        price,
        total,
        createdAt: new Date().toISOString()
      });
    }
    
    // حساب الضريبة والشحن والخصم
    const tax = orderData.tax || 0;
    const shippingFee = orderData.shippingFee || 0;
    const discount = orderData.discount || 0;
    
    // حساب الإجمالي النهائي
    const total = subtotal + tax + shippingFee - discount;
    
    // إنشاء معرف فريد للطلب
    const orderId = `order-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء الطلب الجديد
    const newOrder: Order = {
      id: orderId,
      userId,
      status: 'pending',
      total,
      shippingFee,
      tax,
      discount,
      paymentMethod: orderData.paymentMethod || 'cash_on_delivery',
      paymentStatus: 'pending',
      shippingAddress: orderData.shippingAddress || {} as Address,
      billingAddress: orderData.billingAddress || {} as Address,
      notes: orderData.notes || '',
      createdAt: now,
      updatedAt: now
    };
    
    // تحديث معرف الطلب في عناصر الطلب
    for (let i = 0; i < orderItems.length; i++) {
      orderItems[i].orderId = orderId;
    }
    
    // حفظ الطلب وعناصره
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    
    const orderItemsJson = localStorage.getItem(LOCAL_ORDER_ITEMS_KEY);
    const allOrderItems: OrderItem[] = orderItemsJson ? JSON.parse(orderItemsJson) : [];
    
    // إضافة الطلب وعناصره
    orders.push(newOrder);
    allOrderItems.push(...orderItems);
    
    // حفظ الطلب وعناصره
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));
    localStorage.setItem(LOCAL_ORDER_ITEMS_KEY, JSON.stringify(allOrderItems));
    
    // تفريغ سلة التسوق
    const { clearCart } = await import('./CartService');
    await clearCart(userId);
    
    // إرجاع الطلب مع العناصر
    return {
      ...newOrder,
      items: orderItems.map(item => {
        const product = products.find(p => p.id.toString() === item.productId);
        
        return {
          ...item,
          product: product || {} as Product
        };
      })
    };
  } catch (error) {
    console.error(`Error creating order for user ${userId}:`, error);
    throw error;
  }
}

/**
 * تحديث حالة الطلب
 */
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<Order | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    
    // البحث عن الطلب
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex === -1) {
      return null;
    }
    
    // تحديث حالة الطلب
    orders[orderIndex] = {
      ...orders[orderIndex],
      status,
      updatedAt: new Date().toISOString()
    };
    
    // حفظ الطلبات
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));
    
    return orders[orderIndex];
  } catch (error) {
    console.error(`Error updating status for order ${orderId}:`, error);
    return null;
  }
}

/**
 * تحديث حالة الدفع
 */
export async function updatePaymentStatus(orderId: string, paymentStatus: Order['paymentStatus']): Promise<Order | null> {
  try {
    // محاولة الحصول على الطلبات من localStorage
    const ordersJson = localStorage.getItem(LOCAL_ORDERS_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    
    // البحث عن الطلب
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex === -1) {
      return null;
    }
    
    // تحديث حالة الدفع
    orders[orderIndex] = {
      ...orders[orderIndex],
      paymentStatus,
      updatedAt: new Date().toISOString()
    };
    
    // حفظ الطلبات
    localStorage.setItem(LOCAL_ORDERS_KEY, JSON.stringify(orders));
    
    return orders[orderIndex];
  } catch (error) {
    console.error(`Error updating payment status for order ${orderId}:`, error);
    return null;
  }
}
