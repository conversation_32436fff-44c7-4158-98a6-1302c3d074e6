/**
 * خدمة إدارة خطوط الإنتاج باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { ProductionLine } from '../types/index';
import { productionLines as initialProductionLines } from '../data/productionLines';

// مفتاح التخزين المحلي
const LOCAL_PRODUCTION_LINES_KEY = 'local-production-lines';

/**
 * الحصول على جميع خطوط الإنتاج
 */
export async function getAllProductionLines(): Promise<ProductionLine[]> {
  try {
    // محاولة الحصول على خطوط الإنتاج من SQLite
    const productionLines = sqliteDB.getProductionLines();
    
    // إذا لم تكن هناك خطوط إنتاج، قم بتهيئة خطوط الإنتاج الافتراضية
    if (productionLines.length === 0) {
      await initializeDefaultProductionLines();
      return sqliteDB.getProductionLines();
    }
    
    return productionLines;
  } catch (error) {
    console.error('Error getting production lines:', error);
    return [];
  }
}

/**
 * الحصول على خط إنتاج بواسطة المعرف
 */
export async function getProductionLineById(id: string): Promise<ProductionLine | null> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    return productionLines.find(p => p.id === id) || null;
  } catch (error) {
    console.error(`Error getting production line by ID ${id}:`, error);
    return null;
  }
}

/**
 * الحصول على خط إنتاج بواسطة الرابط
 */
export async function getProductionLineBySlug(slug: string): Promise<ProductionLine | null> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    return productionLines.find(p => p.slug === slug) || null;
  } catch (error) {
    console.error(`Error getting production line by slug ${slug}:`, error);
    return null;
  }
}

/**
 * إنشاء خط إنتاج جديد
 */
export async function createProductionLine(lineData: Partial<ProductionLine>): Promise<ProductionLine> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    
    // إنشاء معرف فريد لخط الإنتاج الجديد
    const id = lineData.id || `line-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء خط الإنتاج الجديد
    const newLine: ProductionLine = {
      id,
      name: lineData.name || '',
      slug: lineData.slug || `line-${id}`,
      description: lineData.description || '',
      capacity: lineData.capacity || '',
      specifications: lineData.specifications || {},
      images: lineData.images || [],
      category: lineData.category || '',
      features: lineData.features || [],
      createdAt: now
    };
    
    // إضافة خط الإنتاج الجديد إلى خطوط الإنتاج
    sqliteDB.saveProductionLines([...productionLines, newLine]);
    
    return newLine;
  } catch (error) {
    console.error('Error creating production line:', error);
    throw new Error('فشل إنشاء خط الإنتاج');
  }
}

/**
 * تحديث خط إنتاج
 */
export async function updateProductionLine(id: string, lineData: Partial<ProductionLine>): Promise<ProductionLine | null> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    const index = productionLines.findIndex(p => p.id === id);
    
    if (index === -1) {
      return null;
    }
    
    // تحديث خط الإنتاج
    const updatedLine: ProductionLine = {
      ...productionLines[index],
      ...lineData
    };
    
    // حفظ خطوط الإنتاج المحدثة
    productionLines[index] = updatedLine;
    sqliteDB.saveProductionLines(productionLines);
    
    return updatedLine;
  } catch (error) {
    console.error(`Error updating production line ${id}:`, error);
    return null;
  }
}

/**
 * حذف خط إنتاج
 */
export async function deleteProductionLine(id: string): Promise<boolean> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    const filteredLines = productionLines.filter(p => p.id !== id);
    
    if (filteredLines.length === productionLines.length) {
      return false;
    }
    
    sqliteDB.saveProductionLines(filteredLines);
    return true;
  } catch (error) {
    console.error(`Error deleting production line ${id}:`, error);
    return false;
  }
}

/**
 * البحث عن خطوط الإنتاج
 */
export async function searchProductionLines(query: string): Promise<ProductionLine[]> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    
    if (!query) {
      return productionLines;
    }
    
    const lowerQuery = query.toLowerCase();
    
    return productionLines.filter(p => 
      p.name.toLowerCase().includes(lowerQuery) ||
      p.description.toLowerCase().includes(lowerQuery) ||
      p.capacity.toLowerCase().includes(lowerQuery)
    );
  } catch (error) {
    console.error(`Error searching production lines for "${query}":`, error);
    return [];
  }
}

/**
 * تهيئة خطوط الإنتاج الافتراضية
 */
export async function initializeDefaultProductionLines(): Promise<void> {
  try {
    const productionLines = sqliteDB.getProductionLines();
    
    if (productionLines.length === 0) {
      console.log('Initializing default production lines...');
      sqliteDB.saveProductionLines(initialProductionLines);
    }
  } catch (error) {
    console.error('Error initializing default production lines:', error);
  }
}

// تهيئة خطوط الإنتاج الافتراضية عند تحميل الخدمة
if (typeof window !== 'undefined') {
  initializeDefaultProductionLines();
}
