/**
 * خدمة إدارة المراجعات باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { Review } from '../types/index';

// مفتاح التخزين المحلي
const LOCAL_REVIEWS_KEY = 'local-reviews';

/**
 * الحصول على جميع المراجعات
 */
export async function getAllReviews(): Promise<Review[]> {
  try {
    // محاولة الحصول على المراجعات من SQLite
    const reviews = sqliteDB.getReviews();
    return reviews;
  } catch (error) {
    console.error('Error getting reviews:', error);
    return [];
  }
}

/**
 * الحصول على مراجعة بواسطة المعرف
 */
export async function getReviewById(id: string): Promise<Review | null> {
  try {
    const reviews = sqliteDB.getReviews();
    return reviews.find(r => r.id === id) || null;
  } catch (error) {
    console.error(`Error getting review by ID ${id}:`, error);
    return null;
  }
}

/**
 * الحصول على مراجعات منتج
 */
export async function getReviewsByProductId(productId: string): Promise<Review[]> {
  try {
    const reviews = sqliteDB.getReviews();
    return reviews.filter(r => r.productId === productId);
  } catch (error) {
    console.error(`Error getting reviews for product ${productId}:`, error);
    return [];
  }
}

/**
 * الحصول على مراجعات مستخدم
 */
export async function getReviewsByUserId(userId: string): Promise<Review[]> {
  try {
    const reviews = sqliteDB.getReviews();
    return reviews.filter(r => r.userId === userId);
  } catch (error) {
    console.error(`Error getting reviews for user ${userId}:`, error);
    return [];
  }
}

/**
 * إنشاء مراجعة جديدة
 */
export async function createReview(reviewData: Partial<Review>): Promise<Review> {
  try {
    const reviews = sqliteDB.getReviews();
    
    // التحقق من وجود المراجعة
    const existingReview = reviews.find(r => 
      r.productId === reviewData.productId && r.userId === reviewData.userId
    );
    
    if (existingReview) {
      throw new Error('لقد قمت بمراجعة هذا المنتج بالفعل');
    }
    
    // إنشاء معرف فريد للمراجعة الجديدة
    const id = reviewData.id || `review-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء المراجعة الجديدة
    const newReview: Review = {
      id,
      productId: reviewData.productId || '',
      userId: reviewData.userId || '',
      userName: reviewData.userName || '',
      userAvatar: reviewData.userAvatar,
      rating: reviewData.rating || 5,
      title: reviewData.title || '',
      comment: reviewData.comment || '',
      createdAt: now,
      updatedAt: now,
      helpful: 0,
      verified: false,
      images: reviewData.images || []
    };
    
    // إضافة المراجعة الجديدة إلى المراجعات
    sqliteDB.saveReviews([...reviews, newReview]);
    
    // تحديث تقييم المنتج
    updateProductRating(reviewData.productId || '');
    
    return newReview;
  } catch (error) {
    console.error('Error creating review:', error);
    throw error;
  }
}

/**
 * تحديث مراجعة
 */
export async function updateReview(id: string, reviewData: Partial<Review>): Promise<Review | null> {
  try {
    const reviews = sqliteDB.getReviews();
    const index = reviews.findIndex(r => r.id === id);
    
    if (index === -1) {
      return null;
    }
    
    const now = new Date().toISOString();
    
    // تحديث المراجعة
    const updatedReview: Review = {
      ...reviews[index],
      ...reviewData,
      updatedAt: now
    };
    
    // حفظ المراجعات المحدثة
    reviews[index] = updatedReview;
    sqliteDB.saveReviews(reviews);
    
    // تحديث تقييم المنتج
    updateProductRating(updatedReview.productId);
    
    return updatedReview;
  } catch (error) {
    console.error(`Error updating review ${id}:`, error);
    return null;
  }
}

/**
 * حذف مراجعة
 */
export async function deleteReview(id: string): Promise<boolean> {
  try {
    const reviews = sqliteDB.getReviews();
    const reviewToDelete = reviews.find(r => r.id === id);
    
    if (!reviewToDelete) {
      return false;
    }
    
    const productId = reviewToDelete.productId;
    const filteredReviews = reviews.filter(r => r.id !== id);
    
    if (filteredReviews.length === reviews.length) {
      return false;
    }
    
    sqliteDB.saveReviews(filteredReviews);
    
    // تحديث تقييم المنتج
    updateProductRating(productId);
    
    return true;
  } catch (error) {
    console.error(`Error deleting review ${id}:`, error);
    return false;
  }
}

/**
 * تحديث تقييم المنتج
 */
async function updateProductRating(productId: string): Promise<void> {
  try {
    // الحصول على مراجعات المنتج
    const productReviews = await getReviewsByProductId(productId);
    
    if (productReviews.length === 0) {
      return;
    }
    
    // حساب متوسط التقييم
    const totalRating = productReviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / productReviews.length;
    
    // تحديث تقييم المنتج
    const products = sqliteDB.getProducts();
    const productIndex = products.findIndex(p => p.id.toString() === productId);
    
    if (productIndex === -1) {
      return;
    }
    
    products[productIndex] = {
      ...products[productIndex],
      rating: averageRating,
      reviews: productReviews.length
    };
    
    sqliteDB.saveProducts(products);
  } catch (error) {
    console.error(`Error updating product rating for product ${productId}:`, error);
  }
}

/**
 * تحديث عدد الإعجابات بالمراجعة
 */
export async function markReviewAsHelpful(id: string, userId: string): Promise<Review | null> {
  try {
    const reviews = sqliteDB.getReviews();
    const index = reviews.findIndex(r => r.id === id);
    
    if (index === -1) {
      return null;
    }
    
    // تحديث عدد الإعجابات
    const updatedReview: Review = {
      ...reviews[index],
      helpful: reviews[index].helpful + 1
    };
    
    // حفظ المراجعات المحدثة
    reviews[index] = updatedReview;
    sqliteDB.saveReviews(reviews);
    
    return updatedReview;
  } catch (error) {
    console.error(`Error marking review ${id} as helpful:`, error);
    return null;
  }
}
