/**
 * خدمة إدارة المستخدمين باستخدام SQLite
 * تستبدل هذه الخدمة استخدام LocalUserService في المشروع
 */

import { sqlite } from '../lib/sqlite';
import { User } from '../types';

// مفاتيح التخزين المحلي
const LOCAL_USERS_KEY = 'local-users';
const LOCAL_PROFILES_KEY = 'local-profiles';
const LOCAL_ADMIN_INITIALIZED_KEY = 'admin-initialized';

// الحصول على المستخدمين المحليين من localStorage
export async function getLocalUsers(): Promise<any[]> {
  if (typeof window !== 'undefined') {
    try {
      const usersJson = localStorage.getItem(LOCAL_USERS_KEY);
      return usersJson ? JSON.parse(usersJson) : [];
    } catch (error) {
      console.error('Error getting local users:', error);
      return [];
    }
  }
  return [];
};

// الحصول على ملفات المستخدمين المحليين من localStorage
export async function getLocalProfiles(): Promise<User[]> {
  try {
    const profiles = await sqlite.getUsers();
    return profiles;
  } catch (error) {
    console.error('Error getting local profiles:', error);
    return [];
  }
};

// حفظ المستخدمين المحليين في localStorage
export async function saveLocalUsers(users: any[]): Promise<void> {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(LOCAL_USERS_KEY, JSON.stringify(users));
    } catch (error) {
      console.error('Error saving local users:', error);
    }
  }
};

// حفظ ملفات المستخدمين المحليين في localStorage
export async function saveLocalProfiles(profiles: User[]): Promise<void> {
  try {
    // لا يوجد طريقة مباشرة لحفظ مجموعة من المستخدمين في sqlite
    // لذلك نقوم بحفظ كل مستخدم على حدة
    for (const profile of profiles) {
      await sqlite.updateUser(profile.id, profile);
    }
  } catch (error) {
    console.error('Error saving local profiles:', error);
  }
};

// التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته
export async function isAdminInitialized(): Promise<boolean> {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(LOCAL_ADMIN_INITIALIZED_KEY) === 'true';
  }
  return false;
};

// تعيين حالة تهيئة المستخدم المسؤول
export async function setAdminInitialized(initialized: boolean): Promise<void> {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LOCAL_ADMIN_INITIALIZED_KEY, initialized ? 'true' : 'false');
  }
};

// إعادة تعيين جميع بيانات المستخدمين المحليين
export async function resetLocalUsers(): Promise<void> {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(LOCAL_USERS_KEY);
    localStorage.removeItem(LOCAL_PROFILES_KEY);
    localStorage.removeItem(LOCAL_ADMIN_INITIALIZED_KEY);

    // إعادة تعيين المستخدمين في SQLite
    await sqlite.resetLocalUsers();
  }
};

// إنشاء مستخدم مسؤول افتراضي
export async function createDefaultAdminUser(): Promise<boolean> {
  if (typeof window === 'undefined') {
    return false; // تنفيذ فقط في جانب العميل
  }

  console.log('Creating default admin user with SQLite...');

  // التحقق مما إذا كان المستخدم المسؤول قد تم تهيئته بالفعل
  if (await isAdminInitialized()) {
    console.log('Admin user already initialized');
    return false;
  }

  const users = await getLocalUsers();
  const profiles = await getLocalProfiles();

  // مسح جميع المستخدمين والملفات الشخصية الموجودة
  await resetLocalUsers();

  // إنشاء معرف فريد للمستخدم المدير
  const adminUserId = 'admin-user-id';

  // إنشاء مستخدم مدير افتراضي
  const defaultAdminUser = {
    id: adminUserId,
    email: '<EMAIL>',
    password: 'password', // في الإنتاج، يجب تشفير كلمات المرور
  };

  // إنشاء ملف المستخدم المدير
  const defaultAdminProfile: User = {
    id: adminUserId,
    email: '<EMAIL>',
    firstName: 'أحمد',
    lastName: 'المدير',
    role: 'admin', // تأكد من أن الدور هو 'admin'
    createdAt: new Date().toISOString(),
    address: {
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      postalCode: '12345',
      country: 'المملكة العربية السعودية'
    },
    preferences: {
      language: 'ar',
      theme: 'light',
      notifications: true,
      newsletter: false
    },
    phone: '+966500000000'
  };

  // إضافة المستخدم المدير إلى المستخدمين المحليين
  await saveLocalUsers([defaultAdminUser]);
  await saveLocalProfiles([defaultAdminProfile]);

  // تعيين حالة تهيئة المستخدم المسؤول
  await setAdminInitialized(true);

  console.log('Default admin user created:', defaultAdminUser.email);
  return true;
};

// إنشاء مستخدم مدير ثانٍ
export async function createSecondAdminUser(): Promise<boolean> {
  if (typeof window === 'undefined') {
    return false; // تنفيذ فقط في جانب العميل
  }

  console.log('Creating second admin user with SQLite...');

  const users = await getLocalUsers();
  const profiles = await getLocalProfiles();

  // التحقق من وجود مستخدم مدير ثانٍ
  const managerUser = users.find((u: any) => u.email === '<EMAIL>');

  if (!managerUser) {
    console.log('Second admin user not found, creating...');

    // إنشاء معرف فريد للمستخدم المدير الثاني
    const managerUserId = 'manager-user-id';

    // إنشاء مستخدم مدير ثانٍ
    const secondAdminUser = {
      id: managerUserId,
      email: '<EMAIL>',
      password: 'password', // في الإنتاج، يجب تشفير كلمات المرور
    };

    // إنشاء ملف المستخدم المدير الثاني
    const secondAdminProfile: User = {
      id: managerUserId,
      email: '<EMAIL>',
      firstName: 'خالد',
      lastName: 'المدير',
      role: 'admin',
      createdAt: new Date().toISOString(),
      phone: '+966500000001',
      address: {
        street: 'شارع العليا',
        city: 'الرياض',
        state: 'الرياض',
        postalCode: '12345',
        country: 'المملكة العربية السعودية'
      },
      preferences: {
        language: 'ar',
        theme: 'light',
        notifications: true,
        newsletter: false
      }
    };

    // إضافة المستخدم المدير الثاني إلى المستخدمين المحليين
    await saveLocalUsers([...users, secondAdminUser]);
    await saveLocalProfiles([...profiles, secondAdminProfile]);

    console.log('Second admin user created:', secondAdminUser.email);
    return true;
  } else {
    console.log('Second admin user already exists:', managerUser.email);
    return false;
  }
};

// البحث عن مستخدم بالبريد الإلكتروني وكلمة المرور
export async function findUserByCredentials(email: string, password: string): Promise<any> {
  const users = await getLocalUsers();
  return users.find((u: any) => u.email === email && u.password === password);
};

// البحث عن ملف مستخدم بالمعرف
export async function findProfileById(id: string): Promise<User | undefined> {
  const profiles = await getLocalProfiles();
  return profiles.find((p: any) => p.id === id);
};

// البحث عن ملف مستخدم بالبريد الإلكتروني
export async function findProfileByEmail(email: string): Promise<User | undefined> {
  const profiles = await getLocalProfiles();
  return profiles.find((p: any) => p.email === email);
};

// إضافة أو تحديث ملف مستخدم
export async function saveProfile(profile: User): Promise<User> {
  const profiles = await getLocalProfiles();
  const existingIndex = profiles.findIndex((p: any) => p.id === profile.id);

  if (existingIndex >= 0) {
    // تحديث ملف موجود
    profiles[existingIndex] = { ...profiles[existingIndex], ...profile };
  } else {
    // إضافة ملف جديد
    profiles.push(profile);
  }

  await saveLocalProfiles(profiles);
  return profile;
};

// تهيئة البيانات الافتراضية
export async function initializeDefaultData(): Promise<void> {
  // تهيئة البيانات الافتراضية تتم تلقائيًا عند إنشاء كائن sqlite
  console.log('Initializing default data...');
};
