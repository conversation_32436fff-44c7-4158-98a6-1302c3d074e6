/**
 * خدمة إدارة الخدمات باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { Service } from '../types';
import { services as initialServices } from '../data/services';

// مفتاح التخزين المحلي
const LOCAL_SERVICES_KEY = 'local-services';

/**
 * الحصول على جميع الخدمات
 */
export async function getAllServices(): Promise<Service[]> {
  try {
    // محاولة الحصول على الخدمات من SQLite
    const services = sqliteDB.getServices();
    
    // إذا لم تكن هناك خدمات، قم بتهيئة الخدمات الافتراضية
    if (services.length === 0) {
      await initializeDefaultServices();
      return sqliteDB.getServices();
    }
    
    return services;
  } catch (error) {
    console.error('Error getting services:', error);
    return [];
  }
}

/**
 * الحصول على خدمة بواسطة المعرف
 */
export async function getServiceById(id: string | number): Promise<Service | null> {
  try {
    const services = sqliteDB.getServices();
    return services.find(s => s.id.toString() === id.toString()) || null;
  } catch (error) {
    console.error(`Error getting service by ID ${id}:`, error);
    return null;
  }
}

/**
 * الحصول على خدمة بواسطة الرابط
 */
export async function getServiceBySlug(slug: string): Promise<Service | null> {
  try {
    const services = sqliteDB.getServices();
    return services.find(s => s.slug === slug) || null;
  } catch (error) {
    console.error(`Error getting service by slug ${slug}:`, error);
    return null;
  }
}

/**
 * إنشاء خدمة جديدة
 */
export async function createService(serviceData: Partial<Service>): Promise<Service> {
  try {
    const services = sqliteDB.getServices();
    
    // إنشاء معرف فريد للخدمة الجديدة
    const id = serviceData.id || `service-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء الخدمة الجديدة
    const newService: Service = {
      id,
      name: serviceData.name || '',
      name_ar: serviceData.name_ar || '',
      slug: serviceData.slug || `service-${id}`,
      description: serviceData.description || '',
      description_ar: serviceData.description_ar || '',
      icon: serviceData.icon || '',
      features: serviceData.features || [],
      features_ar: serviceData.features_ar || [],
      createdAt: now
    };
    
    // إضافة الخدمة الجديدة إلى الخدمات
    sqliteDB.saveServices([...services, newService]);
    
    return newService;
  } catch (error) {
    console.error('Error creating service:', error);
    throw new Error('فشل إنشاء الخدمة');
  }
}

/**
 * تحديث خدمة
 */
export async function updateService(id: string | number, serviceData: Partial<Service>): Promise<Service | null> {
  try {
    const services = sqliteDB.getServices();
    const index = services.findIndex(s => s.id.toString() === id.toString());
    
    if (index === -1) {
      return null;
    }
    
    // تحديث الخدمة
    const updatedService: Service = {
      ...services[index],
      ...serviceData
    };
    
    // حفظ الخدمات المحدثة
    services[index] = updatedService;
    sqliteDB.saveServices(services);
    
    return updatedService;
  } catch (error) {
    console.error(`Error updating service ${id}:`, error);
    return null;
  }
}

/**
 * حذف خدمة
 */
export async function deleteService(id: string | number): Promise<boolean> {
  try {
    const services = sqliteDB.getServices();
    const filteredServices = services.filter(s => s.id.toString() !== id.toString());
    
    if (filteredServices.length === services.length) {
      return false;
    }
    
    sqliteDB.saveServices(filteredServices);
    return true;
  } catch (error) {
    console.error(`Error deleting service ${id}:`, error);
    return false;
  }
}

/**
 * البحث عن خدمات
 */
export async function searchServices(query: string): Promise<Service[]> {
  try {
    const services = sqliteDB.getServices();
    
    if (!query) {
      return services;
    }
    
    const lowerQuery = query.toLowerCase();
    
    return services.filter(s => 
      s.name.toLowerCase().includes(lowerQuery) ||
      (s.name_ar && s.name_ar.includes(lowerQuery)) ||
      s.description.toLowerCase().includes(lowerQuery) ||
      (s.description_ar && s.description_ar.includes(lowerQuery))
    );
  } catch (error) {
    console.error(`Error searching services for "${query}":`, error);
    return [];
  }
}

/**
 * تهيئة الخدمات الافتراضية
 */
export async function initializeDefaultServices(): Promise<void> {
  try {
    const services = sqliteDB.getServices();
    
    if (services.length === 0) {
      console.log('Initializing default services...');
      sqliteDB.saveServices(initialServices);
    }
  } catch (error) {
    console.error('Error initializing default services:', error);
  }
}

// تهيئة الخدمات الافتراضية عند تحميل الخدمة
if (typeof window !== 'undefined') {
  initializeDefaultServices();
}
