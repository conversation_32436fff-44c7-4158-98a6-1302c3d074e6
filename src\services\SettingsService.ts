/**
 * خدمة إدارة الإعدادات باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';

// مفتاح التخزين المحلي
const LOCAL_SETTINGS_KEY = 'local-settings';

// نوع الإعدادات
interface Settings {
  // إعدادات المتجر
  storeName: string;
  storeEmail: string;
  storePhone: string;
  storeAddress: string;
  storeLogo: string;
  storeFavicon: string;
  
  // إعدادات الشحن
  enableShipping: boolean;
  shippingMethods: {
    id: string;
    name: string;
    price: number;
    estimatedDelivery: string;
    enabled: boolean;
  }[];
  
  // إعدادات الدفع
  enablePayments: boolean;
  paymentMethods: {
    id: string;
    name: string;
    enabled: boolean;
  }[];
  
  // إعدادات النظام
  defaultLanguage: 'ar' | 'en';
  defaultCurrency: string;
  enableMultiCurrency: boolean;
  currencies: {
    code: string;
    name: string;
    symbol: string;
    rate: number;
    default: boolean;
  }[];
  
  // إعدادات الأمان
  csrfProtection: boolean;
  rateLimiting: boolean;
  maxLoginAttempts: number;
  
  // إعدادات التسويق
  enableCoupons: boolean;
  enableNewsletter: boolean;
  enableReviews: boolean;
  enableWishlist: boolean;
  
  // إعدادات SEO
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  
  // إعدادات أخرى
  maintenanceMode: boolean;
  enableDarkMode: boolean;
  
  // تاريخ التحديث
  updatedAt: string;
}

// الإعدادات الافتراضية
const DEFAULT_SETTINGS: Settings = {
  // إعدادات المتجر
  storeName: 'كوميرس برو',
  storeEmail: '<EMAIL>',
  storePhone: '+966500000000',
  storeAddress: 'الرياض، المملكة العربية السعودية',
  storeLogo: '/images/logo.svg',
  storeFavicon: '/favicon.ico',
  
  // إعدادات الشحن
  enableShipping: true,
  shippingMethods: [
    {
      id: 'standard',
      name: 'الشحن القياسي',
      price: 15,
      estimatedDelivery: '3-5 أيام عمل',
      enabled: true
    },
    {
      id: 'express',
      name: 'الشحن السريع',
      price: 30,
      estimatedDelivery: '1-2 يوم عمل',
      enabled: true
    }
  ],
  
  // إعدادات الدفع
  enablePayments: true,
  paymentMethods: [
    {
      id: 'cod',
      name: 'الدفع عند الاستلام',
      enabled: true
    },
    {
      id: 'mada',
      name: 'مدى',
      enabled: true
    },
    {
      id: 'visa',
      name: 'فيزا / ماستركارد',
      enabled: true
    }
  ],
  
  // إعدادات النظام
  defaultLanguage: 'ar',
  defaultCurrency: 'SAR',
  enableMultiCurrency: true,
  currencies: [
    {
      code: 'SAR',
      name: 'ريال سعودي',
      symbol: 'ر.س',
      rate: 1,
      default: true
    },
    {
      code: 'USD',
      name: 'دولار أمريكي',
      symbol: '$',
      rate: 0.27,
      default: false
    },
    {
      code: 'CNY',
      name: 'يوان صيني',
      symbol: '¥',
      rate: 1.73,
      default: false
    }
  ],
  
  // إعدادات الأمان
  csrfProtection: true,
  rateLimiting: true,
  maxLoginAttempts: 5,
  
  // إعدادات التسويق
  enableCoupons: true,
  enableNewsletter: true,
  enableReviews: true,
  enableWishlist: true,
  
  // إعدادات SEO
  seoTitle: 'كوميرس برو | حلول الأعمال المتكاملة',
  seoDescription: 'منصة تجارية متكاملة تقدم خدمات البيع بالتجزئة والجملة وخطوط الإنتاج وخدمات الأعمال والمزيد.',
  seoKeywords: 'تجارة إلكترونية، بيع بالتجزئة، بيع بالجملة، خطوط إنتاج، خدمات أعمال',
  
  // إعدادات أخرى
  maintenanceMode: false,
  enableDarkMode: true,
  
  // تاريخ التحديث
  updatedAt: new Date().toISOString()
};

/**
 * الحصول على جميع الإعدادات
 */
export async function getAllSettings(): Promise<Settings> {
  try {
    // محاولة الحصول على الإعدادات من localStorage
    const settingsJson = localStorage.getItem(LOCAL_SETTINGS_KEY);
    
    if (!settingsJson) {
      // إذا لم تكن هناك إعدادات، قم بتهيئة الإعدادات الافتراضية
      await initializeDefaultSettings();
      return DEFAULT_SETTINGS;
    }
    
    return JSON.parse(settingsJson);
  } catch (error) {
    console.error('Error getting settings:', error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * الحصول على إعداد محدد
 */
export async function getSetting<K extends keyof Settings>(key: K): Promise<Settings[K]> {
  try {
    const settings = await getAllSettings();
    return settings[key];
  } catch (error) {
    console.error(`Error getting setting ${key}:`, error);
    return DEFAULT_SETTINGS[key];
  }
}

/**
 * تحديث إعداد محدد
 */
export async function updateSetting<K extends keyof Settings>(key: K, value: Settings[K]): Promise<boolean> {
  try {
    const settings = await getAllSettings();
    
    // تحديث الإعداد
    settings[key] = value;
    settings.updatedAt = new Date().toISOString();
    
    // حفظ الإعدادات
    localStorage.setItem(LOCAL_SETTINGS_KEY, JSON.stringify(settings));
    
    return true;
  } catch (error) {
    console.error(`Error updating setting ${key}:`, error);
    return false;
  }
}

/**
 * تحديث عدة إعدادات
 */
export async function updateSettings(newSettings: Partial<Settings>): Promise<boolean> {
  try {
    const settings = await getAllSettings();
    
    // تحديث الإعدادات
    const updatedSettings = {
      ...settings,
      ...newSettings,
      updatedAt: new Date().toISOString()
    };
    
    // حفظ الإعدادات
    localStorage.setItem(LOCAL_SETTINGS_KEY, JSON.stringify(updatedSettings));
    
    return true;
  } catch (error) {
    console.error('Error updating settings:', error);
    return false;
  }
}

/**
 * إعادة تعيين الإعدادات إلى الإعدادات الافتراضية
 */
export async function resetSettings(): Promise<boolean> {
  try {
    // حفظ الإعدادات الافتراضية
    localStorage.setItem(LOCAL_SETTINGS_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    return true;
  } catch (error) {
    console.error('Error resetting settings:', error);
    return false;
  }
}

/**
 * تهيئة الإعدادات الافتراضية
 */
export async function initializeDefaultSettings(): Promise<boolean> {
  try {
    // التحقق مما إذا كانت الإعدادات موجودة بالفعل
    const settingsJson = localStorage.getItem(LOCAL_SETTINGS_KEY);
    
    if (!settingsJson) {
      // حفظ الإعدادات الافتراضية
      localStorage.setItem(LOCAL_SETTINGS_KEY, JSON.stringify(DEFAULT_SETTINGS));
      console.log('Initialized default settings');
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing default settings:', error);
    return false;
  }
}

// تهيئة الإعدادات الافتراضية عند تحميل الخدمة
if (typeof window !== 'undefined') {
  initializeDefaultSettings();
}
