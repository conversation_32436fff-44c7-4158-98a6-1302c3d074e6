/**
 * خدمة إدارة طلبات الجملة باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';

// مفتاح التخزين المحلي
const LOCAL_WHOLESALE_QUOTES_KEY = 'local-wholesale-quotes';

// نوع طلب الجملة
interface WholesaleQuote {
  id: string;
  userId: string | null;
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  productInterest: string;
  quantity: number;
  message: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

/**
 * الحصول على جميع طلبات الجملة
 */
export async function getAllWholesaleQuotes(): Promise<WholesaleQuote[]> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    return quotes;
  } catch (error) {
    console.error('Error getting wholesale quotes:', error);
    return [];
  }
}

/**
 * الحصول على طلبات الجملة للمستخدم
 */
export async function getUserWholesaleQuotes(userId: string): Promise<WholesaleQuote[]> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    // تصفية الطلبات حسب المستخدم
    return quotes.filter(quote => quote.userId === userId);
  } catch (error) {
    console.error(`Error getting wholesale quotes for user ${userId}:`, error);
    return [];
  }
}

/**
 * الحصول على طلب جملة بواسطة المعرف
 */
export async function getWholesaleQuoteById(quoteId: string): Promise<WholesaleQuote | null> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    // البحث عن الطلب
    return quotes.find(quote => quote.id === quoteId) || null;
  } catch (error) {
    console.error(`Error getting wholesale quote ${quoteId}:`, error);
    return null;
  }
}

/**
 * إنشاء طلب جملة جديد
 */
export async function createWholesaleQuote(quoteData: Partial<WholesaleQuote>): Promise<WholesaleQuote> {
  try {
    // التحقق من البيانات المطلوبة
    if (!quoteData.contactName || !quoteData.email) {
      throw new Error('الاسم والبريد الإلكتروني مطلوبان');
    }
    
    // إنشاء معرف فريد للطلب
    const quoteId = `quote-${Date.now()}`;
    const now = new Date().toISOString();
    
    // إنشاء طلب الجملة الجديد
    const newQuote: WholesaleQuote = {
      id: quoteId,
      userId: quoteData.userId || null,
      companyName: quoteData.companyName || '',
      contactName: quoteData.contactName,
      email: quoteData.email,
      phone: quoteData.phone || '',
      productInterest: quoteData.productInterest || '',
      quantity: quoteData.quantity || 0,
      message: quoteData.message || '',
      status: 'pending',
      createdAt: now,
      updatedAt: now
    };
    
    // حفظ طلب الجملة
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    quotes.push(newQuote);
    
    localStorage.setItem(LOCAL_WHOLESALE_QUOTES_KEY, JSON.stringify(quotes));
    
    return newQuote;
  } catch (error) {
    console.error('Error creating wholesale quote:', error);
    throw error;
  }
}

/**
 * تحديث حالة طلب الجملة
 */
export async function updateWholesaleQuoteStatus(quoteId: string, status: WholesaleQuote['status']): Promise<WholesaleQuote | null> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    // البحث عن الطلب
    const quoteIndex = quotes.findIndex(quote => quote.id === quoteId);
    
    if (quoteIndex === -1) {
      return null;
    }
    
    // تحديث حالة الطلب
    quotes[quoteIndex] = {
      ...quotes[quoteIndex],
      status,
      updatedAt: new Date().toISOString()
    };
    
    // حفظ طلبات الجملة
    localStorage.setItem(LOCAL_WHOLESALE_QUOTES_KEY, JSON.stringify(quotes));
    
    return quotes[quoteIndex];
  } catch (error) {
    console.error(`Error updating status for wholesale quote ${quoteId}:`, error);
    return null;
  }
}

/**
 * حذف طلب جملة
 */
export async function deleteWholesaleQuote(quoteId: string): Promise<boolean> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    // البحث عن الطلب
    const quoteIndex = quotes.findIndex(quote => quote.id === quoteId);
    
    if (quoteIndex === -1) {
      return false;
    }
    
    // حذف الطلب
    quotes.splice(quoteIndex, 1);
    
    // حفظ طلبات الجملة
    localStorage.setItem(LOCAL_WHOLESALE_QUOTES_KEY, JSON.stringify(quotes));
    
    return true;
  } catch (error) {
    console.error(`Error deleting wholesale quote ${quoteId}:`, error);
    return false;
  }
}

/**
 * البحث عن طلبات الجملة
 */
export async function searchWholesaleQuotes(query: string): Promise<WholesaleQuote[]> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    if (!query) {
      return quotes;
    }
    
    const lowerQuery = query.toLowerCase();
    
    // البحث في طلبات الجملة
    return quotes.filter(quote => 
      quote.companyName.toLowerCase().includes(lowerQuery) ||
      quote.contactName.toLowerCase().includes(lowerQuery) ||
      quote.email.toLowerCase().includes(lowerQuery) ||
      quote.productInterest.toLowerCase().includes(lowerQuery) ||
      quote.message.toLowerCase().includes(lowerQuery)
    );
  } catch (error) {
    console.error(`Error searching wholesale quotes for "${query}":`, error);
    return [];
  }
}

/**
 * تصفية طلبات الجملة حسب الحالة
 */
export async function filterWholesaleQuotesByStatus(status: WholesaleQuote['status']): Promise<WholesaleQuote[]> {
  try {
    // محاولة الحصول على طلبات الجملة من localStorage
    const quotesJson = localStorage.getItem(LOCAL_WHOLESALE_QUOTES_KEY);
    const quotes: WholesaleQuote[] = quotesJson ? JSON.parse(quotesJson) : [];
    
    // تصفية الطلبات حسب الحالة
    return quotes.filter(quote => quote.status === status);
  } catch (error) {
    console.error(`Error filtering wholesale quotes by status "${status}":`, error);
    return [];
  }
}
