/**
 * خدمة إدارة قائمة المفضلة باستخدام SQLite
 */

import { sqliteDB, sqlite } from '../lib/sqlite';
import { Product } from '../types';

// مفتاح التخزين المحلي
const LOCAL_WISHLIST_KEY = 'local-wishlist';

// نوع عنصر المفضلة
interface WishlistItem {
  id: string;
  userId: string;
  productId: string;
  createdAt: string;
}

// نوع عنصر المفضلة مع تفاصيل المنتج
interface WishlistItemWithProduct extends WishlistItem {
  product: Product;
}

/**
 * الحصول على عناصر قائمة المفضلة للمستخدم
 */
export async function getWishlistItems(userId: string): Promise<WishlistItemWithProduct[]> {
  try {
    // محاولة الحصول على عناصر المفضلة من localStorage
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // تصفية العناصر حسب المستخدم
    const userWishlistItems = wishlistItems.filter(item => item.userId === userId);
    
    // الحصول على المنتجات
    const products = sqliteDB.getProducts();
    
    // إضافة تفاصيل المنتج إلى كل عنصر
    const wishlistItemsWithProducts: WishlistItemWithProduct[] = userWishlistItems.map(item => {
      const product = products.find(p => p.id.toString() === item.productId);
      
      if (!product) {
        // إذا لم يتم العثور على المنتج، قم بإزالة العنصر من المفضلة
        removeFromWishlist(userId, item.productId);
        return null;
      }
      
      return {
        ...item,
        product
      };
    }).filter(Boolean) as WishlistItemWithProduct[];
    
    return wishlistItemsWithProducts;
  } catch (error) {
    console.error(`Error getting wishlist items for user ${userId}:`, error);
    return [];
  }
}

/**
 * إضافة منتج إلى قائمة المفضلة
 */
export async function addToWishlist(userId: string, productId: string): Promise<WishlistItem | null> {
  try {
    // التحقق من وجود المنتج
    const products = sqliteDB.getProducts();
    const product = products.find(p => p.id.toString() === productId);
    
    if (!product) {
      throw new Error('المنتج غير موجود');
    }
    
    // الحصول على عناصر المفضلة
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // التحقق مما إذا كان المنتج موجودًا بالفعل في المفضلة
    const existingItem = wishlistItems.find(item => 
      item.userId === userId && item.productId === productId
    );
    
    if (existingItem) {
      // المنتج موجود بالفعل في المفضلة
      return existingItem;
    }
    
    // إنشاء عنصر جديد
    const newItem: WishlistItem = {
      id: `wishlist-item-${Date.now()}`,
      userId,
      productId,
      createdAt: new Date().toISOString()
    };
    
    // إضافة العنصر إلى المفضلة
    wishlistItems.push(newItem);
    
    // حفظ عناصر المفضلة
    localStorage.setItem(LOCAL_WISHLIST_KEY, JSON.stringify(wishlistItems));
    
    return newItem;
  } catch (error) {
    console.error(`Error adding product ${productId} to wishlist for user ${userId}:`, error);
    throw error;
  }
}

/**
 * إزالة منتج من قائمة المفضلة
 */
export async function removeFromWishlist(userId: string, productId: string): Promise<WishlistItem | null> {
  try {
    // الحصول على عناصر المفضلة
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // البحث عن العنصر في المفضلة
    const existingItemIndex = wishlistItems.findIndex(item => 
      item.userId === userId && item.productId === productId
    );
    
    if (existingItemIndex === -1) {
      return null;
    }
    
    // حفظ العنصر قبل إزالته
    const removedItem = wishlistItems[existingItemIndex];
    
    // إزالة العنصر من المفضلة
    wishlistItems.splice(existingItemIndex, 1);
    
    // حفظ عناصر المفضلة
    localStorage.setItem(LOCAL_WISHLIST_KEY, JSON.stringify(wishlistItems));
    
    return removedItem;
  } catch (error) {
    console.error(`Error removing product ${productId} from wishlist for user ${userId}:`, error);
    throw error;
  }
}

/**
 * التحقق مما إذا كان المنتج في قائمة المفضلة
 */
export async function isInWishlist(userId: string, productId: string): Promise<boolean> {
  try {
    // الحصول على عناصر المفضلة
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // البحث عن العنصر في المفضلة
    return wishlistItems.some(item => 
      item.userId === userId && item.productId === productId
    );
  } catch (error) {
    console.error(`Error checking if product ${productId} is in wishlist for user ${userId}:`, error);
    return false;
  }
}

/**
 * تفريغ قائمة المفضلة
 */
export async function clearWishlist(userId: string): Promise<boolean> {
  try {
    // الحصول على عناصر المفضلة
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // تصفية العناصر لإزالة عناصر المستخدم
    const updatedWishlistItems = wishlistItems.filter(item => item.userId !== userId);
    
    // حفظ عناصر المفضلة
    localStorage.setItem(LOCAL_WISHLIST_KEY, JSON.stringify(updatedWishlistItems));
    
    return true;
  } catch (error) {
    console.error(`Error clearing wishlist for user ${userId}:`, error);
    return false;
  }
}

/**
 * الحصول على عدد العناصر في قائمة المفضلة
 */
export async function getWishlistItemCount(userId: string): Promise<number> {
  try {
    // الحصول على عناصر المفضلة
    const wishlistItemsJson = localStorage.getItem(LOCAL_WISHLIST_KEY);
    const wishlistItems: WishlistItem[] = wishlistItemsJson ? JSON.parse(wishlistItemsJson) : [];
    
    // تصفية العناصر حسب المستخدم
    const userWishlistItems = wishlistItems.filter(item => item.userId === userId);
    
    return userWishlistItems.length;
  } catch (error) {
    console.error(`Error getting wishlist item count for user ${userId}:`, error);
    return 0;
  }
}

/**
 * نقل عنصر من قائمة المفضلة إلى سلة التسوق
 */
export async function moveToCart(userId: string, productId: string, quantity: number = 1): Promise<boolean> {
  try {
    // إضافة المنتج إلى سلة التسوق
    const { addToCart } = await import('./CartService');
    await addToCart(userId, productId, quantity);
    
    // إزالة المنتج من قائمة المفضلة
    await removeFromWishlist(userId, productId);
    
    return true;
  } catch (error) {
    console.error(`Error moving product ${productId} from wishlist to cart for user ${userId}:`, error);
    return false;
  }
}
