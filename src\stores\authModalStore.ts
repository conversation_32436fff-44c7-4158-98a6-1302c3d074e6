import { create } from 'zustand';

interface AuthModalState {
  isOpen: boolean;
  mode: 'sign-in' | 'sign-up';
  openModal: (mode?: 'sign-in' | 'sign-up') => void;
  closeModal: () => void;
}

export const useAuthModalStore = create<AuthModalState>((set) => ({
  isOpen: false,
  mode: 'sign-in',
  
  openModal: (mode = 'sign-in') => set({ isOpen: true, mode }),
  closeModal: () => set({ isOpen: false }),
}));
