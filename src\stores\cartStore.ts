import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CartItem, Product } from '../types/index';
import { useAuthStore } from './authStore';

interface CartStore {
  items: CartItem[];
  addItem: (product: Product, quantity: number) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
  isProductInCart: (productId: string) => boolean;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],

      addItem: (product, quantity) => {
        console.log('Adding item to cart:', product.name, 'quantity:', quantity);

        const { items } = get();
        const existingItem = items.find(item => item.productId === product.id);

        if (existingItem) {
          console.log('Item already in cart, updating quantity');
          set({
            items: items.map(item =>
              item.productId === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            ),
          });
        } else {
          console.log('Adding new item to cart');
          set({
            items: [
              ...items,
              {
                id: crypto.randomUUID(),
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity,
                image: product.images[0],
              },
            ],
          });
        }
      },

      removeItem: (id) => {
        console.log('Removing item from cart:', id);
        const { items } = get();
        set({ items: items.filter(item => item.id !== id) });
      },

      updateQuantity: (id, quantity) => {
        console.log('Updating item quantity:', id, 'new quantity:', quantity);
        const { items } = get();

        if (quantity <= 0) {
          // إذا كانت الكمية صفر أو أقل، قم بإزالة العنصر
          set({ items: items.filter(item => item.id !== id) });
        } else {
          // تحديث الكمية
          set({
            items: items.map(item =>
              item.id === id ? { ...item, quantity } : item
            ),
          });
        }
      },

      clearCart: () => {
        console.log('Clearing cart');
        set({ items: [] });
      },

      getTotalItems: () => {
        const { items } = get();
        return items.reduce((total, item) => total + item.quantity, 0);
      },

      getTotalPrice: () => {
        const { items } = get();
        return items.reduce((total, item) => total + item.price * item.quantity, 0);
      },

      isProductInCart: (productId) => {
        const { items } = get();
        return items.some(item => item.productId === productId);
      },
    }),
    {
      name: 'cart-storage',
    }
  )
);