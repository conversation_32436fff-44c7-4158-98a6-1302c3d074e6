import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Currency } from '../types';

// قائمة العملات المدعومة
const supportedCurrencies: Currency[] = [
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    rate: 1,
    default: true,
  },
  {
    code: 'SAR',
    name: 'Saudi Riyal',
    symbol: '﷼',
    rate: 3.75,
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    rate: 7.25,
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    rate: 0.93,
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    rate: 0.79,
  },
];

interface CurrencyState {
  currency: string;
  setCurrency: (currency: string) => void;
  convertPrice: (priceInUSD: number) => number;
  formatPrice: (price: number) => string;
  getCurrencySymbol: () => string;
  getSupportedCurrencies: () => Currency[];
}

export const useCurrencyStore = create<CurrencyState>()(
  persist(
    (set, get) => ({
      currency: 'USD',

      setCurrency: (currency) => {
        // التحقق من أن العملة مدعومة
        if (supportedCurrencies.some(c => c.code === currency)) {
          set({ currency });
        }
      },

      // تحويل السعر من الدولار الأمريكي إلى العملة المحددة
      convertPrice: (priceInUSD) => {
        const { currency } = get();
        const selectedCurrency = supportedCurrencies.find(c => c.code === currency);

        if (!selectedCurrency) {
          return priceInUSD; // إذا لم يتم العثور على العملة، أعد السعر بالدولار الأمريكي
        }

        return priceInUSD * selectedCurrency.rate;
      },

      // تنسيق السعر بالعملة المحددة
      formatPrice: (price) => {
        const { currency } = get();
        const selectedCurrency = supportedCurrencies.find(c => c.code === currency);

        if (!selectedCurrency) {
          return `$${price.toFixed(2)}`; // إذا لم يتم العثور على العملة، استخدم الدولار الأمريكي
        }

        // تنسيق السعر حسب العملة
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: selectedCurrency.code,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(price);
      },

      // الحصول على رمز العملة
      getCurrencySymbol: () => {
        const { currency } = get();
        const selectedCurrency = supportedCurrencies.find(c => c.code === currency);

        return selectedCurrency?.symbol || '$';
      },

      // الحصول على قائمة العملات المدعومة
      getSupportedCurrencies: () => {
        return supportedCurrencies;
      },
    }),
    {
      name: 'currency-storage',
    }
  )
);

// تحديث وظيفة formatCurrency في ملف utils.ts لاستخدام مخزن العملات
export const formatCurrencyWithStore = (amount: number): string => {
  const { formatPrice } = useCurrencyStore.getState();
  return formatPrice(amount);
};