import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface LanguageState {
  language: 'en' | 'ar';
  direction: 'ltr' | 'rtl';
  setLanguage: (language: 'en' | 'ar') => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set) => ({
      language: 'ar', // اللغة الافتراضية هي العربية
      direction: 'rtl', // الاتجاه الافتراضي هو من اليمين لليسار
      setLanguage: (language) => set({ 
        language,
        direction: language === 'ar' ? 'rtl' : 'ltr',
      }),
    }),
    {
      name: 'language-storage',
    }
  )
);