import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// ThemeMode remains to store the user's explicit preference
export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemePreferenceState {
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
}

// This store now primarily holds the user's selected theme preference,
// which can then be used to instruct next-themes.
export const useThemeStore = create<ThemePreferenceState>()(
  persist(
    (set) => ({
      themeMode: 'system', // Default to system preference
      setThemeMode: (mode: ThemeMode) => set({ themeMode: mode }),
    }),
    {
      name: 'theme-preference-storage', // Changed name to reflect its new role
      // You might want to consider a migration if old 'theme-storage' exists and has relevant data
    }
  )
);

// The initializeTheme and applyTheme functions are no longer needed here,
// as next-themes will handle DOM manipulation and system preference detection.
