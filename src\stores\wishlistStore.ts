import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Product } from '../types/index';

interface WishlistStore {
  items: Product[];
  addItem: (item: Product) => void;
  removeItem: (id: string) => void;
  isInWishlist: (id: string) => boolean;
  clearWishlist: () => void;
  getWishlistCount: () => number;
}

export const useWishlistStore = create<WishlistStore>()(
  persist(
    (set, get) => ({
      items: [],

      addItem: (item) => {
        console.log('Adding item to wishlist:', item.name);

        // التحقق من عدم وجود العنصر بالفعل في المفضلة
        if (!get().isInWishlist(item.id)) {
          set((state) => ({
            items: [...state.items, item],
          }));
        }
      },

      removeItem: (id) => {
        console.log('Removing item from wishlist:', id);

        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }));
      },

      isInWishlist: (id) => {
        return get().items.some((item) => item.id === id);
      },

      clearWishlist: () => {
        console.log('Clearing wishlist');
        set({ items: [] });
      },

      getWishlistCount: () => {
        return get().items.length;
      },
    }),
    {
      name: 'wishlist-storage',
    }
  )
);