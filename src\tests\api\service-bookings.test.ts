/**
 * Comprehensive API Tests for Service Bookings
 * Tests all CRUD operations and edge cases
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

// Mock Next.js request/response
const mockRequest = (method: string, body?: any, searchParams?: URLSearchParams) => ({
  method,
  json: async () => body,
  url: `http://localhost:3000/api/service-bookings${searchParams ? `?${searchParams}` : ''}`,
});

const mockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// Mock SQLite database
jest.mock('../../lib/sqlite', () => ({
  sqlite: {
    getServiceBookings: jest.fn(() => []),
    saveServiceBookings: jest.fn(),
  },
}));

import { POST, GET, PUT } from '../../app/api/service-bookings/route';

describe('Service Bookings API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/service-bookings', () => {
    it('should create a new service booking with valid data', async () => {
      const validBooking = {
        serviceName: 'Inspection Services',
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        companyName: 'Test Company',
        serviceDate: '2025-01-15',
        preferredTime: '10:00 AM',
        urgency: 'normal',
        message: 'Test booking',
      };

      const request = mockRequest('POST', validBooking) as any;
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.bookingId).toBeDefined();
    });

    it('should reject booking with missing required fields', async () => {
      const invalidBooking = {
        serviceName: 'Inspection Services',
        // Missing fullName, email, phone, serviceDate
      };

      const request = mockRequest('POST', invalidBooking) as any;
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Missing required fields');
    });

    it('should reject booking with invalid email format', async () => {
      const invalidBooking = {
        serviceName: 'Inspection Services',
        fullName: 'John Doe',
        email: 'invalid-email',
        phone: '+1234567890',
        serviceDate: '2025-01-15',
      };

      const request = mockRequest('POST', invalidBooking) as any;
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid email format');
    });

    it('should reject booking with past service date', async () => {
      const invalidBooking = {
        serviceName: 'Inspection Services',
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        serviceDate: '2020-01-01', // Past date
      };

      const request = mockRequest('POST', invalidBooking) as any;
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Service date must be in the future');
    });
  });

  describe('GET /api/service-bookings', () => {
    it('should retrieve all service bookings', async () => {
      const request = mockRequest('GET') as any;
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.bookings).toBeDefined();
      expect(data.data.total).toBeDefined();
    });

    it('should handle pagination parameters', async () => {
      const searchParams = new URLSearchParams({
        limit: '10',
        offset: '5',
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.limit).toBe(10);
      expect(data.data.offset).toBe(5);
    });
  });

  describe('PUT /api/service-bookings', () => {
    it('should update booking status with valid data', async () => {
      const updateData = {
        bookingId: 'sb-123456789',
        status: 'confirmed',
        notes: 'Booking confirmed by admin',
      };

      const request = mockRequest('PUT', updateData) as any;
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('should reject update with invalid status', async () => {
      const updateData = {
        bookingId: 'sb-123456789',
        status: 'invalid-status',
      };

      const request = mockRequest('PUT', updateData) as any;
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid status value');
    });

    it('should reject update with missing required fields', async () => {
      const updateData = {
        // Missing bookingId and status
        notes: 'Some notes',
      };

      const request = mockRequest('PUT', updateData) as any;
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Booking ID and status are required');
    });
  });
});

// Performance Tests
describe('Service Bookings API Performance', () => {
  it('should handle multiple concurrent requests', async () => {
    const validBooking = {
      serviceName: 'Inspection Services',
      fullName: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      serviceDate: '2025-01-15',
    };

    const requests = Array(10).fill(null).map(() => 
      POST(mockRequest('POST', validBooking) as any)
    );

    const startTime = Date.now();
    const responses = await Promise.all(requests);
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    responses.forEach(response => {
      expect(response.status).toBe(201);
    });
  });

  it('should handle large data retrieval efficiently', async () => {
    const request = mockRequest('GET', undefined, new URLSearchParams({
      limit: '1000',
      offset: '0',
    })) as any;

    const startTime = Date.now();
    const response = await GET(request);
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(500); // Should complete within 500ms
    expect(response.status).toBe(200);
  });
});

// Security Tests
describe('Service Bookings API Security', () => {
  it('should sanitize input data', async () => {
    const maliciousBooking = {
      serviceName: '<script>alert("xss")</script>',
      fullName: 'John<script>alert("xss")</script>Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      serviceDate: '2025-01-15',
      message: '<img src="x" onerror="alert(\'xss\')" />',
    };

    const request = mockRequest('POST', maliciousBooking) as any;
    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    // In a real implementation, we would check that the data is sanitized
  });

  it('should handle SQL injection attempts', async () => {
    const sqlInjectionBooking = {
      serviceName: "'; DROP TABLE service_bookings; --",
      fullName: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      serviceDate: '2025-01-15',
    };

    const request = mockRequest('POST', sqlInjectionBooking) as any;
    const response = await POST(request);

    // Should not crash and should handle gracefully
    expect(response.status).toBeLessThan(500);
  });
});
