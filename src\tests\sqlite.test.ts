/**
 * اختبارات SQLite
 * هذا الملف يحتوي على اختبارات للتأكد من أن التحول إلى SQLite يعمل بشكل صحيح
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { sqlite } from '../lib/sqlite';
import { getSQLiteDB, closeSQLiteDB } from '../lib/sqliteServer';
import { User, Product, Service } from '../types';
import { signIn } from '../lib/auth';

// اختبارات SQLite في بيئة المتصفح
describe('SQLite Browser Tests', () => {
  // قبل كل اختبار، قم بتهيئة البيانات الافتراضية
  beforeEach(() => {
    // محاكاة localStorage
    const localStorageMock = (() => {
      let store: Record<string, string> = {};
      return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => {
          store[key] = value.toString();
        },
        removeItem: (key: string) => {
          delete store[key];
        },
        clear: () => {
          store = {};
        }
      };
    })();

    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    });
  });

  // اختبار الحصول على المستخدمين
  it('should get users', async () => {
    const users = await sqlite.getUsers();
    expect(users).toBeDefined();
    expect(Array.isArray(users)).toBe(true);
  });

  // اختبار إضافة مستخدم جديد
  it('should add a new user', async () => {
    const initialUsers = await sqlite.getUsers();
    const initialCount = initialUsers.length;

    const newUserPayload = {
      email: '<EMAIL>', // Ensure this email is unique for the test context
      firstName: 'Test',
      lastName: 'User',
      role: 'user'
      // id and createdAt will be handled by createUser
    };

    const createdUser = await sqlite.createUser(newUserPayload, 'password123');

    expect(createdUser).toBeDefined();
    expect(createdUser?.email).toBe(newUserPayload.email);
    expect(createdUser?.id).toBeDefined();

    const updatedUsers = await sqlite.getUsers();
    expect(updatedUsers.length).toBe(initialCount + 1);
    const foundUser = updatedUsers.find((u: User) => u.email === newUserPayload.email);
    expect(foundUser).toBeDefined();
    expect(foundUser?.id).toBe(createdUser?.id);
  });

  // اختبار تسجيل الدخول لمستخدم موجود عبر وحدة المصادقة
  it('should sign in an existing user via auth module', async () => {
    // تسجيل الدخول
    const { user, error } = await signIn('<EMAIL>', 'password');

    expect(error).toBeNull();
    expect(user).toBeDefined();
    expect(user?.email).toBe('<EMAIL>');
  });
});

// اختبارات SQLite في بيئة الخادم
/** @jest-environment node */
describe('SQLite Server Tests', () => {
  let db: any;

  // قبل كل اختبار، قم بالحصول على نسخة من قاعدة البيانات
  beforeEach(() => {
    db = getSQLiteDB();
  });

  // بعد كل اختبار، قم بإغلاق قاعدة البيانات
  afterEach(() => {
    closeSQLiteDB();
  });

  // اختبار الحصول على المستخدمين
  it('should get users', async () => {
    const users = await db.getUsers();
    expect(users).toBeDefined();
    expect(Array.isArray(users)).toBe(true);
  });

  // اختبار إنشاء مستخدم جديد
  it('should create a new user', async () => {
    const newUser = await db.createUser({
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'user'
    });

    expect(newUser).toBeDefined();
    expect(newUser.id).toBeDefined();
    expect(newUser.email).toBe('<EMAIL>');

    // التحقق من وجود المستخدم في قاعدة البيانات
    const user = await db.getUserByEmail('<EMAIL>');
    expect(user).toBeDefined();
    expect(user?.email).toBe('<EMAIL>');
  });

  // اختبار تحديث مستخدم
  it('should update a user', async () => {
    // إنشاء مستخدم جديد
    const newUser = await db.createUser({
      email: '<EMAIL>',
      firstName: 'Before',
      lastName: 'Update',
      role: 'user'
    });

    // تحديث المستخدم
    const updatedUser = await db.updateUser(newUser.id, {
      firstName: 'After',
      lastName: 'Update'
    });

    expect(updatedUser).toBeDefined();
    expect(updatedUser?.firstName).toBe('After');
    expect(updatedUser?.lastName).toBe('Update');

    // التحقق من تحديث المستخدم في قاعدة البيانات
    const user = await db.getUserById(newUser.id);
    expect(user).toBeDefined();
    expect(user?.firstName).toBe('After');
    expect(user?.lastName).toBe('Update');
  });

  // اختبار حذف مستخدم
  it('should delete a user', async () => {
    // إنشاء مستخدم جديد
    const newUser = await db.createUser({
      email: '<EMAIL>',
      firstName: 'Delete',
      lastName: 'Test',
      role: 'user'
    });

    // حذف المستخدم
    const result = await db.deleteUser(newUser.id);
    expect(result).toBe(true);

    // التحقق من حذف المستخدم من قاعدة البيانات
    const user = await db.getUserById(newUser.id);
    expect(user).toBeNull();
  });
});
