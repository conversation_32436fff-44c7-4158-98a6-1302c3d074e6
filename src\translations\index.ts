import { useLanguageStore } from '../stores/languageStore';

type TranslationKey = string;
type Translation = Record<TranslationKey, string>;

const translations: Record<'en' | 'ar', Translation> = {
  en: {
    // App Info
    'app.name': 'ARTAL',
    'app.tagline': 'Your Complete Business Solution',

    // Navigation
    'nav.home': 'Home',
    'nav.shop': 'Shop',
    'nav.productionLines': 'Production Lines',
    'nav.services': 'Services',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',

    // Header Actions
    'header.search': 'Search',
    'header.searchPlaceholder': 'Search products...',
    'header.specialOffers': 'Special Offers',
    'header.wishlist': 'Wishlist',
    'header.cart': 'Cart',
    'header.account': 'Account',
    'header.signIn': 'Sign In',
    'header.signUp': 'Sign Up',

    // Hero Section
    'hero.cta.explore': 'Explore Products',
    'hero.cta.services': 'Our Services',

    // Business Solutions
    'solutions.title': 'Comprehensive Business Solutions',
    'solutions.subtitle': 'Explore our full range of services designed to meet all your commercial needs.',
    'solutions.features': 'Key Features',

    // Products Section
    'products.title': 'Featured Products',
    'products.subtitle': 'Discover our latest innovations and best-selling industrial solutions.',
    'products.viewAll': 'View All Products',
    'products.newArrivals': 'New Arrivals',
    'products.inStock': 'In Stock',
    'products.outOfStock': 'Out of Stock',
    'products.addToCart': 'Add to Cart',
    'products.requestQuote': 'Request Quote',

    // Shop Section
    'shop.title': 'Shop',
    'shop.filters.title': 'Filters',
    'shop.filters.categories': 'Categories',
    'shop.filters.allCategories': 'All Categories',
    'shop.filters.priceRange': 'Price Range',
    'shop.filters.availability': 'Availability & Features',
    'shop.filters.inStockOnly': 'In Stock Only',
    'shop.filters.onSaleOnly': 'On Sale Only',
    'shop.filters.featuredOnly': 'Featured Products Only',
    'shop.filters.newArrivalsOnly': 'New Arrivals Only',
    'shop.filters.reset': 'Reset Filters',
    'shop.filters.clearAll': 'Clear All',
    'shop.filters.activeFilters': 'Active Filters',
    'shop.filters.showFilters': 'Show Filters',
    'shop.filters.hideFilters': 'Hide Filters',
    'shop.search.placeholder': 'Search for products...',
    'shop.search.clear': 'Clear search',
    'shop.sort.featured': 'Featured',
    'shop.sort.newest': 'Newest',
    'shop.sort.priceAsc': 'Price: Low to High',
    'shop.sort.priceDesc': 'Price: High to Low',
    'shop.sort.popular': 'Most Popular',
    'shop.sort.discount': 'Highest Discount',
    'shop.categories.browse': 'Browse by Category',
    'shop.categories.viewAll': 'View All in Category',

    // Blog Section
    'blog.title': 'Latest Industry Insights',
    'blog.subtitle': 'Stay informed with our latest articles, trends, and industry news.',
    'blog.readMore': 'Read More',
    'blog.viewAll': 'View All Articles',

    // Common Actions
    'actions.viewDetails': 'View Details',
    'actions.learnMore': 'Learn More',
    'actions.getStarted': 'Get Started',
    'actions.contactUs': 'Contact Us',
    'actions.bookNow': 'Book Now',

    // Authentication
    'auth.signIn': 'Sign In',
    'auth.signUp': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.firstName': 'First Name',
    'auth.lastName': 'Last Name',
    'auth.processing': 'Processing...',
    'auth.noAccount': 'Don\'t have an account?',
    'auth.haveAccount': 'Already have an account?',
    'auth.signInSuccess': 'Signed in successfully!',
    'auth.signUpSuccess': 'Account created successfully!',
    'auth.genericError': 'An error occurred. Please try again.',
    'auth.emailRequired': 'Email is required',
    'auth.invalidEmail': 'Invalid email format',
    'auth.passwordRequired': 'Password is required',
    'auth.passwordTooShort': 'Password must be at least 6 characters',
    'auth.firstNameRequired': 'First name is required',
    'auth.lastNameRequired': 'Last name is required',
    'auth.passwordRequirements': 'Password must be at least 6 characters',
    'auth.invalidCredentials': 'Invalid email or password',
    'auth.emailAlreadyInUse': 'This email is already registered',
    'auth.accountCreated': 'Account created successfully',
    'auth.loginSuccess': 'Logged in successfully',

    // Cart
    'cart.title': 'Your Cart',
    'cart.empty': 'Your cart is empty',
    'cart.emptyMessage': 'Add some items to your cart to get started',
    'cart.continueShopping': 'Continue Shopping',
    'cart.subtotal': 'Subtotal',
    'cart.shipping': 'Shipping',
    'cart.calculatedAtCheckout': 'Calculated at checkout',
    'cart.tax': 'Tax',
    'cart.total': 'Total',
    'cart.proceedToCheckout': 'Proceed to Checkout',
    'cart.remove': 'Remove',
    'cart.quantity': 'Quantity',
    'cart.clearCart': 'Clear Cart',
    'cart.orderSummary': 'Order Summary',

    // Wishlist
    'wishlist.title': 'My Wishlist',
    'wishlist.empty': 'Your wishlist is empty',
    'wishlist.emptyMessage': 'Add items to your wishlist to save them for later',
    'wishlist.continueShopping': 'Continue Shopping',
    'wishlist.clearAll': 'Clear All',
    'wishlist.addAllToCart': 'Add All to Cart',
    'wishlist.remove': 'Remove',
    'wishlist.addToCart': 'Add to Cart',
    'wishlist.alreadyInCart': 'Already in cart',

    // Account
    'account.myAccount': 'My Account',
    'account.profile': 'Profile',
    'account.orders': 'Orders',
    'account.addresses': 'Addresses',
    'account.paymentMethods': 'Payment Methods',
    'account.wishlist': 'Wishlist',
    'account.loyalty': 'Loyalty Program',
    'account.settings': 'Settings',
    'account.signOut': 'Sign Out',
    'account.notLoggedIn': 'Not logged in',
    'account.loginRequired': 'You need to log in to access your account',
    'account.profileInformation': 'Profile Information',
    'account.firstName': 'First Name',
    'account.lastName': 'Last Name',
    'account.email': 'Email',
    'account.phone': 'Phone',
    'account.company': 'Company',
    'account.emailCannotBeChanged': 'Email address cannot be changed',
    'account.saveChanges': 'Save Changes',
    'account.profileUpdated': 'Profile updated successfully',
    'account.myOrders': 'My Orders',
    'account.shippingAddresses': 'Shipping Addresses',

    // Orders
    'orders.searchOrders': 'Search orders',
    'orders.allOrders': 'All Orders',
    'orders.statusProcessing': 'Processing',
    'orders.statusShipped': 'Shipped',
    'orders.statusDelivered': 'Delivered',
    'orders.statusCancelled': 'Cancelled',
    'orders.noOrders': 'No orders found',
    'orders.noOrdersMatchingFilters': 'No orders matching your filters',
    'orders.noOrdersYet': 'You haven\'t placed any orders yet',
    'orders.items': 'items',
    'orders.orderItems': 'Order Items',
    'orders.quantity': 'Quantity',
    'orders.shippingAddress': 'Shipping Address',
    'orders.tracking': 'Tracking Information',
    'orders.trackPackage': 'Track Package',

    // Addresses
    'addresses.addNew': 'Add New Address',
    'addresses.editAddress': 'Edit Address',
    'addresses.addNewAddress': 'Add New Address',
    'addresses.fullName': 'Full Name',
    'addresses.streetAddress': 'Street Address',
    'addresses.city': 'City',
    'addresses.stateProvince': 'State/Province',
    'addresses.postalCode': 'Postal Code',
    'addresses.country': 'Country',
    'addresses.setAsDefault': 'Set as default address',
    'addresses.addAddress': 'Add Address',
    'addresses.noAddresses': 'No addresses found',
    'addresses.addAddressPrompt': 'Add a shipping address to speed up checkout',
    'addresses.addFirst': 'Add Your First Address',
    'addresses.default': 'Default',
    'addresses.setDefault': 'Set as Default',

    // Payment Methods
    'payment.addNew': 'Add New Payment Method',
    'payment.editCard': 'Edit Card',
    'payment.addNewCard': 'Add New Card',
    'payment.cardNumber': 'Card Number',
    'payment.cardholderName': 'Cardholder Name',
    'payment.expiryDate': 'Expiry Date',
    'payment.cvv': 'CVV',
    'payment.setAsDefault': 'Set as default payment method',
    'payment.addCard': 'Add Card',
    'payment.noCards': 'No payment methods found',
    'payment.addCardPrompt': 'Add a payment method to speed up checkout',
    'payment.addFirst': 'Add Your First Payment Method',
    'payment.default': 'Default',
    'payment.setDefault': 'Set as Default',
    'payment.expires': 'Expires',
    'payment.securityNote': 'For demonstration purposes only. Do not enter real card information.',
    'payment.creditCard': 'Credit Card',
    'payment.paypal': 'PayPal',
    'payment.bankTransfer': 'Bank Transfer',

    // Settings
    'settings.password': 'Password',
    'settings.notifications': 'Notifications',
    'settings.preferences': 'Preferences',
    'settings.currentPassword': 'Current Password',
    'settings.newPassword': 'New Password',
    'settings.confirmPassword': 'Confirm Password',
    'settings.changePassword': 'Change Password',
    'settings.passwordChanged': 'Password changed successfully',
    'settings.emailNotifications': 'Enable email notifications',
    'settings.orderUpdates': 'Order status updates',
    'settings.promotions': 'Promotions and special offers',
    'settings.newsletter': 'Newsletter subscription',
    'settings.notificationsUpdated': 'Notification preferences updated',
    'settings.savePreferences': 'Save Preferences',
    'settings.language': 'Language',
    'settings.currency': 'Currency',
    'settings.theme': 'Theme',
    'settings.lightMode': 'Light Mode',
    'settings.darkMode': 'Dark Mode',

    // Common
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',

    // Wholesale Quote Form
    'wholesale.wholesaleTitle': 'Wholesale Quote Request',
    'wholesale.customProductTitle': 'Custom Product Quote Request',
    'wholesale.companyName': 'Company Name',
    'wholesale.contactName': 'Contact Name',
    'wholesale.email': 'Email',
    'wholesale.phone': 'Phone',
    'wholesale.productType': 'Product Type',
    'wholesale.productTypePlaceholder': 'e.g., Electronics, Machinery, Raw Materials',
    'wholesale.specifications': 'Product Specifications',
    'wholesale.specificationsPlaceholder': 'Please provide detailed specifications for your product requirements',
    'wholesale.targetQuantity': 'Target Quantity',
    'wholesale.targetQuantityPlaceholder': 'e.g., 1000 units',
    'wholesale.targetPrice': 'Target Price (Optional)',
    'wholesale.targetPricePlaceholder': 'Your desired price point',
    'wholesale.timeline': 'Timeline',
    'wholesale.timelinePlaceholder': 'When do you need the products?',
    'wholesale.additionalNotes': 'Additional Notes',
    'wholesale.additionalNotesPlaceholder': 'Any other information you\'d like to share',
    'wholesale.uploadFiles': 'Upload Files (Optional)',
    'wholesale.dropFilesHere': 'Drop files here or click to upload product drawings, specifications, or reference images',
    'wholesale.selectFiles': 'Select Files',
    'wholesale.cancel': 'Cancel',
    'wholesale.submitRequest': 'Submit Request',
    'wholesale.submitting': 'Submitting...',
    'wholesale.requestSubmitted': 'Request Submitted',
    'wholesale.thankYou': 'Thank you for your request. Our team will contact you shortly.',
    'wholesale.close': 'Close',
    'wholesale.authRequired': 'You must be logged in to submit a quote request',
    'wholesale.submitError': 'An error occurred while submitting your request. Please try again.',

    // Common (Added placeholders)
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.confirm': 'Confirm',
    'common.close': 'Close',
    'common.loading': 'Loading',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.previous': 'Previous',
    'common.next': 'Next',

    // Footer
    'footer.quickLinks': 'Quick Links',
    'footer.support': 'Customer Support',
    'footer.contact': 'Contact Information',
    'footer.rights': 'All rights reserved',
  },
  ar: {
    // App Info
    'app.name': 'ارتال',
    'app.tagline': 'حلول الأعمال المتكاملة',

    // Navigation
    'nav.home': 'الرئيسية',
    'nav.shop': 'المتجر',
    'nav.productionLines': 'خطوط الإنتاج',
    'nav.services': 'الخدمات',
    'nav.blog': 'المدونة',
    'nav.contact': 'اتصل بنا',

    // Header Actions
    'header.search': 'بحث',
    'header.searchPlaceholder': 'ابحث عن المنتجات...',
    'header.specialOffers': 'عروض خاصة',
    'header.wishlist': 'المفضلة',
    'header.cart': 'السلة',
    'header.account': 'الحساب',
    'header.signIn': 'تسجيل الدخول',
    'header.signUp': 'إنشاء حساب',

    // Hero Section
    'hero.cta.explore': 'استكشف المنتجات',
    'hero.cta.services': 'خدماتنا',

    // Business Solutions
    'solutions.title': 'حلول أعمال شاملة',
    'solutions.subtitle': 'استكشف مجموعتنا الكاملة من الخدمات المصممة لتلبية جميع احتياجات عملك.',
    'solutions.features': 'الميزات الرئيسية',

    // Products Section
    'products.title': 'المنتجات المميزة',
    'products.subtitle': 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً.',
    'products.viewAll': 'عرض جميع المنتجات',
    'products.newArrivals': 'وصل حديثاً',
    'products.inStock': 'متوفر',
    'products.outOfStock': 'نفذ المخزون',
    'products.addToCart': 'أضف إلى السلة',
    'products.requestQuote': 'طلب عرض سعر',

    // Shop Section
    'shop.title': 'المتجر',
    'shop.filters.title': 'الفلاتر',
    'shop.filters.categories': 'الفئات',
    'shop.filters.allCategories': 'جميع الفئات',
    'shop.filters.priceRange': 'نطاق السعر',
    'shop.filters.availability': 'التوفر والميزات',
    'shop.filters.inStockOnly': 'متوفر في المخزون فقط',
    'shop.filters.onSaleOnly': 'العروض فقط',
    'shop.filters.featuredOnly': 'المنتجات المميزة فقط',
    'shop.filters.newArrivalsOnly': 'وصل حديثاً فقط',
    'shop.filters.reset': 'إعادة تعيين الفلاتر',
    'shop.filters.clearAll': 'مسح الكل',
    'shop.filters.activeFilters': 'الفلاتر النشطة',
    'shop.filters.showFilters': 'عرض الفلاتر',
    'shop.filters.hideFilters': 'إخفاء الفلاتر',
    'shop.search.placeholder': 'ابحث عن منتجات...',
    'shop.search.clear': 'مسح البحث',
    'shop.sort.featured': 'مميز',
    'shop.sort.newest': 'الأحدث',
    'shop.sort.priceAsc': 'السعر: من الأقل إلى الأعلى',
    'shop.sort.priceDesc': 'السعر: من الأعلى إلى الأقل',
    'shop.sort.popular': 'الأكثر شعبية',
    'shop.sort.discount': 'أعلى خصم',
    'shop.categories.browse': 'تصفح حسب الفئة',
    'shop.categories.viewAll': 'عرض الكل في الفئة',

    // Blog Section
    'blog.title': 'آخر رؤى الصناعة',
    'blog.subtitle': 'ابق على اطلاع بأحدث المقالات والاتجاهات وأخبار الصناعة.',
    'blog.readMore': 'اقرأ المزيد',
    'blog.viewAll': 'عرض جميع المقالات',

    // Common Actions
    'actions.viewDetails': 'عرض التفاصيل',
    'actions.learnMore': 'اعرف المزيد',
    'actions.getStarted': 'ابدأ الآن',
    'actions.contactUs': 'اتصل بنا',
    'actions.bookNow': 'احجز الآن',

    // Authentication
    'auth.signIn': 'تسجيل الدخول',
    'auth.signUp': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.forgotPassword': 'نسيت كلمة المرور؟',
    'auth.firstName': 'الاسم الأول',
    'auth.lastName': 'الاسم الأخير',
    'auth.processing': 'جاري المعالجة...',
    'auth.noAccount': 'ليس لديك حساب؟',
    'auth.haveAccount': 'لديك حساب بالفعل؟',
    'auth.signInSuccess': 'تم تسجيل الدخول بنجاح!',
    'auth.signUpSuccess': 'تم إنشاء الحساب بنجاح!',
    'auth.genericError': 'حدث خطأ. يرجى المحاولة مرة أخرى.',
    'auth.emailRequired': 'البريد الإلكتروني مطلوب',
    'auth.invalidEmail': 'تنسيق البريد الإلكتروني غير صالح',
    'auth.passwordRequired': 'كلمة المرور مطلوبة',
    'auth.passwordTooShort': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    'auth.firstNameRequired': 'الاسم الأول مطلوب',
    'auth.lastNameRequired': 'الاسم الأخير مطلوب',
    'auth.passwordRequirements': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    'auth.invalidCredentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    'auth.emailAlreadyInUse': 'هذا البريد الإلكتروني مسجل بالفعل',
    'auth.accountCreated': 'تم إنشاء الحساب بنجاح',
    'auth.loginSuccess': 'تم تسجيل الدخول بنجاح',

    // Cart
    'cart.title': 'السلة',
    'cart.empty': 'السلة فارغة',
    'cart.emptyMessage': 'أضف بعض المنتجات إلى السلة للبدء',
    'cart.continueShopping': 'مواصلة التسوق',
    'cart.subtotal': 'المجموع الفرعي',
    'cart.shipping': 'الشحن',
    'cart.calculatedAtCheckout': 'يتم حسابها عند الدفع',
    'cart.tax': 'الضريبة',
    'cart.total': 'المجموع',
    'cart.proceedToCheckout': 'المتابعة إلى الدفع',
    'cart.remove': 'إزالة',
    'cart.quantity': 'الكمية',
    'cart.clearCart': 'تفريغ السلة',
    'cart.orderSummary': 'ملخص الطلب',

    // Wishlist
    'wishlist.title': 'المفضلة',
    'wishlist.empty': 'قائمة المفضلة فارغة',
    'wishlist.emptyMessage': 'أضف عناصر إلى المفضلة لحفظها لوقت لاحق',
    'wishlist.continueShopping': 'مواصلة التسوق',
    'wishlist.clearAll': 'مسح الكل',
    'wishlist.addAllToCart': 'إضافة الكل إلى السلة',
    'wishlist.remove': 'إزالة',
    'wishlist.addToCart': 'أضف إلى السلة',
    'wishlist.alreadyInCart': 'موجود بالفعل في السلة',

    // Account
    'account.myAccount': 'حسابي',
    'account.profile': 'الملف الشخصي',
    'account.orders': 'الطلبات',
    'account.addresses': 'العناوين',
    'account.paymentMethods': 'طرق الدفع',
    'account.wishlist': 'المفضلة',
    'account.loyalty': 'برنامج الولاء',
    'account.settings': 'الإعدادات',
    'account.signOut': 'تسجيل الخروج',
    'account.notLoggedIn': 'لم يتم تسجيل الدخول',
    'account.loginRequired': 'تحتاج إلى تسجيل الدخول للوصول إلى حسابك',
    'account.profileInformation': 'معلومات الملف الشخصي',
    'account.firstName': 'الاسم الأول',
    'account.lastName': 'الاسم الأخير',
    'account.email': 'البريد الإلكتروني',
    'account.phone': 'الهاتف',
    'account.company': 'الشركة',
    'account.emailCannotBeChanged': 'لا يمكن تغيير عنوان البريد الإلكتروني',
    'account.saveChanges': 'حفظ التغييرات',
    'account.profileUpdated': 'تم تحديث الملف الشخصي بنجاح',
    'account.myOrders': 'طلباتي',
    'account.shippingAddresses': 'عناوين الشحن',

    // Orders
    'orders.searchOrders': 'البحث في الطلبات',
    'orders.allOrders': 'جميع الطلبات',
    'orders.statusProcessing': 'قيد المعالجة',
    'orders.statusShipped': 'تم الشحن',
    'orders.statusDelivered': 'تم التسليم',
    'orders.statusCancelled': 'ملغي',
    'orders.noOrders': 'لم يتم العثور على طلبات',
    'orders.noOrdersMatchingFilters': 'لا توجد طلبات تطابق المرشحات',
    'orders.noOrdersYet': 'لم تقم بإجراء أي طلبات بعد',
    'orders.items': 'عناصر',
    'orders.orderItems': 'عناصر الطلب',
    'orders.quantity': 'الكمية',
    'orders.shippingAddress': 'عنوان الشحن',
    'orders.tracking': 'معلومات التتبع',
    'orders.trackPackage': 'تتبع الشحنة',

    // Addresses
    'addresses.addNew': 'إضافة عنوان جديد',
    'addresses.editAddress': 'تعديل العنوان',
    'addresses.addNewAddress': 'إضافة عنوان جديد',
    'addresses.fullName': 'الاسم الكامل',
    'addresses.streetAddress': 'عنوان الشارع',
    'addresses.city': 'المدينة',
    'addresses.stateProvince': 'الولاية/المنطقة',
    'addresses.postalCode': 'الرمز البريدي',
    'addresses.country': 'البلد',
    'addresses.setAsDefault': 'تعيين كعنوان افتراضي',
    'addresses.addAddress': 'إضافة عنوان',
    'addresses.noAddresses': 'لم يتم العثور على عناوين',
    'addresses.addAddressPrompt': 'أضف عنوان شحن لتسريع عملية الدفع',
    'addresses.addFirst': 'أضف عنوانك الأول',
    'addresses.default': 'افتراضي',
    'addresses.setDefault': 'تعيين كافتراضي',

    // Payment Methods
    'payment.addNew': 'إضافة طريقة دفع جديدة',
    'payment.editCard': 'تعديل البطاقة',
    'payment.addNewCard': 'إضافة بطاقة جديدة',
    'payment.cardNumber': 'رقم البطاقة',
    'payment.cardholderName': 'اسم حامل البطاقة',
    'payment.expiryDate': 'تاريخ الانتهاء',
    'payment.cvv': 'رمز الأمان',
    'payment.setAsDefault': 'تعيين كطريقة دفع افتراضية',
    'payment.addCard': 'إضافة بطاقة',
    'payment.noCards': 'لم يتم العثور على طرق دفع',
    'payment.addCardPrompt': 'أضف طريقة دفع لتسريع عملية الدفع',
    'payment.addFirst': 'أضف طريقة الدفع الأولى',
    'payment.default': 'افتراضي',
    'payment.setDefault': 'تعيين كافتراضي',
    'payment.expires': 'تنتهي الصلاحية',
    'payment.securityNote': 'لأغراض العرض فقط. لا تدخل معلومات بطاقة حقيقية.',
    'payment.creditCard': 'بطاقة ائتمان',
    'payment.paypal': 'باي بال',
    'payment.bankTransfer': 'تحويل بنكي',

    // Settings
    'settings.password': 'كلمة المرور',
    'settings.notifications': 'الإشعارات',
    'settings.preferences': 'التفضيلات',
    'settings.currentPassword': 'كلمة المرور الحالية',
    'settings.newPassword': 'كلمة المرور الجديدة',
    'settings.confirmPassword': 'تأكيد كلمة المرور',
    'settings.changePassword': 'تغيير كلمة المرور',
    'settings.passwordChanged': 'تم تغيير كلمة المرور بنجاح',
    'settings.emailNotifications': 'تفعيل إشعارات البريد الإلكتروني',
    'settings.orderUpdates': 'تحديثات حالة الطلب',
    'settings.promotions': 'العروض والعروض الخاصة',
    'settings.newsletter': 'الاشتراك في النشرة الإخبارية',
    'settings.notificationsUpdated': 'تم تحديث تفضيلات الإشعارات',
    'settings.savePreferences': 'حفظ التفضيلات',
    'settings.language': 'اللغة',
    'settings.currency': 'العملة',
    'settings.theme': 'المظهر',
    'settings.lightMode': 'الوضع الفاتح',
    'settings.darkMode': 'الوضع الداكن',

    // Common
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',

    // Wholesale Quote Form
    'wholesale.wholesaleTitle': 'طلب عرض سعر بالجملة',
    'wholesale.customProductTitle': 'طلب عرض سعر لمنتج مخصص',
    'wholesale.companyName': 'اسم الشركة',
    'wholesale.contactName': 'اسم جهة الاتصال',
    'wholesale.email': 'البريد الإلكتروني',
    'wholesale.phone': 'رقم الهاتف',
    'wholesale.productType': 'نوع المنتج',
    'wholesale.productTypePlaceholder': 'مثال: إلكترونيات، آلات، مواد خام',
    'wholesale.specifications': 'مواصفات المنتج',
    'wholesale.specificationsPlaceholder': 'يرجى تقديم مواصفات مفصلة لمتطلبات المنتج الخاص بك',
    'wholesale.targetQuantity': 'الكمية المستهدفة',
    'wholesale.targetQuantityPlaceholder': 'مثال: 1000 وحدة',
    'wholesale.targetPrice': 'السعر المستهدف (اختياري)',
    'wholesale.targetPricePlaceholder': 'نقطة السعر المرغوبة',
    'wholesale.timeline': 'الجدول الزمني',
    'wholesale.timelinePlaceholder': 'متى تحتاج المنتجات؟',
    'wholesale.additionalNotes': 'ملاحظات إضافية',
    'wholesale.additionalNotesPlaceholder': 'أي معلومات أخرى ترغب في مشاركتها',
    'wholesale.uploadFiles': 'تحميل الملفات (اختياري)',
    'wholesale.dropFilesHere': 'قم بإسقاط الملفات هنا أو انقر لتحميل رسومات المنتج أو المواصفات أو الصور المرجعية',
    'wholesale.selectFiles': 'اختر الملفات',
    'wholesale.cancel': 'إلغاء',
    'wholesale.submitRequest': 'إرسال الطلب',
    'wholesale.submitting': 'جاري الإرسال...',
    'wholesale.requestSubmitted': 'تم إرسال الطلب',
    'wholesale.thankYou': 'شكرًا لطلبك. سيتواصل فريقنا معك قريبًا.',
    'wholesale.close': 'إغلاق',
    'wholesale.authRequired': 'يجب تسجيل الدخول لتقديم طلب عرض سعر',
    'wholesale.submitError': 'حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.',

    // Common (Added placeholders)
    'common.yes': '[ARABIC TEXT FOR KEY: common.yes]',
    'common.no': '[ARABIC TEXT FOR KEY: common.no]',
    'common.confirm': '[ARABIC TEXT FOR KEY: common.confirm]',
    'common.close': '[ARABIC TEXT FOR KEY: common.close]',
    'common.loading': '[ARABIC TEXT FOR KEY: common.loading]',
    'common.error': '[ARABIC TEXT FOR KEY: common.error]',
    'common.success': '[ARABIC TEXT FOR KEY: common.success]',
    'common.previous': '[ARABIC TEXT FOR KEY: common.previous]',
    'common.next': '[ARABIC TEXT FOR KEY: common.next]',

    // Footer
    'footer.quickLinks': 'روابط سريعة',
    'footer.support': 'دعم العملاء',
    'footer.contact': 'معلومات الاتصال',
    'footer.rights': 'جميع الحقوق محفوظة',
  }
};

export type Locale = 'en' | 'ar';

export function useLocale(): Locale {
  // Extract language from the path
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  const localeMatch = pathname.match(/^\/(ar|en)/);
  return localeMatch ? (localeMatch[1] as Locale) : 'en'; // English is the default language
}

export function useTranslation() {
  const { language } = useLanguageStore();
  const locale = useLocale();

  // Use language from the path or from the store
  const currentLanguage = locale || language;

  const t = (key: TranslationKey): string => {
    return translations[currentLanguage][key] || key;
  };

  return { t, language: currentLanguage, locale };
}