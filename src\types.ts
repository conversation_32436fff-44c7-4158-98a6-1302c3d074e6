// تعريف أنواع البيانات المستخدمة في التطبيق

// نوع المستخدم
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'admin' | 'user';
  createdAt: string;
  updatedAt?: string;
  avatar?: string;
  phone?: string;
  address?: Address;
  preferences?: UserPreferences;
  company?: string;
}

// نوع العنوان
export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// نوع تفضيلات المستخدم
export interface UserPreferences {
  language: string;
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  newsletter: boolean;
}

// نوع المنتج
export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  salePrice?: number;
  category: string;
  tags: string[];
  images: string[];
  featured: boolean;
  inStock: boolean;
  rating: number;
  reviews: number;
  specifications?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// نوع الخدمة
export interface Service {
  id: number;
  title: string;
  slug: string;
  description: string;
  shortDescription: string;
  icon: string;
  image: string;
  features: string[];
  price?: number;
  priceUnit?: string;
  category: string;
  createdAt: string;
  updatedAt: string;
}

// نوع منشور المدونة
export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author: Author;
  image: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  updatedAt?: string;
  readTime: number;
}

// نوع المؤلف
export interface Author {
  id: number;
  name: string;
  avatar: string;
  bio?: string;
  role: string;
}

// نوع عنصر التصفية
export interface ClearanceItem {
  id: number;
  name: string;
  slug: string;
  description: string;
  originalPrice: number;
  clearancePrice: number;
  discount: number;
  image: string;
  category: string;
  inStock: boolean;
  quantity: number;
  expiryDate?: string;
}

// نوع عنصر السلة
export interface CartItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

// نوع الطلب
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: OrderStatus;
  total: number;
  shipping: number;
  tax: number;
  address: Address;
  paymentMethod: string;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
  trackingNumber?: string;
  notes?: string;
}

// نوع عنصر الطلب
export interface OrderItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  quantity: number;
  total: number;
}

// حالة الطلب
export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

// حالة الدفع
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// نوع الإشعار
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  link?: string;
}

// نوع الإشعار
export type NotificationType = 'info' | 'success' | 'warning' | 'error';

// نوع حالة المصادقة
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  setUser: (user: User | null) => void;
}

// نوع حالة اللغة
export interface LanguageState {
  language: 'ar' | 'en';
  setLanguage: (language: 'ar' | 'en') => void;
  toggleLanguage: () => void;
}

// نوع حالة السمة
export interface ThemeState {
  isDarkMode: boolean;
  toggleTheme: () => void;
  setDarkMode: (isDarkMode: boolean) => void;
}

// نوع حالة السلة
export interface CartState {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (id: number) => void;
  updateQuantity: (id: number, quantity: number) => void;
  clearCart: () => void;
  getTotal: () => number;
  getItemsCount: () => number;
}
